#!/usr/bin/env node

console.log('🔥 INVINCIBLE V3 SYSTEM - COMPREHENSIVE TEST');
console.log('===========================================');

async function testV3API() {
  console.log('\n🎯 Testing V3 API endpoint...');
  
  try {
    const response = await fetch('http://localhost:3000/api/invincible-v3', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        topic: 'V3 system testing workflow',
        contentLength: 500,
        tone: 'technical',
        preset: 'fast'
      }),
    });

    console.log(`📡 V3 API Response: ${response.status}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ V3 API Success');
      console.log(`📊 Result Preview:`, {
        success: data.success,
        sessionId: data.sessionId,
        executionTime: data.executionTime,
        hasArticle: !!data.result?.article,
        wordCount: data.result?.article?.wordCount,
        version: data.version,
        messageCount: data.messages?.length || 0,
      });
      
      if (data.result?.article) {
        console.log(`📝 Generated Title: "${data.result.article.title}"`);
        console.log(`📊 Quality Score: ${data.result.performance?.qualityScore}/100`);
        console.log(`🔍 Research Sources: ${data.result.analytics?.totalSources}`);
      }
      
      return data;
    } else {
      const errorData = await response.json().catch(() => ({ error: 'Parse failed' }));
      console.log('❌ V3 API Failed');
      console.log('📄 Error:', errorData);
      return null;
    }
  } catch (error) {
    console.log('❌ V3 API Error:', error.message);
    return null;
  }
}

async function testV3Streaming() {
  console.log('\n🌊 Testing V3 streaming endpoint...');
  
  try {
    const response = await fetch('http://localhost:3000/api/invincible-v3/stream', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        topic: 'streaming workflow test',
        contentLength: 400,
        tone: 'professional',
        preset: 'fast'
      }),
    });

    console.log(`📡 V3 Streaming Response: ${response.status}`);
    
    if (response.ok && response.body) {
      console.log('✅ V3 Streaming Started');
      
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let eventCount = 0;
      let workflowEvents = [];
      let finalResult = null;
      
      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          
          const chunk = decoder.decode(value);
          const lines = chunk.split('\n');
          
          for (const line of lines) {
            if (line.startsWith('event:')) {
              eventCount++;
              const eventType = line.replace('event:', '').trim();
              workflowEvents.push(eventType);
              
              console.log(`   📨 Event ${eventCount}: ${eventType}`);
            }
            
            if (line.startsWith('data:')) {
              try {
                const data = JSON.parse(line.replace('data:', '').trim());
                if (data.result) {
                  finalResult = data;
                }
              } catch (e) {
                // Ignore JSON parse errors for streaming data
              }
            }
          }
          
          // Limit test duration
          if (eventCount > 15) break;
        }
        
        console.log(`📊 V3 Streaming Test Results:`);
        console.log(`   📨 Total Events: ${eventCount}`);
        console.log(`   🔄 Event Types: ${[...new Set(workflowEvents)].join(', ')}`);
        console.log(`   ✅ Completed: ${finalResult ? 'Yes' : 'No'}`);
        
        if (finalResult) {
          console.log(`   📝 Final Word Count: ${finalResult.result?.article?.wordCount}`);
          console.log(`   ⏱️ Total Time: ${finalResult.totalTime}ms`);
        }
        
        return finalResult;
        
      } finally {
        reader.releaseLock();
      }
    } else {
      console.log('❌ V3 Streaming Failed to Start');
      return null;
    }
  } catch (error) {
    console.log('❌ V3 Streaming Test Error:', error.message);
    return null;
  }
}

async function testV3SystemStatus() {
  console.log('\n📊 Testing V3 system status...');
  
  try {
    const response = await fetch('http://localhost:3000/api/invincible-v3');
    
    if (response.ok) {
      const status = await response.json();
      console.log('✅ V3 System Status:', {
        name: status.name,
        version: status.version,
        status: status.status,
        presets: status.presets,
      });
      return true;
    } else {
      console.log('❌ V3 System Status Failed');
      return false;
    }
  } catch (error) {
    console.log('❌ V3 System Status Error:', error.message);
    return false;
  }
}

async function testAPIKeyValidation() {
  console.log('\n🔑 Testing API key validation...');
  
  try {
    const response = await fetch('http://localhost:3000/api/invincible-v3', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        topic: 'test without proper setup'
      }),
    });

    const data = await response.json();
    
    if (data.success === false && data.error.includes('API key')) {
      console.log('✅ API key validation working correctly');
      return true;
    } else if (data.success === true) {
      console.log('✅ API keys configured and working');
      return true;
    } else {
      console.log('⚠️ Unexpected API key validation response');
      return false;
    }
  } catch (error) {
    console.log('❌ API key validation test error:', error.message);
    return false;
  }
}

async function testDifferentPresets() {
  console.log('\n⚙️ Testing different workflow presets...');
  
  const presets = ['fast', 'standard', 'premium'];
  const results = [];
  
  for (const preset of presets) {
    try {
      console.log(`\n   🧪 Testing ${preset} preset...`);
      
      const response = await fetch('http://localhost:3000/api/invincible-v3', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          topic: `${preset} preset test`,
          contentLength: 300,
          preset: preset
        }),
      });

      if (response.ok) {
        const data = await response.json();
        console.log(`   ✅ ${preset}: ${data.executionTime}ms, ${data.result?.article?.wordCount || 0} words`);
        results.push({
          preset,
          success: true,
          executionTime: data.executionTime,
          wordCount: data.result?.article?.wordCount || 0,
        });
      } else {
        console.log(`   ❌ ${preset}: Failed`);
        results.push({ preset, success: false });
      }
      
      // Rate limiting between tests
      await new Promise(resolve => setTimeout(resolve, 1000));
      
    } catch (error) {
      console.log(`   ❌ ${preset}: Error - ${error.message}`);
      results.push({ preset, success: false, error: error.message });
    }
  }
  
  console.log('\n📊 Preset Test Summary:');
  results.forEach(result => {
    if (result.success) {
      console.log(`   ✅ ${result.preset}: ${result.executionTime}ms, ${result.wordCount} words`);
    } else {
      console.log(`   ❌ ${result.preset}: Failed`);
    }
  });
  
  return results;
}

async function runComprehensiveTest() {
  console.log('🚀 Starting comprehensive V3 system testing...\n');
  
  const results = {
    systemStatus: false,
    apiKeyValidation: false,
    regularAPI: null,
    streamingAPI: null,
    presetTests: [],
  };
  
  // Test 1: System Status
  results.systemStatus = await testV3SystemStatus();
  
  console.log('\n' + '='.repeat(50));
  
  // Test 2: API Key Validation
  results.apiKeyValidation = await testAPIKeyValidation();
  
  console.log('\n' + '='.repeat(50));
  
  // Test 3: Regular API
  results.regularAPI = await testV3API();
  
  console.log('\n' + '='.repeat(50));
  
  // Test 4: Streaming API
  results.streamingAPI = await testV3Streaming();
  
  console.log('\n' + '='.repeat(50));
  
  // Test 5: Different Presets
  results.presetTests = await testDifferentPresets();
  
  console.log('\n' + '='.repeat(50));
  console.log('🎯 COMPREHENSIVE V3 TESTING COMPLETE');
  
  // Summary
  const successfulTests = [
    results.systemStatus,
    results.apiKeyValidation,
    !!results.regularAPI,
    !!results.streamingAPI,
    results.presetTests.some(p => p.success),
  ].filter(Boolean).length;
  
  console.log(`\n📊 Test Results: ${successfulTests}/5 tests passed`);
  console.log('✅ System Status:', results.systemStatus ? 'PASS' : 'FAIL');
  console.log('✅ API Key Validation:', results.apiKeyValidation ? 'PASS' : 'FAIL');
  console.log('✅ Regular API:', results.regularAPI ? 'PASS' : 'FAIL');
  console.log('✅ Streaming API:', results.streamingAPI ? 'PASS' : 'FAIL');
  console.log('✅ Preset Tests:', results.presetTests.filter(p => p.success).length + '/' + results.presetTests.length + ' PASS');
  
  if (successfulTests >= 4) {
    console.log('\n🎉 V3 SYSTEM IS OPERATIONAL!');
  } else {
    console.log('\n⚠️ V3 System has issues that need attention');
  }
  
  return results;
}

runComprehensiveTest().catch(console.error); 