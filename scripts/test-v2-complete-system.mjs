#!/usr/bin/env node
/**
 * Comprehensive Test Script for Invincible V.2 Agent System
 * Tests all components: Research, Content, SEO/GEO, Quality agents
 */

import fetch from 'node-fetch';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs/promises';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
dotenv.config({ path: join(__dirname, '..', '.env.local') });

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';

// Test configuration
const TEST_CONFIG = {
  // API Keys - Use from environment or prompt user
  openRouterApiKey: process.env.OPENROUTER_API_KEY,
  tavilyApiKey: process.env.TAVILY_API_KEY,
  
  // Test cases
  testCases: [
    {
      name: 'Technical Article Test',
      payload: {
        topic: 'How to build a REST API with Node.js and Express in 2024',
        contentType: 'how-to',
        contentLength: 2000,
        tone: 'professional',
        targetAudience: 'intermediate developers',
        customInstructions: 'Include code examples and best practices'
      }
    },
    {
      name: 'SEO-Optimized Blog Test',
      payload: {
        topic: 'Best productivity apps for remote workers 2024',
        contentType: 'listicle',
        contentLength: 1500,
        tone: 'conversational',
        targetAudience: 'remote workers and digital nomads',
        customInstructions: 'Focus on free and paid options with pros/cons'
      }
    },
    {
      name: 'In-Depth Guide Test',
      payload: {
        topic: 'Complete guide to machine learning for beginners',
        contentType: 'guide',
        contentLength: 3000,
        tone: 'educational',
        targetAudience: 'beginners with no ML background',
        customInstructions: 'Start from basics, use simple analogies'
      }
    }
  ]
};

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

/**
 * Test non-streaming API endpoint
 */
async function testNonStreamingAPI(testCase) {
  log(`\n🧪 Testing Non-Streaming API: ${testCase.name}`, 'bright');
  log(`📝 Topic: ${testCase.payload.topic}`, 'cyan');
  
  const startTime = Date.now();
  
  try {
    const response = await fetch(`${API_URL}/api/invincible-v2`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ...testCase.payload,
        openRouterApiKey: TEST_CONFIG.openRouterApiKey,
        tavilyApiKey: TEST_CONFIG.tavilyApiKey
      })
    });

    const duration = (Date.now() - startTime) / 1000;
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(`API Error (${response.status}): ${error.error || 'Unknown error'}`);
    }

    const result = await response.json();
    
    // Validate response structure
    if (!result.success || !result.result) {
      throw new Error('Invalid response structure');
    }

    // Display results
    log(`\n✅ SUCCESS - Duration: ${duration}s`, 'green');
    log('\n📊 Quality Metrics:', 'bright');
    
    const quality = result.metadata.quality;
    log(`  • Content Score: ${quality.contentScore}/100`, quality.contentScore >= 80 ? 'green' : 'yellow');
    log(`  • SEO Score: ${quality.seoScore}/100`, quality.seoScore >= 70 ? 'green' : 'yellow');
    log(`  • GEO Score: ${quality.geoScore}/100`, quality.geoScore >= 70 ? 'green' : 'yellow');
    log(`  • AI Detection: ${quality.aiDetectionProbability}%`, quality.aiDetectionProbability <= 30 ? 'green' : 'red');
    log(`  • Human Score: ${quality.humanScore}/100`, quality.humanScore >= 70 ? 'green' : 'yellow');
    log(`  • Originality: ${quality.originalityScore}/100`, quality.originalityScore >= 60 ? 'green' : 'yellow');

    log('\n📈 Performance Stats:', 'bright');
    log(`  • Word Count: ${result.metadata.performance.wordCount} (target: ${testCase.payload.contentLength})`);
    log(`  • Research Queries: ${result.metadata.performance.researchQueries}`);
    log(`  • Competitors Analyzed: ${result.metadata.performance.competitorsAnalyzed}`);
    log(`  • Humanization Techniques: ${result.metadata.performance.humanizationTechniques}`);

    log('\n🤖 Agents Executed:', 'bright');
    result.metadata.workflow.completedAgents.forEach(agent => {
      log(`  • ${agent}`, 'cyan');
    });

    // Save result to file
    const outputDir = join(__dirname, '..', 'test-results', 'v2');
    await fs.mkdir(outputDir, { recursive: true });
    
    const filename = `${testCase.name.toLowerCase().replace(/\s+/g, '-')}-${Date.now()}.json`;
    await fs.writeFile(
      join(outputDir, filename),
      JSON.stringify(result, null, 2)
    );
    
    log(`\n💾 Result saved to: test-results/v2/${filename}`, 'blue');

    return { success: true, duration, quality };

  } catch (error) {
    log(`\n❌ FAILED: ${error.message}`, 'red');
    return { success: false, error: error.message };
  }
}

/**
 * Test streaming API endpoint
 */
async function testStreamingAPI(testCase) {
  log(`\n🧪 Testing Streaming API: ${testCase.name}`, 'bright');
  log(`📝 Topic: ${testCase.payload.topic}`, 'cyan');
  
  const startTime = Date.now();
  const events = [];
  
  try {
    const response = await fetch(`${API_URL}/api/invincible-v2/stream`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ...testCase.payload,
        openRouterApiKey: TEST_CONFIG.openRouterApiKey,
        tavilyApiKey: TEST_CONFIG.tavilyApiKey
      })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(`API Error (${response.status}): ${error.error || 'Unknown error'}`);
    }

    // Read SSE stream
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      buffer += decoder.decode(value, { stream: true });
      const lines = buffer.split('\n');
      
      // Keep the last incomplete line in buffer
      buffer = lines.pop() || '';

      for (const line of lines) {
        if (line.startsWith('event:')) {
          const eventType = line.substring(6).trim();
          log(`  📡 Event: ${eventType}`, 'yellow');
        } else if (line.startsWith('data:')) {
          try {
            const data = JSON.parse(line.substring(5).trim());
            events.push(data);
            
            // Display progress messages
            if (data.action) {
              log(`    → ${data.action}`, 'cyan');
            }
          } catch (e) {
            // Ignore JSON parse errors for non-JSON data
          }
        }
      }
    }

    const duration = (Date.now() - startTime) / 1000;
    
    // Find completion event
    const completionEvent = events.find(e => e.success === true && e.result);
    
    if (!completionEvent) {
      throw new Error('No completion event received');
    }

    log(`\n✅ STREAMING SUCCESS - Duration: ${duration}s`, 'green');
    log(`📨 Total events received: ${events.length}`, 'blue');

    return { success: true, duration, events: events.length };

  } catch (error) {
    log(`\n❌ STREAMING FAILED: ${error.message}`, 'red');
    return { success: false, error: error.message };
  }
}

/**
 * Run all tests
 */
async function runAllTests() {
  log('\n🚀 Starting Invincible V.2 Complete System Test', 'bright');
  log('================================================\n', 'bright');

  // Check API keys
  if (!TEST_CONFIG.openRouterApiKey || !TEST_CONFIG.tavilyApiKey) {
    log('❌ Missing API keys. Please set OPENROUTER_API_KEY and TAVILY_API_KEY in .env.local', 'red');
    process.exit(1);
  }

  // Check if server is running
  try {
    const health = await fetch(`${API_URL}/api/invincible-v2`);
    const healthData = await health.json();
    log(`✅ Server is running - Version: ${healthData.version}`, 'green');
    log(`📋 Features: ${healthData.features.join(', ')}\n`, 'cyan');
  } catch (error) {
    log('❌ Server is not running. Please start the server first.', 'red');
    process.exit(1);
  }

  const results = {
    nonStreaming: [],
    streaming: []
  };

  // Test non-streaming API
  log('\n📝 TESTING NON-STREAMING API', 'bright');
  log('==============================', 'bright');
  
  for (const testCase of TEST_CONFIG.testCases) {
    const result = await testNonStreamingAPI(testCase);
    results.nonStreaming.push({
      name: testCase.name,
      ...result
    });
    
    // Add delay between tests to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 5000));
  }

  // Test streaming API
  log('\n\n📡 TESTING STREAMING API', 'bright');
  log('==========================', 'bright');
  
  for (const testCase of TEST_CONFIG.testCases.slice(0, 1)) { // Test only first case for streaming
    const result = await testStreamingAPI(testCase);
    results.streaming.push({
      name: testCase.name,
      ...result
    });
  }

  // Summary
  log('\n\n📊 TEST SUMMARY', 'bright');
  log('================', 'bright');
  
  const nonStreamingSuccess = results.nonStreaming.filter(r => r.success).length;
  const streamingSuccess = results.streaming.filter(r => r.success).length;
  
  log(`\nNon-Streaming API:`, 'bright');
  log(`  ✅ Passed: ${nonStreamingSuccess}/${results.nonStreaming.length}`, 
      nonStreamingSuccess === results.nonStreaming.length ? 'green' : 'yellow');
  
  results.nonStreaming.forEach(r => {
    if (r.success) {
      log(`    • ${r.name}: SUCCESS (${r.duration}s)`, 'green');
    } else {
      log(`    • ${r.name}: FAILED - ${r.error}`, 'red');
    }
  });

  log(`\nStreaming API:`, 'bright');
  log(`  ✅ Passed: ${streamingSuccess}/${results.streaming.length}`, 
      streamingSuccess === results.streaming.length ? 'green' : 'yellow');
  
  results.streaming.forEach(r => {
    if (r.success) {
      log(`    • ${r.name}: SUCCESS (${r.duration}s, ${r.events} events)`, 'green');
    } else {
      log(`    • ${r.name}: FAILED - ${r.error}`, 'red');
    }
  });

  // Average quality scores
  const qualityScores = results.nonStreaming
    .filter(r => r.success && r.quality)
    .map(r => r.quality);
  
  if (qualityScores.length > 0) {
    log(`\n📈 Average Quality Scores:`, 'bright');
    const avgContent = qualityScores.reduce((sum, q) => sum + q.contentScore, 0) / qualityScores.length;
    const avgSEO = qualityScores.reduce((sum, q) => sum + q.seoScore, 0) / qualityScores.length;
    const avgGEO = qualityScores.reduce((sum, q) => sum + q.geoScore, 0) / qualityScores.length;
    const avgAI = qualityScores.reduce((sum, q) => sum + q.aiDetectionProbability, 0) / qualityScores.length;
    
    log(`  • Content: ${avgContent.toFixed(1)}/100`, avgContent >= 80 ? 'green' : 'yellow');
    log(`  • SEO: ${avgSEO.toFixed(1)}/100`, avgSEO >= 70 ? 'green' : 'yellow');
    log(`  • GEO: ${avgGEO.toFixed(1)}/100`, avgGEO >= 70 ? 'green' : 'yellow');
    log(`  • AI Detection: ${avgAI.toFixed(1)}%`, avgAI <= 30 ? 'green' : 'red');
  }

  const totalTests = results.nonStreaming.length + results.streaming.length;
  const totalSuccess = nonStreamingSuccess + streamingSuccess;
  
  log(`\n🎯 Overall: ${totalSuccess}/${totalTests} tests passed`, 
      totalSuccess === totalTests ? 'green' : 'red');
  
  process.exit(totalSuccess === totalTests ? 0 : 1);
}

// Run tests
runAllTests().catch(error => {
  log(`\n💥 Unexpected error: ${error.message}`, 'red');
  console.error(error);
  process.exit(1);
}); 