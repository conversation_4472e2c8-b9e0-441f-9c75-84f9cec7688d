#!/usr/bin/env node

import { KimiK2Client } from '../src/lib/agents/v2/tools/kimi-k2-client.js';

console.log('🧪 Testing Groq Integration');
console.log('===========================\n');

async function testGroqClient() {
  try {
    console.log('📡 Initializing Groq client...');
    const client = new KimiK2Client();
    
    console.log('🤖 Testing content generation...');
    const response = await client.generateContent([
      {
        role: 'user',
        content: 'Write a brief paragraph about artificial intelligence. Keep it under 100 words.'
      }
    ]);

    console.log('✅ SUCCESS: Groq client is working!');
    console.log('\n📝 Generated Content:');
    console.log('=' .repeat(50));
    console.log(response.content);
    console.log('=' .repeat(50));
    
    if (response.usage) {
      console.log('\n📊 Token Usage:');
      console.log(`- Prompt tokens: ${response.usage.prompt_tokens}`);
      console.log(`- Completion tokens: ${response.usage.completion_tokens}`);
      console.log(`- Total tokens: ${response.usage.total_tokens}`);
    }
    
    console.log('\n🎉 Groq integration test PASSED!');
    return true;
    
  } catch (error) {
    console.error('❌ FAILED: Groq client error');
    console.error('Error:', error.message);
    console.error('\nStack trace:', error.stack);
    return false;
  }
}

async function main() {
  const success = await testGroqClient();
  process.exit(success ? 0 : 1);
}

main().catch(error => {
  console.error('Unexpected error:', error);
  process.exit(1);
}); 