'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Activity, 
  Brain, 
  Search, 
  Globe, 
  Zap, 
  Shield, 
  Target, 
  Code, 
  Database,
  Eye,
  Cpu,
  Terminal,
  Wifi,
  TrendingUp,
  CheckCircle,
  AlertCircle,
  ArrowRight,
  Clock,
  FileText,
  Crown,
  Sparkles
} from 'lucide-react';

interface StreamEvent {
  type: string;
  data: any;
  timestamp: number;
}

interface InvincibleV2StreamingUIProps {
  topic: string;
  contentLength?: number;
  tone?: string;
  targetAudience?: string;
  customInstructions?: string;
  contentType?: string;
  onComplete?: (result: any) => void;
  onError?: (error: string) => void;
  isSaving?: boolean;
}

const InvincibleV2StreamingUI: React.FC<InvincibleV2StreamingUIProps> = ({
  topic,
  contentLength,
  tone,
  targetAudience,
  customInstructions,
  contentType = 'article',
  onComplete,
  onError,
  isSaving
}) => {
  const [isStreaming, setIsStreaming] = useState(false);
  const [currentPhase, setCurrentPhase] = useState<string>('');
  const [progress, setProgress] = useState(0);
  const [events, setEvents] = useState<StreamEvent[]>([]);
  const [agentStats, setAgentStats] = useState<Record<string, any>>({});
  const [finalResult, setFinalResult] = useState<any>(null);
  const [error, setError] = useState<string>('');
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'error' | 'complete'>('connecting');
  const eventsContainerRef = useRef<HTMLDivElement>(null);
  const eventSourceRef = useRef<EventSource | null>(null);

  // Auto-start streaming when component mounts
  useEffect(() => {
    const timer = setTimeout(() => {
      startStreaming();
    }, 1000);
    
    return () => {
      clearTimeout(timer);
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }
    };
  }, []);

  const startStreaming = () => {
    if (isStreaming) return;
    
    console.log('🚀 InvincibleV2StreamingUI: Starting V2 streaming');
    
    setIsStreaming(true);
    setProgress(0);
    setEvents([]);
    setAgentStats({});
    setFinalResult(null);
    setError('');
    setConnectionStatus('connecting');

    // Safe JSON parsing helper
    const safeParseJSON = (rawData: string) => {
      try {
        if (!rawData || rawData === 'undefined' || rawData === 'null') {
          console.warn('Received invalid SSE data:', rawData);
          return null;
        }
        return JSON.parse(rawData);
      } catch (error) {
        console.error('Failed to parse SSE data:', rawData, error);
        return null;
      }
    };

    console.log('🎯 Routing to V2 streaming endpoint');
    
    // Create request payload
    const payload = {
      topic,
      contentLength: contentLength || 2000,
      tone: tone || 'professional',
      targetAudience: targetAudience || 'general audience',
      customInstructions: customInstructions || '',
      contentType
    };

    // Use fetch to POST data for V2 streaming
    fetch('/api/invincible-v2/stream', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload)
    }).then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('Failed to get response reader');
      }

      setConnectionStatus('connected');
      addEvent('connection', { message: 'V2 Connection established', timestamp: Date.now() });

      const decoder = new TextDecoder();
      let buffer = '';

      const readStream = async () => {
        try {
          while (true) {
            const { done, value } = await reader.read();
            
            if (done) {
              console.log('✅ V2 Stream completed');
              setConnectionStatus('complete');
              setIsStreaming(false);
              break;
            }

            buffer += decoder.decode(value, { stream: true });
            const lines = buffer.split('\n');
            buffer = lines.pop() || '';

            for (const line of lines) {
              if (line.trim() === '') continue;
              
              if (line.startsWith('event: ')) {
                continue; // Skip event type lines
              }
              
              if (line.startsWith('data: ')) {
                const data = safeParseJSON(line.slice(6));
                if (data) {
                  handleV2StreamEvent('progress', data);
                }
              }
            }
          }
        } catch (error) {
          console.error('V2 Stream reading error:', error);
          setError(`V2 streaming error: ${error}`);
          setConnectionStatus('error');
          setIsStreaming(false);
        }
      };

      readStream();
    }).catch(error => {
      console.error('V2 Streaming setup error:', error);
      setError(`V2 connection failed: ${error.message}`);
      setConnectionStatus('error');
      setIsStreaming(false);
    });
  };

  const handleV2StreamEvent = (type: string, data: any) => {
    console.log('📡 V2 Stream event:', type, data);

    // Handle different V2 event types
    switch (type) {
      case 'start':
        setCurrentPhase('initialization');
        addEvent('start', data);
        break;
        
      case 'research_agent':
        setCurrentPhase('research');
        setProgress(20);
        setAgentStats(prev => ({ ...prev, research: data }));
        addEvent('research', data);
        break;
        
      case 'content_agent':
        setCurrentPhase('content_generation');
        setProgress(40);
        setAgentStats(prev => ({ ...prev, content: data }));
        addEvent('content', data);
        break;
        
      case 'seo_geo_agent':
        setCurrentPhase('seo_geo_optimization');
        setProgress(60);
        setAgentStats(prev => ({ ...prev, seo_geo: data }));
        addEvent('seo_geo', data);
        break;
        
      case 'quality_agent':
        setCurrentPhase('quality_assurance');
        setProgress(80);
        setAgentStats(prev => ({ ...prev, quality: data }));
        addEvent('quality', data);
        break;
        
      case 'complete':
        setFinalResult(data);
        setProgress(100);
        setConnectionStatus('complete');
        setIsStreaming(false);
        addEvent('success', data);
        onComplete?.(data);
        break;
        
      case 'error':
        setError(data.error || 'An error occurred');
        addEvent('error', data);
        onError?.(data.error || 'An error occurred');
        break;
        
      default:
        // Handle generic progress events
        if (data.message) {
          addEvent('progress', data);
        }
        break;
    }
  };

  const addEvent = (type: string, data: any) => {
    const event: StreamEvent = {
      type,
      data,
      timestamp: Date.now()
    };
    setEvents(prev => [...prev, event].slice(-50));
  };

  // Auto-scroll events container
  useEffect(() => {
    if (eventsContainerRef.current) {
      eventsContainerRef.current.scrollTop = eventsContainerRef.current.scrollHeight;
    }
  }, [events]);

  const getPhaseInfo = (phase: string) => {
    switch (phase) {
      case 'initialization':
        return {
          icon: Cpu,
          name: 'V2 System Initialization',
          color: 'from-blue-400 to-cyan-400',
          bgColor: 'bg-blue-500/10',
          description: 'Booting Invincible V.2 autonomous agents...'
        };
      case 'research':
        return {
          icon: Search,
          name: 'AI Research Agent',
          color: 'from-green-400 to-emerald-400',
          bgColor: 'bg-green-500/10',
          description: 'Advanced research with competitive intelligence...'
        };
      case 'content_generation':
        return {
          icon: Brain,
          name: 'Content Generation Agent',
          color: 'from-purple-400 to-pink-400',
          bgColor: 'bg-purple-500/10',
          description: 'Human-like content creation with Kimi K2...'
        };
      case 'seo_geo_optimization':
        return {
          icon: TrendingUp,
          name: 'SEO & GEO Agent',
          color: 'from-orange-400 to-red-400',
          bgColor: 'bg-orange-500/10',
          description: 'Optimizing for search engines and AI systems...'
        };
      case 'quality_assurance':
        return {
          icon: Shield,
          name: 'Quality & Humanization Agent',
          color: 'from-violet-400 to-indigo-400',
          bgColor: 'bg-violet-500/10',
          description: 'AI detection bypass and quality assurance...'
        };
      default:
        return {
          icon: Activity,
          name: 'Processing',
          color: 'from-gray-400 to-gray-500',
          bgColor: 'bg-gray-500/10',
          description: 'V2 system operations active...'
        };
    }
  };

  const getConnectionStatusColor = () => {
    switch (connectionStatus) {
      case 'connecting': return 'text-yellow-400';
      case 'connected': return 'text-green-400';
      case 'error': return 'text-red-400';
      case 'complete': return 'text-blue-400';
      default: return 'text-gray-400';
    }
  };

  const phaseInfo = getPhaseInfo(currentPhase);
  const PhaseIcon = phaseInfo.icon;

  return (
    <div className="min-h-screen bg-black relative overflow-hidden">
      {/* Animated Background Matrix */}
      <div className="fixed inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black" />
        
        {/* V2 specific background pattern */}
        <div className="absolute inset-0 opacity-10">
          {[...Array(15)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-px bg-gradient-to-b from-transparent via-violet-400 to-transparent"
              style={{
                left: `${(i * 7)}%`,
                height: '100%'
              }}
              animate={{
                opacity: [0.1, 0.4, 0.1],
                scaleY: [0.5, 1, 0.5]
              }}
              transition={{
                duration: 3 + Math.random() * 2,
                repeat: Infinity,
                delay: Math.random() * 2
              }}
            />
          ))}
        </div>

        {/* Floating particles */}
        {[...Array(20)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-violet-400 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`
            }}
            animate={{
              y: [0, -30, 0],
              opacity: [0.3, 1, 0.3],
              scale: [0.5, 1.2, 0.5]
            }}
            transition={{
              duration: 4 + Math.random() * 3,
              repeat: Infinity,
              delay: Math.random() * 3
            }}
          />
        ))}
      </div>

      {/* Main Content */}
      <div className="relative z-10 p-6 max-w-7xl mx-auto">
        
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <div className="inline-flex items-center space-x-3 mb-4">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-violet-600 to-indigo-600 rounded-xl blur-lg opacity-70" />
              <div className="relative bg-black rounded-xl p-3 border border-violet-500/50">
                <Crown className="w-8 h-8 text-violet-400" />
              </div>
            </div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-white via-violet-200 to-indigo-200 bg-clip-text text-transparent">
              INVINCIBLE V.2 AUTONOMOUS GENERATION
            </h1>
            <div className="px-3 py-1 bg-violet-600/20 border border-violet-500/50 rounded-full">
              <span className="text-xs text-violet-300 font-bold">BETA</span>
            </div>
          </div>
          
          <div className="flex items-center justify-center space-x-2 text-sm">
            <Wifi className={`w-4 h-4 ${getConnectionStatusColor()}`} />
            <span className={`${getConnectionStatusColor()} font-medium`}>
              {connectionStatus.toUpperCase()}
            </span>
            <span className="text-gray-500">•</span>
            <span className="text-gray-400">
              Kimi K2 + LangGraph + Tavily
            </span>
          </div>
        </motion.div>

        {/* Topic Display */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          className="mb-8"
        >
          <div className="bg-gradient-to-r from-violet-900/20 to-indigo-900/20 backdrop-blur-xl border border-violet-500/30 rounded-2xl p-6">
            <div className="flex items-start space-x-4">
              <div className="p-3 bg-gradient-to-r from-violet-500 to-indigo-500 rounded-xl">
                <Target className="w-6 h-6 text-white" />
              </div>
              <div className="flex-1">
                <h2 className="text-xl font-bold text-white mb-2">V.2 Mission Target</h2>
                <p className="text-2xl font-bold bg-gradient-to-r from-violet-400 to-indigo-400 bg-clip-text text-transparent mb-3">
                  "{topic}"
                </p>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="text-gray-400">Length:</span>
                    <span className="text-white ml-2 font-medium">{contentLength || 2000} words</span>
                  </div>
                  <div>
                    <span className="text-gray-400">Tone:</span>
                    <span className="text-white ml-2 font-medium">{tone || 'professional'}</span>
                  </div>
                  <div>
                    <span className="text-gray-400">Audience:</span>
                    <span className="text-white ml-2 font-medium">{targetAudience || 'general'}</span>
                  </div>
                  <div>
                    <span className="text-gray-400">Mode:</span>
                    <span className="text-violet-400 ml-2 font-medium">INVINCIBLE V.2</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Current Phase Display */}
        {isStreaming && currentPhase && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-8"
          >
            <div className={`${phaseInfo.bgColor} backdrop-blur-xl border border-white/10 rounded-2xl p-6`}>
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-4">
                  <div className={`p-3 bg-gradient-to-r ${phaseInfo.color} rounded-xl`}>
                    <PhaseIcon className="w-6 h-6 text-black" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-white">{phaseInfo.name}</h3>
                    <p className="text-gray-300">{phaseInfo.description}</p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-3xl font-bold text-white mb-1">{Math.round(progress)}%</div>
                  <div className="text-sm text-gray-400">Complete</div>
                </div>
              </div>
              
              <div className="relative">
                <div className="w-full bg-black/50 rounded-full h-3">
                  <motion.div
                    className={`h-3 rounded-full bg-gradient-to-r ${phaseInfo.color} relative overflow-hidden`}
                    initial={{ width: 0 }}
                    animate={{ width: `${progress}%` }}
                    transition={{ duration: 0.8, ease: "easeOut" }}
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-pulse" />
                  </motion.div>
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {/* Live Agent Stats Grid */}
        {isStreaming && (
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
            {[
              { 
                icon: Search, 
                label: 'Research Agent', 
                value: Object.keys(agentStats.research || {}).length || 0, 
                color: 'from-green-400 to-emerald-400',
                bgColor: 'bg-green-500/10',
                unit: 'queries'
              },
              { 
                icon: Brain, 
                label: 'Content Agent', 
                value: Object.keys(agentStats.content || {}).length || 0, 
                color: 'from-purple-400 to-pink-400',
                bgColor: 'bg-purple-500/10',
                unit: 'sections'
              },
              { 
                icon: TrendingUp, 
                label: 'SEO/GEO Agent', 
                value: Object.keys(agentStats.seo_geo || {}).length || 0, 
                color: 'from-orange-400 to-red-400',
                bgColor: 'bg-orange-500/10',
                unit: 'optimized'
              },
              { 
                icon: Shield, 
                label: 'Quality Agent', 
                value: Object.keys(agentStats.quality || {}).length || 0, 
                color: 'from-violet-400 to-indigo-400',
                bgColor: 'bg-violet-500/10',
                unit: 'checks'
              }
            ].map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className={`${stat.bgColor} backdrop-blur-xl border border-white/10 rounded-xl p-4`}
              >
                <div className="flex items-center space-x-3 mb-3">
                  <div className={`p-2 bg-gradient-to-r ${stat.color} rounded-lg`}>
                    <stat.icon className="w-5 h-5 text-black" />
                  </div>
                  <span className="text-white font-medium text-sm">{stat.label}</span>
                </div>
                <div className="text-center">
                  <motion.div 
                    className="text-3xl font-bold text-white mb-1"
                    key={stat.value}
                    initial={{ scale: 0.8, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ type: "spring", stiffness: 500, damping: 25 }}
                  >
                    {stat.value}
                  </motion.div>
                  <div className="text-xs text-gray-400">{stat.unit}</div>
                </div>
              </motion.div>
            ))}
          </div>
        )}

        {/* Live Activity Terminal */}
        {isStreaming && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-8"
          >
            <div className="bg-black/60 backdrop-blur-xl border border-violet-500/30 rounded-2xl overflow-hidden">
              <div className="bg-gradient-to-r from-violet-900/50 to-indigo-900/50 px-6 py-4 border-b border-violet-500/30">
                <div className="flex items-center space-x-3">
                  <Terminal className="w-5 h-5 text-violet-400" />
                  <span className="text-violet-400 font-mono font-bold">INVINCIBLE_V2_TERMINAL</span>
                  <div className="flex space-x-2 ml-auto">
                    <div className="w-3 h-3 rounded-full bg-red-500"></div>
                    <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                    <div className="w-3 h-3 rounded-full bg-green-500"></div>
                  </div>
                </div>
              </div>
              
              <div 
                ref={eventsContainerRef}
                className="h-80 overflow-y-auto p-4 space-y-2 font-mono text-sm"
                style={{ 
                  background: 'linear-gradient(180deg, rgba(0,0,0,0.8) 0%, rgba(20,0,20,0.3) 100%)'
                }}
              >
                <AnimatePresence>
                  {events.map((event, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: 20 }}
                      className="flex items-start space-x-3"
                    >
                      <span className="text-violet-500 text-xs mt-1 flex-shrink-0">
                        [{new Date(event.timestamp).toLocaleTimeString()}]
                      </span>
                      <div className="flex-1">
                        <span className={`${
                          event.type === 'error' ? 'text-red-400' :
                          event.type === 'success' ? 'text-green-400' :
                          event.type === 'research' ? 'text-blue-400' :
                          event.type === 'content' ? 'text-purple-400' :
                          event.type === 'seo_geo' ? 'text-orange-400' :
                          event.type === 'quality' ? 'text-violet-400' :
                          'text-green-300'
                        }`}>
                          {event.type === 'research' ? '>>> RESEARCH_AGENT: ' : 
                           event.type === 'content' ? '>>> CONTENT_AGENT: ' :
                           event.type === 'seo_geo' ? '>>> SEO_GEO_AGENT: ' :
                           event.type === 'quality' ? '>>> QUALITY_AGENT: ' : ''}
                          {event.data.message || JSON.stringify(event.data).slice(0, 100)}
                        </span>
                      </div>
                    </motion.div>
                  ))}
                </AnimatePresence>
                
                {/* Typing cursor */}
                {isStreaming && (
                  <motion.div
                    animate={{ opacity: [1, 0] }}
                    transition={{ duration: 1, repeat: Infinity }}
                    className="text-violet-400"
                  >
                    <span className="text-xs">[{new Date().toLocaleTimeString()}]</span>
                    <span className="ml-3">█</span>
                  </motion.div>
                )}
              </div>
            </div>
          </motion.div>
        )}

        {/* Error Display */}
        {error && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="mb-8"
          >
            <div className="bg-red-900/20 backdrop-blur-xl border border-red-500/50 rounded-2xl p-6">
              <div className="flex items-center space-x-3">
                <AlertCircle className="w-6 h-6 text-red-400" />
                <div>
                  <h3 className="text-lg font-bold text-red-400 mb-1">V2 System Alert</h3>
                  <p className="text-red-300">{error}</p>
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {/* Final Result */}
        {finalResult && (
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="mb-8"
          >
            <div className="bg-gradient-to-r from-violet-900/30 to-indigo-900/30 backdrop-blur-xl border border-violet-500/50 rounded-2xl p-8">
              <div className="flex items-center space-x-3 mb-6">
                {isSaving ? (
                  <>
                    <div className="animate-spin w-8 h-8 border-2 border-violet-400 border-t-transparent rounded-full"></div>
                    <h3 className="text-3xl font-bold text-violet-400">SAVING V.2 ARTICLE...</h3>
                  </>
                ) : (
                  <>
                    <CheckCircle className="w-8 h-8 text-violet-400" />
                    <h3 className="text-3xl font-bold text-violet-400">V.2 MISSION ACCOMPLISHED</h3>
                  </>
                )}
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                {[
                  { 
                    icon: FileText, 
                    label: 'Words Generated', 
                    value: finalResult.article?.wordCount || finalResult.result?.article?.wordCount || 0,
                    color: 'text-blue-400'
                  },
                  { 
                    icon: TrendingUp, 
                    label: 'Quality Score', 
                    value: `${finalResult.article?.qualityScore || finalResult.result?.performance?.qualityScore || 0}/100`,
                    color: 'text-green-400'
                  },
                  { 
                    icon: Clock, 
                    label: 'Generation Time', 
                    value: `${Math.round((finalResult.performance?.executionTime || finalResult.result?.performance?.executionTime || 0) / 1000)}s`,
                    color: 'text-purple-400'
                  }
                ].map((metric, index) => (
                  <div key={index} className="text-center">
                    <div className="flex justify-center mb-2">
                      <metric.icon className={`w-8 h-8 ${metric.color}`} />
                    </div>
                    <div className={`text-3xl font-bold ${metric.color} mb-1`}>
                      {metric.value}
                    </div>
                    <div className="text-gray-400 text-sm">{metric.label}</div>
                  </div>
                ))}
              </div>

              <div className="bg-black/40 rounded-xl p-6 border border-violet-500/30">
                <h4 className="font-bold text-violet-400 mb-3 flex items-center">
                  <ArrowRight className="w-5 h-5 mr-2" />
                  V.2 Generated Article Preview
                </h4>
                
                <div className="space-y-4">
                  <div>
                    <span className="text-gray-400 text-sm">TITLE:</span>
                    <p className="text-xl font-bold text-white mt-1">
                      {finalResult.article?.title || finalResult.result?.article?.title || 'V.2 Generated Article'}
                    </p>
                  </div>
                  
                  <div>
                    <span className="text-gray-400 text-sm">META DESCRIPTION:</span>
                    <p className="text-gray-300 mt-1 leading-relaxed">
                      {finalResult.article?.metaDescription || finalResult.result?.article?.metaDescription || 'Generated with Invincible V.2'}
                    </p>
                  </div>
                </div>
                
                {isSaving && (
                  <div className="mt-6 p-4 bg-violet-900/20 border border-violet-500/30 rounded-xl">
                    <div className="flex items-center space-x-3">
                      <div className="animate-spin w-5 h-5 border-2 border-violet-400 border-t-transparent rounded-full"></div>
                      <span className="text-violet-400 font-medium">
                        Saving V.2 article and redirecting to article view...
                      </span>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        )}

      </div>
    </div>
  );
};

export default InvincibleV2StreamingUI;