/**
 * Invincible V3 Streaming API Route
 * Server-sent events endpoint for real-time workflow progress
 */

import { NextRequest } from 'next/server';
import { executeWorkflowWithProgress, WorkflowPresets } from '@/lib/agents/v3/workflow/invincible-workflow';
import { ContentRequirements } from '@/lib/agents/v3/core/state';

export async function POST(request: NextRequest) {
  console.log(`🌊 V3 Streaming API request received at ${new Date().toLocaleTimeString()}`);

  try {
    // Parse request body
    const body = await request.json();
    
    // Validate required fields
    if (!body.topic) {
      return new Response(
        JSON.stringify({ success: false, error: 'Topic is required' }),
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Create content requirements
    const requirements: ContentRequirements = {
      topic: body.topic,
      contentLength: body.contentLength || 2000,
      tone: body.tone || 'professional',
      targetAudience: body.targetAudience || 'general audience',
      contentType: body.contentType || 'article',
      customInstructions: body.customInstructions,
    };

    console.log(`📝 V3 Streaming - Starting workflow for topic: ${requirements.topic}`);

    // Get API keys from environment
    const openRouterApiKey = process.env.OPENROUTER_API_KEY;
    const tavilyApiKey = process.env.TAVILY_API_KEY;

    if (!openRouterApiKey) {
      return new Response(
        JSON.stringify({ success: false, error: 'OpenRouter API key not configured' }),
        { 
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Create workflow configuration
    const preset = body.preset || 'standard';
    let workflowConfig;

    switch (preset) {
      case 'fast':
        workflowConfig = WorkflowPresets.fast({
          openRouter: openRouterApiKey,
          tavily: tavilyApiKey,
        });
        break;
      case 'premium':
        workflowConfig = WorkflowPresets.premium({
          openRouter: openRouterApiKey,
          tavily: tavilyApiKey,
        });
        break;
      default:
        workflowConfig = WorkflowPresets.standard({
          openRouter: openRouterApiKey,
          tavily: tavilyApiKey,
        });
    }

    // Create streaming response
    const stream = new ReadableStream({
      start(controller) {
        // Execute workflow with progress callbacks
        executeWorkflowWithProgress(
          requirements,
          workflowConfig,
          (update) => {
            // Send SSE update
            const sseData = `event: ${update.type}\ndata: ${JSON.stringify(update.data)}\n\n`;
            controller.enqueue(new TextEncoder().encode(sseData));
          }
        ).then((result) => {
          // Send final completion event
          const completionData = `event: workflow_completed\ndata: ${JSON.stringify({
            success: true,
            result: result.result,
            sessionId: result.sessionId,
            totalTime: (result.endTime || Date.now()) - result.startTime,
          })}\n\n`;
          
          controller.enqueue(new TextEncoder().encode(completionData));
          controller.close();
          
          console.log(`✅ V3 Streaming workflow completed`);
          
        }).catch((error) => {
          // Send error event
          const errorData = `event: workflow_error\ndata: ${JSON.stringify({
            error: error instanceof Error ? error.message : String(error),
          })}\n\n`;
          
          controller.enqueue(new TextEncoder().encode(errorData));
          controller.close();
          
          console.error('❌ V3 Streaming workflow failed:', error);
        });
      },
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    });

  } catch (error) {
    console.error('❌ V3 Streaming API request failed:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error: 'Streaming workflow setup failed',
        details: error instanceof Error ? error.message : String(error),
      }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

export async function GET() {
  return new Response(
    JSON.stringify({
      name: 'Invincible V3 Streaming API',
      version: '3.0.0',
      description: 'Real-time content generation with server-sent events',
      method: 'POST',
      events: [
        'workflow_started',
        'agent_started', 
        'agent_completed',
        'workflow_completed',
        'workflow_error',
      ],
      presets: ['fast', 'standard', 'premium'],
      status: 'operational',
    }),
    {
      headers: { 'Content-Type': 'application/json' }
    }
  );
} 