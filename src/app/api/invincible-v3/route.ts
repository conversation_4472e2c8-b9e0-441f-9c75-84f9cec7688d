/**
 * Invincible V3 API Route
 * Main endpoint for the new LangGraph-based content generation system
 */

import { NextRequest, NextResponse } from 'next/server';
import { executeWorkflow, WorkflowPresets } from '@/lib/agents/v3/workflow/invincible-workflow';
import { ContentRequirements } from '@/lib/agents/v3/core/state';

export async function POST(request: NextRequest) {
  const startTime = Date.now();
  console.log(`🚀 V3 API request received at ${new Date().toLocaleTimeString()}`);

  try {
    // Parse request body
    const body = await request.json();
    
    // Validate required fields
    if (!body.topic) {
      return NextResponse.json(
        { success: false, error: 'Topic is required' },
        { status: 400 }
      );
    }

    // Create content requirements
    const requirements: ContentRequirements = {
      topic: body.topic,
      contentLength: body.contentLength || 2000,
      tone: body.tone || 'professional',
      targetAudience: body.targetAudience || 'general audience',
      contentType: body.contentType || 'article',
      customInstructions: body.customInstructions,
    };

    console.log(`📝 Processing V3 request:`, {
      topic: requirements.topic,
      contentLength: requirements.contentLength,
      tone: requirements.tone,
    });

    // Get API keys from environment
    const openRouterApiKey = process.env.OPENROUTER_API_KEY;
    const tavilyApiKey = process.env.TAVILY_API_KEY;

    if (!openRouterApiKey) {
      return NextResponse.json(
        { success: false, error: 'OpenRouter API key not configured' },
        { status: 500 }
      );
    }

    // Create workflow configuration
    const preset = body.preset || 'standard';
    let workflowConfig;

    switch (preset) {
      case 'fast':
        workflowConfig = WorkflowPresets.fast({
          openRouter: openRouterApiKey,
          tavily: tavilyApiKey,
        });
        break;
      case 'premium':
        workflowConfig = WorkflowPresets.premium({
          openRouter: openRouterApiKey,
          tavily: tavilyApiKey,
        });
        break;
      default:
        workflowConfig = WorkflowPresets.standard({
          openRouter: openRouterApiKey,
          tavily: tavilyApiKey,
        });
    }

    console.log(`⚙️ Using ${preset} preset with workflow config`);

    // Execute the V3 workflow
    const result = await executeWorkflow(requirements, workflowConfig);

    const executionTime = Date.now() - startTime;

    console.log(`✅ V3 workflow completed successfully in ${executionTime}ms`);

    // Return the result
    return NextResponse.json({
      success: true,
      result: result.result,
      sessionId: result.sessionId,
      executionTime,
      version: 'v3',
      messages: result.workflow.messages.slice(-10), // Last 10 messages for debugging
    });

  } catch (error) {
    const executionTime = Date.now() - startTime;
    
    console.error('❌ V3 API request failed:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'Workflow execution failed',
        details: error instanceof Error ? error.message : String(error),
        executionTime,
        version: 'v3',
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    name: 'Invincible V3 API',
    version: '3.0.0',
    description: 'LangGraph-based content generation system',
    endpoints: {
      POST: 'Execute content generation workflow',
      'POST /stream': 'Execute with server-sent events streaming',
    },
    presets: ['fast', 'standard', 'premium'],
    status: 'operational',
  });
} 