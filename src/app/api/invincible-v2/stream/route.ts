/**
 * Invincible V.2 - Streaming API Route
 * Real-time SSE streaming for V2 autonomous agent system
 */

import { NextRequest } from 'next/server';
import { createSupervisorAgent } from '@/lib/agents/v2/core/supervisor';
import { ContentRequirements } from '@/lib/agents/v2/core/state-schema';
import { logger } from '@/lib/agents/v2/utils/logger';

export async function POST(request: NextRequest) {
  const requestId = `stream_req_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;
  
  logger.info('V2 Streaming API request received', { 
    requestId,
    endpoint: '/api/invincible-v2/stream',
    method: 'POST'
  });

  // Validate API keys first
  const openRouterApiKey = process.env.OPENROUTER_API_KEY;
  const tavilyApiKey = process.env.TAVILY_API_KEY;

  if (!openRouterApiKey || !tavilyApiKey) {
    return new Response(
      JSON.stringify({ error: 'API keys not configured' }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }

  try {
    const body = await request.json();
    const {
      topic,
      contentLength = 2000,
      tone = 'professional',
      targetAudience = 'general audience',
      customInstructions = '',
      contentType = 'article'
    } = body;

    // Validate required fields
    if (!topic) {
      return new Response(
        JSON.stringify({ error: 'Topic is required' }),
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    console.log('✅ V.2 Streaming - Starting workflow for topic:', topic);

    // Create requirements object
    const requirements: ContentRequirements = {
      topic,
      contentLength: parseInt(contentLength.toString()),
      tone,
      targetAudience,
      customInstructions,
      contentType: contentType as 'article' | 'blog' | 'guide' | 'review',
    };

    // Set up SSE headers
    const headers = new Headers({
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST',
      'Access-Control-Allow-Headers': 'Content-Type',
    });

    // Create readable stream for SSE
    const encoder = new TextEncoder();
    const stream = new ReadableStream({
      async start(controller) {
        // Track controller state to prevent errors
        let isControllerClosed = false;
        
        // Handle client disconnection
        const handleDisconnect = () => {
          if (!isControllerClosed) {
            try {
              controller.close();
              isControllerClosed = true;
            } catch (error) {
              // Ignore errors when closing
            }
          }
        };
        
        try {
          // Helper function to send SSE data with robust error protection
          const sendEvent = (type: string, data: any) => {
            if (isControllerClosed) {
              console.warn('SSE controller already closed, skipping event:', type);
              return false;
            }
            
            try {
              const message = `event: ${type}\ndata: ${JSON.stringify(data)}\n\n`;
              controller.enqueue(encoder.encode(message));
              return true;
            } catch (error) {
              console.warn('Failed to send SSE event:', error);
              isControllerClosed = true;
              return false;
            }
          };

          // Send initial connection event
          sendEvent('start', {
            message: 'Invincible V.2 workflow starting...',
            sessionId: `v2_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            timestamp: Date.now(),
          });

          // Create supervisor agent
          const supervisor = createSupervisorAgent({
            openRouterApiKey,
            tavilyApiKey,
            streaming: true,
            qualityThreshold: 80,
          });

          console.log('🤖 V.2 Streaming - Supervisor created, starting workflow...');

          // Use the supervisor's progress stream
          const progressStream = supervisor.generateProgressStream(requirements);

          for await (const update of progressStream) {
            // Check if controller is still active before processing
            if (isControllerClosed) {
              console.log('🛑 SSE controller closed, stopping stream processing');
              break;
            }

            console.log('📡 V.2 Stream update:', update.type, update.data);

            // Send progress update via SSE with validation
            const sent = sendEvent(update.type, {
              ...update.data,
              timestamp: update.timestamp,
            });

            // If send failed, break the loop
            if (!sent) {
              console.log('🛑 Failed to send SSE event, stopping stream');
              break;
            }

            // Add small delay to prevent overwhelming the client
            await new Promise(resolve => setTimeout(resolve, 50));
          }

          // Send completion event if controller is still active
          if (!isControllerClosed) {
            sendEvent('complete', {
              message: 'Invincible V.2 workflow completed successfully',
              timestamp: Date.now(),
            });
          }

          console.log('✅ V.2 Streaming workflow completed');

        } catch (error) {
          console.error('❌ V.2 Streaming error:', error);
          
          // Try to send error event if controller is still open
          if (!isControllerClosed) {
            try {
              const message = `event: error\ndata: ${JSON.stringify({
                error: error.toString(),
                timestamp: Date.now(),
              })}\n\n`;
              controller.enqueue(encoder.encode(message));
            } catch (controllerError) {
              console.warn('Failed to send error event:', controllerError);
            }
          }
        } finally {
          if (!isControllerClosed) {
            try {
              controller.close();
              isControllerClosed = true;
            } catch (closeError) {
              console.warn('Failed to close controller:', closeError);
            }
          }
        }
      },
    });

    return new Response(stream, { headers });

  } catch (error) {
    console.error('❌ V.2 Streaming setup error:', error);
    
    return new Response(
      JSON.stringify({ 
        error: 'Streaming setup failed',
        details: process.env.NODE_ENV === 'development' ? error.toString() : undefined,
      }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}