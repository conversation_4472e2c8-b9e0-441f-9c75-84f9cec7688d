/**
 * Invincible V.2 - Enhanced SSE Streaming API Route
 * Robust server-sent events implementation with proper error handling
 */

import { NextRequest } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { SupervisorAgent } from '@/lib/agents/v2/core/supervisor';
import { 
  InvincibleV2State, 
  StateManager, 
  ContentRequirements 
} from '@/lib/agents/v2/core/state-schema';
import { logger } from '@/lib/agents/v2/utils/logger';

// Constants for SSE
const SSE_RETRY = 5000; // 5 seconds
const SSE_HEADERS = {
  'Content-Type': 'text/event-stream',
  'Cache-Control': 'no-cache, no-transform',
  'Connection': 'keep-alive',
  'X-Accel-Buffering': 'no', // Disable nginx buffering
  'Access-Control-Allow-Origin': '*',
};

interface StreamRequest {
  topic: string;
  contentType: 'article' | 'blog' | 'guide' | 'review' | 'listicle' | 'how-to';
  contentLength: number;
  tone: string;
  targetAudience: string;
  customInstructions?: string;
  openRouterApiKey?: string;
  tavilyApiKey?: string;
}

export async function POST(request: NextRequest) {
  const sessionStartTime = Date.now();
  let encoder: TextEncoder | null = null;
  let controller: ReadableStreamDefaultController | null = null;
  let streamClosed = false;

  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
    return new Response(
        JSON.stringify({ error: 'Unauthorized' }), 
        { status: 401, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Parse request body
    const body = await request.json() as StreamRequest;

    // Validate required fields
    if (!body.topic || !body.contentType || !body.contentLength) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields: topic, contentType, contentLength' }), 
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Get API keys from request or environment
    const openRouterApiKey = body.openRouterApiKey || process.env.OPENROUTER_API_KEY;
    const tavilyApiKey = body.tavilyApiKey || process.env.TAVILY_API_KEY;

    if (!openRouterApiKey || !tavilyApiKey) {
      return new Response(
        JSON.stringify({ error: 'API keys not configured. Please provide openRouterApiKey and tavilyApiKey.' }), 
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }

    logger.info('🚀 Starting V.2 streaming session: ' + session.user.email);

    // Create initial state
    const requirements: ContentRequirements = {
      topic: body.topic,
      contentType: body.contentType,
      contentLength: body.contentLength,
      tone: body.tone || 'professional',
      targetAudience: body.targetAudience || 'general',
      customInstructions: body.customInstructions
    };

    let state = StateManager.createInitialState(requirements);

    // Initialize supervisor
    const supervisor = new SupervisorAgent({
      openRouterApiKey: openRouterApiKey,
      tavilyApiKey: tavilyApiKey,
      streaming: true,
      maxRetries: 3,
      qualityThreshold: 80
    });

    // Create SSE stream
    encoder = new TextEncoder();
    const stream = new ReadableStream({
      async start(streamController) {
        controller = streamController;
        
        try {
          // Send initial connection event
          const connectEvent = formatSSE({
            type: 'connected',
            data: {
              sessionId: Date.now().toString(),
              requirements: requirements
            }
          });
          controller.enqueue(encoder!.encode(connectEvent));

          // Set up message streaming
          const messageHandler = (message: any) => {
            if (streamClosed || !controller) return;

            try {
              const event = formatSSE({
                type: message.type || 'update',
                data: message
              });
              controller.enqueue(encoder!.encode(event));
            } catch (error) {
              logger.error('Failed to send SSE message: ' + String(error));
            }
          };

          // Execute workflow with streaming
          logger.info('🔄 Starting V.2 workflow execution');
          
          // Create a wrapper to capture messages
          const originalAddMessage = StateManager.addMessage;
          StateManager.addMessage = (state: InvincibleV2State, message: any) => {
            messageHandler(message);
            return originalAddMessage(state, message);
          };

          try {
            const finalState = await supervisor.executeWorkflow(requirements);
            
            if (!streamClosed && controller) {
              // Send completion event
              const completionEvent = formatSSE({
                type: 'completed',
                data: {
                  success: true,
                  result: finalState.result,
                  duration: Date.now() - sessionStartTime,
                  quality: finalState.content.quality
                }
              });
              controller.enqueue(encoder!.encode(completionEvent));
              
              // Close stream gracefully
              controller.close();
            }

            logger.info('✅ V.2 streaming workflow completed - ' + 
              `Duration: ${(Date.now() - sessionStartTime) / 1000}s, ` +
              `Words: ${finalState.content.generation.wordCount}, ` +
              `Score: ${finalState.content.quality.contentScore}`);

          } finally {
            // Restore original function
            StateManager.addMessage = originalAddMessage;
          }

        } catch (error) {
          const errorMsg = error instanceof Error ? error.message : 'Unknown error';
          logger.error('❌ V.2 streaming workflow failed: ' + errorMsg);

          if (!streamClosed && controller) {
            // Send error event
            const errorEvent = formatSSE({
              type: 'error',
              data: {
                error: errorMsg,
                recoverable: false,
                timestamp: Date.now()
              }
            });
            
            try {
              controller.enqueue(encoder!.encode(errorEvent));
            } catch (e) {
              // Controller might be closed
              logger.error('Failed to send error event: ' + String(e));
            }
            
            // Close stream on error
            controller.close();
          }
        } finally {
          streamClosed = true;
        }
      },
      
      cancel() {
        streamClosed = true;
        logger.info('⚠️ Stream cancelled by client');
      }
    });

    // Return streaming response
    return new Response(stream, {
      headers: SSE_HEADERS,
      status: 200
    });

  } catch (error) {
    const errorMsg = error instanceof Error ? error.message : 'Unknown error';
    logger.error('❌ Failed to initialize V.2 streaming: ' + errorMsg);
    
    return new Response(
      JSON.stringify({ 
        error: 'Failed to initialize streaming',
        details: errorMsg 
      }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

/**
 * Format message as SSE
 */
function formatSSE(message: { type: string; data: any }): string {
  const lines: string[] = [];
  
  if (message.type) {
    lines.push(`event: ${message.type}`);
  }
  
  if (message.data !== undefined) {
    const dataStr = typeof message.data === 'string' 
      ? message.data 
      : JSON.stringify(message.data);
    
    // Split data by newlines for proper SSE format
    dataStr.split('\n').forEach(line => {
      lines.push(`data: ${line}`);
    });
  }
  
  lines.push(`id: ${Date.now()}`);
  lines.push(`retry: ${SSE_RETRY}`);
  lines.push(''); // Empty line to end the event
  lines.push(''); // Extra empty line for better compatibility
  
  return lines.join('\n');
}

// OPTIONS method for CORS
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}