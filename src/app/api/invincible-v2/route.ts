/**
 * Invincible V.2 - Enhanced Main API Route
 * Non-streaming endpoint for V2 content generation
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { SupervisorAgent } from '@/lib/agents/v2/core/supervisor';
import { ContentRequirements } from '@/lib/agents/v2/core/state-schema';
import { logger } from '@/lib/agents/v2/utils/logger';

interface GenerateRequest {
  topic: string;
  contentType: 'article' | 'blog' | 'guide' | 'review' | 'listicle' | 'how-to';
  contentLength: number;
  tone: string;
  targetAudience: string;
  customInstructions?: string;
  openRouterApiKey?: string;
  tavilyApiKey?: string;
}

export async function POST(request: NextRequest) {
  const startTime = Date.now();
  
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' }, 
        { status: 401 }
      );
    }

    // Parse and validate request
    const body = await request.json() as GenerateRequest;
    
    // Validate required fields
    if (!body.topic || !body.contentType || !body.contentLength) {
      return NextResponse.json(
        { error: 'Missing required fields: topic, contentType, contentLength' }, 
        { status: 400 }
      );
    }

    // Get API keys from request or environment
    const openRouterApiKey = body.openRouterApiKey || process.env.OPENROUTER_API_KEY;
    const tavilyApiKey = body.tavilyApiKey || process.env.TAVILY_API_KEY;

    if (!openRouterApiKey || !tavilyApiKey) {
      return NextResponse.json(
        { error: 'API keys not configured. Please provide openRouterApiKey and tavilyApiKey.' }, 
        { status: 400 }
      );
    }

    logger.info('🚀 Starting V.2 content generation - User: ' + session.user.email);
    logger.info('📝 Topic: ' + body.topic);

    // Create requirements
    const requirements: ContentRequirements = {
      topic: body.topic,
      contentType: body.contentType,
      contentLength: body.contentLength,
      tone: body.tone || 'professional',
      targetAudience: body.targetAudience || 'general audience',
      customInstructions: body.customInstructions
    };

    // Initialize supervisor
    const supervisor = new SupervisorAgent({
      openRouterApiKey,
      tavilyApiKey,
      streaming: false,
      maxRetries: 3,
      qualityThreshold: 80,
      timeout: 300000 // 5 minutes
    });

    // Execute workflow
    logger.info('🔄 Executing V.2 workflow...');
    const finalState = await supervisor.executeWorkflow(requirements);

    // Check if result exists
    if (!finalState.result) {
      throw new Error('Workflow completed but no result was generated');
    }

    const duration = (Date.now() - startTime) / 1000;
    logger.info('✅ V.2 content generation completed - ' + 
      `Duration: ${duration}s, ` +
      `Words: ${finalState.content.generation.wordCount}, ` +
      `Quality: ${finalState.content.quality.contentScore}`);

    // Return success response
    return NextResponse.json({
      success: true,
      result: finalState.result,
      metadata: {
        duration,
        sessionId: finalState.sessionId,
        workflow: {
          completedAgents: finalState.workflow.completedAgents,
          totalAgents: finalState.workflow.completedAgents.length,
          status: finalState.workflow.status,
          progress: finalState.workflow.progress
        },
        quality: {
          contentScore: finalState.content.quality.contentScore,
          seoScore: finalState.content.quality.seoScore,
          geoScore: finalState.content.quality.geoScore,
          aiDetectionProbability: finalState.content.quality.aiDetectionProbability,
          humanScore: finalState.content.quality.humanScore,
          originalityScore: finalState.content.quality.originalityScore
        },
        performance: {
          wordCount: finalState.content.generation.wordCount,
          researchQueries: finalState.research.data.length,
          competitorsAnalyzed: finalState.research.competitiveAnalysis.topCompetitors.length,
          humanizationTechniques: finalState.content.generation.humanizationApplied.length
        }
      }
    });

  } catch (error) {
    const errorMsg = error instanceof Error ? error.message : 'Unknown error';
    const duration = (Date.now() - startTime) / 1000;
    
    logger.error('❌ V.2 content generation failed: ' + errorMsg);
    logger.error('Duration: ' + duration + 's');

    // Determine error type and status code
    let statusCode = 500;
    let errorType = 'INTERNAL_ERROR';
    
    if (errorMsg.includes('API key')) {
      statusCode = 401;
      errorType = 'API_KEY_ERROR';
    } else if (errorMsg.includes('rate limit')) {
      statusCode = 429;
      errorType = 'RATE_LIMIT_ERROR';
    } else if (errorMsg.includes('timeout')) {
      statusCode = 504;
      errorType = 'TIMEOUT_ERROR';
    } else if (errorMsg.includes('validation')) {
      statusCode = 400;
      errorType = 'VALIDATION_ERROR';
    }

    return NextResponse.json(
      { 
        success: false,
        error: errorMsg,
        errorType,
        duration,
        timestamp: new Date().toISOString()
      }, 
      { status: statusCode }
    );
  }
}

// GET method for health check
export async function GET() {
  return NextResponse.json({
    status: 'healthy',
    version: '2.0',
    timestamp: new Date().toISOString(),
    features: [
      'Multi-agent system',
      'Advanced research with Tavily',
      'SEO/GEO optimization',
      'AI detection bypass',
      'Quality assurance',
      'Competitive analysis'
    ]
  });
}