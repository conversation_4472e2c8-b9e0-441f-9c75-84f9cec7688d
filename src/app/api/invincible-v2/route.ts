/**
 * Invincible V.2 - Main API Route
 * Non-streaming endpoint for V2 autonomous agent system
 */

import { NextRequest, NextResponse } from 'next/server';
import { createSupervisorAgent } from '@/lib/agents/v2/core/supervisor';
import { ContentRequirements } from '@/lib/agents/v2/core/state-schema';
import { logger } from '@/lib/agents/v2/utils/logger';

export async function POST(request: NextRequest) {
  const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;
  
  try {
    logger.info('V2 API request received', { 
      requestId,
      endpoint: '/api/invincible-v2',
      method: 'POST'
    });
    
    const body = await request.json();
    const {
      topic,
      contentLength = 2000,
      tone = 'professional',
      targetAudience = 'general audience',
      customInstructions = '',
      contentType = 'article'
    } = body;

    logger.info('V2 API request parsed', {
      requestId,
      topic,
      contentLength,
      tone,
      targetAudience,
      contentType
    });

    // Validate required fields
    if (!topic) {
      logger.warn('V2 API validation failed: missing topic', { requestId });
      return NextResponse.json(
        { success: false, error: 'Topic is required' },
        { status: 400 }
      );
    }

    // Validate API keys
    const openRouterApiKey = process.env.OPENROUTER_API_KEY;
    const tavilyApiKey = process.env.TAVILY_API_KEY;

    if (!openRouterApiKey) {
      return NextResponse.json(
        { success: false, error: 'OpenRouter API key not configured' },
        { status: 500 }
      );
    }

    if (!tavilyApiKey) {
      return NextResponse.json(
        { success: false, error: 'Tavily API key not configured' },
        { status: 500 }
      );
    }

    console.log('✅ API keys validated, creating supervisor agent');

    // Create requirements object
    const requirements: ContentRequirements = {
      topic,
      contentLength: parseInt(contentLength.toString()),
      tone,
      targetAudience,
      customInstructions,
      contentType: contentType as 'article' | 'blog' | 'guide' | 'review',
    };

    // Create supervisor agent
    const supervisor = createSupervisorAgent({
      openRouterApiKey,
      tavilyApiKey,
      streaming: false,
      qualityThreshold: 80,
    });

    console.log('🤖 Starting V.2 workflow execution...');
    
    // Execute the complete workflow
    const startTime = Date.now();
    const finalState = await supervisor.executeWorkflow(requirements);
    const executionTime = Date.now() - startTime;

    console.log('✅ V.2 workflow completed in', executionTime, 'ms');

    // Check for errors
    if (finalState.workflow.errors.length > 0) {
      console.error('❌ V.2 workflow errors:', finalState.workflow.errors);
      return NextResponse.json(
        { 
          success: false, 
          error: 'Workflow execution failed',
          details: finalState.workflow.errors,
        },
        { status: 500 }
      );
    }

    // Check if result is available
    if (!finalState.result) {
      console.error('❌ No result generated');
      return NextResponse.json(
        { 
          success: false, 
          error: 'No result generated - workflow may have failed',
        },
        { status: 500 }
      );
    }

    console.log('📊 V.2 Results:', {
      wordCount: finalState.result.article?.wordCount,
      qualityScore: finalState.result.performance?.qualityScore,
      executionTime: finalState.result.performance?.executionTime,
    });

    // Return successful result
    return NextResponse.json({
      success: true,
      result: finalState.result,
      metadata: {
        sessionId: finalState.sessionId,
        version: 'v2',
        executionTime,
        agentsExecuted: finalState.workflow.completedAgents.length,
        messagesCount: finalState.messages.length,
        researchQueries: finalState.research.data.length,
        totalSources: finalState.research.data.reduce((sum, r) => sum + r.results.length, 0),
      },
    });

  } catch (error) {
    console.error('❌ Invincible V.2 API Error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? error.toString() : undefined,
      },
      { status: 500 }
    );
  }
}