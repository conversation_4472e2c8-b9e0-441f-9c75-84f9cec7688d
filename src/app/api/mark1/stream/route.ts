/**
 * Mark1 Agent System - Streaming API Route
 * Real-time SSE streaming with robust error handling
 */

import { NextRequest } from 'next/server';
import { createSupervisorAgent } from '@/lib/agents/mark1/core/supervisor';
import { ContentRequirements } from '@/lib/agents/mark1/core/state-schema';
import { logger } from '@/lib/agents/mark1/utils/logger';

export async function POST(request: NextRequest) {
  const requestId = `mark1_stream_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;
  
  try {
    logger.info('Mark1 streaming API request received', { 
      requestId,
      endpoint: '/api/mark1/stream',
      method: 'POST'
    });
    
    const body = await request.json();
    const {
      topic,
      contentLength = 2000,
      tone = 'professional',
      targetAudience = 'general audience',
      customInstructions = '',
      contentType = 'article'
    } = body;

    // Validate required fields
    if (!topic) {
      return new Response(
        JSON.stringify({ error: 'Topic is required' }),
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Get API keys from environment
    const openRouterApiKey = process.env.OPENROUTER_API_KEY;
    const tavilyApiKey = process.env.TAVILY_API_KEY;

    if (!openRouterApiKey || !tavilyApiKey) {
      return new Response(
        JSON.stringify({ error: 'API keys not configured' }),
        { 
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Create content requirements
    const requirements: ContentRequirements = {
      topic: topic.trim(),
      contentLength,
      tone,
      targetAudience,
      customInstructions,
      contentType,
      keywords: [],
    };

    // Create streaming response
    const stream = new ReadableStream({
      async start(controller) {
        const encoder = new TextEncoder();
        
        const sendEvent = (type: string, data: any) => {
          const message = `data: ${JSON.stringify({ type, data, timestamp: Date.now() })}\n\n`;
          controller.enqueue(encoder.encode(message));
        };

        const sendError = (error: string, details?: any) => {
          const message = `data: ${JSON.stringify({ 
            type: 'error', 
            data: { error, details }, 
            timestamp: Date.now() 
          })}\n\n`;
          controller.enqueue(encoder.encode(message));
        };

        const sendComplete = (result: any) => {
          const message = `data: ${JSON.stringify({ 
            type: 'complete', 
            data: result, 
            timestamp: Date.now() 
          })}\n\n`;
          controller.enqueue(encoder.encode(message));
          controller.close();
        };

        try {
          sendEvent('started', { 
            requestId,
            topic: requirements.topic,
            estimatedTime: '2-3 minutes'
          });

          // Create supervisor agent
          const supervisor = createSupervisorAgent({
            openRouterApiKey,
            tavilyApiKey,
            streaming: true,
            qualityThreshold: 75,
            maxRetries: 2,
          });

          sendEvent('supervisor_created', { 
            agents: ['research_agent', 'content_agent', 'seo_geo_agent', 'quality_agent']
          });

          // Execute workflow with progress updates
          const startTime = Date.now();
          let lastProgress = 0;

          // Create a simple progress tracker
          const progressInterval = setInterval(() => {
            // This is a simplified progress simulation
            // In a real implementation, you'd get actual progress from agents
            if (lastProgress < 90) {
              lastProgress += Math.random() * 10;
              sendEvent('progress', { 
                progress: Math.min(90, Math.round(lastProgress)),
                phase: lastProgress < 25 ? 'research' : 
                       lastProgress < 50 ? 'content_generation' :
                       lastProgress < 75 ? 'seo_optimization' : 'quality_assurance'
              });
            }
          }, 2000);

          try {
            const finalState = await supervisor.executeWorkflow(requirements);
            clearInterval(progressInterval);

            const executionTime = Date.now() - startTime;

            // Check for critical errors
            const totalErrors = finalState.research.errors.length + 
                               finalState.generation.errors.length + 
                               finalState.analysis.errors.length + 
                               finalState.quality.errors.length + 
                               finalState.workflow.errors.length;

            if (totalErrors > 10 || !finalState.result) {
              sendError('Workflow execution encountered critical errors', {
                totalErrors,
                errorSummary: [
                  ...finalState.research.errors.slice(0, 2),
                  ...finalState.generation.errors.slice(0, 2),
                  ...finalState.analysis.errors.slice(0, 2),
                  ...finalState.quality.errors.slice(0, 2),
                  ...finalState.workflow.errors.slice(0, 2),
                ].slice(0, 5),
              });
              return;
            }

            sendEvent('progress', { progress: 100, phase: 'completed' });

            // Send completion event
            sendComplete({
              success: true,
              result: finalState.result,
              metadata: {
                sessionId: finalState.sessionId,
                version: 'mark1',
                executionTime,
                agentsExecuted: finalState.workflow.completedAgents.length,
                messagesCount: finalState.messages.length,
                researchQueries: finalState.research.data.length,
                totalSources: finalState.research.data.reduce((sum, r) => sum + r.results.length, 0),
                totalErrors,
                errorSummary: totalErrors > 0 ? {
                  research: finalState.research.errors.length,
                  generation: finalState.generation.errors.length,
                  analysis: finalState.analysis.errors.length,
                  quality: finalState.quality.errors.length,
                  workflow: finalState.workflow.errors.length,
                } : undefined,
              },
            });

          } catch (workflowError) {
            clearInterval(progressInterval);
            logger.error('Mark1 streaming workflow error', {
              requestId,
              error: workflowError.toString(),
            });
            
            sendError('Workflow execution failed', {
              error: workflowError.toString(),
              phase: 'workflow_execution'
            });
          }

        } catch (error) {
          logger.error('Mark1 streaming setup error', {
            requestId,
            error: error.toString(),
          });
          
          sendError('Stream setup failed', {
            error: error.toString(),
            phase: 'initialization'
          });
        }
      },

      cancel() {
        logger.info('Mark1 streaming cancelled', { requestId });
      }
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    });

  } catch (error) {
    logger.error('Mark1 streaming API critical error', {
      requestId,
      error: error.toString(),
    });
    
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? error.toString() : undefined,
        requestId,
      }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

// OPTIONS handler for CORS
export async function OPTIONS(request: NextRequest) {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
