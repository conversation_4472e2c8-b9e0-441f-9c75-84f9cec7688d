/**
 * Mark1 Agent System - Main API Route
 * Simplified and robust endpoint with comprehensive error handling
 */

import { NextRequest, NextResponse } from 'next/server';
import { createSupervisorAgent } from '@/lib/agents/mark1/core/supervisor';
import { ContentRequirements } from '@/lib/agents/mark1/core/state-schema';
import { logger } from '@/lib/agents/mark1/utils/logger';

export async function POST(request: NextRequest) {
  const requestId = `mark1_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;
  
  try {
    logger.info('Mark1 API request received', { 
      requestId,
      endpoint: '/api/mark1',
      method: 'POST'
    });
    
    const body = await request.json();
    const {
      topic,
      contentLength = 2000,
      tone = 'professional',
      targetAudience = 'general audience',
      customInstructions = '',
      contentType = 'article'
    } = body;

    logger.info('Mark1 API request parsed', {
      requestId,
      topic,
      contentLength,
      tone,
      targetAudience,
      contentType
    });

    // Validate required fields
    if (!topic) {
      logger.warn('Mark1 API validation failed: missing topic', { requestId });
      return NextResponse.json(
        { success: false, error: 'Topic is required' },
        { status: 400 }
      );
    }

    // Validate content length
    if (contentLength < 500 || contentLength > 10000) {
      logger.warn('Mark1 API validation failed: invalid content length', { requestId, contentLength });
      return NextResponse.json(
        { success: false, error: 'Content length must be between 500 and 10000 words' },
        { status: 400 }
      );
    }

    // Get API keys from environment
    const openRouterApiKey = process.env.OPENROUTER_API_KEY;
    const tavilyApiKey = process.env.TAVILY_API_KEY;

    if (!openRouterApiKey) {
      logger.error('Mark1 API configuration error: missing OpenRouter API key', { requestId });
      return NextResponse.json(
        { success: false, error: 'OpenRouter API key not configured' },
        { status: 500 }
      );
    }

    if (!tavilyApiKey) {
      logger.error('Mark1 API configuration error: missing Tavily API key', { requestId });
      return NextResponse.json(
        { success: false, error: 'Tavily API key not configured' },
        { status: 500 }
      );
    }

    // Create content requirements
    const requirements: ContentRequirements = {
      topic: topic.trim(),
      contentLength,
      tone,
      targetAudience,
      customInstructions,
      contentType,
      keywords: [], // Will be populated during research
    };

    logger.info('Mark1 requirements created', { requestId, requirements });

    // Create supervisor agent
    const supervisor = createSupervisorAgent({
      openRouterApiKey,
      tavilyApiKey,
      streaming: false,
      qualityThreshold: 75, // Slightly lower threshold for better success rate
      maxRetries: 2,
    });

    console.log('🤖 Starting Mark1 workflow execution...');
    
    // Execute the complete workflow
    const startTime = Date.now();
    const finalState = await supervisor.executeWorkflow(requirements);
    const executionTime = Date.now() - startTime;

    console.log('✅ Mark1 workflow completed in', executionTime, 'ms');

    // Check for critical errors
    const totalErrors = finalState.research.errors.length + 
                       finalState.generation.errors.length + 
                       finalState.analysis.errors.length + 
                       finalState.quality.errors.length + 
                       finalState.workflow.errors.length;

    if (totalErrors > 10 || !finalState.result) {
      console.error('❌ Mark1 workflow critical errors:', {
        researchErrors: finalState.research.errors,
        generationErrors: finalState.generation.errors,
        analysisErrors: finalState.analysis.errors,
        qualityErrors: finalState.quality.errors,
        workflowErrors: finalState.workflow.errors,
      });
      
      return NextResponse.json(
        { 
          success: false, 
          error: 'Workflow execution encountered critical errors',
          details: {
            totalErrors,
            errorSummary: [
              ...finalState.research.errors.slice(0, 2),
              ...finalState.generation.errors.slice(0, 2),
              ...finalState.analysis.errors.slice(0, 2),
              ...finalState.quality.errors.slice(0, 2),
              ...finalState.workflow.errors.slice(0, 2),
            ].slice(0, 5),
          },
        },
        { status: 500 }
      );
    }

    // Log successful completion
    logger.info('Mark1 workflow completed successfully', {
      requestId,
      executionTime,
      agentsExecuted: finalState.workflow.completedAgents.length,
      totalErrors,
      qualityScore: finalState.result?.performance?.qualityScore,
    });

    // Return successful result
    return NextResponse.json({
      success: true,
      result: finalState.result,
      metadata: {
        sessionId: finalState.sessionId,
        version: 'mark1',
        executionTime,
        agentsExecuted: finalState.workflow.completedAgents.length,
        messagesCount: finalState.messages.length,
        researchQueries: finalState.research.data.length,
        totalSources: finalState.research.data.reduce((sum, r) => sum + r.results.length, 0),
        totalErrors,
        errorSummary: totalErrors > 0 ? {
          research: finalState.research.errors.length,
          generation: finalState.generation.errors.length,
          analysis: finalState.analysis.errors.length,
          quality: finalState.quality.errors.length,
          workflow: finalState.workflow.errors.length,
        } : undefined,
      },
    });

  } catch (error) {
    console.error('❌ Mark1 API Error:', error);
    
    logger.error('Mark1 API critical error', {
      requestId,
      error: error.toString(),
      stack: error instanceof Error ? error.stack : undefined,
    });
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? error.toString() : undefined,
        requestId,
      },
      { status: 500 }
    );
  }
}

// GET endpoint to check Mark1 agent status
export async function GET(req: NextRequest) {
  try {
    // Check environment variables
    const openRouterApiKey = process.env.OPENROUTER_API_KEY;
    const tavilyApiKey = process.env.TAVILY_API_KEY;
    
    const configStatus = {
      openRouterConfigured: !!openRouterApiKey,
      tavilyConfigured: !!tavilyApiKey,
    };

    return NextResponse.json({
      agent: 'Mark1',
      version: '1.0.0',
      status: 'ready',
      description: 'Simplified and robust multi-agent content generation system',
      improvements: [
        'Simplified state management with robust error handling',
        'Fixed API key rotation issues',
        'Improved error isolation and recovery',
        'Streamlined agent communication',
        'Enhanced fallback mechanisms',
        'Reduced complexity while maintaining functionality',
        'Better timeout and retry logic',
        'Comprehensive logging and monitoring'
      ],
      capabilities: [
        'Comprehensive research with Tavily search',
        'Competitive analysis and market intelligence',
        'Advanced content generation with AI',
        'SEO and GEO optimization',
        'Quality assurance and humanization',
        'Robust error handling and recovery',
        'Simplified workflow coordination'
      ],
      workflow: {
        agents: ['research_agent', 'content_agent', 'seo_geo_agent', 'quality_agent'],
        description: 'Fixed sequential workflow optimized for reliability',
        estimatedTime: '2-3 minutes',
        errorHandling: 'Comprehensive with fallback mechanisms'
      },
      configuration: configStatus,
      differences_from_v2: [
        'Simplified state schema with better error tracking',
        'Fixed API key management without complex rotation',
        'Improved error isolation between agents',
        'Better fallback content generation',
        'Streamlined JSON parsing with safe defaults',
        'Reduced memory usage and complexity',
        'More predictable execution flow',
        'Enhanced logging and debugging'
      ]
    });
  } catch (error) {
    return NextResponse.json(
      { 
        agent: 'Mark1',
        status: 'error',
        error: 'Status check failed',
        details: process.env.NODE_ENV === 'development' ? error.toString() : undefined,
      },
      { status: 500 }
    );
  }
}
