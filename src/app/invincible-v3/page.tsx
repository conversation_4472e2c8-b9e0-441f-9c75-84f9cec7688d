'use client';

import React, { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Sparkles, 
  Zap, 
  Crown, 
  Play, 
  StopCircle,
  Download,
  Copy,
  CheckCircle,
  AlertCircle,
  Clock,
  BarChart3,
  Search,
  Edit,
  Shield,
  Star
} from 'lucide-react';

interface WorkflowProgress {
  type: string;
  data: any;
  timestamp: number;
}

interface GeneratedResult {
  success: boolean;
  result?: {
    article: {
      title: string;
      content: string;
      wordCount: number;
      metaDescription: string;
      seoOptimized: boolean;
      geoOptimized: boolean;
      humanizationApplied: string[];
    };
    metrics: {
      aiDetectionScore: number;
      humanLikenessScore: number;
      seoScore: number;
      geoScore: number;
      originalityScore: number;
      competitiveAdvantage: number;
    };
    analytics: {
      researchQueries: number;
      totalSources: number;
      executionTime: number;
      agentsExecuted: string[];
    };
    performance: {
      success: boolean;
      qualityScore: number;
    };
  };
  sessionId?: string;
  executionTime?: number;
  error?: string;
}

const presetConfigs = {
  fast: {
    icon: Zap,
    name: 'Fast',
    description: 'Quick generation with essential features',
    color: 'bg-green-500',
    features: ['Basic Research', 'SEO Optimization', 'Quick Generation'],
    estimatedTime: '2-3 minutes'
  },
  standard: {
    icon: Sparkles,
    name: 'Standard',
    description: 'Balanced quality and speed',
    color: 'bg-blue-500',
    features: ['Comprehensive Research', 'SEO + GEO Optimization', 'Competitive Analysis', 'Humanization'],
    estimatedTime: '3-5 minutes'
  },
  premium: {
    icon: Crown,
    name: 'Premium',
    description: 'Maximum quality and optimization',
    color: 'bg-purple-500',
    features: ['Deep Research', 'Advanced SEO/GEO', 'Competitive Intelligence', 'AI Detection Bypass', 'Quality Assurance'],
    estimatedTime: '5-8 minutes'
  }
};

export default function InvincibleV3Page() {
  const [formData, setFormData] = useState({
    topic: '',
    contentLength: 2000,
    tone: 'professional',
    targetAudience: 'general audience',
    contentType: 'article',
    customInstructions: '',
    preset: 'standard'
  });

  const [isGenerating, setIsGenerating] = useState(false);
  const [progress, setProgress] = useState(0);
  const [currentAgent, setCurrentAgent] = useState('');
  const [workflowEvents, setWorkflowEvents] = useState<WorkflowProgress[]>([]);
  const [result, setResult] = useState<GeneratedResult | null>(null);
  const [activeTab, setActiveTab] = useState('form');
  const [copied, setCopied] = useState(false);

  const abortControllerRef = useRef<AbortController | null>(null);

  const handleInputChange = (field: string, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleGenerate = async () => {
    if (!formData.topic.trim()) {
      alert('Please enter a topic');
      return;
    }

    setIsGenerating(true);
    setProgress(0);
    setCurrentAgent('');
    setWorkflowEvents([]);
    setResult(null);
    setActiveTab('progress');

    abortControllerRef.current = new AbortController();

    try {
      const response = await fetch('/api/invincible-v3/stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
        signal: abortControllerRef.current.signal,
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const reader = response.body?.getReader();
      const decoder = new TextDecoder();

      if (!reader) {
        throw new Error('No response body');
      }

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('event:') && lines[lines.indexOf(line) + 1]?.startsWith('data:')) {
            const eventType = line.replace('event:', '').trim();
            const dataLine = lines[lines.indexOf(line) + 1];
            
            try {
              const eventData = JSON.parse(dataLine.replace('data:', '').trim());
              
              const progressEvent: WorkflowProgress = {
                type: eventType,
                data: eventData,
                timestamp: Date.now()
              };

              setWorkflowEvents(prev => [...prev, progressEvent]);

              // Update progress based on event type
              switch (eventType) {
                case 'workflow_started':
                  setProgress(5);
                  setCurrentAgent('Initializing...');
                  break;
                case 'agent_started':
                  setCurrentAgent(eventData.agent || 'Unknown');
                  setProgress(eventData.progress || 0);
                  break;
                case 'agent_completed':
                  setProgress(eventData.progress || 0);
                  break;
                case 'workflow_completed':
                  setProgress(100);
                  setCurrentAgent('Completed');
                  setResult(eventData);
                  setActiveTab('results');
                  break;
                case 'workflow_error':
                  setResult({ success: false, error: eventData.error });
                  setActiveTab('results');
                  break;
              }
            } catch (e) {
              console.warn('Failed to parse SSE data:', e);
            }
          }
        }
      }

    } catch (error: any) {
      if (error.name !== 'AbortError') {
        console.error('Generation failed:', error);
        setResult({ 
          success: false, 
          error: error.message || 'Generation failed' 
        });
        setActiveTab('results');
      }
    } finally {
      setIsGenerating(false);
      setCurrentAgent('');
    }
  };

  const handleStop = () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    setIsGenerating(false);
    setCurrentAgent('Stopped');
  };

  const handleCopy = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  const formatAgentName = (agent: string) => {
    const agentNames: Record<string, string> = {
      'research_node': 'Research & Analysis',
      'content_node': 'Content Generation',
      'optimization_node': 'SEO/GEO Optimization',
      'quality_node': 'Quality Assurance'
    };
    return agentNames[agent] || agent;
  };

  const getQualityColor = (score: number) => {
    if (score >= 85) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Invincible V3
          </h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Next-generation content creation powered by LangGraph workflows. 
            Research, create, optimize, and humanize content with AI precision.
          </p>
          
          {/* System Status */}
          <div className="flex items-center justify-center gap-2 mt-4">
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-sm text-muted-foreground">V3 System Operational</span>
            </div>
            <Badge variant="outline" className="ml-2">
              LangGraph Powered
            </Badge>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="form">Configuration</TabsTrigger>
            <TabsTrigger value="progress">Progress</TabsTrigger>
            <TabsTrigger value="results">Results</TabsTrigger>
          </TabsList>

          {/* Configuration Tab */}
          <TabsContent value="form" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Edit className="w-5 h-5" />
                  Content Requirements
                </CardTitle>
                <CardDescription>
                  Define what you want to create
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="topic">Topic *</Label>
                    <Input
                      id="topic"
                      placeholder="e.g., Machine Learning in Healthcare"
                      value={formData.topic}
                      onChange={(e) => handleInputChange('topic', e.target.value)}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="contentLength">Word Count</Label>
                    <Input
                      id="contentLength"
                      type="number"
                      min="500"
                      max="5000"
                      value={formData.contentLength}
                      onChange={(e) => handleInputChange('contentLength', parseInt(e.target.value))}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="tone">Tone</Label>
                    <select
                      id="tone"
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                      value={formData.tone}
                      onChange={(e) => handleInputChange('tone', e.target.value)}
                    >
                      <option value="professional">Professional</option>
                      <option value="casual">Casual</option>
                      <option value="technical">Technical</option>
                      <option value="conversational">Conversational</option>
                    </select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="targetAudience">Target Audience</Label>
                    <Input
                      id="targetAudience"
                      placeholder="e.g., Software developers"
                      value={formData.targetAudience}
                      onChange={(e) => handleInputChange('targetAudience', e.target.value)}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="customInstructions">Custom Instructions (Optional)</Label>
                  <Textarea
                    id="customInstructions"
                    placeholder="Any specific requirements or guidelines..."
                    value={formData.customInstructions}
                    onChange={(e) => handleInputChange('customInstructions', e.target.value)}
                    rows={3}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Preset Selection */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Sparkles className="w-5 h-5" />
                  Workflow Preset
                </CardTitle>
                <CardDescription>
                  Choose the quality and speed balance
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-3">
                  {Object.entries(presetConfigs).map(([key, config]) => {
                    const Icon = config.icon;
                    const isSelected = formData.preset === key;
                    
                    return (
                      <Card 
                        key={key}
                        className={`cursor-pointer transition-all ${
                          isSelected 
                            ? 'ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-950' 
                            : 'hover:shadow-md'
                        }`}
                        onClick={() => handleInputChange('preset', key)}
                      >
                        <CardContent className="p-4">
                          <div className="flex items-center gap-2 mb-2">
                            <div className={`p-2 rounded-lg ${config.color} text-white`}>
                              <Icon className="w-4 h-4" />
                            </div>
                            <h3 className="font-semibold">{config.name}</h3>
                          </div>
                          
                          <p className="text-sm text-muted-foreground mb-3">
                            {config.description}
                          </p>
                          
                          <div className="space-y-2">
                            <div className="flex items-center gap-1 text-sm">
                              <Clock className="w-3 h-3" />
                              <span>{config.estimatedTime}</span>
                            </div>
                            
                            <div className="flex flex-wrap gap-1">
                              {config.features.map((feature, idx) => (
                                <Badge key={idx} variant="secondary" className="text-xs">
                                  {feature}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            {/* Generate Button */}
            <div className="flex justify-center">
              <Button
                onClick={handleGenerate}
                disabled={isGenerating || !formData.topic.trim()}
                size="lg"
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
              >
                {isGenerating ? (
                  <>
                    <StopCircle className="w-4 h-4 mr-2" />
                    Generating...
                  </>
                ) : (
                  <>
                    <Play className="w-4 h-4 mr-2" />
                    Generate Content
                  </>
                )}
              </Button>
              
              {isGenerating && (
                <Button
                  onClick={handleStop}
                  variant="outline"
                  size="lg"
                  className="ml-2"
                >
                  <StopCircle className="w-4 h-4 mr-2" />
                  Stop
                </Button>
              )}
            </div>
          </TabsContent>

          {/* Progress Tab */}
          <TabsContent value="progress" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="w-5 h-5" />
                  Workflow Progress
                </CardTitle>
                <CardDescription>
                  Real-time updates from the V3 system
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Progress Bar */}
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>{currentAgent || 'Ready'}</span>
                    <span>{progress}%</span>
                  </div>
                  <Progress value={progress} className="h-2" />
                </div>

                {/* Live Events */}
                <div className="space-y-2 max-h-96 overflow-y-auto">
                  {workflowEvents.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      <Sparkles className="w-8 h-8 mx-auto mb-2 opacity-50" />
                      <p>Workflow events will appear here</p>
                    </div>
                  ) : (
                    workflowEvents.map((event, idx) => (
                      <div key={idx} className="flex items-start gap-3 p-3 bg-muted/50 rounded-lg">
                        <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="font-medium text-sm">
                              {event.type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                            </span>
                            <span className="text-xs text-muted-foreground">
                              {new Date(event.timestamp).toLocaleTimeString()}
                            </span>
                          </div>
                          {event.data.agent && (
                            <p className="text-sm text-muted-foreground">
                              {formatAgentName(event.data.agent)}
                            </p>
                          )}
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Results Tab */}
          <TabsContent value="results" className="space-y-6">
            {result ? (
              result.success && result.result ? (
                <>
                  {/* Quality Metrics */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Star className="w-5 h-5" />
                        Quality Metrics
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid gap-4 md:grid-cols-3">
                        <div className="text-center">
                          <div className={`text-2xl font-bold ${getQualityColor(result.result.performance.qualityScore)}`}>
                            {result.result.performance.qualityScore}
                          </div>
                          <div className="text-sm text-muted-foreground">Overall Quality</div>
                        </div>
                        
                        <div className="text-center">
                          <div className={`text-2xl font-bold ${getQualityColor(result.result.metrics.humanLikenessScore)}`}>
                            {result.result.metrics.humanLikenessScore}
                          </div>
                          <div className="text-sm text-muted-foreground">Human-Likeness</div>
                        </div>
                        
                        <div className="text-center">
                          <div className={`text-2xl font-bold ${getQualityColor(100 - result.result.metrics.aiDetectionScore)}`}>
                            {100 - result.result.metrics.aiDetectionScore}
                          </div>
                          <div className="text-sm text-muted-foreground">AI Bypass Score</div>
                        </div>
                      </div>
                      
                      <div className="grid gap-2 md:grid-cols-2 mt-4">
                        <div className="flex justify-between">
                          <span>SEO Score:</span>
                          <span className={getQualityColor(result.result.metrics.seoScore)}>
                            {result.result.metrics.seoScore}/100
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span>GEO Score:</span>
                          <span className={getQualityColor(result.result.metrics.geoScore)}>
                            {result.result.metrics.geoScore}/100
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span>Originality:</span>
                          <span className={getQualityColor(result.result.metrics.originalityScore)}>
                            {result.result.metrics.originalityScore}/100
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span>Competitive Edge:</span>
                          <span className={getQualityColor(result.result.metrics.competitiveAdvantage)}>
                            {result.result.metrics.competitiveAdvantage}/100
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Article Content */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Edit className="w-5 h-5" />
                          Generated Article
                        </div>
                        <div className="flex gap-2">
                          <Badge variant="outline">
                            {result.result.article.wordCount} words
                          </Badge>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleCopy(result.result!.article.content)}
                          >
                            {copied ? <CheckCircle className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                          </Button>
                        </div>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <h3 className="font-semibold mb-2">Title:</h3>
                        <p className="text-lg">{result.result.article.title}</p>
                      </div>
                      
                      <div>
                        <h3 className="font-semibold mb-2">Meta Description:</h3>
                        <p className="text-sm text-muted-foreground">{result.result.article.metaDescription}</p>
                      </div>
                      
                      <div>
                        <h3 className="font-semibold mb-2">Content:</h3>
                        <div 
                          className="prose max-w-none p-4 bg-muted/50 rounded-lg max-h-96 overflow-y-auto"
                          dangerouslySetInnerHTML={{ __html: result.result.article.content }}
                        />
                      </div>
                      
                      {result.result.article.humanizationApplied.length > 0 && (
                        <div>
                          <h3 className="font-semibold mb-2">Humanization Applied:</h3>
                          <div className="flex flex-wrap gap-1">
                            {result.result.article.humanizationApplied.map((technique, idx) => (
                              <Badge key={idx} variant="secondary">
                                {technique}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>

                  {/* Analytics */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <BarChart3 className="w-5 h-5" />
                        Generation Analytics
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid gap-4 md:grid-cols-4">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-blue-600">
                            {result.result.analytics.researchQueries}
                          </div>
                          <div className="text-sm text-muted-foreground">Research Queries</div>
                        </div>
                        
                        <div className="text-center">
                          <div className="text-2xl font-bold text-green-600">
                            {result.result.analytics.totalSources}
                          </div>
                          <div className="text-sm text-muted-foreground">Sources Analyzed</div>
                        </div>
                        
                        <div className="text-center">
                          <div className="text-2xl font-bold text-purple-600">
                            {Math.round(result.result.analytics.executionTime / 1000)}s
                          </div>
                          <div className="text-sm text-muted-foreground">Execution Time</div>
                        </div>
                        
                        <div className="text-center">
                          <div className="text-2xl font-bold text-orange-600">
                            {result.result.analytics.agentsExecuted.length}
                          </div>
                          <div className="text-sm text-muted-foreground">Agents Used</div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </>
              ) : (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    {result.error || 'Generation failed'}
                  </AlertDescription>
                </Alert>
              )
            ) : (
              <div className="text-center py-12 text-muted-foreground">
                <Sparkles className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>Results will appear here after generation</p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
} 