'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import Link from 'next/link';
import { useSession } from 'next-auth/react';
import { 
  ArrowLeft, 
  Sparkles, 
  Brain, 
  Zap, 
  Target, 
  Search, 
  TrendingUp, 
  Eye, 
  Rocket,
  Plus,
  X,
  Settings,
  Play,
  ChevronRight,
  Shield,
  Crown,
  Lightbulb,
  FileText,
  Users,
  Globe,
  Clock,
  BarChart,
  Monitor,
  Activity
} from 'lucide-react';
import { cn } from '@/lib/utils';
import InvincibleStreamingUI from '@/components/InvincibleStreamingUI';

interface InvincibleConfig {
  topic: string;
  customInstructions?: string;
  targetAudience?: string;
  contentLength?: number;
  tone?: string;
  keywords?: string[];
  searchDepth?: number;
  competitorCount?: number;
  deepSearchQueriesPerTopic?: number;
  uniquenessLevel?: 'standard' | 'high' | 'maximum';
  version?: 'v1';
}

export default function InvinciblePage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { data: session, status } = useSession();
  const [config, setConfig] = useState<InvincibleConfig>({
    topic: '',
    customInstructions: '',
    targetAudience: '',
    contentLength: 2000,
    tone: 'professional',
    keywords: [],
    searchDepth: 7,
    competitorCount: 5,
    deepSearchQueriesPerTopic: 7,
    uniquenessLevel: 'high',
    version: 'v1',
  });
  const [keywordInput, setKeywordInput] = useState('');
  const [isStreaming, setIsStreaming] = useState(false);
  const [showStreamingUI, setShowStreamingUI] = useState(false);
  const [streamingResult, setStreamingResult] = useState<any>(null);
  const [error, setError] = useState('');
  const [isSaving, setIsSaving] = useState(false);

  // Handle URL parameters from Megatron
  useEffect(() => {
    const topic = searchParams.get('topic');
    const customInstructions = searchParams.get('customInstructions');
    const source = searchParams.get('source');
    const videoUrl = searchParams.get('videoUrl');
    const videoTitle = searchParams.get('videoTitle');

    if (source === 'megatron' && topic) {
      setConfig(prevConfig => ({
        ...prevConfig,
        topic: topic,
        customInstructions: customInstructions || prevConfig.customInstructions,
        // Add metadata about the source
        ...(videoUrl && { sourceVideoUrl: videoUrl }),
        ...(videoTitle && { sourceVideoTitle: videoTitle })
      }));
    }
  }, [searchParams]);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (status === 'unauthenticated') {
      window.location.href = '/login'
    }
  }, [status])

  // Loading state
  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin w-12 h-12 border-2 border-violet-400 border-t-transparent rounded-full mx-auto"></div>
          <p className="text-gray-400">Loading...</p>
        </div>
      </div>
    )
  }

  // Don't render if not authenticated
  if (status === 'unauthenticated') {
    return null
  }

  const handleStartStreaming = async () => {
    if (!config.topic.trim()) {
      setError('Please enter a topic');
      return;
    }
    
    setError('');
    setIsStreaming(true);
    
    // For all modes including autonomous, use the streaming UI
    setShowStreamingUI(true);
  };

  const handleStreamingComplete = async (result: any) => {
    setStreamingResult(result);
    setIsStreaming(false);
    setIsSaving(true);

    try {
      console.log('🔍 Raw result structure:', {
        hasResult: !!result.result,
        hasArticle: !!result.article,
        hasContent: !!result.content,
        resultKeys: Object.keys(result || {}),
        resultType: typeof result.result,
        resultContent: result.result ? Object.keys(result.result) : 'N/A',
      });

      // Extract article data with comprehensive fallback logic
      let articleData = null;
      let articleTitle = '';
      let articleContent = '';

      // Handle different result structures
      if (result.result) {
        // result.result path
        articleData = result.result;
        articleTitle = result.result.title || '';
        articleContent = result.result.content || '';
        console.log('📄 Using result.result path');
      } else if (result.article) {
        // Alternative: result.article path
        articleData = result.article;
        articleTitle = result.article.title || '';
        articleContent = result.article.content || '';
        console.log('📄 Using result.article path');
      } else if (result.content) {
        // Direct content path
        articleContent = result.content;
        articleTitle = result.title || `Article about ${config.topic}`;
        console.log('📄 Using result.content path');
      } else {
        // Last resort: use result directly
        articleData = result;
        articleTitle = result.title || `Article about ${config.topic}`;
        articleContent = result.content || '';
        console.log('📄 Using result direct path');
      }

      console.log('📊 Extracted article data:', {
        hasArticleData: !!articleData,
        title: articleTitle,
        titleLength: articleTitle?.length || 0,
        contentLength: articleContent?.length || 0,
        contentPreview: articleContent?.substring(0, 200) || 'EMPTY',
        contentType: typeof articleContent
      });

      // Validate title
      if (!articleTitle || articleTitle.trim().length === 0) {
        articleTitle = `Complete Guide to ${config.topic}`;
        console.log('⚠️ Using fallback title:', articleTitle);
      }

      // Validate content with detailed error reporting
      if (!articleContent || articleContent.trim().length === 0) {
        console.error('❌ Content validation failed:', {
          contentExists: !!articleContent,
          contentType: typeof articleContent,
          contentLength: articleContent?.length || 0,
          rawContent: articleContent,
          resultStructure: JSON.stringify(result, null, 2).substring(0, 1000),
          configVersion: config.version,
          extractionPath: 'fallback paths'
        });

        // Error message for content generation failure
        const errorMessage = `Content generation failed - no content was generated. This might be due to API issues or content filtering. Please try again or contact support if the issue persists.`;

        throw new Error(errorMessage);
      }

      // Final validation
      if (articleContent.trim().length < 50) {
        throw new Error(`Generated content is too short (${articleContent.length} characters). Please try again.`);
      }

      // Save the article to the database
      const response = await fetch('/api/articles/store', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: articleTitle,
          content: articleContent,
          type: 'invincible',
          metadata: {
            topic: config.topic,
            tone: config.tone,
            targetAudience: config.targetAudience,
            contentLength: config.contentLength,
            customInstructions: config.customInstructions,
            keywords: config.keywords,
            executionTime: result.executionTime || result.stats?.executionTime,
            totalSources: result.insights?.totalSources || result.stats?.totalSources,
            uniquenessScore: result.stats?.uniquenessScore,
            seoScore: articleData?.seoScore || result.article?.seoScore,
            readabilityScore: articleData?.readabilityScore || result.article?.readabilityScore,
            qualityScore: result.qualityScore || result.insights?.qualityScore,
            competitorsAnalyzed: result.insights?.competitorsAnalyzed,
            iterationsCompleted: result.insights?.iterationsCompleted,
            factCheckReport: result.factCheckReport,
            generatedAt: new Date().toISOString()
          },
          tone: config.tone,
          language: 'en'
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to save article: ${errorText}`);
      }

      const saveResult = await response.json();
      
      if (saveResult.success) {
        // Small delay to show saving completion
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Redirect to the article view page with the saved article ID
        router.push(`/article-view/${saveResult.article.id}`);
      } else {
        throw new Error(saveResult.error || 'Failed to save article');
      }
    } catch (error) {
      console.error('Error saving article:', error);
      setIsSaving(false);
      setError(`Article generated successfully but failed to save: ${error instanceof Error ? error.message : 'Unknown error'}. Please try again.`);
    }
  };

  const handleStreamingError = (error: string) => {
    setError(error);
    setIsStreaming(false);
  };

  const addKeyword = () => {
    if (keywordInput.trim() && !config.keywords?.includes(keywordInput.trim())) {
      setConfig({
        ...config,
        keywords: [...(config.keywords || []), keywordInput.trim()]
      });
      setKeywordInput('');
    }
  };

  const removeKeyword = (keyword: string) => {
    setConfig({
      ...config,
      keywords: config.keywords?.filter(k => k !== keyword) || []
    });
  };

  const resetToConfiguration = () => {
    setShowStreamingUI(false);
    setIsStreaming(false);
    setStreamingResult(null);
    setError('');
    setIsSaving(false);
  };

  const viewGeneratedArticle = () => {
    router.push('/article-view');
  };

  // If showing streaming UI, render the streaming component
  if (showStreamingUI) {
    return (
      <InvincibleStreamingUI
        topic={config.topic}
        contentLength={config.contentLength}
        tone={config.tone}
        targetAudience={config.targetAudience}
        customInstructions={config.customInstructions}
        onComplete={handleStreamingComplete}
        onError={handleStreamingError}
        isSaving={isSaving}
      />
    );
  }

  return (
    <div className="min-h-screen bg-black">
      {/* Animated Background */}
      <div className="fixed inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-violet-900/10 via-black to-indigo-900/10" />
        
        {/* Animated orbs */}
        <motion.div
          animate={{
            x: [0, 100, 0],
            y: [0, -100, 0],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute top-1/4 left-1/4 w-[500px] h-[500px] bg-violet-500/10 rounded-full blur-[100px]"
        />
        <motion.div
          animate={{
            x: [0, -100, 0],
            y: [0, 100, 0],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute bottom-1/4 right-1/4 w-[600px] h-[600px] bg-indigo-500/10 rounded-full blur-[120px]"
        />
      </div>

      {/* Header */}
      <motion.header 
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="relative z-10 border-b border-white/10 backdrop-blur-xl bg-black/40"
      >
        <div className="max-w-7xl mx-auto px-6 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-6">
              <Link 
                href="/dashboard" 
                className="flex items-center space-x-3 text-gray-400 hover:text-white transition-colors group"
              >
                <ArrowLeft className="w-5 h-5 group-hover:-translate-x-1 transition-transform" />
                <span className="font-medium">Back to Dashboard</span>
              </Link>
              
              <div className="h-6 w-px bg-white/20" />
              
              <div className="flex items-center space-x-3">
                <div className="relative">
                  <div className="absolute inset-0 rounded-xl blur-lg opacity-70 bg-gradient-to-r from-violet-600 to-indigo-600" />
                  <div className="relative bg-black rounded-xl p-2.5 border border-white/20">
                      <Crown className="w-6 h-6 text-white" />
                  </div>
                </div>
                <div>
                  <h1 className="text-xl font-bold text-white">
                    Invincible
                  </h1>
                  <p className="text-sm text-gray-400">
                    Advanced AI Content Generation
                  </p>
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <div className="hidden md:flex items-center space-x-6 text-sm text-gray-400">
                <div className="flex items-center space-x-2">
                  <Shield className="w-4 h-4 text-emerald-400" />
                  <span>AI Detection Bypass</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Monitor className="w-4 h-4 text-blue-400" />
                  <span>Live Streaming</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Zap className="w-4 h-4 text-yellow-400" />
                  <span>Real-time Analysis</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </motion.header>

      {/* Main Content */}
      <main className="relative z-10 max-w-6xl mx-auto px-6 py-12">
        {/* Hero Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center space-x-2 px-4 py-2 rounded-full bg-white/10 backdrop-blur-md border border-white/20 mb-8">
            <Activity className="w-4 h-4 text-green-400" />
            <span className="text-sm text-gray-200">Live Streaming Generation</span>
            <Sparkles className="w-4 h-4 text-violet-400" />
            <span className="text-sm text-gray-200">AI Detection Bypass</span>
            <Crown className="w-4 h-4 text-yellow-400" />
          </div>
          
          <h2 className="text-5xl md:text-6xl font-bold text-white mb-6 leading-tight">
            Watch Your Content
            <span className="block bg-gradient-to-r from-violet-400 via-purple-400 to-indigo-400 bg-clip-text text-transparent">
              Come to Life
            </span>
          </h2>
          
          <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8 leading-relaxed">
            Experience the most advanced content generation with real-time streaming. Watch as our AI analyzes competitors, 
            processes research, applies humanization techniques, and creates superior articles that bypass AI detection.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto mt-12">
            <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6">
              <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center mb-4 mx-auto">
                <Activity className="w-6 h-6 text-white" />
              </div>
              <h3 className="font-semibold text-white mb-2">
                Live Streaming
              </h3>
              <p className="text-sm text-gray-400">
                Watch queries, analysis, and generation in real-time
              </p>
            </div>
            
            <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center mb-4 mx-auto">
                <Shield className="w-6 h-6 text-white" />
              </div>
              <h3 className="font-semibold text-white mb-2">
                AI Detection Bypass
              </h3>
              <p className="text-sm text-gray-400">
                Advanced humanization with date variation and jargon removal
              </p>
            </div>
            
            <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6">
              <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center mb-4 mx-auto">
                <Crown className="w-6 h-6 text-white" />
              </div>
              <h3 className="font-semibold text-white mb-2">
                Superior Quality
              </h3>
              <p className="text-sm text-gray-400">
                Content that surpasses all competitors with comprehensive research
              </p>
            </div>
          </div>
        </motion.div>

        {/* Main Configuration Form */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Configuration Form */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.1 }}
            className="lg:col-span-2"
          >
            <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-8">
              <div className="flex items-center space-x-3 mb-8">
                <div className="p-2 bg-gradient-to-r from-violet-600 to-indigo-600 rounded-lg">
                  <Settings className="w-5 h-5 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-white">Configuration</h3>
              </div>

              <div className="space-y-8">
                {/* Version Selector */}
                <div className="space-y-4">
                  <label className="block text-sm font-medium text-white/90">
                    Invincible Version
                  </label>
                  <div className="grid grid-cols-2 gap-4">
                    <button
                      type="button"
                      onClick={() => setConfig({ ...config, version: 'v1' })}
                      className={cn(
                        "px-6 py-4 rounded-xl border transition-all flex items-center space-x-3",
                        config.version === 'v1'
                          ? "bg-green-600/20 border-green-500/50 text-green-300"
                          : "bg-white/5 border-white/10 text-gray-300 hover:bg-white/10 hover:border-white/20"
                      )}
                    >
                      <Activity className="w-5 h-5" />
                      <div className="text-left">
                        <div className="font-semibold">V.1 Classic</div>
                        <div className="text-xs opacity-80">Original streaming system</div>
                      </div>
                    </button>
                    <button
                      type="button"
                      onClick={() => setConfig({ ...config, version: 'v2' })}
                      className={cn(
                        "px-6 py-4 rounded-xl border transition-all flex items-center space-x-3 relative",
                        config.version === 'v2'
                          ? "bg-violet-600/20 border-violet-500/50 text-violet-300"
                          : "bg-white/5 border-white/10 text-gray-300 hover:bg-white/10 hover:border-white/20"
                      )}
                    >
                      <Crown className="w-5 h-5" />
                      <div className="text-left">
                        <div className="font-semibold flex items-center space-x-2">
                          <span>V.2 Autonomous</span>
                          <span className="px-2 py-1 bg-violet-600/30 text-xs rounded-full">BETA</span>
                        </div>
                        <div className="text-xs opacity-80">Kimi K2 + LangGraph + Tavily</div>
                      </div>
                    </button>
                  </div>
                </div>

                {/* Topic */}
                <div className="space-y-4">
                  <label className="block text-sm font-medium text-white/90">
                    Topic <span className="text-violet-400">*</span>
                  </label>
                  <input
                    type="text"
                    value={config.topic}
                    onChange={(e) => setConfig({ ...config, topic: e.target.value })}
                    className="w-full px-4 py-4 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:bg-white/10 focus:border-violet-500/50 transition-all"
                    placeholder="e.g., The 5 Best CLI Agents of 2025"
                  />
                </div>


                {/* Target Audience & Content Length */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <label className="block text-sm font-medium text-white/90">
                      Target Audience
                    </label>
                    <input
                      type="text"
                      value={config.targetAudience}
                      onChange={(e) => setConfig({ ...config, targetAudience: e.target.value })}
                      className="w-full px-4 py-4 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:bg-white/10 focus:border-violet-500/50 transition-all"
                      placeholder="e.g., Developers and tech enthusiasts"
                    />
                  </div>

                  <div className="space-y-4">
                    <label className="block text-sm font-medium text-white/90">
                      Content Length (words)
                    </label>
                    <input
                      type="number"
                      value={config.contentLength}
                      onChange={(e) => setConfig({ ...config, contentLength: parseInt(e.target.value) || 2000 })}
                      className="w-full px-4 py-4 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:bg-white/10 focus:border-violet-500/50 transition-all"
                      min="500"
                      max="5000"
                    />
                  </div>
                </div>

                {/* Custom Instructions */}
                <div className="space-y-4">
                  <label className="block text-sm font-medium text-white/90">
                    Custom Instructions
                  </label>
                  <textarea
                    value={config.customInstructions}
                    onChange={(e) => setConfig({ ...config, customInstructions: e.target.value })}
                    className="w-full px-4 py-4 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:bg-white/10 focus:border-violet-500/50 transition-all resize-none"
                    rows={4}
                    placeholder="e.g., Focus on practical value and real-world usage. Include specific examples and avoid generic advice."
                  />
                </div>

                {/* Tone Selection */}
                <div className="space-y-4">
                  <label className="block text-sm font-medium text-white/90">
                    Writing Tone
                  </label>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    {['professional', 'conversational', 'casual', 'authoritative', 'friendly', 'technical'].map((tone) => (
                      <button
                        key={tone}
                        type="button"
                        onClick={() => setConfig({ ...config, tone })}
                        className={cn(
                          "px-4 py-3 rounded-xl border transition-all capitalize",
                          config.tone === tone
                            ? "bg-violet-600/20 border-violet-500/50 text-violet-300"
                            : "bg-white/5 border-white/10 text-gray-300 hover:bg-white/10 hover:border-white/20"
                        )}
                      >
                        {tone}
                      </button>
                    ))}
                  </div>
                </div>



                {/* Error Display */}
                  {error && (
                    <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4"
                    >
                    <p className="text-red-700 dark:text-red-300 text-sm">{error}</p>
                    </motion.div>
                  )}

                {/* Start Generation Button */}
                <motion.button
                  onClick={handleStartStreaming}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="w-full py-4 bg-gradient-to-r from-violet-600 via-purple-600 to-indigo-600 text-white rounded-xl font-semibold text-lg hover:shadow-2xl transition-all duration-300 flex items-center justify-center space-x-3"
                >
                  <Activity className="w-6 h-6" />
                  <span>Start Live Generation</span>
                  <Rocket className="w-6 h-6" />
                </motion.button>
              </div>
            </div>
          </motion.div>

          {/* Live Preview Panel */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
            className="space-y-6"
          >
            {/* Features */}
            <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
              <div className="flex items-center space-x-3 mb-6">
                <div className="p-2 bg-gradient-to-r from-green-600 to-emerald-600 rounded-lg">
                  <Sparkles className="w-5 h-5 text-white" />
                </div>
                <h3 className="text-xl font-bold text-white">What You'll See</h3>
              </div>

              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                  <div>
                    <p className="text-white font-medium">Live Search Queries</p>
                    <p className="text-gray-400 text-sm">Watch as AI generates and executes targeted research queries</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                  <div>
                    <p className="text-white font-medium">Real-time Analysis</p>
                    <p className="text-gray-400 text-sm">See competitor analysis and content understanding in action</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-purple-400 rounded-full mt-2 flex-shrink-0"></div>
                  <div>
                    <p className="text-white font-medium">AI Humanization</p>
                    <p className="text-gray-400 text-sm">Watch date variation, jargon removal, and bypass techniques</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-orange-400 rounded-full mt-2 flex-shrink-0"></div>
                  <div>
                    <p className="text-white font-medium">Content Generation</p>
                    <p className="text-gray-400 text-sm">See superior content creation with full context</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Stats Preview */}
            <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
              <div className="flex items-center space-x-3 mb-6">
                <div className="p-2 bg-gradient-to-r from-blue-600 to-cyan-600 rounded-lg">
                  <BarChart className="w-5 h-5 text-white" />
                </div>
                <h3 className="text-xl font-bold text-white">Generation Stats</h3>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-400">10+</div>
                  <div className="text-xs text-gray-400">Search Queries</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-400">15+</div>
                  <div className="text-xs text-gray-400">Sources Analyzed</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-400">4</div>
                  <div className="text-xs text-gray-400">Analysis Phases</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-400">8+</div>
                  <div className="text-xs text-gray-400">AI Bypass Techniques</div>
                </div>
              </div>
            </div>

            {/* Enhanced Features */}
            <div className="bg-gradient-to-br from-violet-900/20 to-indigo-900/20 border border-violet-500/20 rounded-2xl p-6">
              <div className="flex items-center space-x-3 mb-4">
                <Crown className="w-6 h-6 text-yellow-400" />
                <h3 className="text-lg font-bold text-white">Enhanced Features</h3>
              </div>

              <div className="space-y-3 text-sm">
                <div className="flex items-center space-x-2">
                  <Shield className="w-4 h-4 text-emerald-400" />
                  <span className="text-gray-300">Advanced AI Detection Bypass</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Brain className="w-4 h-4 text-purple-400" />
                  <span className="text-gray-300">Human Writing Pattern Analysis</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Search className="w-4 h-4 text-blue-400" />
                  <span className="text-gray-300">Comprehensive Research</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Target className="w-4 h-4 text-red-400" />
                  <span className="text-gray-300">Superior Competition Analysis</span>
                  </div>
              </div>
            </div>
          </motion.div>
        </div>
      </main>
    </div>
  );
} 