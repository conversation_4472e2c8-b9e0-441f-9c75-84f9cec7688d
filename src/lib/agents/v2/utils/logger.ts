/**
 * Invincible V.2 - Advanced Logging System
 * Structured logging with levels, context, and performance tracking
 */

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  CRITICAL = 4
}

export interface LogContext {
  sessionId?: string;
  agentName?: string;
  userId?: string;
  requestId?: string;
  workflowId?: string;
  phase?: string;
  action?: string;
  timestamp?: number;
  duration?: number;
  metadata?: Record<string, any>;
}

export interface LogEntry {
  level: LogLevel;
  message: string;
  context: LogContext;
  timestamp: number;
  error?: Error;
  stack?: string;
}

export class V2Logger {
  private static instance: V2Logger;
  private logLevel: LogLevel;
  private logs: LogEntry[] = [];
  private maxLogs = 1000;
  private sessionId: string;

  private constructor() {
    this.logLevel = process.env.NODE_ENV === 'development' ? LogLevel.DEBUG : LogLevel.INFO;
    this.sessionId = `v2_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  static getInstance(): V2Logger {
    if (!V2Logger.instance) {
      V2Logger.instance = new V2Logger();
    }
    return V2Logger.instance;
  }

  /**
   * Set the minimum log level
   */
  setLogLevel(level: LogLevel): void {
    this.logLevel = level;
  }

  /**
   * Log debug information
   */
  debug(message: string, context: LogContext = {}): void {
    this.log(LogLevel.DEBUG, message, context);
  }

  /**
   * Log informational messages
   */
  info(message: string, context: LogContext = {}): void {
    this.log(LogLevel.INFO, message, context);
  }

  /**
   * Log warnings
   */
  warn(message: string, context: LogContext = {}): void {
    this.log(LogLevel.WARN, message, context);
  }

  /**
   * Log errors
   */
  error(message: string, context: LogContext = {}, error?: Error): void {
    this.log(LogLevel.ERROR, message, context, error);
  }

  /**
   * Log critical errors
   */
  critical(message: string, context: LogContext = {}, error?: Error): void {
    this.log(LogLevel.CRITICAL, message, context, error);
  }

  /**
   * Log agent actions with structured context
   */
  agentAction(
    agentName: string,
    action: string,
    status: 'started' | 'completed' | 'failed',
    context: LogContext = {}
  ): void {
    const level = status === 'failed' ? LogLevel.ERROR : LogLevel.INFO;
    const message = `${agentName.toUpperCase()}: ${action} ${status}`;
    
    this.log(level, message, {
      ...context,
      agentName,
      action,
      status,
      sessionId: this.sessionId,
    });
  }

  /**
   * Log workflow progress
   */
  workflowProgress(
    phase: string,
    progress: number,
    context: LogContext = {}
  ): void {
    this.log(LogLevel.INFO, `Workflow progress: ${phase} (${progress}%)`, {
      ...context,
      phase,
      progress,
      sessionId: this.sessionId,
    });
  }

  /**
   * Log performance metrics
   */
  performance(
    operation: string,
    duration: number,
    context: LogContext = {}
  ): void {
    const level = duration > 5000 ? LogLevel.WARN : LogLevel.INFO;
    const message = `Performance: ${operation} completed in ${duration}ms`;
    
    this.log(level, message, {
      ...context,
      operation,
      duration,
      sessionId: this.sessionId,
    });
  }

  /**
   * Log API calls with details
   */
  apiCall(
    service: string,
    endpoint: string,
    method: string,
    status: number,
    duration: number,
    context: LogContext = {}
  ): void {
    const level = status >= 400 ? LogLevel.ERROR : LogLevel.INFO;
    const message = `API Call: ${method} ${service}/${endpoint} - ${status} (${duration}ms)`;
    
    this.log(level, message, {
      ...context,
      service,
      endpoint,
      method,
      status,
      duration,
      sessionId: this.sessionId,
    });
  }

  /**
   * Log search operations
   */
  searchOperation(
    query: string,
    resultsCount: number,
    duration: number,
    context: LogContext = {}
  ): void {
    this.log(LogLevel.INFO, `Search: "${query}" returned ${resultsCount} results in ${duration}ms`, {
      ...context,
      query,
      resultsCount,
      duration,
      operation: 'search',
      sessionId: this.sessionId,
    });
  }

  /**
   * Log content generation
   */
  contentGeneration(
    type: string,
    wordCount: number,
    qualityScore: number,
    duration: number,
    context: LogContext = {}
  ): void {
    this.log(LogLevel.INFO, `Content Generated: ${type} (${wordCount} words, quality: ${qualityScore}) in ${duration}ms`, {
      ...context,
      contentType: type,
      wordCount,
      qualityScore,
      duration,
      operation: 'content_generation',
      sessionId: this.sessionId,
    });
  }

  /**
   * Core logging method
   */
  private log(
    level: LogLevel,
    message: string,
    context: LogContext = {},
    error?: Error
  ): void {
    if (level < this.logLevel) {
      return;
    }

    const logEntry: LogEntry = {
      level,
      message,
      context: {
        ...context,
        sessionId: context.sessionId || this.sessionId,
        timestamp: Date.now(),
      },
      timestamp: Date.now(),
      error,
      stack: error?.stack,
    };

    // Add to internal log buffer
    this.logs.push(logEntry);
    if (this.logs.length > this.maxLogs) {
      this.logs.shift();
    }

    // Console output with formatting
    this.outputToConsole(logEntry);

    // In production, you could also send to external logging service
    if (process.env.NODE_ENV === 'production') {
      this.sendToExternalLogger(logEntry);
    }
  }

  /**
   * Format and output to console (minimalistic)
   */
  private outputToConsole(entry: LogEntry): void {
    const time = new Date(entry.timestamp).toLocaleTimeString();
    const level = ['🔍', '✅', '⚠️', '❌', '🚨'][entry.level];
    
    // Build minimal context
    const parts = [];
    if (entry.context.agentName) parts.push(entry.context.agentName);
    if (entry.context.action) parts.push(entry.context.action);
    if (entry.context.duration) parts.push(`${entry.context.duration}ms`);
    
    const context = parts.length > 0 ? `[${parts.join('|')}]` : '';
    
    // Single line output
    console.log(`${level} ${time} ${context} ${entry.message}`);
    
    // Only show metadata for errors or if it contains important info
    if (entry.level >= LogLevel.ERROR && entry.error) {
      console.log(`   ↳ ${entry.error.message}`);
    }
  }

  /**
   * Send to external logging service (placeholder)
   */
  private sendToExternalLogger(entry: LogEntry): void {
    // In production, implement sending to services like:
    // - DataDog
    // - New Relic
    // - CloudWatch
    // - Sentry
    // - Custom logging endpoint
    
    // Example structure for external logging:
    // await fetch('/api/logs', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(entry)
    // });
  }

  /**
   * Get recent logs
   */
  getRecentLogs(count: number = 50): LogEntry[] {
    return this.logs.slice(-count);
  }

  /**
   * Get logs by level
   */
  getLogsByLevel(level: LogLevel): LogEntry[] {
    return this.logs.filter(log => log.level === level);
  }

  /**
   * Get logs by agent
   */
  getLogsByAgent(agentName: string): LogEntry[] {
    return this.logs.filter(log => log.context.agentName === agentName);
  }

  /**
   * Clear logs
   */
  clearLogs(): void {
    this.logs = [];
  }

  /**
   * Get session statistics
   */
  getSessionStats(): {
    totalLogs: number;
    logsByLevel: Record<string, number>;
    avgDuration: number;
    errors: number;
    warnings: number;
  } {
    const logsByLevel = this.logs.reduce((acc, log) => {
      const levelName = LogLevel[log.level];
      acc[levelName] = (acc[levelName] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const durationsMs = this.logs
      .filter(log => log.context.duration)
      .map(log => log.context.duration!);
    
    const avgDuration = durationsMs.length > 0 
      ? durationsMs.reduce((sum, d) => sum + d, 0) / durationsMs.length 
      : 0;

    return {
      totalLogs: this.logs.length,
      logsByLevel,
      avgDuration: Math.round(avgDuration),
      errors: this.logs.filter(log => log.level >= LogLevel.ERROR).length,
      warnings: this.logs.filter(log => log.level === LogLevel.WARN).length,
    };
  }
}

/**
 * Singleton logger instance
 */
export const logger = V2Logger.getInstance();

/**
 * Performance tracking decorator
 */
export function logPerformance(operation: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;
    
    descriptor.value = async function (...args: any[]) {
      const startTime = Date.now();
      const context = { 
        agentName: target.constructor.name,
        operation,
        method: propertyName 
      };
      
      try {
        logger.debug(`Starting ${operation}`, context);
        const result = await method.apply(this, args);
        const duration = Date.now() - startTime;
        
        logger.performance(operation, duration, context);
        return result;
      } catch (error) {
        const duration = Date.now() - startTime;
        logger.error(`${operation} failed after ${duration}ms`, context, error as Error);
        throw error;
      }
    };
    
    return descriptor;
  };
}

/**
 * Helper function to create a child logger with context
 */
export function createLogger(context: LogContext): {
  debug: (message: string, additionalContext?: LogContext) => void;
  info: (message: string, additionalContext?: LogContext) => void;
  warn: (message: string, additionalContext?: LogContext) => void;
  error: (message: string, additionalContext?: LogContext, error?: Error) => void;
} {
  return {
    debug: (message: string, additionalContext: LogContext = {}) => 
      logger.debug(message, { ...context, ...additionalContext }),
    info: (message: string, additionalContext: LogContext = {}) => 
      logger.info(message, { ...context, ...additionalContext }),
    warn: (message: string, additionalContext: LogContext = {}) => 
      logger.warn(message, { ...context, ...additionalContext }),
    error: (message: string, additionalContext: LogContext = {}, error?: Error) => 
      logger.error(message, { ...context, ...additionalContext }, error),
  };
}