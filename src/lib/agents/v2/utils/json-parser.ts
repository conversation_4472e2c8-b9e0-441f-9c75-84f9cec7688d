/**
 * Utility to safely parse JSON responses from AI models
 * Handles markdown-wrapped JSON and other common response formats
 */

export function safeJsonParse<T = any>(content: string, fallback?: T): T | null {
  if (!content || typeof content !== 'string') {
    return fallback || null;
  }

  // Remove markdown code blocks if present
  let cleanedContent = content.trim();
  
  // Remove ```json and ``` wrappers
  if (cleanedContent.startsWith('```json')) {
    cleanedContent = cleanedContent.replace(/^```json\s*/, '').replace(/\s*```$/, '');
  } else if (cleanedContent.startsWith('```')) {
    cleanedContent = cleanedContent.replace(/^```\s*/, '').replace(/\s*```$/, '');
  }
  
  // Remove any leading/trailing whitespace
  cleanedContent = cleanedContent.trim();
  
  try {
    return JSON.parse(cleanedContent);
  } catch (error) {
    console.warn('JSON parsing failed:', error);
    console.warn('Original content:', content);
    console.warn('Cleaned content:', cleanedContent);
    
    // Try to extract <PERSON><PERSON><PERSON> from the content using regex
    const jsonMatch = cleanedContent.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      try {
        return JSON.parse(jsonMatch[0]);
      } catch (regexError) {
        console.warn('Regex JSON extraction also failed:', regexError);
      }
    }
    
    return fallback || null;
  }
}

/**
 * Parse JSON response with specific fallback structure
 */
export function parseJsonWithFallback<T>(content: string, fallback: T): T {
  const result = safeJsonParse<T>(content);
  return result || fallback;
} 