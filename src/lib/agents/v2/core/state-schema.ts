/**
 * Invincible V.2 - Enhanced State Schema for Multi-Agent System
 * Simplified structure with better type safety and error handling
 */

export interface ResearchData {
  query: string;
  results: Array<{
    title: string;
    url: string;
    content: string;
    score?: number;
    publishedDate?: string;
  }>;
  metadata: {
    searchDepth: 'basic' | 'advanced';
    totalResults: number;
    apiKeyUsed?: string;
    responseTime: number;
  };
  timestamp: number;
}

export interface ContentRequirements {
  topic: string;
  contentType: 'article' | 'blog' | 'guide' | 'review' | 'listicle' | 'how-to';
  contentLength: number;
  tone: string;
  targetAudience: string;
  customInstructions?: string;
}

export interface SEOData {
  primaryKeywords: string[];
  secondaryKeywords: string[];
  searchIntent: 'informational' | 'commercial' | 'navigational' | 'transactional';
  keywordDensity: number;
  competitorKeywords: string[];
}

export interface GEOData {
  aiSearchOptimized: boolean;
  citationReady: boolean;
  voiceSearchOptimized: boolean;
  structuredData: Record<string, any>;
  aiPlatformScores: {
    perplexity: number;
    chatgpt: number;
    gemini: number;
    claude: number;
  };
}

export interface ContentGeneration {
  title: string;
  metaDescription: string;
  content: string;
  outline: Array<{
    heading: string;
    level: number;
    content?: string;
    wordCount?: number;
  }>;
  wordCount: number;
  readabilityScore: number;
  humanizationApplied: string[];
}

export interface QualityMetrics {
  contentScore: number;
  seoScore: number;
  geoScore: number;
  humanScore: number;
  originalityScore: number;
  factualAccuracy: number;
  engagementScore: number;
  aiDetectionProbability: number;
}

export interface AgentMessage {
  agent: string;
  action: string;
  data: any;
  status: 'in_progress' | 'completed' | 'error' | 'warning';
  timestamp: number;
  duration?: number;
}

export interface CompetitiveAnalysis {
  topCompetitors: Array<{
    url: string;
    title: string;
    wordCount: number;
    strengths: string[];
    weaknesses: string[];
  }>;
  contentGaps: string[];
  opportunities: string[];
  averageWordCount: number;
  commonKeywords: string[];
}

/**
 * Simplified Main State Interface
 */
export interface InvincibleV2State {
  // Configuration
  sessionId: string;
  requirements: ContentRequirements;
  
  // Research Data
  research: {
    data: ResearchData[];
    competitiveAnalysis: CompetitiveAnalysis;
    completed: boolean;
  };
  
  // SEO & GEO Analysis
  optimization: {
    seo: SEOData;
    geo: GEOData;
    completed: boolean;
  };
  
  // Generated Content
  content: {
    generation: ContentGeneration;
    quality: QualityMetrics;
    iterations: number;
    completed: boolean;
  };
  
  // Workflow State
  workflow: {
    currentAgent: string;
    completedAgents: string[];
    progress: number;
    status: 'idle' | 'running' | 'completed' | 'error';
    errors: Array<{
      agent: string;
      error: string;
      timestamp: number;
      recoverable: boolean;
    }>;
  };
  
  // Agent Communication
  messages: AgentMessage[];
  
  // Final Result
  result: {
    success: boolean;
    article?: {
      title: string;
      content: string;
      metaDescription: string;
      wordCount: number;
      seoKeywords: string[];
    };
    performance?: {
      totalTime: number;
      qualityScore: number;
      seoScore: number;
      geoScore: number;
    };
    error?: string;
  } | null;
  
  // Timing
  startTime: number;
  endTime?: number;
}

/**
 * Enhanced State Manager with Better Error Handling
 */
export class StateManager {
  static createInitialState(requirements: ContentRequirements): InvincibleV2State {
    return {
      sessionId: `v2_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      requirements,
      research: {
        data: [],
        competitiveAnalysis: {
          topCompetitors: [],
          contentGaps: [],
          opportunities: [],
          averageWordCount: 0,
          commonKeywords: []
        },
        completed: false
      },
      optimization: {
        seo: {
          primaryKeywords: [],
          secondaryKeywords: [],
          searchIntent: 'informational',
          keywordDensity: 0,
          competitorKeywords: []
        },
        geo: {
          aiSearchOptimized: false,
          citationReady: false,
          voiceSearchOptimized: false,
          structuredData: {},
          aiPlatformScores: {
            perplexity: 0,
            chatgpt: 0,
            gemini: 0,
            claude: 0
          }
        },
        completed: false
      },
      content: {
        generation: {
          title: '',
          metaDescription: '',
          content: '',
          outline: [],
          wordCount: 0,
          readabilityScore: 0,
          humanizationApplied: []
        },
        quality: {
          contentScore: 0,
          seoScore: 0,
          geoScore: 0,
          humanScore: 0,
          originalityScore: 0,
          factualAccuracy: 0,
          engagementScore: 0,
          aiDetectionProbability: 100
        },
        iterations: 0,
        completed: false
      },
      workflow: {
        currentAgent: 'supervisor',
        completedAgents: [],
        progress: 0,
        status: 'idle',
        errors: []
      },
      messages: [],
      result: null,
      startTime: Date.now()
    };
  }
  
  static updateResearch(state: InvincibleV2State, data: ResearchData): InvincibleV2State {
    return {
      ...state,
      research: {
        ...state.research,
        data: [...state.research.data, data]
      }
    };
  }
  
  static updateCompetitiveAnalysis(state: InvincibleV2State, analysis: Partial<CompetitiveAnalysis>): InvincibleV2State {
    return {
      ...state,
      research: {
        ...state.research,
        competitiveAnalysis: {
          ...state.research.competitiveAnalysis,
          ...analysis
        }
      }
    };
  }
  
  static updateSEO(state: InvincibleV2State, seo: Partial<SEOData>): InvincibleV2State {
    return {
      ...state,
      optimization: {
        ...state.optimization,
        seo: {
          ...state.optimization.seo,
          ...seo
        }
      }
    };
  }
  
  static updateGEO(state: InvincibleV2State, geo: Partial<GEOData>): InvincibleV2State {
    return {
      ...state,
      optimization: {
        ...state.optimization,
        geo: {
          ...state.optimization.geo,
          ...geo
        }
      }
    };
  }
  
  static updateContent(state: InvincibleV2State, content: Partial<ContentGeneration>): InvincibleV2State {
    return {
      ...state,
      content: {
        ...state.content,
        generation: {
          ...state.content.generation,
          ...content
        }
      }
    };
  }
  
  static updateQuality(state: InvincibleV2State, quality: Partial<QualityMetrics>): InvincibleV2State {
    return {
      ...state,
      content: {
        ...state.content,
        quality: {
          ...state.content.quality,
          ...quality
        }
      }
    };
  }
  
  static updateWorkflow(state: InvincibleV2State, updates: {
    currentAgent?: string;
    progress?: number;
    status?: 'idle' | 'running' | 'completed' | 'error';
    completedAgent?: string;
  }): InvincibleV2State {
    const newState = { ...state };
    
    if (updates.currentAgent) {
      newState.workflow.currentAgent = updates.currentAgent;
    }
    
    if (updates.progress !== undefined) {
      newState.workflow.progress = updates.progress;
    }
    
    if (updates.status) {
      newState.workflow.status = updates.status;
    }
    
    if (updates.completedAgent) {
      newState.workflow.completedAgents = [
        ...newState.workflow.completedAgents,
        updates.completedAgent
      ];
    }
    
    return newState;
  }
  
  static addError(state: InvincibleV2State, error: {
    agent: string;
    error: string;
    recoverable?: boolean;
  }): InvincibleV2State {
    return {
      ...state,
      workflow: {
        ...state.workflow,
        errors: [
          ...state.workflow.errors,
          {
            ...error,
            timestamp: Date.now(),
            recoverable: error.recoverable ?? false
          }
        ]
      }
    };
  }
  
  static addMessage(state: InvincibleV2State, message: Omit<AgentMessage, 'timestamp'>): InvincibleV2State {
    const startTime = state.messages
      .filter(m => m.agent === message.agent && m.action === message.action && m.status === 'in_progress')
      .pop()?.timestamp;
    
    return {
      ...state,
      messages: [
        ...state.messages,
        {
          ...message,
          timestamp: Date.now(),
          duration: startTime ? Date.now() - startTime : undefined
        }
      ]
    };
  }
  
  static markPhaseComplete(state: InvincibleV2State, phase: 'research' | 'optimization' | 'content'): InvincibleV2State {
    const newState = { ...state };
    
    switch (phase) {
      case 'research':
        newState.research.completed = true;
        break;
      case 'optimization':
        newState.optimization.completed = true;
        break;
      case 'content':
        newState.content.completed = true;
        break;
    }
    
    return newState;
  }
  
  static setResult(state: InvincibleV2State, result: InvincibleV2State['result']): InvincibleV2State {
    return {
      ...state,
      result,
      endTime: Date.now(),
      workflow: {
        ...state.workflow,
        status: result?.success ? 'completed' : 'error'
      }
    };
  }
}

// Export helper functions
export function calculateProgress(state: InvincibleV2State): number {
  const weights = {
    research: 25,
    optimization: 25,
    content: 40,
    quality: 10
  };
  
  let progress = 0;
  
  if (state.research.completed) progress += weights.research;
  if (state.optimization.completed) progress += weights.optimization;
  if (state.content.completed) progress += weights.content;
  if (state.content.quality.contentScore > 80) progress += weights.quality;
  
  return Math.min(progress, 100);
}

export function getAgentStatus(state: InvincibleV2State, agentName: string): 'pending' | 'running' | 'completed' | 'error' {
  if (state.workflow.completedAgents.includes(agentName)) return 'completed';
  if (state.workflow.currentAgent === agentName) return 'running';
  if (state.workflow.errors.some(e => e.agent === agentName && !e.recoverable)) return 'error';
  return 'pending';
}