/**
 * Invincible V.2 - Advanced State Schema for LangGraph Multi-Agent System
 * Built for state-of-the-art content generation with competitive intelligence
 */

export interface ResearchData {
  query: string;
  results: any[];
  competitorAnalysis: {
    topCompetitors: string[];
    contentGaps: string[];
    strengthsWeaknesses: string[];
  };
  searchDepth: 'basic' | 'advanced';
  timestamp: number;
}

export interface ContentRequirements {
  topic: string;
  contentLength: number;
  tone: string;
  targetAudience: string;
  customInstructions?: string;
  keywords?: string[];
  contentType: 'article' | 'blog' | 'guide' | 'review';
}

export interface SEOAnalysis {
  primaryKeywords: string[];
  secondaryKeywords: string[];
  searchIntent: 'informational' | 'commercial' | 'navigational' | 'transactional';
  competitorKeywords: string[];
  geoOptimization: {
    voiceSearchOptimized: boolean;
    aiSearchOptimized: boolean;
    citationStructure: string[];
  };
}

export interface GEOOptimization {
  aiSearchVisibility: {
    perplexity: boolean;
    chatgpt: boolean;
    gemini: boolean;
    claude: boolean;
  };
  referenceOptimization: {
    citations: string[];
    sourceCredibility: number;
    factualAccuracy: number;
  };
  multimodalOptimization: {
    voiceSearchReady: boolean;
    visualSearchReady: boolean;
    structuredData: any;
  };
}

export interface ContentGeneration {
  outline: string[];
  content: string;
  title: string;
  metaDescription: string;
  wordCount: number;
  readabilityScore: number;
  humanizationApplied: string[];
}

export interface QualityMetrics {
  aiDetectionScore: number;
  originalityScore: number;
  competitiveAdvantage: number;
  seoScore: number;
  geoScore: number;
  humanLikenessScore: number;
}

export interface AgentMessage {
  agent: string;
  action: string;
  data: any;
  timestamp: number;
  status: 'pending' | 'in_progress' | 'completed' | 'error';
}

/**
 * Main State Interface for LangGraph
 * This represents the complete state that flows through all agents
 */
export interface InvincibleV2State {
  // Input Configuration
  requirements: ContentRequirements;
  
  // Research Phase
  research: {
    data: ResearchData[];
    currentQuery: string;
    completed: boolean;
  };
  
  // Analysis Phase
  analysis: {
    seo: SEOAnalysis;
    geo: GEOOptimization;
    competitive: {
      topContent: any[];
      contentGaps: string[];
      opportunities: string[];
    };
    completed: boolean;
  };
  
  // Content Generation Phase
  generation: {
    content: ContentGeneration;
    iterations: number;
    completed: boolean;
  };
  
  // Quality Assurance Phase
  quality: {
    metrics: QualityMetrics;
    validationResults: string[];
    humanizationApplied: string[];
    completed: boolean;
  };
  
  // Workflow Management
  workflow: {
    currentAgent: string;
    nextAgent: string | null;
    completedAgents: string[];
    errors: string[];
    progress: number;
  };
  
  // Communication
  messages: AgentMessage[];
  
  // Results
  result: {
    article: any;
    analytics: any;
    performance: any;
  } | null;
  
  // Metadata
  sessionId: string;
  startTime: number;
  endTime?: number;
  version: 'v2';
}

/**
 * State Update Functions
 * These functions help agents update specific parts of the state
 */
export class StateManager {
  static updateResearch(state: InvincibleV2State, researchData: ResearchData): InvincibleV2State {
    return {
      ...state,
      research: {
        ...state.research,
        data: [...state.research.data, researchData],
      }
    };
  }

  static updateAnalysis(state: InvincibleV2State, analysis: Partial<InvincibleV2State['analysis']>): InvincibleV2State {
    return {
      ...state,
      analysis: {
        ...state.analysis,
        ...analysis,
      }
    };
  }

  static updateGeneration(state: InvincibleV2State, generation: Partial<ContentGeneration>): InvincibleV2State {
    return {
      ...state,
      generation: {
        ...state.generation,
        content: {
          ...state.generation.content,
          ...generation,
        }
      }
    };
  }

  static updateQuality(state: InvincibleV2State, quality: Partial<QualityMetrics>): InvincibleV2State {
    return {
      ...state,
      quality: {
        ...state.quality,
        metrics: {
          ...state.quality.metrics,
          ...quality,
        }
      }
    };
  }

  static updateWorkflow(state: InvincibleV2State, workflow: Partial<InvincibleV2State['workflow']>): InvincibleV2State {
    return {
      ...state,
      workflow: {
        ...state.workflow,
        ...workflow,
      }
    };
  }

  static addMessage(state: InvincibleV2State, message: Omit<AgentMessage, 'timestamp'>): InvincibleV2State {
    return {
      ...state,
      messages: [
        ...state.messages,
        {
          ...message,
          timestamp: Date.now(),
        }
      ]
    };
  }
}

/**
 * Initial State Factory
 */
export function createInitialState(requirements: ContentRequirements): InvincibleV2State {
  return {
    requirements,
    research: {
      data: [],
      currentQuery: '',
      completed: false,
    },
    analysis: {
      seo: {
        primaryKeywords: [],
        secondaryKeywords: [],
        searchIntent: 'informational',
        competitorKeywords: [],
        geoOptimization: {
          voiceSearchOptimized: false,
          aiSearchOptimized: false,
          citationStructure: [],
        },
      },
      geo: {
        aiSearchVisibility: {
          perplexity: false,
          chatgpt: false,
          gemini: false,
          claude: false,
        },
        referenceOptimization: {
          citations: [],
          sourceCredibility: 0,
          factualAccuracy: 0,
        },
        multimodalOptimization: {
          voiceSearchReady: false,
          visualSearchReady: false,
          structuredData: {},
        },
      },
      competitive: {
        topContent: [],
        contentGaps: [],
        opportunities: [],
      },
      completed: false,
    },
    generation: {
      content: {
        outline: [],
        content: '',
        title: '',
        metaDescription: '',
        wordCount: 0,
        readabilityScore: 0,
        humanizationApplied: [],
      },
      iterations: 0,
      completed: false,
    },
    quality: {
      metrics: {
        aiDetectionScore: 0,
        originalityScore: 0,
        competitiveAdvantage: 0,
        seoScore: 0,
        geoScore: 0,
        humanLikenessScore: 0,
      },
      validationResults: [],
      humanizationApplied: [],
      completed: false,
    },
    workflow: {
      currentAgent: 'supervisor',
      nextAgent: 'research_agent',
      completedAgents: [],
      errors: [],
      progress: 0,
    },
    messages: [],
    result: null,
    sessionId: `v2_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    startTime: Date.now(),
    version: 'v2',
  };
}

/**
 * State Validation
 */
export function validateState(state: InvincibleV2State): boolean {
  return !!(
    state.requirements &&
    state.sessionId &&
    state.version === 'v2' &&
    state.workflow &&
    Array.isArray(state.messages)
  );
}