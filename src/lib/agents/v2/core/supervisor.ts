/**
 * Invincible V.2 - Intelligent Supervisor Agent
 * Dynamic workflow coordination with decision-making capabilities
 */

import { InvincibleV2State, StateManager, createInitialState, ContentRequirements } from './state-schema';
import { KimiK2Client, createKimiK2Client, KimiK2Tool } from '../tools/kimi-k2-client';
import { logger, createLogger, logPerformance } from '../utils/logger';
import { safeJsonParse } from '../utils/json-parser';

export interface SupervisorConfig {
  openRouterApiKey: string;
  tavilyApiKey: string;
  streaming?: boolean;
  maxRetries?: number;
  qualityThreshold?: number;
}

export interface AgentCapability {
  name: string;
  description: string;
  inputs: string[];
  outputs: string[];
  estimatedTime: number; // in seconds
  dependencies: string[];
}

export interface WorkflowDecision {
  nextAgent: string;
  reasoning: string;
  parameters: any;
  skipConditions?: string[];
}

export class SupervisorAgent {
  private kimiClient: KimiK2Client;
  private config: SupervisorConfig;
  private agentCapabilities: Map<string, AgentCapability>;
  private currentWorkflow: string[] = [];
  private supervisorLogger: ReturnType<typeof createLogger>;

  constructor(config: SupervisorConfig) {
    this.config = {
      streaming: true,
      maxRetries: 3,
      qualityThreshold: 85,
      ...config,
    };

    this.kimiClient = createKimiK2Client(this.config.openRouterApiKey);
    this.supervisorLogger = createLogger({ 
      agentName: 'SupervisorAgent',
      workflowId: `workflow_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`
    });
    
    this.supervisorLogger.info('Supervisor initialized');
    
    this.initializeAgentCapabilities();
  }

  /**
   * Initialize agent capabilities and dependencies
   */
  private initializeAgentCapabilities() {
    this.agentCapabilities = new Map([
      ['research_agent', {
        name: 'Research Agent',
        description: 'Conducts comprehensive research with competitive analysis and SEO intelligence',
        inputs: ['topic', 'contentType', 'targetAudience'],
        outputs: ['researchData', 'competitiveAnalysis', 'seoIntelligence'],
        estimatedTime: 45,
        dependencies: [],
      }],
      ['content_agent', {
        name: 'Content Generation Agent',
        description: 'Creates high-quality, human-like content using advanced AI techniques',
        inputs: ['researchData', 'outline', 'requirements', 'competitiveAnalysis'],
        outputs: ['content', 'title', 'metaDescription'],
        estimatedTime: 60,
        dependencies: ['research_agent'],
      }],
      ['seo_geo_agent', {
        name: 'SEO & GEO Optimization Agent',
        description: 'Optimizes content for traditional SEO and generative engine optimization',
        inputs: ['content', 'keywords', 'competitiveAnalysis'],
        outputs: ['optimizedContent', 'seoScore', 'geoOptimization'],
        estimatedTime: 30,
        dependencies: ['content_agent'],
      }],
      ['quality_agent', {
        name: 'Quality Assurance Agent',
        description: 'Validates content quality, applies humanization, and ensures AI detection bypass',
        inputs: ['content', 'requirements', 'qualityMetrics'],
        outputs: ['validatedContent', 'qualityScore', 'humanizationReport'],
        estimatedTime: 25,
        dependencies: ['seo_geo_agent'],
      }],
    ]);
  }

  /**
   * Main workflow execution method
   */
  async executeWorkflow(requirements: ContentRequirements): Promise<InvincibleV2State> {
    const workflowStartTime = Date.now();
    let state = createInitialState(requirements);
    
    this.supervisorLogger.info(`Workflow started: "${requirements.topic}"`);
    
    try {
      // Update supervisor status
      state = StateManager.updateWorkflow(state, {
        currentAgent: 'supervisor',
        progress: 5,
      });

      const estimatedTime = this.calculateEstimatedTime();
      this.supervisorLogger.info(`Workflow planned: ${this.currentWorkflow.join(' → ')} (~${estimatedTime}s)`);

      state = StateManager.addMessage(state, {
        agent: 'supervisor',
        action: 'workflow_started',
        data: { 
          topic: requirements.topic,
          estimatedTime,
        },
        status: 'in_progress',
      });

      // Plan the optimal workflow
      const workflowPlan = await this.planWorkflow(state);
      this.currentWorkflow = workflowPlan.agents;

      state = StateManager.addMessage(state, {
        agent: 'supervisor',
        action: 'workflow_planned',
        data: { 
          plannedAgents: workflowPlan.agents,
          reasoning: workflowPlan.reasoning,
        },
        status: 'completed',
      });

      // Execute each agent in the planned sequence
      for (const agentName of this.currentWorkflow) {
        state = await this.executeAgent(agentName, state);
        
        // Check if we should continue or modify the workflow
        const shouldContinue = await this.evaluateProgress(state);
        if (!shouldContinue.continue) {
          break;
        }

        if (shouldContinue.modifyWorkflow) {
          const newPlan = await this.replanWorkflow(state, shouldContinue.reason);
          this.currentWorkflow = newPlan.agents;
        }
      }

      // Final workflow validation
      const finalValidation = await this.validateFinalResult(state);
      
      if (finalValidation.isValid) {
        state = StateManager.updateWorkflow(state, {
          progress: 100,
          currentAgent: 'supervisor',
          nextAgent: null,
        });

        state = StateManager.addMessage(state, {
          agent: 'supervisor',
          action: 'workflow_completed',
          data: { 
            qualityScore: finalValidation.qualityScore,
            completionTime: Date.now() - state.startTime,
          },
          status: 'completed',
        });

        state.endTime = Date.now();
      } else {
        throw new Error(`Final validation failed: ${finalValidation.issues.join(', ')}`);
      }

      return state;

    } catch (error) {
      console.error('Supervisor workflow error:', error);
      
      return StateManager.addMessage(state, {
        agent: 'supervisor',
        action: 'workflow_failed',
        data: { error: error.toString() },
        status: 'error',
      });
    }
  }

  /**
   * Plan the optimal workflow based on requirements
   */
  private async planWorkflow(state: InvincibleV2State): Promise<{
    agents: string[];
    reasoning: string;
    estimatedTime: number;
  }> {
    const systemPrompt = `You are an intelligent workflow supervisor for an advanced content generation system. Your role is to analyze content requirements and plan the optimal sequence of AI agents to execute.

Available Agents:
${Array.from(this.agentCapabilities.entries()).map(([name, cap]) => 
  `- ${cap.name}: ${cap.description} (${cap.estimatedTime}s)`
).join('\n')}

Consider:
1. Dependencies between agents
2. Content complexity and requirements
3. Quality objectives
4. Time constraints
5. Potential optimization opportunities`;

    const userPrompt = `Plan the optimal workflow for this content generation task:

**Requirements:**
- Topic: "${state.requirements.topic}"
- Content Type: ${state.requirements.contentType}
- Length: ${state.requirements.contentLength} words
- Tone: ${state.requirements.tone}
- Target Audience: ${state.requirements.targetAudience}
- Custom Instructions: ${state.requirements.customInstructions || 'None'}

**Quality Objectives:**
- SEO optimized for search engines
- GEO optimized for AI search (ChatGPT, Perplexity, etc.)
- Human-like writing that bypasses AI detection
- Competitive advantage over existing content
- Factual accuracy and credibility

Return a JSON response with:
{
  "agents": ["agent1", "agent2", ...],
  "reasoning": "Explanation of why this sequence is optimal",
  "skipConditions": ["Optional conditions where agents might be skipped"],
  "optimizations": ["Potential workflow optimizations"]
}`;

    try {
      const response = await this.kimiClient.generateContent([
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ]);

      const plan = safeJsonParse(response.content, {
        agents: ['research_agent', 'content_agent', 'seo_geo_agent', 'quality_agent'],
        reasoning: 'Fallback to standard workflow due to parsing error'
      });
      
      // Map agent names to proper identifiers
      const agentNameMap: Record<string, string> = {
        'Research Agent': 'research_agent',
        'Content Generation Agent': 'content_agent', 
        'SEO & GEO Optimization Agent': 'seo_geo_agent',
        'Quality Assurance Agent': 'quality_agent',
        'research_agent': 'research_agent',
        'content_agent': 'content_agent',
        'seo_geo_agent': 'seo_geo_agent', 
        'quality_agent': 'quality_agent'
      };

      const mappedAgents = (plan?.agents || ['research_agent', 'content_agent', 'seo_geo_agent', 'quality_agent'])
        .map((agent: string) => agentNameMap[agent] || agent);

      return {
        agents: mappedAgents,
        reasoning: plan?.reasoning || 'Standard workflow sequence',
        estimatedTime: this.calculateEstimatedTime(mappedAgents),
      };
    } catch (error) {
      console.error('Workflow planning failed:', error);
      
      // Fallback to standard workflow
      return {
        agents: ['research_agent', 'content_agent', 'seo_geo_agent', 'quality_agent'],
        reasoning: 'Fallback to standard workflow due to planning error',
        estimatedTime: this.calculateEstimatedTime(),
      };
    }
  }

  /**
   * Execute a specific agent
   */
  private async executeAgent(agentName: string, state: InvincibleV2State): Promise<InvincibleV2State> {
    const capability = this.agentCapabilities.get(agentName);
    if (!capability) {
      throw new Error(`Unknown agent: ${agentName}`);
    }

    try {
      // Dynamic agent loading and execution
      switch (agentName) {
        case 'research_agent':
          const { ResearchAgent } = await import('../agents/research-agent');
          const researchAgent = new ResearchAgent({
            tavilyApiKey: this.config.tavilyApiKey,
          });
          return await researchAgent.execute(state);

        case 'content_agent':
          const { ContentAgent } = await import('../agents/content-agent');
          const contentAgent = new ContentAgent({
            kimiApiKey: this.config.openRouterApiKey,
          });
          return await contentAgent.execute(state);

        case 'seo_geo_agent':
          const { SEOGEOAgent } = await import('../agents/seo-geo-agent');
          const seoAgent = new SEOGEOAgent({
            kimiApiKey: this.config.openRouterApiKey,
          });
          return await seoAgent.execute(state);

        case 'quality_agent':
          const { QualityAgent } = await import('../agents/quality-agent');
          const qualityAgent = new QualityAgent({
            kimiApiKey: this.config.openRouterApiKey,
          });
          return await qualityAgent.execute(state);

        default:
          throw new Error(`Agent execution not implemented: ${agentName}`);
      }
    } catch (error) {
      console.error(`Agent ${agentName} execution failed:`, error);
      
      return StateManager.addMessage(state, {
        agent: 'supervisor',
        action: 'agent_execution_failed',
        data: { 
          agentName,
          error: error.toString(),
        },
        status: 'error',
      });
    }
  }

  /**
   * Evaluate progress and decide whether to continue
   */
  private async evaluateProgress(state: InvincibleV2State): Promise<{
    continue: boolean;
    modifyWorkflow?: boolean;
    reason?: string;
  }> {
    // Check for errors
    if (state.workflow.errors.length > 0) {
      return {
        continue: false,
        reason: `Errors detected: ${state.workflow.errors.join(', ')}`,
      };
    }

    // Check completion status of current phase
    const currentAgent = state.workflow.currentAgent;
    
    switch (currentAgent) {
      case 'research_agent':
        if (!state.research.completed) {
          return {
            continue: false,
            reason: 'Research phase incomplete',
          };
        }
        break;

      case 'content_agent':
        if (!state.generation.completed) {
          return {
            continue: false,
            reason: 'Content generation phase incomplete',
          };
        }
        break;

      case 'seo_geo_agent':
        if (!state.analysis.completed) {
          return {
            continue: false,
            reason: 'SEO/GEO optimization phase incomplete',
          };
        }
        break;

      case 'quality_agent':
        if (!state.quality.completed) {
          return {
            continue: false,
            reason: 'Quality assurance phase incomplete',
          };
        }
        break;
    }

    return { continue: true };
  }

  /**
   * Replan workflow based on current state
   */
  private async replanWorkflow(state: InvincibleV2State, reason: string): Promise<{
    agents: string[];
    reasoning: string;
  }> {
    const prompt = `Based on the current workflow state, determine if the remaining workflow should be modified.

**Current State:**
- Completed Agents: ${state.workflow.completedAgents.join(', ')}
- Current Agent: ${state.workflow.currentAgent}
- Progress: ${state.workflow.progress}%
- Modification Reason: ${reason}

**Available Remaining Agents:**
${Array.from(this.agentCapabilities.keys())
  .filter(agent => !state.workflow.completedAgents.includes(agent))
  .join(', ')}

Should the workflow be modified? Return JSON with:
{
  "modify": true/false,
  "agents": ["remaining_agent1", "remaining_agent2", ...],
  "reasoning": "Explanation for any modifications"
}`;

    try {
      const response = await this.kimiClient.generateContent([
        { role: 'user', content: prompt }
      ]);

      const decision = safeJsonParse(response.content, {
        modify: false,
        agents: this.currentWorkflow,
        reasoning: 'No workflow modification needed'
      });
      
      return {
        agents: decision?.agents || this.currentWorkflow,
        reasoning: decision?.reasoning || 'No workflow modification needed',
      };
    } catch (error) {
      console.error('Workflow replanning failed:', error);
      return {
        agents: this.currentWorkflow,
        reasoning: 'Replanning failed, continuing with original workflow',
      };
    }
  }

  /**
   * Validate the final result
   */
  private async validateFinalResult(state: InvincibleV2State): Promise<{
    isValid: boolean;
    qualityScore: number;
    issues: string[];
  }> {
    const issues: string[] = [];
    let qualityScore = 0;

    // Check if all required phases are completed
    if (!state.research.completed) {
      issues.push('Research phase not completed');
    } else {
      qualityScore += 25;
    }

    if (!state.generation.completed) {
      issues.push('Content generation not completed');
    } else {
      qualityScore += 25;
    }

    if (!state.analysis.completed) {
      issues.push('SEO/GEO analysis not completed');
    } else {
      qualityScore += 25;
    }

    if (!state.quality.completed) {
      issues.push('Quality assurance not completed');
    } else {
      qualityScore += 25;
    }

    // Check content quality metrics
    if (state.quality.metrics.aiDetectionScore > 50) {
      issues.push('Content may be detected as AI-generated');
      qualityScore -= 10;
    }

    if (state.quality.metrics.seoScore < 70) {
      issues.push('SEO score below acceptable threshold');
      qualityScore -= 10;
    }

    if (state.generation.content.wordCount < state.requirements.contentLength * 0.8) {
      issues.push('Content length significantly below target');
      qualityScore -= 15;
    }

    const isValid = issues.length === 0 && qualityScore >= this.config.qualityThreshold!;

    return {
      isValid,
      qualityScore: Math.max(0, qualityScore),
      issues,
    };
  }

  /**
   * Calculate estimated execution time
   */
  private calculateEstimatedTime(agents?: string[]): number {
    const agentsToExecute = agents || Array.from(this.agentCapabilities.keys());
    
    return agentsToExecute.reduce((total, agentName) => {
      const capability = this.agentCapabilities.get(agentName);
      return total + (capability?.estimatedTime || 30);
    }, 0);
  }

  /**
   * Generate workflow progress stream
   */
  async *generateProgressStream(requirements: ContentRequirements): AsyncGenerator<{
    type: string;
    data: any;
    timestamp: number;
  }, void, unknown> {
    let state = createInitialState(requirements);
    
    yield {
      type: 'workflow_started',
      data: { 
        sessionId: state.sessionId,
        estimatedTime: this.calculateEstimatedTime(),
      },
      timestamp: Date.now(),
    };

    try {
      // Plan workflow
      const plan = await this.planWorkflow(state);
      yield {
        type: 'workflow_planned',
        data: { 
          agents: plan.agents,
          reasoning: plan.reasoning,
        },
        timestamp: Date.now(),
      };

      // Execute agents with progress updates
      for (let i = 0; i < plan.agents.length; i++) {
        const agentName = plan.agents[i];
        
        yield {
          type: 'agent_started',
          data: { 
            agent: agentName,
            progress: (i / plan.agents.length) * 100,
          },
          timestamp: Date.now(),
        };

        state = await this.executeAgent(agentName, state);

        yield {
          type: 'agent_completed',
          data: { 
            agent: agentName,
            progress: ((i + 1) / plan.agents.length) * 100,
            messages: state.messages.slice(-5), // Last 5 messages
          },
          timestamp: Date.now(),
        };
      }

      // Final validation
      const validation = await this.validateFinalResult(state);
      
      yield {
        type: 'workflow_completed',
        data: { 
          success: validation.isValid,
          qualityScore: validation.qualityScore,
          result: state.result,
          totalTime: Date.now() - state.startTime,
        },
        timestamp: Date.now(),
      };

    } catch (error) {
      yield {
        type: 'workflow_error',
        data: { error: error.toString() },
        timestamp: Date.now(),
      };
    }
  }

  /**
   * Get workflow status
   */
  getWorkflowStatus(state: InvincibleV2State): {
    phase: string;
    progress: number;
    estimatedTimeRemaining: number;
    currentAgent: string;
    nextAgent: string | null;
  } {
    const completedAgents = state.workflow.completedAgents.length;
    const totalAgents = this.currentWorkflow.length;
    const progress = totalAgents > 0 ? (completedAgents / totalAgents) * 100 : 0;

    const remainingAgents = this.currentWorkflow.slice(completedAgents);
    const estimatedTimeRemaining = remainingAgents.reduce((total, agentName) => {
      const capability = this.agentCapabilities.get(agentName);
      return total + (capability?.estimatedTime || 30);
    }, 0);

    return {
      phase: this.getWorkflowPhase(state),
      progress,
      estimatedTimeRemaining,
      currentAgent: state.workflow.currentAgent,
      nextAgent: state.workflow.nextAgent,
    };
  }

  /**
   * Get current workflow phase name
   */
  private getWorkflowPhase(state: InvincibleV2State): string {
    if (!state.research.completed) return 'Research & Analysis';
    if (!state.generation.completed) return 'Content Generation';
    if (!state.analysis.completed) return 'SEO & GEO Optimization';
    if (!state.quality.completed) return 'Quality Assurance';
    return 'Completion';
  }
}

/**
 * Factory function to create supervisor agent
 */
export function createSupervisorAgent(config: SupervisorConfig): SupervisorAgent {
  return new SupervisorAgent(config);
}