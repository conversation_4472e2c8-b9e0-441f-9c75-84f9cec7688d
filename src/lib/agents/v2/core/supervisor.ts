/**
 * Invincible V.2 - Enhanced Supervisor Agent
 * Robust workflow coordination with improved error handling and SSE support
 */

import { InvincibleV2State, ContentRequirements, StateManager } from './state-schema';
import { logger } from '../utils/logger';

export interface SupervisorConfig {
  groqApiKey: string;
  tavilyApiKey: string;
  streaming: boolean;
  maxRetries: number;
  qualityThreshold: number;
  timeout?: number;
}

interface WorkflowPlan {
  agents: string[];
  reasoning: string;
  estimatedTime: number;
}

interface AgentDefinition {
  name: string;
  description: string;
  required: boolean;
  dependencies: string[];
  timeout: number;
  retryable: boolean;
}

export class SupervisorAgent {
  private config: SupervisorConfig;
  private agents: Map<string, AgentDefinition>;
  private startTime: number = 0;

  constructor(config: SupervisorConfig) {
    this.config = {
      streaming: true,
      maxRetries: 3,
      qualityThreshold: 80,
      timeout: 300000, // 5 minutes
      ...config
    };

    this.initializeAgents();
    logger.info('✅ Supervisor V2 initialized', {
      streaming: this.config.streaming,
      maxRetries: this.config.maxRetries,
      qualityThreshold: this.config.qualityThreshold
    });
  }

  private initializeAgents() {
    this.agents = new Map([
      ['research_agent', {
        name: 'Research Agent',
        description: 'Conducts comprehensive research with competitive analysis',
        required: true,
        dependencies: [],
        timeout: 60000,
        retryable: true
      }],
      ['content_agent', {
        name: 'Content Generation Agent',
        description: 'Creates high-quality, human-like content',
        required: true,
        dependencies: ['research_agent'],
        timeout: 120000,
        retryable: true
      }],
      ['seo_geo_agent', {
        name: 'SEO & GEO Optimization Agent',
        description: 'Optimizes for search engines and AI systems',
        required: false,
        dependencies: ['content_agent'],
        timeout: 60000,
        retryable: true
      }],
      ['quality_agent', {
        name: 'Quality Assurance Agent',
        description: 'Validates quality and applies humanization',
        required: true,
        dependencies: ['content_agent'],
        timeout: 60000,
        retryable: true
      }]
    ]);
  }

  /**
   * Execute the complete workflow
   */
  async executeWorkflow(requirements: ContentRequirements): Promise<InvincibleV2State> {
    this.startTime = Date.now();
    let state = StateManager.createInitialState(requirements);
    
    logger.info('🚀 Starting V2 workflow', {
      sessionId: state.sessionId,
      topic: requirements.topic,
      contentType: requirements.contentType,
      targetLength: requirements.contentLength
    });

    try {
      // Update initial state
      state = StateManager.updateWorkflow(state, {
        status: 'running',
        progress: 5 
      });

      // Plan the workflow
      const plan = this.planWorkflow(state);
      logger.info('📋 Workflow planned', {
        agents: plan.agents,
        estimatedTime: `${plan.estimatedTime}s`
      });

      state = StateManager.addMessage(state, {
        agent: 'supervisor',
        action: 'workflow_planned',
        data: plan,
        status: 'completed'
      });

      // Execute each agent
      for (const agentName of plan.agents) {
        const agentDef = this.agents.get(agentName);
        if (!agentDef) continue;

        // Check dependencies
        const depsReady = agentDef.dependencies.every(dep => 
          state.workflow.completedAgents.includes(dep)
        );
        
        if (!depsReady) {
          logger.warn(`⚠️ Skipping ${agentName} - dependencies not met`);
          continue;
        }

        // Execute agent with retry logic
        let retries = 0;
        let success = false;

        while (!success && retries < this.config.maxRetries) {
          try {
            logger.info(`🤖 Executing ${agentDef.name}`, {
              attempt: retries + 1,
              maxRetries: this.config.maxRetries
            });

            state = await this.executeAgent(agentName, state);
            success = true;

            // Update progress
            const progress = StateManager.calculateProgress(state);
            state = StateManager.updateWorkflow(state, { progress });

          } catch (error) {
            retries++;
            const errorMsg = error instanceof Error ? error.message : String(error);
            
            logger.error(`❌ ${agentDef.name} failed`, {
              attempt: retries,
              error: errorMsg,
              retryable: agentDef.retryable
            });

            state = StateManager.addError(state, {
              agent: agentName,
              error: errorMsg,
              recoverable: agentDef.retryable && retries < this.config.maxRetries
            });

            if (!agentDef.retryable || retries >= this.config.maxRetries) {
              if (agentDef.required) {
                throw new Error(`Critical agent ${agentName} failed: ${errorMsg}`);
              } else {
                logger.warn(`⚠️ Optional agent ${agentName} failed, continuing workflow`);
                break;
              }
            }

            // Wait before retry
            await new Promise(resolve => setTimeout(resolve, 1000 * retries));
          }
        }
      }

      // Validate final result
      const validationResult = await this.validateResult(state);
      
      if (validationResult.success) {
        state = StateManager.setResult(state, {
          success: true,
          article: {
            title: state.content.generation.title,
            content: state.content.generation.content,
            metaDescription: state.content.generation.metaDescription,
            wordCount: state.content.generation.wordCount,
            seoKeywords: state.optimization.seo.primaryKeywords
          },
          performance: {
            totalTime: Date.now() - this.startTime,
            qualityScore: state.content.quality.contentScore,
            seoScore: state.content.quality.seoScore,
            geoScore: state.content.quality.geoScore
          }
        });

        logger.info('✅ Workflow completed successfully', {
          sessionId: state.sessionId,
          totalTime: `${(Date.now() - this.startTime) / 1000}s`,
          wordCount: state.content.generation.wordCount,
          qualityScore: state.content.quality.contentScore
        });
      } else {
        throw new Error(validationResult.error);
      }

      return state;

    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : String(error);
      logger.error('❌ Workflow failed', {
        sessionId: state.sessionId,
        error: errorMsg,
        totalTime: `${(Date.now() - this.startTime) / 1000}s`
      });

      state = StateManager.setResult(state, {
        success: false,
        error: errorMsg
      });

      return state;
    }
  }

  /**
   * Plan the optimal workflow
   */
  private planWorkflow(state: InvincibleV2State): WorkflowPlan {
    const standardWorkflow = ['research_agent', 'content_agent', 'seo_geo_agent', 'quality_agent'];
    
    // For now, use standard workflow
    // In future, can add dynamic workflow planning based on requirements
      return {
      agents: standardWorkflow,
      reasoning: 'Standard workflow for comprehensive content generation',
      estimatedTime: 240 // 4 minutes
    };
  }

  /**
   * Execute a specific agent
   */
  private async executeAgent(agentName: string, state: InvincibleV2State): Promise<InvincibleV2State> {
    const agentDef = this.agents.get(agentName);
    if (!agentDef) {
      throw new Error(`Unknown agent: ${agentName}`);
    }

    // Update state
    state = StateManager.updateWorkflow(state, { 
      currentAgent: agentName 
    });

    state = StateManager.addMessage(state, {
      agent: 'supervisor',
      action: 'agent_started',
      data: { agent: agentName },
      status: 'in_progress'
    });

    try {
      // Dynamic import with timeout
      const agentPromise = this.loadAndExecuteAgent(agentName, state);
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error(`Agent ${agentName} timed out`)), agentDef.timeout);
      });

      state = await Promise.race([agentPromise, timeoutPromise]);

      // Mark agent as completed
      state = StateManager.updateWorkflow(state, {
        completedAgent: agentName
      });

      state = StateManager.addMessage(state, {
        agent: 'supervisor',
        action: 'agent_completed',
        data: { agent: agentName },
        status: 'completed'
      });

      return state;

    } catch (error) {
      logger.error(`Agent ${agentName} execution failed`, { error });
      throw error;
    }
  }

  /**
   * Load and execute agent dynamically
   */
  private async loadAndExecuteAgent(agentName: string, state: InvincibleV2State): Promise<InvincibleV2State> {
    switch (agentName) {
      case 'research_agent': {
        const { ResearchAgent } = await import('../agents/research-agent');
        const agent = new ResearchAgent({
          tavilyApiKey: this.config.tavilyApiKey,
          maxSearchQueries: 8,
          searchDepth: 'advanced'
        });
        return await agent.execute(state);
      }

      case 'content_agent': {
        const { ContentAgent } = await import('../agents/content-agent');
        const agent = new ContentAgent({
          groqApiKey: this.config.groqApiKey,
          temperature: 0.7,
          enableHumanization: true
        });
        return await agent.execute(state);
      }

      case 'seo_geo_agent': {
        const { SEOGEOAgent } = await import('../agents/seo-geo-agent');
        const agent = new SEOGEOAgent({
          groqApiKey: this.config.groqApiKey,
          enableGEO: true,
          enableAdvancedSEO: true
        });
        return await agent.execute(state);
      }

      case 'quality_agent': {
        const { QualityAgent } = await import('../agents/quality-agent');
        const agent = new QualityAgent({
          groqApiKey: this.config.groqApiKey,
          qualityThreshold: this.config.qualityThreshold,
          enableAdvancedHumanization: true
        });
        return await agent.execute(state);
      }

      default:
        throw new Error(`Agent ${agentName} not implemented`);
    }
  }

  /**
   * Validate the final result
   */
  private async validateResult(state: InvincibleV2State): Promise<{
    success: boolean;
    error?: string;
  }> {
    // Check if content was generated
    if (!state.content.generation.content || state.content.generation.wordCount === 0) {
      return { success: false, error: 'No content generated' };
    }

    // Check word count
    const targetWords = state.requirements.contentLength;
    const actualWords = state.content.generation.wordCount;
    const variance = Math.abs(actualWords - targetWords) / targetWords;
    
    if (variance > 0.2) { // 20% variance allowed
      return { 
        success: false, 
        error: `Word count mismatch: target ${targetWords}, actual ${actualWords}` 
      };
    }

    // Check quality scores
    const qualityScore = state.content.quality.contentScore;
    if (qualityScore < this.config.qualityThreshold) {
    return {
        success: false, 
        error: `Quality score ${qualityScore} below threshold ${this.config.qualityThreshold}` 
      };
    }

    return { success: true };
  }

  /**
   * Generate progress stream for SSE
   */
  async *generateProgressStream(requirements: ContentRequirements): AsyncGenerator<{
    type: string;
    data: any;
  }> {
    let state = StateManager.createInitialState(requirements);
    
    // Initial event
    yield {
      type: 'start',
      data: { 
        sessionId: state.sessionId,
        message: 'Invincible V2 workflow starting...'
      }
    };

    try {
      const plan = this.planWorkflow(state);
      
      yield {
        type: 'workflow_planned',
        data: { 
          agents: plan.agents,
          estimatedTime: plan.estimatedTime
        }
      };

      // Execute agents and stream progress
      for (const agentName of plan.agents) {
        const agentDef = this.agents.get(agentName);
        if (!agentDef) continue;
        
        yield {
          type: 'agent_starting',
          data: { 
            agent: agentName,
            name: agentDef.name,
            description: agentDef.description
          }
        };

        try {
        state = await this.executeAgent(agentName, state);
          
          // Stream agent-specific updates
          yield* this.streamAgentUpdates(agentName, state);

        yield {
          type: 'agent_completed',
          data: { 
            agent: agentName,
              progress: StateManager.calculateProgress(state)
            }
          };

        } catch (error) {
          yield {
            type: 'agent_error',
            data: {
              agent: agentName,
              error: error instanceof Error ? error.message : String(error)
            }
          };
        }
      }

      // Final validation
      const validation = await this.validateResult(state);
      
      if (validation.success) {
      yield {
          type: 'complete',
        data: { 
            success: true,
            article: state.content.generation,
            performance: {
          totalTime: Date.now() - state.startTime,
              qualityScore: state.content.quality.contentScore
            }
          }
        };
      } else {
        yield {
          type: 'error',
          data: {
            error: validation.error
          }
        };
      }

    } catch (error) {
      yield {
        type: 'error',
        data: {
          error: error instanceof Error ? error.message : String(error)
        }
      };
    }
  }

  /**
   * Stream agent-specific updates
   */
  private async *streamAgentUpdates(agentName: string, state: InvincibleV2State): AsyncGenerator<{
    type: string;
    data: any;
  }> {
    switch (agentName) {
      case 'research_agent':
        yield {
          type: 'research_update',
          data: {
            totalQueries: state.research.data.length,
            competitorsFound: state.research.competitiveAnalysis.topCompetitors.length,
            keywordsIdentified: state.optimization.seo.primaryKeywords.length
          }
        };
        break;

      case 'content_agent':
        yield {
          type: 'content_update',
          data: {
            title: state.content.generation.title,
            wordCount: state.content.generation.wordCount,
            outlineSections: state.content.generation.outline.length
          }
        };
        break;

      case 'seo_geo_agent':
        yield {
          type: 'optimization_update',
          data: {
            seoScore: state.content.quality.seoScore,
            geoScore: state.content.quality.geoScore,
            aiPlatformScores: state.optimization.geo.aiPlatformScores
          }
        };
        break;

      case 'quality_agent':
        yield {
          type: 'quality_update',
          data: {
            contentScore: state.content.quality.contentScore,
            humanScore: state.content.quality.humanScore,
            aiDetectionProbability: state.content.quality.aiDetectionProbability
          }
        };
        break;
    }
  }
}

// Factory function
export function createSupervisorAgent(config: SupervisorConfig): SupervisorAgent {
  return new SupervisorAgent(config);
}