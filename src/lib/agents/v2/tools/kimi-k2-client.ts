/**
 * Invincible V.2 - Kimi K2 OpenRouter Client Integration
 * Advanced AI client with agentic capabilities and tool calling
 */

import OpenAI from 'openai';
import { logger, createLogger } from '../utils/logger';

export interface KimiK2Config {
  apiKey: string;
  model: 'moonshotai/kimi-k2' | 'moonshotai/kimi-k2:free';
  baseURL?: string;
  temperature?: number;
  maxTokens?: number;
  streaming?: boolean;
}

export interface KimiK2Message {
  role: 'system' | 'user' | 'assistant' | 'tool';
  content: string;
  name?: string;
  tool_calls?: any[];
  tool_call_id?: string;
}

export interface KimiK2Tool {
  type: 'function';
  function: {
    name: string;
    description: string;
    parameters: {
      type: 'object';
      properties: Record<string, any>;
      required?: string[];
    };
  };
}

export interface KimiK2Response {
  content: string;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
    cost: number;
  };
  tool_calls?: any[];
  finish_reason: string;
}

export class KimiK2Client {
  private client: OpenAI;
  private config: KimiK2Config;
  private aiLogger: ReturnType<typeof createLogger>;

  constructor(config: KimiK2Config) {
    this.config = {
      baseURL: 'https://openrouter.ai/api/v1',
      temperature: 0.7,
      maxTokens: 4000,
      streaming: false,
      ...config,
    };

    this.client = new OpenAI({
      baseURL: this.config.baseURL,
      apiKey: this.config.apiKey,
      defaultHeaders: {
        'HTTP-Referer': 'http://localhost:3000',
        'X-Title': 'Invincible V2 Agent System'
      }
    });

    this.aiLogger = createLogger({ 
      agentName: 'KimiK2Client',
      model: this.config.model
    });

    this.aiLogger.info('🤖 OpenRouter Kimi K2 Client initialized', {
      model: this.config.model,
      baseURL: this.config.baseURL,
      temperature: this.config.temperature,
      maxTokens: this.config.maxTokens,
      apiKeyPrefix: this.config.apiKey.substring(0, 15) + '...'
    });
  }

  /**
   * Generate content using Kimi K2 with advanced prompting
   */
  async generateContent(
    messages: KimiK2Message[],
    tools?: KimiK2Tool[],
    options?: Partial<KimiK2Config>
  ): Promise<KimiK2Response> {
    const requestConfig = { ...this.config, ...options };
    const requestId = `openrouter_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;

    this.aiLogger.info('🚀 OpenRouter API Request Starting', {
      requestId,
      model: requestConfig.model,
      temperature: requestConfig.temperature,
      maxTokens: requestConfig.maxTokens,
      messagesCount: messages.length,
      hasTools: !!tools,
      toolsCount: tools?.length || 0,
      firstMessagePreview: messages[0]?.content?.substring(0, 100) + '...'
    });

    const startTime = Date.now();

    try {
      this.aiLogger.debug('📡 Making OpenRouter API call', {
        requestId,
        endpoint: `${this.config.baseURL}/chat/completions`,
        payload: {
          model: requestConfig.model,
          temperature: requestConfig.temperature,
          max_tokens: requestConfig.maxTokens,
          stream: requestConfig.streaming
        }
      });

      const response = await this.client.chat.completions.create({
        model: requestConfig.model,
        messages: messages as any[],
        temperature: requestConfig.temperature,
        max_tokens: requestConfig.maxTokens,
        tools: tools as any[],
        tool_choice: tools ? 'auto' : undefined,
        stream: requestConfig.streaming,
      });

      const responseTime = Date.now() - startTime;

      this.aiLogger.info('✅ OpenRouter API Response Received', {
        requestId,
        responseTime: `${responseTime}ms`,
        model: requestConfig.model,
        hasResponse: !!response
      });

      const choice = response.choices[0];
      const usage = response.usage;

      // Calculate cost (Kimi K2 pricing: $0.15 input, $2.50 output per 1M tokens)
      const cost = (
        (usage?.prompt_tokens || 0) * 0.15 / 1000000 +
        (usage?.completion_tokens || 0) * 2.50 / 1000000
      );

      const result = {
        content: choice.message.content || '',
        usage: {
          prompt_tokens: usage?.prompt_tokens || 0,
          completion_tokens: usage?.completion_tokens || 0,
          total_tokens: usage?.total_tokens || 0,
          cost: cost || 0,
        },
        tool_calls: choice.message.tool_calls,
        finish_reason: choice.finish_reason || 'stop',
      };

      this.aiLogger.info('📊 OpenRouter API Call Completed', {
        requestId,
        responseTime: `${responseTime}ms`,
        promptTokens: result.usage.prompt_tokens,
        completionTokens: result.usage.completion_tokens,
        totalTokens: result.usage.total_tokens,
        cost: `$${(result.usage.cost || 0).toFixed(6)}`,
        finishReason: result.finish_reason,
        contentLength: result.content.length,
        contentPreview: result.content.substring(0, 150) + '...'
      });

      return result;
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      this.aiLogger.error('❌ OpenRouter API Error', {
        requestId,
        responseTime: `${responseTime}ms`,
        model: requestConfig.model,
        error: (error as Error).message,
        errorStack: (error as Error).stack,
        errorName: (error as Error).name
      });
      
      console.error('Kimi K2 API Error:', error);
      throw new Error(`Kimi K2 generation failed: ${error}`);
    }
  }

  /**
   * Generate content with streaming for real-time updates
   */
  async *generateStream(
    messages: KimiK2Message[],
    tools?: KimiK2Tool[],
    options?: Partial<KimiK2Config>
  ): AsyncGenerator<{ content: string; done: boolean; usage?: any }, void, unknown> {
    const requestConfig = { ...this.config, ...options, streaming: true };

    try {
      const stream = await this.client.chat.completions.create({
        model: requestConfig.model,
        messages: messages as any[],
        temperature: requestConfig.temperature,
        max_tokens: requestConfig.maxTokens,
        tools: tools as any[],
        tool_choice: tools ? 'auto' : undefined,
        stream: true,
      });

      let content = '';
      let usage: any = null;

      for await (const chunk of stream) {
        const delta = chunk.choices[0]?.delta;
        
        if (delta?.content) {
          content += delta.content;
          yield { content: delta.content, done: false };
        }

        if (chunk.usage) {
          usage = chunk.usage;
        }

        if (chunk.choices[0]?.finish_reason) {
          yield { 
            content: '', 
            done: true, 
            usage: usage ? {
              ...usage,
              cost: (usage.prompt_tokens * 0.15 / 1000000) + (usage.completion_tokens * 2.50 / 1000000)
            } : undefined
          };
          break;
        }
      }
    } catch (error) {
      console.error('Kimi K2 Streaming Error:', error);
      throw new Error(`Kimi K2 streaming failed: ${error}`);
    }
  }

  /**
   * Advanced content generation with specific prompting strategies
   */
  async generateArticleContent(params: {
    topic: string;
    outline: string[];
    requirements: any;
    research: any[];
    competitorAnalysis: any;
  }): Promise<KimiK2Response> {
    const systemPrompt = `You are Kimi, an advanced AI content writer specialized in creating superior, human-like articles that outperform competitors. Your expertise includes:

1. **Human-Like Writing**: Create content that flows naturally, uses varied sentence structures, and incorporates conversational elements
2. **Competitive Intelligence**: Leverage competitor analysis to identify gaps and create superior content
3. **SEO & GEO Optimization**: Optimize for both traditional search engines and AI search systems (ChatGPT, Perplexity, Gemini)
4. **Factual Accuracy**: Ensure all information is accurate and well-researched
5. **Engagement**: Write compelling content that captures and maintains reader attention

Write in a style that:
- Bypasses AI detection through natural language patterns
- Incorporates industry expertise and insider knowledge
- Uses strategic keyword placement without keyword stuffing
- Creates content that AI systems will want to reference and cite
- Maintains perfect grammar while using conversational tone`;

    const userPrompt = `Create a comprehensive article on "${params.topic}" that surpasses all competitor content.

**Content Requirements:**
- Length: ${params.requirements.contentLength || 2000} words
- Tone: ${params.requirements.tone || 'professional'}
- Audience: ${params.requirements.targetAudience || 'general'}
- Custom Instructions: ${params.requirements.customInstructions || 'None'}

**Article Outline:**
${params.outline.map((section, index) => `${index + 1}. ${section}`).join('\n')}

**Research Data:**
${params.research.map(r => `- ${r.query}: ${r.results.length} sources`).join('\n')}

**Competitor Analysis:**
- Top Competitors: ${params.competitorAnalysis.topCompetitors?.join(', ') || 'None'}
- Content Gaps: ${params.competitorAnalysis.contentGaps?.join(', ') || 'None'}
- Opportunities: ${params.competitorAnalysis.strengthsWeaknesses?.join(', ') || 'None'}

Create an article that:
1. Covers all outlined sections comprehensively
2. Incorporates unique insights not found in competitor content
3. Uses natural, human-like language patterns
4. Includes strategic internal linking opportunities
5. Is optimized for AI search engines (GEO)
6. Maintains perfect factual accuracy

Return the content in HTML format with proper semantic structure.`;

    return await this.generateContent([
      { role: 'system', content: systemPrompt },
      { role: 'user', content: userPrompt }
    ]);
  }

  /**
   * Generate SEO-optimized meta content
   */
  async generateMetaContent(params: {
    title: string;
    content: string;
    keywords: string[];
    targetLength?: number;
  }): Promise<KimiK2Response> {
    const prompt = `Based on the article content and title "${params.title}", create SEO-optimized meta content:

**Keywords to include:** ${params.keywords.join(', ')}
**Target meta description length:** ${params.targetLength || 160} characters

**Article Content Preview:**
${params.content.substring(0, 500)}...

Generate:
1. **Title Tag** (50-60 characters, include primary keyword)
2. **Meta Description** (${params.targetLength || 160} characters max, compelling and keyword-rich)
3. **H1 Tag** (Different from title tag, keyword optimized)
4. **Focus Keyword** (Primary keyword for this article)
5. **Secondary Keywords** (3-5 related keywords)

Format as JSON with keys: titleTag, metaDescription, h1Tag, focusKeyword, secondaryKeywords`;

    return await this.generateContent([
      { role: 'user', content: prompt }
    ]);
  }

  /**
   * Human-like content refinement and AI detection bypass
   */
  async humanizeContent(content: string): Promise<KimiK2Response> {
    const prompt = `Refine this content to make it more human-like and bypass AI detection while maintaining quality and meaning:

**Original Content:**
${content}

Apply these humanization techniques:
1. **Sentence Variation**: Mix short, medium, and long sentences
2. **Natural Transitions**: Use conversational connectors
3. **Personal Touch**: Add subtle personal observations where appropriate
4. **Rhythm Variation**: Vary paragraph lengths and structure
5. **Colloquial Elements**: Include natural speech patterns
6. **Unique Insights**: Add distinctive perspectives or analogies
7. **Date Variations**: Update any date references to be current and relevant

Maintain:
- All factual accuracy
- SEO optimization
- Professional tone
- Comprehensive coverage
- Logical flow

Return the humanized content in the same format as the original.`;

    return await this.generateContent([
      { role: 'user', content: prompt }
    ], undefined, { temperature: 0.9 });
  }

  /**
   * Get estimated cost for a request
   */
  estimateCost(inputTokens: number, outputTokens: number): number {
    return (inputTokens * 0.15 / 1000000) + (outputTokens * 2.50 / 1000000);
  }

  /**
   * Validate API connection
   */
  async validateConnection(): Promise<boolean> {
    try {
      const response = await this.generateContent([
        { role: 'user', content: 'Hello, respond with "OK" if you are working correctly.' }
      ], undefined, { maxTokens: 10 });
      
      return response.content.toLowerCase().includes('ok');
    } catch (error) {
      console.error('Kimi K2 connection validation failed:', error);
      return false;
    }
  }
}

/**
 * Factory function to create Kimi K2 client
 */
export function createKimiK2Client(apiKey: string, useFreeModel = false): KimiK2Client {
  return new KimiK2Client({
    apiKey,
    model: useFreeModel ? 'moonshotai/kimi-k2:free' : 'moonshotai/kimi-k2',
  });
}

/**
 * Utility functions for token estimation
 */
export class KimiK2Utils {
  /**
   * Estimate token count (rough approximation: 1 token ≈ 4 characters)
   */
  static estimateTokens(text: string): number {
    return Math.ceil(text.length / 4);
  }

  /**
   * Prepare messages for API call
   */
  static prepareMessages(
    systemPrompt: string,
    userPrompt: string,
    conversation: { role: string; content: string }[] = []
  ): KimiK2Message[] {
    const messages: KimiK2Message[] = [
      { role: 'system', content: systemPrompt }
    ];

    // Add conversation history
    conversation.forEach(msg => {
      messages.push({
        role: msg.role as 'user' | 'assistant',
        content: msg.content
      });
    });

    // Add current user prompt
    messages.push({ role: 'user', content: userPrompt });

    return messages;
  }

  /**
   * Create tools definition for function calling
   */
  static createTools(toolDefinitions: any[]): KimiK2Tool[] {
    return toolDefinitions.map(tool => ({
      type: 'function',
      function: {
        name: tool.name,
        description: tool.description,
        parameters: tool.parameters,
      }
    }));
  }
}