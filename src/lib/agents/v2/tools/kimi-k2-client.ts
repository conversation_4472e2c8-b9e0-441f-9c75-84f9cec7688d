/**
 * Invincible V.2 - Enhanced Kimi K2 OpenRouter Client
 * Robust AI client with improved error handling and retry logic
 */

import OpenAI from 'openai';
import { logger } from '../utils/logger';

export interface KimiK2Config {
  apiKey: string;
  model: string;
  temperature: number;
  maxTokens: number;
  streaming: boolean;
  maxRetries: number;
  timeout: number;
}

export interface KimiK2Message {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface KimiK2Response {
  content: string;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
  finish_reason?: string;
}

export class KimiK2Client {
  private client: OpenAI;
  private config: KimiK2Config;

  constructor(config: Partial<KimiK2Config> = {}) {
    this.config = {
      apiKey: config.apiKey || '********************************************************',
      model: 'moonshotai/kimi-k2-instruct',
      temperature: config.temperature || 0.7,
      maxTokens: config.maxTokens || 4000,
      streaming: config.streaming || false,
      maxRetries: config.maxRetries || 3,
      timeout: config.timeout || 60000
    };

    // Initialize Groq client with OpenAI compatibility
    this.client = new OpenAI({
      baseURL: 'https://api.groq.com/openai/v1',
      apiKey: this.config.apiKey,
      timeout: this.config.timeout,
      maxRetries: 0 // We handle retries manually
    });

    logger.info(`🤖 Groq KimiK2 client initialized - Model: ${this.config.model}, Temp: ${this.config.temperature}, MaxTokens: ${this.config.maxTokens}`);
  }

  /**
   * Generate content with improved error handling
   */
  async generateContent(
    messages: KimiK2Message[],
    options?: {
      temperature?: number;
      maxTokens?: number;
      stream?: boolean;
    }
  ): Promise<KimiK2Response> {
    const startTime = Date.now();
    const temperature = options?.temperature ?? this.config.temperature;
    const maxTokens = options?.maxTokens ?? this.config.maxTokens;
    const stream = options?.stream ?? this.config.streaming;

    // Add retry logic
    for (let attempt = 0; attempt < this.config.maxRetries; attempt++) {
      try {
        logger.info(`🤖 Generating content (attempt ${attempt + 1}/${this.config.maxRetries}) - Model: ${this.config.model}, Messages: ${messages.length}, Temp: ${temperature}, MaxTokens: ${maxTokens}`);

        const completion = await this.client.chat.completions.create({
          model: this.config.model,
          messages: messages as any,
          temperature,
          max_tokens: maxTokens,
          stream: false, // Always non-streaming for simplicity
          presence_penalty: 0.1,
          frequency_penalty: 0.1
        });

        const response = completion.choices[0];
        
        if (!response?.message?.content) {
          throw new Error('Empty response from AI model');
        }

        const duration = Date.now() - startTime;
        logger.info(`✅ Content generated successfully - Model: ${this.config.model}, Duration: ${duration}ms, Usage: ${JSON.stringify(completion.usage)}`);

        return {
          content: response.message.content,
          usage: completion.usage ? {
            prompt_tokens: completion.usage.prompt_tokens,
            completion_tokens: completion.usage.completion_tokens,
            total_tokens: completion.usage.total_tokens
          } : undefined,
          finish_reason: response.finish_reason || undefined
        };

      } catch (error) {
        const errorMsg = error instanceof Error ? error.message : String(error);
        const isRetryable = this.isRetryableError(error);
        
        logger.error(`❌ Generation attempt ${attempt + 1} failed - Error: ${errorMsg}, Retryable: ${isRetryable}, Model: ${this.config.model}`);

        // If not retryable or last attempt, throw error
        if (!isRetryable || attempt === this.config.maxRetries - 1) {
          // Try fallback model on final attempt
          if (attempt === this.config.maxRetries - 1 && this.config.model !== 'openai/gpt-3.5-turbo') {
            logger.warn('🔄 Attempting fallback to GPT-3.5-turbo');
            return await this.fallbackGeneration(messages, options);
          }
          
          throw new Error(`Content generation failed after ${attempt + 1} attempts: ${errorMsg}`);
        }

        // Exponential backoff
        const waitTime = Math.min(1000 * Math.pow(2, attempt), 10000);
        logger.info(`⏳ Waiting ${waitTime}ms before retry...`);
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
    }

    throw new Error('Content generation failed: Maximum retries exceeded');
  }

  /**
   * Fallback generation using GPT-3.5-turbo
   */
  private async fallbackGeneration(
    messages: KimiK2Message[],
    options?: {
      temperature?: number;
      maxTokens?: number;
    }
  ): Promise<KimiK2Response> {
    try {
      const completion = await this.client.chat.completions.create({
        model: 'openai/gpt-3.5-turbo',
        messages: messages as any,
        temperature: options?.temperature ?? this.config.temperature,
        max_tokens: Math.min(options?.maxTokens ?? this.config.maxTokens, 4000),
        stream: false
      });

      const response = completion.choices[0];
      
      if (!response?.message?.content) {
        throw new Error('Empty response from fallback model');
      }

      logger.info('✅ Fallback generation successful');

      return {
        content: response.message.content,
        usage: completion.usage ? {
          prompt_tokens: completion.usage.prompt_tokens,
          completion_tokens: completion.usage.completion_tokens,
          total_tokens: completion.usage.total_tokens
        } : undefined,
        finish_reason: response.finish_reason || undefined
      };

    } catch (error) {
      logger.error(`❌ Fallback generation failed - Error: ${error}`);
      throw new Error('Both primary and fallback generation failed');
    }
  }

  /**
   * Check if error is retryable
   */
  private isRetryableError(error: any): boolean {
    if (!error) return false;
    
    const errorMessage = error.message || '';
    const statusCode = error.status || error.response?.status;
    
    // Rate limit errors
    if (statusCode === 429) return true;
    
    // Temporary server errors
    if (statusCode >= 500 && statusCode < 600) return true;
    
    // Timeout errors
    if (errorMessage.includes('timeout') || errorMessage.includes('ETIMEDOUT')) return true;
    
    // Network errors
    if (errorMessage.includes('ECONNRESET') || errorMessage.includes('ENOTFOUND')) return true;
    
    // OpenRouter specific errors that are retryable
    if (errorMessage.includes('temporarily unavailable')) return true;
    if (errorMessage.includes('model is currently overloaded')) return true;
    
    return false;
  }

  /**
   * Generate content with system prompt
   */
  async generateWithSystemPrompt(
    systemPrompt: string,
    userPrompt: string,
    options?: {
      temperature?: number;
      maxTokens?: number;
    }
  ): Promise<KimiK2Response> {
    const messages: KimiK2Message[] = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: userPrompt }
    ];
    
    return this.generateContent(messages, options);
  }

  /**
   * Simple content generation helper
   */
  async generate(prompt: string, options?: {
    temperature?: number;
    maxTokens?: number;
  }): Promise<string> {
    const response = await this.generateContent([
      { role: 'user', content: prompt }
    ], options);
    
    return response.content;
  }

  /**
   * Get current configuration
   */
  getConfig(): Required<KimiK2Config> {
    return { ...this.config };
  }

  /**
   * Update configuration
   */
  updateConfig(updates: Partial<KimiK2Config>): void {
    this.config = {
      ...this.config,
      ...updates
    };
    
    // Reinitialize client if API key changed
    if (updates.apiKey) {
      this.client = new OpenAI({
        baseURL: 'https://api.groq.com/openai/v1',
        apiKey: this.config.apiKey,
        timeout: this.config.timeout,
        maxRetries: 0
      });
    }
  }
}

// Factory function for backward compatibility
export function createKimiK2Client(apiKey: string, config?: Partial<KimiK2Config>): KimiK2Client {
  return new KimiK2Client({
    apiKey,
    ...config
  });
}