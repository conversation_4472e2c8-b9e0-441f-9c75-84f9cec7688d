/**
 * Invincible V.2 - Advanced Research Agent
 * Autonomous research with competitive intelligence and market analysis
 */

import { InvincibleV2State, StateManager, ResearchData } from '../core/state-schema';
import { TavilySearchTool, CompetitiveAnalysis, SEOIntelligence } from '../tools/tavily-search';
import { logger, createLogger, logPerformance } from '../utils/logger';

export interface ResearchAgentConfig {
  tavilyApiKey: string;
  maxSearchQueries?: number;
  searchDepth?: 'basic' | 'advanced';
  competitiveAnalysis?: boolean;
  trendAnalysis?: boolean;
}

export class ResearchAgent {
  private searchTool: TavilySearchTool;
  private config: ResearchAgentConfig;
  private researchLogger: ReturnType<typeof createLogger>;

  constructor(config: ResearchAgentConfig) {
    this.config = {
      maxSearchQueries: 10,
      searchDepth: 'advanced',
      competitiveAnalysis: true,
      trendAnalysis: true,
      ...config,
    };

    this.searchTool = new TavilySearchTool({
      apiKey: this.config.tavilyApiKey,
      searchDepth: this.config.searchDepth,
      maxResults: 12,
      includeImages: true,
      includeAnswer: true,
      includeRawContent: true,
    });
    
    this.researchLogger = createLogger({ 
      agentName: 'ResearchAgent',
      phase: 'research'
    });
    
    this.researchLogger.info('Research agent ready');
  }

  /**
   * Main research execution method
   */
  async execute(state: InvincibleV2State): Promise<InvincibleV2State> {
    const researchStartTime = Date.now();
    
    this.researchLogger.info(`Research started: "${state.requirements.topic}"`);
    
    try {
      // Update workflow status
      let updatedState = StateManager.updateWorkflow(state, {
        currentAgent: 'research_agent',
        progress: 10,
      });

      updatedState = StateManager.addMessage(updatedState, {
        agent: 'research_agent',
        action: 'starting_research',
        data: { topic: state.requirements.topic },
        status: 'in_progress',
      });

      // Phase 1: Core topic research
      const coreResearch = await this.conductCoreResearch(state.requirements.topic);
      updatedState = StateManager.updateResearch(updatedState, coreResearch);
      
      this.researchLogger.info(`Core research: ${coreResearch.results.length} results in ${Date.now() - researchStartTime}ms`);
      
      updatedState = StateManager.updateWorkflow(updatedState, { progress: 25 });
      updatedState = StateManager.addMessage(updatedState, {
        agent: 'research_agent',
        action: 'core_research_completed',
        data: { resultsCount: coreResearch.results.length },
        status: 'completed',
      });

      // Phase 2: Competitive analysis
      if (this.config.competitiveAnalysis) {
        const competitiveAnalysis = await this.conductCompetitiveAnalysis(state.requirements.topic);
        
        updatedState = StateManager.updateAnalysis(updatedState, {
          competitive: {
            topContent: competitiveAnalysis.competitorContent,
            contentGaps: competitiveAnalysis.contentGaps,
            opportunities: competitiveAnalysis.opportunities,
          }
        });

        updatedState = StateManager.updateWorkflow(updatedState, { progress: 50 });
        updatedState = StateManager.addMessage(updatedState, {
          agent: 'research_agent',
          action: 'competitive_analysis_completed',
          data: { 
            competitors: competitiveAnalysis.topCompetitors.length,
            opportunities: competitiveAnalysis.opportunities.length,
          },
          status: 'completed',
        });
      }

      // Phase 3: SEO intelligence gathering
      const seoIntelligence = await this.gatherSEOIntelligence(state.requirements.topic);
      
      updatedState = StateManager.updateAnalysis(updatedState, {
        seo: {
          ...updatedState.analysis.seo,
          searchIntent: seoIntelligence.searchIntent,
          primaryKeywords: seoIntelligence.relatedQueries.slice(0, 5),
          secondaryKeywords: seoIntelligence.relatedQueries.slice(5, 15),
        }
      });

      updatedState = StateManager.updateWorkflow(updatedState, { progress: 75 });
      updatedState = StateManager.addMessage(updatedState, {
        agent: 'research_agent',
        action: 'seo_intelligence_completed',
        data: { 
          searchIntent: seoIntelligence.searchIntent,
          keywordsFound: seoIntelligence.relatedQueries.length,
        },
        status: 'completed',
      });

      // Phase 4: Trend analysis (if enabled)
      if (this.config.trendAnalysis) {
        const trendAnalysis = await this.conductTrendAnalysis(state.requirements.topic);
        
        updatedState = StateManager.addMessage(updatedState, {
          agent: 'research_agent',
          action: 'trend_analysis_completed',
          data: { 
            emergingTrends: trendAnalysis.emergingTrends,
            recentDevelopments: trendAnalysis.recentDevelopments,
          },
          status: 'completed',
        });
      }

      // Phase 5: Comprehensive query variations
      const variationResearch = await this.conductVariationResearch(state.requirements.topic);
      variationResearch.forEach(research => {
        updatedState = StateManager.updateResearch(updatedState, research);
      });

      // Mark research as completed
      updatedState = {
        ...updatedState,
        research: {
          ...updatedState.research,
          completed: true,
        }
      };

      updatedState = StateManager.updateWorkflow(updatedState, {
        progress: 100,
        nextAgent: 'content_agent',
        completedAgents: [...updatedState.workflow.completedAgents, 'research_agent'],
      });

      updatedState = StateManager.addMessage(updatedState, {
        agent: 'research_agent',
        action: 'research_completed',
        data: { 
          totalQueries: updatedState.research.data.length,
          totalResults: updatedState.research.data.reduce((sum, r) => sum + r.results.length, 0),
        },
        status: 'completed',
      });

      return updatedState;

    } catch (error) {
      console.error('Research Agent error:', error);
      
      const errorState = StateManager.updateWorkflow(state, {
        errors: [...state.workflow.errors, `Research failed: ${error}`],
      });

      return StateManager.addMessage(errorState, {
        agent: 'research_agent',
        action: 'research_failed',
        data: { error: error.toString() },
        status: 'error',
      });
    }
  }

  /**
   * Conduct core research on the main topic
   */
  private async conductCoreResearch(topic: string): Promise<ResearchData> {
    const searchResponse = await this.searchTool.enhancedSearch(
      `${topic} comprehensive guide complete overview`,
      { maxResults: 15, searchDepth: 'advanced' }
    );

    return {
      query: `Core research: ${topic}`,
      results: searchResponse.results,
      competitorAnalysis: {
        topCompetitors: [],
        contentGaps: [],
        strengthsWeaknesses: [],
      },
      searchDepth: 'advanced',
      timestamp: Date.now(),
    };
  }

  /**
   * Conduct comprehensive competitive analysis
   */
  private async conductCompetitiveAnalysis(topic: string): Promise<CompetitiveAnalysis> {
    return await this.searchTool.competitiveAnalysis(topic);
  }

  /**
   * Gather SEO intelligence and search insights
   */
  private async gatherSEOIntelligence(topic: string): Promise<SEOIntelligence> {
    return await this.searchTool.seoIntelligence(topic);
  }

  /**
   * Conduct trend analysis for current market insights
   */
  private async conductTrendAnalysis(topic: string) {
    return await this.searchTool.trendAnalysis(topic);
  }

  /**
   * Research topic variations and related queries
   */
  private async conductVariationResearch(topic: string): Promise<ResearchData[]> {
    const variations = [
      `${topic} best practices tips`,
      `${topic} benefits advantages`,
      `${topic} challenges problems solutions`,
      `${topic} examples case studies`,
      `${topic} tools software resources`,
      `${topic} future trends 2025`,
    ];

    const searchResults = await this.searchTool.multiQueryResearch(topic, variations);
    
    return searchResults.map((result, index) => ({
      query: result.query,
      results: result.results,
      competitorAnalysis: {
        topCompetitors: [],
        contentGaps: [],
        strengthsWeaknesses: [],
      },
      searchDepth: 'advanced' as const,
      timestamp: Date.now() + index, // Slight offset for uniqueness
    }));
  }

  /**
   * Extract key insights from research data
   */
  extractKeyInsights(researchData: ResearchData[]): {
    topSources: string[];
    keyTopics: string[];
    expertQuotes: string[];
    statisticsData: string[];
    actionableInsights: string[];
  } {
    const allResults = researchData.flatMap(r => r.results);
    
    // Extract top sources (domains with highest scores)
    const domainScores = new Map<string, number>();
    allResults.forEach(result => {
      const domain = result.domain;
      domainScores.set(domain, (domainScores.get(domain) || 0) + result.score);
    });

    const topSources = Array.from(domainScores.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([domain]) => domain);

    // Extract key topics (frequent terms across results)
    const allText = allResults.map(r => r.content).join(' ').toLowerCase();
    const words = allText.split(/\W+/).filter(word => word.length > 4);
    const wordCounts = new Map<string, number>();
    
    words.forEach(word => {
      wordCounts.set(word, (wordCounts.get(word) || 0) + 1);
    });

    const keyTopics = Array.from(wordCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 20)
      .map(([word]) => word);

    // Extract expert quotes (sentences with attribution)
    const expertQuotes = allResults
      .flatMap(result => {
        const sentences = result.content.split(/[.!?]+/);
        return sentences.filter(sentence => 
          sentence.includes('expert') || 
          sentence.includes('according to') || 
          sentence.includes('research shows')
        );
      })
      .slice(0, 10);

    // Extract statistics (sentences with numbers/percentages)
    const statisticsData = allResults
      .flatMap(result => {
        const sentences = result.content.split(/[.!?]+/);
        return sentences.filter(sentence => 
          /\d+%|\d+\s*(percent|million|billion|thousand)/.test(sentence)
        );
      })
      .slice(0, 15);

    // Generate actionable insights
    const actionableInsights = [
      'Focus on comprehensive coverage to outperform competitors',
      'Include current statistics and data to establish authority',
      'Address common user questions and pain points',
      'Leverage expert opinions and research citations',
      'Optimize for voice search and AI assistants',
    ];

    return {
      topSources,
      keyTopics,
      expertQuotes,
      statisticsData,
      actionableInsights,
    };
  }

  /**
   * Generate content outline based on research
   */
  generateContentOutline(
    topic: string, 
    researchData: ResearchData[], 
    competitiveAnalysis: any,
    requirements: any
  ): string[] {
    const insights = this.extractKeyInsights(researchData);
    const targetLength = requirements.contentLength || 2000;
    
    // Base outline structure
    const outline = [
      `Introduction to ${topic}`,
      `What is ${topic}? - Complete Definition and Overview`,
    ];

    // Add sections based on content length and research insights
    if (targetLength >= 1500) {
      outline.push(
        `Key Benefits and Advantages of ${topic}`,
        `How ${topic} Works - Step-by-Step Process`,
        `Best Practices and Professional Tips`,
      );
    }

    if (targetLength >= 2000) {
      outline.push(
        `Common Challenges and How to Overcome Them`,
        `Real-World Examples and Case Studies`,
        `Tools and Resources for ${topic}`,
      );
    }

    if (targetLength >= 3000) {
      outline.push(
        `Advanced Techniques and Strategies`,
        `Future Trends and Predictions`,
        `Expert Insights and Industry Analysis`,
      );
    }

    // Add conclusion
    outline.push(`Conclusion and Next Steps`);

    // Add FAQ section for voice search optimization
    outline.push(`Frequently Asked Questions (FAQ)`);

    return outline;
  }

  /**
   * Validate research completeness
   */
  validateResearchCompleteness(state: InvincibleV2State): {
    isComplete: boolean;
    missingElements: string[];
    qualityScore: number;
  } {
    const missingElements: string[] = [];
    let qualityScore = 0;

    // Check research data
    if (state.research.data.length === 0) {
      missingElements.push('No research data found');
    } else {
      qualityScore += 25;
    }

    // Check competitive analysis
    if (state.analysis.competitive.topContent.length === 0) {
      missingElements.push('Missing competitive analysis');
    } else {
      qualityScore += 25;
    }

    // Check SEO data
    if (state.analysis.seo.primaryKeywords.length === 0) {
      missingElements.push('Missing SEO keyword analysis');
    } else {
      qualityScore += 25;
    }

    // Check research depth
    const totalResults = state.research.data.reduce((sum, r) => sum + r.results.length, 0);
    if (totalResults < 50) {
      missingElements.push('Insufficient research depth');
    } else {
      qualityScore += 25;
    }

    return {
      isComplete: missingElements.length === 0,
      missingElements,
      qualityScore,
    };
  }
}

/**
 * Factory function to create research agent
 */
export function createResearchAgent(config: ResearchAgentConfig): ResearchAgent {
  return new ResearchAgent(config);
}