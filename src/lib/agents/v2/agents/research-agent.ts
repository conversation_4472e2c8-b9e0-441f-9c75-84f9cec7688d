/**
 * Invincible V.2 - Enhanced Research Agent
 * Robust research with competitive intelligence and SEO analysis
 */

import { 
  InvincibleV2State, 
  StateManager, 
  ResearchData, 
  CompetitiveAnalysis,
  ContentRequirements 
} from '../core/state-schema';
import { TavilySearchTool } from '../tools/tavily-search';
import { logger } from '../utils/logger';

export interface ResearchAgentConfig {
  tavilyApiKey: string;
  maxSearchQueries?: number;
  searchDepth?: 'basic' | 'advanced';
  competitiveAnalysis?: boolean;
  minResultsPerQuery?: number;
}

export class ResearchAgent {
  private searchTool: TavilySearchTool;
  private config: ResearchAgentConfig;

  constructor(config: ResearchAgentConfig) {
    this.config = {
      maxSearchQueries: 8,
      searchDepth: 'advanced',
      competitiveAnalysis: true,
      minResultsPerQuery: 3,
      ...config
    };

    this.searchTool = new TavilySearchTool({
      apiKey: this.config.tavilyApi<PERSON>ey,
      searchDepth: this.config.searchDepth,
      maxResults: 10,
      includeAnswer: true,
      includeImages: false,
      includeRawContent: true
    });

    logger.info('✅ Research Agent initialized');
  }

  /**
   * Execute research phase
   */
  async execute(state: InvincibleV2State): Promise<InvincibleV2State> {
    const startTime = Date.now();
    
    logger.info(`🔍 Starting research for: "${state.requirements.topic}"`);
    
    try {
      // Update workflow
      let updatedState = StateManager.updateWorkflow(state, {
        currentAgent: 'research_agent'
      });

      updatedState = StateManager.addMessage(updatedState, {
        agent: 'research_agent',
        action: 'research_started',
        data: { topic: state.requirements.topic },
        status: 'in_progress'
      });

      // Phase 1: Core topic research
      logger.info('📌 Phase 1: Core topic research');
      const coreQueries = this.generateCoreQueries(state.requirements);
      updatedState = await this.conductResearch(updatedState, coreQueries, 'core');

      // Phase 2: Competitive analysis
      if (this.config.competitiveAnalysis) {
        logger.info('🏆 Phase 2: Competitive analysis');
        const competitiveQueries = this.generateCompetitiveQueries(state.requirements);
        updatedState = await this.conductResearch(updatedState, competitiveQueries, 'competitive');
        
        // Analyze competition
        const competitiveAnalysis = await this.analyzeCompetition(updatedState);
        updatedState = StateManager.updateCompetitiveAnalysis(updatedState, competitiveAnalysis);
      }

      // Phase 3: SEO keyword research
      logger.info('🎯 Phase 3: SEO keyword research');
      const seoQueries = this.generateSEOQueries(state.requirements);
      updatedState = await this.conductResearch(updatedState, seoQueries, 'seo');
      
      // Extract keywords
      const keywords = this.extractKeywords(updatedState);
      updatedState = StateManager.updateSEO(updatedState, {
        primaryKeywords: keywords.primary,
        secondaryKeywords: keywords.secondary,
        competitorKeywords: keywords.competitor
      });

      // Phase 4: Long-tail and related searches
      logger.info('🔗 Phase 4: Long-tail research');
      const longtailQueries = this.generateLongtailQueries(state.requirements, keywords);
      updatedState = await this.conductResearch(updatedState, longtailQueries, 'longtail');

      // Mark research as completed
      updatedState = StateManager.markPhaseComplete(updatedState, 'research');
      updatedState = StateManager.updateWorkflow(updatedState, {
        completedAgent: 'research_agent'
      });

      // Log summary
      const totalResults = updatedState.research.data.reduce((sum, d) => sum + d.results.length, 0);
      logger.info(`✅ Research completed - Queries: ${updatedState.research.data.length}, Results: ${totalResults}, Competitors: ${updatedState.research.competitiveAnalysis.topCompetitors.length}, Keywords: ${keywords.primary.length + keywords.secondary.length}, Duration: ${(Date.now() - startTime) / 1000}s`);

      updatedState = StateManager.addMessage(updatedState, {
        agent: 'research_agent',
        action: 'research_completed',
        data: { 
          totalQueries: updatedState.research.data.length,
          totalResults,
          keywords: keywords.primary.length
        },
        status: 'completed'
      });

      return updatedState;

    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : String(error);
      logger.error('❌ Research Agent failed: ' + errorMsg);
      
      state = StateManager.addError(state, {
        agent: 'research_agent',
        error: errorMsg,
        recoverable: true
      });

      throw error;
    }
  }

  /**
   * Generate core research queries
   */
  private generateCoreQueries(requirements: ContentRequirements): string[] {
    const { topic, contentType } = requirements;
    const queries: string[] = [topic]; // Always include exact topic

    // Add content-type specific queries
    switch (contentType) {
      case 'how-to':
        queries.push(
          `how to ${topic} step by step`,
          `${topic} tutorial guide`,
          `${topic} for beginners`
        );
        break;
      case 'review':
        queries.push(
          `${topic} review 2025`,
          `${topic} pros and cons`,
          `best ${topic} comparison`
        );
        break;
      case 'listicle':
        queries.push(
          `best ${topic} list`,
          `top 10 ${topic}`,
          `${topic} examples`
        );
        break;
      case 'guide':
        queries.push(
          `${topic} complete guide`,
          `${topic} explained`,
          `everything about ${topic}`
        );
        break;
      default:
        queries.push(
          `${topic} comprehensive information`,
          `${topic} latest updates 2025`
        );
    }

    return queries.slice(0, Math.ceil((this.config.maxSearchQueries || 8) / 2));
  }

  /**
   * Generate competitive analysis queries
   */
  private generateCompetitiveQueries(requirements: ContentRequirements): string[] {
    const { topic } = requirements;
    return [
      `best ${topic} articles`,
      `${topic} top ranking content`,
      `${topic} comprehensive guide`
    ];
  }

  /**
   * Generate SEO-focused queries
   */
  private generateSEOQueries(requirements: ContentRequirements): string[] {
    const { topic } = requirements;
    return [
      `${topic} frequently asked questions`,
      `${topic} common problems`,
      `${topic} related searches`
    ];
  }

  /**
   * Generate long-tail queries
   */
  private generateLongtailQueries(requirements: ContentRequirements, keywords: {
    primary: string[];
    secondary: string[];
    competitor: string[];
  }): string[] {
    const { topic } = requirements;
    const queries: string[] = [];

    // Use primary keywords for long-tail
    keywords.primary.slice(0, 2).forEach((keyword: string) => {
      queries.push(`${topic} ${keyword}`);
    });

    return queries.slice(0, 2);
  }

  /**
   * Conduct research for given queries
   */
  private async conductResearch(
    state: InvincibleV2State, 
    queries: string[], 
    phase: string
  ): Promise<InvincibleV2State> {
    let updatedState = state;

    for (const query of queries) {
      try {
        logger.info(`🔎 Searching: "${query}" (${phase})`);
        
        const searchResponse = await this.searchTool.search(query);
        
        if (searchResponse.results.length >= (this.config.minResultsPerQuery || 3)) {
          const researchData: ResearchData = {
            query,
            results: searchResponse.results.map(r => ({
              title: r.title,
              url: r.url,
              content: r.content,
              score: r.score,
              publishedDate: r.publishedDate
            })),
            metadata: {
              searchDepth: this.config.searchDepth!,
              totalResults: searchResponse.results.length,
              responseTime: 0 // Set to 0 for now
            },
            timestamp: Date.now()
          };

          updatedState = StateManager.updateResearch(updatedState, researchData);
          
          logger.info(`✅ Found ${searchResponse.results.length} results for: "${query}"`);
        } else {
          logger.warn(`⚠️ Insufficient results for: "${query}" (${searchResponse.results.length} found)`);
        }

        // Small delay between searches
        await new Promise(resolve => setTimeout(resolve, 500));

      } catch (error) {
        logger.error(`❌ Search failed for: "${query}" - ${String(error)}`);
        // Continue with other queries
      }
    }

    return updatedState;
  }

  /**
   * Analyze competition from research data
   */
  private async analyzeCompetition(state: InvincibleV2State): Promise<Partial<CompetitiveAnalysis>> {
    const competitorData: Map<string, any> = new Map();
    
    // Group results by domain
    state.research.data.forEach(research => {
      research.results.forEach(result => {
        try {
          const domain = new URL(result.url).hostname;
          if (!competitorData.has(domain)) {
            competitorData.set(domain, []);
          }
          competitorData.get(domain).push(result);
        } catch (e) {
          // Invalid URL, skip
        }
      });
    });

    // Analyze top competitors
    const topCompetitors = Array.from(competitorData.entries())
      .sort((a, b) => b[1].length - a[1].length)
      .slice(0, 5)
      .map(([domain, results]) => {
        const avgContentLength = results.reduce((sum: number, r: any) => 
          sum + (r.content?.length || 0), 0) / results.length;
        
        return {
          url: `https://${domain}`,
          title: results[0]?.title || domain,
          wordCount: Math.round(avgContentLength / 5), // Rough word count estimate
          strengths: this.identifyStrengths(results),
          weaknesses: this.identifyWeaknesses(results)
        };
      });

    // Extract content gaps and opportunities
    const allContent = state.research.data.flatMap(d => d.results.map(r => r.content));
    const contentGaps = this.identifyContentGaps(allContent, state.requirements.topic);
    const opportunities = this.identifyOpportunities(allContent);

    // Calculate average word count from top results
    const avgWordCount = topCompetitors.reduce((sum, c) => sum + c.wordCount, 0) / 
                        (topCompetitors.length || 1);

    // Extract common keywords
    const commonKeywords = this.extractCommonKeywords(allContent);

    return {
      topCompetitors,
      contentGaps,
      opportunities,
      averageWordCount: Math.round(avgWordCount * 1.1), // Aim for 10% more
      commonKeywords
    };
  }

  /**
   * Identify content strengths
   */
  private identifyStrengths(results: any[]): string[] {
    const strengths: string[] = [];
    
    // Analyze common patterns
    const hasImages = results.some(r => r.content?.includes('<img') || r.content?.includes('image'));
    const hasData = results.some(r => r.content?.match(/\d+%|\d+ percent/));
    const hasExamples = results.some(r => r.content?.includes('example') || r.content?.includes('case study'));
    
    if (hasImages) strengths.push('Visual content');
    if (hasData) strengths.push('Data-driven insights');
    if (hasExamples) strengths.push('Practical examples');
    if (results.length > 3) strengths.push('Comprehensive coverage');
    
    return strengths;
  }

  /**
   * Identify content weaknesses
   */
  private identifyWeaknesses(results: any[]): string[] {
    const weaknesses: string[] = [];
    
    const avgLength = results.reduce((sum, r) => sum + (r.content?.length || 0), 0) / results.length;
    
    if (avgLength < 2000) weaknesses.push('Limited depth');
    if (!results.some(r => r.publishedDate?.includes('2025') || r.publishedDate?.includes('2024'))) {
      weaknesses.push('Outdated information');
    }
    if (!results.some(r => r.content?.includes('FAQ') || r.content?.includes('frequently asked'))) {
      weaknesses.push('No FAQ section');
    }
    
    return weaknesses;
  }

  /**
   * Identify content gaps
   */
  private identifyContentGaps(contents: string[], topic: string): string[] {
    const gaps: string[] = [];
    const combinedContent = contents.join(' ').toLowerCase();
    
    // Check for common missing elements
    const gapChecks = [
      { check: 'step by step', gap: 'Detailed step-by-step instructions' },
      { check: 'beginner', gap: 'Beginner-friendly explanations' },
      { check: 'advanced', gap: 'Advanced techniques and tips' },
      { check: 'troubleshoot', gap: 'Troubleshooting guide' },
      { check: 'alternative', gap: 'Alternative approaches' },
      { check: (content: string) => content.includes('cost') || content.includes('price'), gap: 'Cost analysis and budgeting' },
      { check: 'comparison', gap: 'Detailed comparisons' },
      { check: 'case study', gap: 'Real-world case studies' }
    ];
    
    gapChecks.forEach(({ check, gap }) => {
      if (typeof check === 'string') {
        if (!combinedContent.includes(check)) {
          gaps.push(gap);
        }
      } else if (typeof check === 'function') {
        if (!check(combinedContent)) {
          gaps.push(gap);
        }
      }
    });
    
    return gaps.slice(0, 5);
  }

  /**
   * Identify opportunities
   */
  private identifyOpportunities(contents: string[]): string[] {
    const opportunities: string[] = [
      'Create more comprehensive coverage than competitors',
      'Include current 2025 data and trends',
      'Add interactive elements or tools',
      'Provide downloadable resources',
      'Include expert opinions and quotes'
    ];
    
    return opportunities.slice(0, 4);
  }

  /**
   * Extract keywords from research
   */
  private extractKeywords(state: InvincibleV2State): {
    primary: string[];
    secondary: string[];
    competitor: string[];
  } {
    const keywordMap: Map<string, number> = new Map();
    const topic = state.requirements.topic.toLowerCase();
    
    // Extract keywords from all content
    state.research.data.forEach(research => {
      research.results.forEach(result => {
        // Extract from titles
        const titleWords = result.title.toLowerCase()
          .split(/\s+/)
          .filter(word => word.length > 3 && !this.isStopWord(word));
        
        titleWords.forEach(word => {
          keywordMap.set(word, (keywordMap.get(word) || 0) + 2); // Title words weighted higher
        });
        
        // Extract from content
        const contentWords = result.content.toLowerCase()
          .split(/\s+/)
          .filter(word => word.length > 4 && !this.isStopWord(word))
          .slice(0, 100); // Analyze first 100 words
        
        contentWords.forEach(word => {
          keywordMap.set(word, (keywordMap.get(word) || 0) + 1);
        });
      });
    });
    
    // Sort by frequency
    const sortedKeywords = Array.from(keywordMap.entries())
      .sort((a, b) => b[1] - a[1])
      .map(([keyword]) => keyword)
      .filter(keyword => keyword !== topic && !topic.includes(keyword));

    return {
      primary: sortedKeywords.slice(0, 5),
      secondary: sortedKeywords.slice(5, 15),
      competitor: this.extractCommonKeywords(
        state.research.data.flatMap(d => d.results.map(r => r.content))
      ).slice(0, 10)
    };
  }

  /**
   * Extract common keywords from content
   */
  private extractCommonKeywords(contents: string[]): string[] {
    const keywordFreq: Map<string, number> = new Map();
    
    contents.forEach(content => {
      const words = content.toLowerCase()
        .split(/\s+/)
        .filter(word => word.length > 4 && !this.isStopWord(word));
      
      words.forEach(word => {
        keywordFreq.set(word, (keywordFreq.get(word) || 0) + 1);
      });
    });
    
    return Array.from(keywordFreq.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 20)
      .map(([keyword]) => keyword);
  }

  /**
   * Check if word is a stop word
   */
  private isStopWord(word: string): boolean {
    const stopWords = new Set([
      'the', 'is', 'at', 'which', 'on', 'and', 'a', 'an', 'in', 'to',
      'for', 'of', 'with', 'as', 'by', 'that', 'this', 'it', 'from',
      'be', 'are', 'been', 'being', 'have', 'has', 'had', 'do', 'does',
      'did', 'will', 'would', 'should', 'could', 'may', 'might', 'must',
      'can', 'about', 'after', 'all', 'also', 'but', 'her', 'him', 'his',
      'how', 'its', 'more', 'not', 'only', 'or', 'other', 'our', 'out',
      'so', 'some', 'such', 'than', 'their', 'them', 'then', 'there',
      'these', 'they', 'those', 'through', 'up', 'very', 'was', 'way',
      'we', 'well', 'what', 'when', 'where', 'who', 'why', 'you', 'your'
    ]);
    
    return stopWords.has(word);
  }
}