/**
 * Invincible V.2 - Enhanced Content Generation Agent
 * Human-like writing with competitive intelligence and superior quality
 */

import { InvincibleV2State, ContentRequirements, StateManager, AgentMessage } from '../core/state-schema';
import { logger } from '../utils/logger';
import { safeJsonParse } from '../utils/json-parser';
import { TavilySearchTool } from '../tools/tavily-search';
import { KimiK2Client } from '../tools/kimi-k2-client';

interface ContentAgentConfig {
  groqApiKey: string;
  tavilyApiKey: string;
  maxRetries: number;
}

interface ContentStrategy {
  approach: string;
  writingStyle: string;
  structureType: string;
  tone: string;
  focusAreas: string[];
}

interface ContentOutline {
  sections: Array<{
    heading: string;
    level: number;
    estimatedWords: number;
    keyPoints: string[];
  }>;
  totalEstimatedWords: number;
}

export class ContentAgent {
  private config: ContentAgentConfig;
  private kimiClient: KimiK2Client;

  constructor(config: ContentAgentConfig) {
    this.config = config;
    
    // Initialize Groq client with hardcoded API key and model
    this.kimiClient = new KimiK2Client({
      apiKey: '********************************************************'
    });

    logger.info('✅ Content Agent initialized');
  }

  /**
   * Execute content generation
   */
  async execute(state: InvincibleV2State): Promise<InvincibleV2State> {
    const startTime = Date.now();
    
    logger.info(`✍️ Starting content generation for: "${state.requirements.topic}"`);
    
    try {
      // Update workflow
      let updatedState = StateManager.updateWorkflow(state, {
        currentAgent: 'content_agent'
      });

      updatedState = StateManager.addMessage(updatedState, {
        agent: 'content_agent',
        action: 'content_generation_started',
        data: { 
          topic: state.requirements.topic,
          targetLength: state.requirements.contentLength
        },
        status: 'in_progress'
      });

      // Phase 1: Analyze research data
      logger.info('📊 Phase 1: Analyzing research data');
      const researchAnalysis = this.analyzeResearchData(updatedState);

      // Phase 2: Determine content strategy
      logger.info('🎯 Phase 2: Determining content strategy');
      const strategy = await this.determineContentStrategy(updatedState, researchAnalysis);
      
      updatedState = StateManager.addMessage(updatedState, {
        agent: 'content_agent',
        action: 'strategy_determined',
        data: strategy,
        status: 'completed'
      });

      // Phase 3: Generate content outline
      logger.info('📝 Phase 3: Generating content outline');
      const outline = await this.generateOutline(updatedState, strategy);
      
      updatedState = StateManager.updateContent(updatedState, {
        outline: outline.sections
      });

      // Phase 4: Generate main content
      logger.info('🚀 Phase 4: Generating main content');
      const generatedContent = await this.generateContent(updatedState, strategy, outline);
      
      updatedState = StateManager.updateContent(updatedState, {
        title: generatedContent.title,
        content: generatedContent.content,
        wordCount: generatedContent.wordCount,
        readabilityScore: generatedContent.readabilityScore
      });

      // Phase 5: Generate meta description
      logger.info('🏷️ Phase 5: Generating meta description');
      const metaDescription = await this.generateMetaDescription(generatedContent.title, generatedContent.content);
      
      updatedState = StateManager.updateContent(updatedState, {
        metaDescription
      });

      // Phase 6: Apply humanization (enabled by default)
      logger.info('🤖 Phase 6: Applying humanization');
      const humanized = await this.applyHumanization(generatedContent.content);
    
      updatedState = StateManager.updateContent(updatedState, {
        content: humanized.content,
        humanizationApplied: humanized.techniques
      });

      // Mark content generation as completed
      updatedState = StateManager.markPhaseComplete(updatedState, 'content');
      updatedState = StateManager.updateWorkflow(updatedState, {
        completedAgent: 'content_agent'
      });

      const duration = (Date.now() - startTime) / 1000;
      logger.info(`✅ Content generation completed - Words: ${generatedContent.wordCount}, Duration: ${duration}s`);

      updatedState = StateManager.addMessage(updatedState, {
        agent: 'content_agent',
        action: 'content_generation_completed',
        data: { 
          wordCount: generatedContent.wordCount,
          duration
        },
        status: 'completed'
      });

      return updatedState;

    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : String(error);
      logger.error('❌ Content Agent failed: ' + errorMsg);
      
      state = StateManager.addError(state, {
        agent: 'content_agent',
        error: errorMsg,
        recoverable: true
      });

      throw error;
    }
  }

  /**
   * Analyze research data for content insights
   */
  private analyzeResearchData(state: InvincibleV2State): {
    topThemes: string[];
    contentGaps: string[];
    competitorStrengths: string[];
    keyInsights: string[];
    averageContentLength: number;
  } {
    const research = state.research;
    const themes: Map<string, number> = new Map();

    // Extract themes from research
    research.data.forEach(data => {
      data.results.forEach(result => {
        // Simple theme extraction from titles and content
        const words = (result.title + ' ' + result.content.substring(0, 200))
          .toLowerCase()
          .split(/\s+/)
          .filter(word => word.length > 5);
        
        words.forEach(word => {
          themes.set(word, (themes.get(word) || 0) + 1);
        });
      });
    });

    // Sort themes by frequency
    const topThemes = Array.from(themes.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([theme]) => theme);
    
    // Get competitor insights
    const competitorStrengths = research.competitiveAnalysis.topCompetitors
      .flatMap(c => c.strengths)
      .filter((v, i, a) => a.indexOf(v) === i)
      .slice(0, 5);

    return {
      topThemes,
      contentGaps: research.competitiveAnalysis.contentGaps,
      competitorStrengths,
      keyInsights: [
        `Target word count should be around ${research.competitiveAnalysis.averageWordCount} words`,
        `Focus on ${state.requirements.contentType} format`,
        `Optimize for keywords: ${state.optimization.seo.primaryKeywords.join(', ')}`
      ],
      averageContentLength: research.competitiveAnalysis.averageWordCount
    };
  }

  /**
   * Determine content strategy
   */
  private async determineContentStrategy(
    state: InvincibleV2State, 
    analysis: any
  ): Promise<ContentStrategy> {
    const prompt = `You are a content strategist creating a strategy for an article about "${state.requirements.topic}".

Content Requirements:
- Type: ${state.requirements.contentType}
- Target Length: ${state.requirements.contentLength} words
- Tone: ${state.requirements.tone}
- Audience: ${state.requirements.targetAudience}

Research Insights:
- Top Themes: ${analysis.topThemes.slice(0, 5).join(', ')}
- Content Gaps to Fill: ${analysis.contentGaps.join(', ')}
- Competitor Strengths: ${analysis.competitorStrengths.join(', ')}
- Average Competitor Length: ${analysis.averageContentLength} words

Create a content strategy that:
1. Addresses all content gaps
2. Exceeds competitor quality
3. Matches the requested tone and audience
4. Optimizes for both SEO and user engagement

Return JSON:
{
  "approach": "How to approach this content",
  "writingStyle": "Specific writing style to use",
  "structureType": "Article structure type",
  "tone": "Refined tone description",
  "focusAreas": ["Area 1", "Area 2", "Area 3"]
}`;

    try {
      const response = await this.kimiClient.generateContent([
        { role: 'user', content: prompt }
      ]);

      return safeJsonParse(response.content, {
        approach: 'Comprehensive and informative',
        writingStyle: 'Clear, engaging, and authoritative',
        structureType: 'Problem-solution with examples',
        tone: state.requirements.tone,
        focusAreas: ['Practical value', 'Expert insights', 'Actionable tips']
      }) as ContentStrategy;

    } catch (error) {
      logger.warn('Strategy generation failed, using defaults');
      return {
        approach: 'Comprehensive coverage with practical focus',
        writingStyle: 'Clear and engaging',
        structureType: 'Standard article structure',
        tone: state.requirements.tone,
        focusAreas: ['Value delivery', 'Clear explanations', 'Practical applications']
      };
    }
  }

  /**
   * Generate content outline
   */
  private async generateOutline(
    state: InvincibleV2State,
    strategy: ContentStrategy
  ): Promise<ContentOutline> {
    const targetWords = state.requirements.contentLength;
    const prompt = `Create a detailed outline for a ${state.requirements.contentType} about "${state.requirements.topic}".

Target: ${targetWords} words
Strategy: ${strategy.approach}
Structure: ${strategy.structureType}

Requirements:
1. Include all sections needed for comprehensive coverage
2. Distribute word count appropriately
3. Address content gaps: ${state.research.competitiveAnalysis.contentGaps.join(', ')}
4. Include sections that competitors miss

Create an outline with:
- Clear hierarchy (H1, H2, H3)
- Estimated word count per section
- Key points to cover in each section

Return JSON:
{
  "sections": [
    {
      "heading": "Section Title",
      "level": 1,
      "estimatedWords": 200,
      "keyPoints": ["Point 1", "Point 2"]
    }
  ],
  "totalEstimatedWords": ${targetWords}
}`;

    try {
      const response = await this.kimiClient.generateContent([
        { role: 'user', content: prompt }
      ]);

      const outline = safeJsonParse(response.content, null) as any;
      
      if (outline && outline.sections && Array.isArray(outline.sections)) {
      return {
          sections: outline.sections,
          totalEstimatedWords: outline.totalEstimatedWords || targetWords
      };
      }
      
      // Fallback outline
      return this.generateDefaultOutline(state);

    } catch (error) {
      logger.warn('Outline generation failed, using default');
      return this.generateDefaultOutline(state);
    }
  }

  /**
   * Generate default outline
   */
  private generateDefaultOutline(state: InvincibleV2State): ContentOutline {
    const targetWords = state.requirements.contentLength;
      const sections = [
      { heading: `Introduction to ${state.requirements.topic}`, level: 1, estimatedWords: Math.round(targetWords * 0.1), keyPoints: ['Hook', 'Overview', 'Value proposition'] },
      { heading: `Understanding ${state.requirements.topic}`, level: 2, estimatedWords: Math.round(targetWords * 0.15), keyPoints: ['Definition', 'Key concepts', 'Importance'] },
      { heading: 'Key Benefits and Advantages', level: 2, estimatedWords: Math.round(targetWords * 0.15), keyPoints: ['Main benefits', 'Real-world impact', 'ROI'] },
      { heading: 'How It Works', level: 2, estimatedWords: Math.round(targetWords * 0.2), keyPoints: ['Process', 'Steps', 'Examples'] },
      { heading: 'Best Practices', level: 2, estimatedWords: Math.round(targetWords * 0.15), keyPoints: ['Tips', 'Common mistakes', 'Expert advice'] },
      { heading: 'Real-World Applications', level: 2, estimatedWords: Math.round(targetWords * 0.1), keyPoints: ['Case studies', 'Success stories'] },
      { heading: 'Getting Started', level: 2, estimatedWords: Math.round(targetWords * 0.1), keyPoints: ['First steps', 'Resources', 'Tools'] },
      { heading: 'Conclusion', level: 1, estimatedWords: Math.round(targetWords * 0.05), keyPoints: ['Summary', 'Next steps', 'Call to action'] }
      ];

      return {
        sections,
      totalEstimatedWords: targetWords
      };
  }

  /**
   * Generate the main content
   */
  private async generateContent(
    state: InvincibleV2State,
    strategy: ContentStrategy,
    outline: ContentOutline
  ): Promise<{
    title: string;
    content: string;
    wordCount: number;
    readabilityScore: number;
  }> {
    const systemPrompt = `You are an expert content writer creating ${state.requirements.contentType} content that ranks #1 and provides exceptional value.

Writing Guidelines:
- Tone: ${strategy.tone}
- Style: ${strategy.writingStyle}
- Audience: ${state.requirements.targetAudience}
- Focus Areas: ${strategy.focusAreas.join(', ')}

Content Requirements:
- EXACT word count: ${state.requirements.contentLength} words
- Include keywords naturally: ${state.optimization.seo.primaryKeywords.join(', ')}
- Address content gaps: ${state.research.competitiveAnalysis.contentGaps.join(', ')}
- Outperform competitors who average ${state.research.competitiveAnalysis.averageWordCount} words

Quality Standards:
- Write like a human expert, not AI
- Use varied sentence structures
- Include specific examples and data
- Add personal insights and expertise
- Create engaging, scannable content
- Optimize for featured snippets`;

    const userPrompt = `Write a comprehensive ${state.requirements.contentType} about "${state.requirements.topic}" following this outline:

${outline.sections.map(section => 
  `${section.level === 1 ? '# ' : section.level === 2 ? '## ' : '### '}${section.heading} (${section.estimatedWords} words)
  Key points: ${section.keyPoints.join(', ')}`
).join('\n\n')}

Custom Instructions: ${state.requirements.customInstructions || 'None'}

Requirements:
1. Start with a compelling title that includes the main keyword
2. Write EXACTLY ${state.requirements.contentLength} words
3. Make it better than any existing content on this topic
4. Include practical, actionable information
5. Use a conversational yet authoritative tone`;

    try {
      const response = await this.kimiClient.generateContent([
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ]);

      const content = response.content;
      
      // Extract title (first line)
      const lines = content.split('\n');
      const title = lines[0].replace(/^#\s*/, '').trim();
      const bodyContent = lines.slice(1).join('\n').trim();
      
      // Calculate word count
      const wordCount = bodyContent.split(/\s+/).filter(word => word.length > 0).length;
      
      // Simple readability score (based on sentence length)
      const sentences = bodyContent.split(/[.!?]+/).filter(s => s.trim().length > 0);
      const avgSentenceLength = sentences.reduce((sum, s) => sum + s.split(/\s+/).length, 0) / sentences.length;
      const readabilityScore = Math.max(0, Math.min(100, 100 - (avgSentenceLength - 15) * 2));

      return {
        title,
        content: bodyContent,
        wordCount,
        readabilityScore
      };

    } catch (error) {
      logger.error('Content generation failed: ' + String(error));
      throw new Error('Failed to generate content');
    }
  }

  /**
   * Generate meta description
   */
  private async generateMetaDescription(title: string, content: string): Promise<string> {
    const prompt = `Create a compelling meta description for this article:

Title: ${title}
Content Preview: ${content.substring(0, 500)}...

Requirements:
- 150-160 characters
- Include main keyword naturally
- Create curiosity and value proposition
- Action-oriented language`;

    try {
      const response = await this.kimiClient.generateContent([
        { role: 'user', content: prompt }
      ]);

      const metaDesc = response.content.trim().replace(/^["']|["']$/g, '');
      
      // Ensure it's within character limits
      if (metaDesc.length > 160) {
        return metaDesc.substring(0, 157) + '...';
      }
      
      return metaDesc;

    } catch (error) {
      logger.warn('Meta description generation failed, using default');
      return `Discover everything about ${title}. Comprehensive guide with expert insights, practical tips, and actionable strategies.`;
    }
  }

  /**
   * Apply humanization techniques
   */
  private async applyHumanization(content: string): Promise<{
    content: string;
    techniques: string[];
  }> {
    const techniques: string[] = [];
    let humanizedContent = content;

    // 1. Add conversational elements
    const conversationalReplacements = [
      { from: /\bIt is important to\b/g, to: "Here's something important:" },
      { from: /\bFurthermore\b/g, to: "What's more" },
      { from: /\bHowever\b/g, to: "But here's the thing" },
      { from: /\bIn conclusion\b/g, to: "So, to wrap things up" },
      { from: /\bAdditionally\b/g, to: "Also" }
    ];

    conversationalReplacements.forEach(({ from, to }) => {
      if (humanizedContent.match(from)) {
        humanizedContent = humanizedContent.replace(from, to);
        techniques.push('Conversational replacements');
        }
    });

    // 2. Add personal touches
    if (!humanizedContent.includes('I ') && !humanizedContent.includes("I've")) {
      const sentences = humanizedContent.split('. ');
      const insertIndex = Math.floor(sentences.length / 3);
      sentences.splice(insertIndex, 0, "I've found that this approach works particularly well in practice");
      humanizedContent = sentences.join('. ');
      techniques.push('Personal experience added');
    }

    // 3. Vary sentence structure
    const sentencePatterns = humanizedContent.match(/\. [A-Z][^.!?]*[.!?]/g) || [];
    if (sentencePatterns.length > 5) {
      // Add some short sentences
      humanizedContent = humanizedContent.replace(
        /(\. )([A-Z][^.!?]{100,}[.!?])/g,
        '$1$2 Simple as that.'
      );
      techniques.push('Sentence variation');
        }
        
    // 4. Add rhetorical questions
    if (!humanizedContent.includes('?')) {
      const paragraphs = humanizedContent.split('\n\n');
      if (paragraphs.length > 2) {
        paragraphs[1] = "Ever wondered why this matters so much? " + paragraphs[1];
        humanizedContent = paragraphs.join('\n\n');
        techniques.push('Rhetorical questions');
      }
    }

    // 5. Use contractions
    const contractionMap = [
      { from: /\bdo not\b/g, to: "don't" },
      { from: /\bcannot\b/g, to: "can't" },
      { from: /\bwill not\b/g, to: "won't" },
      { from: /\bit is\b/g, to: "it's" },
      { from: /\byou are\b/g, to: "you're" }
    ];

    contractionMap.forEach(({ from, to }) => {
      humanizedContent = humanizedContent.replace(from, to);
    });
    techniques.push('Contractions applied');

    return {
      content: humanizedContent,
      techniques: [...new Set(techniques)]
    };
  }
}

/**
 * Factory function to create content agent
 */
export function createContentAgent(config: ContentAgentConfig): ContentAgent {
  return new ContentAgent(config);
}