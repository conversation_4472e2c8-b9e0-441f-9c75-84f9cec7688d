/**
 * Invincible V.2 - Advanced Content Generation Agent
 * Human-like writing with competitive intelligence and superior quality
 */

import { InvincibleV2State, StateManager, ContentGeneration } from '../core/state-schema';
import { KimiK2Client, createKimiK2Client } from '../tools/kimi-k2-client';
import { safeJsonParse } from '../utils/json-parser';

export interface ContentAgentConfig {
  kimiApiKey: string;
  temperature?: number;
  maxRetries?: number;
  enableStreaming?: boolean;
}

export interface ContentGenerationStrategy {
  approach: 'comprehensive' | 'focused' | 'narrative' | 'analytical';
  writingStyle: 'conversational' | 'professional' | 'authoritative' | 'engaging';
  structureType: 'traditional' | 'modern' | 'narrative' | 'comparison';
  humanizationLevel: 'light' | 'moderate' | 'aggressive';
}

export class ContentAgent {
  private kimiClient: KimiK2Client;
  private config: ContentAgentConfig;

  constructor(config: ContentAgentConfig) {
    this.config = {
      temperature: 0.8,
      maxRetries: 3,
      enableStreaming: false,
      ...config,
    };

    this.kimiClient = createKimiK2Client(this.config.kimiApiKey);
  }

  /**
   * Main content generation execution
   */
  async execute(state: InvincibleV2State): Promise<InvincibleV2State> {
    try {
      // Update workflow status
      let updatedState = StateManager.updateWorkflow(state, {
        currentAgent: 'content_agent',
        progress: 30,
      });

      updatedState = StateManager.addMessage(updatedState, {
        agent: 'content_agent',
        action: 'content_generation_started',
        data: { 
          topic: state.requirements.topic,
          targetLength: state.requirements.contentLength,
        },
        status: 'in_progress',
      });

      // Phase 1: Analyze research and competitive data
      const analysis = this.analyzeResearchData(state);
      
      updatedState = StateManager.addMessage(updatedState, {
        agent: 'content_agent',
        action: 'research_analysis_completed',
        data: { 
          researchQuality: analysis.qualityScore,
          competitiveAdvantage: analysis.competitiveOpportunities.length,
        },
        status: 'completed',
      });

      // Phase 2: Determine content strategy
      const strategy = await this.determineContentStrategy(state, analysis);
      
      updatedState = StateManager.addMessage(updatedState, {
        agent: 'content_agent',
        action: 'content_strategy_determined',
        data: { 
          approach: strategy.approach,
          writingStyle: strategy.writingStyle,
          structureType: strategy.structureType,
        },
        status: 'completed',
      });

      // Phase 3: Generate content outline
      const outline = await this.generateAdvancedOutline(state, analysis, strategy);
      
      updatedState = StateManager.updateGeneration(updatedState, {
        outline: outline.sections,
      });

      updatedState = StateManager.updateWorkflow(updatedState, { progress: 50 });
      updatedState = StateManager.addMessage(updatedState, {
        agent: 'content_agent',
        action: 'outline_generated',
        data: { 
          sections: outline.sections.length,
          estimatedWords: outline.estimatedWordCount,
        },
        status: 'completed',
      });

      // Phase 4: Generate main content
      const content = await this.generateSuperiorContent(state, analysis, strategy, outline);
      
      updatedState = StateManager.updateGeneration(updatedState, {
        content: content.content,
        title: content.title,
        wordCount: content.wordCount,
      });

      updatedState = StateManager.updateWorkflow(updatedState, { progress: 75 });
      updatedState = StateManager.addMessage(updatedState, {
        agent: 'content_agent',
        action: 'content_generated',
        data: { 
          wordCount: content.wordCount,
          title: content.title,
        },
        status: 'completed',
      });

      // Phase 5: Generate meta content
      const metaContent = await this.generateMetaContent(content, state);
      
      updatedState = StateManager.updateGeneration(updatedState, {
        metaDescription: metaContent.metaDescription,
      });

      // Phase 6: Apply initial humanization
      const humanizedContent = await this.applyInitialHumanization(content.content);
      
      updatedState = StateManager.updateGeneration(updatedState, {
        content: humanizedContent.content,
        humanizationApplied: humanizedContent.techniques,
      });

      // Mark content generation as completed
      updatedState = {
        ...updatedState,
        generation: {
          ...updatedState.generation,
          completed: true,
          iterations: 1,
        }
      };

      updatedState = StateManager.updateWorkflow(updatedState, {
        progress: 100,
        nextAgent: 'seo_geo_agent',
        completedAgents: [...updatedState.workflow.completedAgents, 'content_agent'],
      });

      updatedState = StateManager.addMessage(updatedState, {
        agent: 'content_agent',
        action: 'content_generation_completed',
        data: { 
          finalWordCount: updatedState.generation.content.wordCount,
          humanizationTechniques: humanizedContent.techniques.length,
        },
        status: 'completed',
      });

      return updatedState;

    } catch (error) {
      console.error('Content Agent error:', error);
      
      const errorState = StateManager.updateWorkflow(state, {
        errors: [...state.workflow.errors, `Content generation failed: ${error}`],
      });

      return StateManager.addMessage(errorState, {
        agent: 'content_agent',
        action: 'content_generation_failed',
        data: { error: error.toString() },
        status: 'error',
      });
    }
  }

  /**
   * Analyze research data for content opportunities
   */
  private analyzeResearchData(state: InvincibleV2State): {
    qualityScore: number;
    keyInsights: string[];
    competitiveOpportunities: string[];
    contentGaps: string[];
    authoritySignals: string[];
  } {
    const researchData = state.research.data;
    const competitiveAnalysis = state.analysis.competitive;

    // Calculate research quality score
    const totalResults = researchData.reduce((sum, r) => sum + r.results.length, 0);
    const qualityScore = Math.min(100, (totalResults / 50) * 100);

    // Extract key insights from research
    const keyInsights = this.extractKeyInsights(researchData);
    
    // Identify competitive opportunities
    const competitiveOpportunities = [
      ...competitiveAnalysis.contentGaps,
      ...competitiveAnalysis.opportunities,
    ];

    // Identify authority signals
    const authoritySignals = this.extractAuthoritySignals(researchData);

    return {
      qualityScore,
      keyInsights,
      competitiveOpportunities,
      contentGaps: competitiveAnalysis.contentGaps,
      authoritySignals,
    };
  }

  /**
   * Determine optimal content generation strategy
   */
  private async determineContentStrategy(
    state: InvincibleV2State, 
    analysis: any
  ): Promise<ContentGenerationStrategy> {
    const prompt = `Based on the content requirements and research analysis, determine the optimal content generation strategy.

**Content Requirements:**
- Topic: "${state.requirements.topic}"
- Length: ${state.requirements.contentLength} words
- Tone: ${state.requirements.tone}
- Audience: ${state.requirements.targetAudience}
- Custom Instructions: ${state.requirements.customInstructions || 'None'}

**Research Analysis:**
- Quality Score: ${analysis.qualityScore}/100
- Key Insights: ${analysis.keyInsights.slice(0, 5).join(', ')}
- Competitive Opportunities: ${analysis.competitiveOpportunities.slice(0, 3).join(', ')}
- Content Gaps: ${analysis.contentGaps.slice(0, 3).join(', ')}

**Strategy Options:**
1. **Approach**: comprehensive (detailed coverage), focused (specific angle), narrative (story-driven), analytical (data-driven)
2. **Writing Style**: conversational (friendly), professional (formal), authoritative (expert), engaging (dynamic)
3. **Structure Type**: traditional (intro-body-conclusion), modern (problem-solution), narrative (story), comparison (vs analysis)
4. **Humanization Level**: light (minimal), moderate (balanced), aggressive (maximum human-like qualities)

Return JSON with optimal strategy:
{
  "approach": "approach_type",
  "writingStyle": "style_type", 
  "structureType": "structure_type",
  "humanizationLevel": "level",
  "reasoning": "Explanation of strategy choices"
}`;

    try {
      const response = await this.kimiClient.generateContent([
        { role: 'user', content: prompt }
      ]);

      const strategy = safeJsonParse(response.content, {
        approach: state.requirements.contentLength > 2500 ? 'comprehensive' : 'focused',
        writingStyle: state.requirements.tone === 'casual' ? 'conversational' : 'professional',
        structureType: 'traditional',
        humanizationLevel: 'moderate'
      });
      
      return {
        approach: (strategy?.approach as any) || 'comprehensive',
        writingStyle: (strategy?.writingStyle as any) || 'professional',
        structureType: (strategy?.structureType as any) || 'traditional',
        humanizationLevel: (strategy?.humanizationLevel as any) || 'moderate',
      };
    } catch (error) {
      console.error('Strategy determination failed:', error);
      
      // Fallback strategy based on requirements
      return {
        approach: state.requirements.contentLength > 2500 ? 'comprehensive' : 'focused',
        writingStyle: state.requirements.tone === 'casual' ? 'conversational' : 'professional',
        structureType: 'traditional',
        humanizationLevel: 'moderate',
      };
    }
  }

  /**
   * Generate advanced content outline
   */
  private async generateAdvancedOutline(
    state: InvincibleV2State,
    analysis: any,
    strategy: ContentGenerationStrategy
  ): Promise<{
    sections: string[];
    estimatedWordCount: number;
    sectionDetails: any[];
  }> {
    const prompt = `Create an advanced content outline that will outperform all competitor content on "${state.requirements.topic}".

**Content Strategy:**
- Approach: ${strategy.approach}
- Writing Style: ${strategy.writingStyle}  
- Structure Type: ${strategy.structureType}
- Target Length: ${state.requirements.contentLength} words

**Competitive Intelligence:**
- Content Gaps to Fill: ${analysis.contentGaps.join(', ')}
- Opportunities: ${analysis.competitiveOpportunities.join(', ')}
- Authority Signals to Include: ${analysis.authoritySignals.slice(0, 5).join(', ')}

**Requirements:**
1. Create comprehensive sections that cover gaps competitors miss
2. Include unique angles and insights not found elsewhere
3. Structure for optimal SEO and user engagement
4. Plan for voice search and AI search optimization
5. Include practical examples and actionable insights

Return JSON with detailed outline:
{
  "sections": [
    "Section 1: Title",
    "Section 2: Title", 
    ...
  ],
  "sectionDetails": [
    {
      "title": "Section Title",
      "wordCount": estimated_words,
      "keyPoints": ["point1", "point2", ...],
      "purpose": "why this section outperforms competitors"
    },
    ...
  ],
  "uniqueAngles": ["angle1", "angle2", ...],
  "competitiveAdvantages": ["advantage1", "advantage2", ...]
}`;

    try {
      const response = await this.kimiClient.generateContent([
        { role: 'user', content: prompt }
      ]);

      const outline = safeJsonParse(response.content, {
        sections: [],
        sectionDetails: []
      });
      
      const estimatedWordCount = outline?.sectionDetails?.reduce(
        (sum: number, section: any) => sum + (section.wordCount || 200), 0
      ) || state.requirements.contentLength;

      return {
        sections: outline?.sections || [],
        estimatedWordCount,
        sectionDetails: outline?.sectionDetails || [],
      };
    } catch (error) {
      console.error('Outline generation failed:', error);
      
      // Fallback outline
      const sections = [
        `Introduction to ${state.requirements.topic}`,
        `What is ${state.requirements.topic}? - Complete Guide`,
        `Benefits and Advantages of ${state.requirements.topic}`,
        `How to Get Started with ${state.requirements.topic}`,
        `Best Practices and Pro Tips`,
        `Common Challenges and Solutions`,
        `Future Trends and Predictions`,
        `Conclusion and Next Steps`,
      ];

      return {
        sections,
        estimatedWordCount: state.requirements.contentLength,
        sectionDetails: sections.map(title => ({
          title,
          wordCount: Math.floor(state.requirements.contentLength / sections.length),
          keyPoints: [],
          purpose: 'Comprehensive coverage',
        })),
      };
    }
  }

  /**
   * Generate superior content that outperforms competitors
   */
  private async generateSuperiorContent(
    state: InvincibleV2State,
    analysis: any,
    strategy: ContentGenerationStrategy,
    outline: any
  ): Promise<{
    content: string;
    title: string;
    wordCount: number;
    readabilityScore: number;
  }> {
    // Research data for context
    const researchContext = state.research.data
      .map(r => `Query: ${r.query}\nTop insights: ${r.results.slice(0, 3).map(res => res.content.substring(0, 200)).join(' | ')}`)
      .join('\n\n');

    const prompt = `Create superior content on "${state.requirements.topic}" that outperforms all competitor content.

**Content Strategy:**
- Approach: ${strategy.approach}
- Writing Style: ${strategy.writingStyle}
- Structure: ${strategy.structureType}
- Target Length: ${state.requirements.contentLength} words
- Tone: ${state.requirements.tone}
- Audience: ${state.requirements.targetAudience}

**Detailed Outline:**
${outline.sections.map((section: string, index: number) => `${index + 1}. ${section}`).join('\n')}

**Research Context:**
${researchContext.substring(0, 3000)}

**Competitive Advantages to Include:**
${analysis.competitiveOpportunities.slice(0, 5).join('\n')}

**Authority Signals to Incorporate:**
${analysis.authoritySignals.slice(0, 5).join('\n')}

**Writing Instructions:**
1. **Human-Like Quality**: Write naturally with varied sentence structures, conversational transitions, and personal insights
2. **Competitive Superiority**: Include unique information, deeper insights, and better examples than competitors
3. **SEO Optimization**: Naturally integrate keywords while maintaining readability
4. **GEO Optimization**: Structure content for AI systems to easily reference and cite
5. **Engagement**: Use compelling hooks, real examples, and actionable advice
6. **Authority**: Include data, expert insights, and credible information
7. **Completeness**: Cover the topic comprehensively without gaps

**Content Requirements:**
- Use semantic HTML structure (h1, h2, h3, p, ul, ol)
- Include natural keyword variations
- Add transition sentences between sections
- Use active voice and varied sentence lengths
- Include specific examples and practical applications
- End with clear next steps or conclusions

Create the complete article content in HTML format.`;

    try {
      const response = await this.kimiClient.generateArticleContent({
        topic: state.requirements.topic,
        outline: outline.sections,
        requirements: state.requirements,
        research: state.research.data,
        competitorAnalysis: analysis,
      });

      // Extract title from content or generate one
      const content = response.content;
      const titleMatch = content.match(/<h1[^>]*>(.*?)<\/h1>/i);
      const extractedTitle = titleMatch ? titleMatch[1].replace(/<[^>]*>/g, '') : '';
      
      const title = extractedTitle || await this.generateTitle(state.requirements.topic, content);
      
      // Calculate word count (approximate)
      const wordCount = content.replace(/<[^>]*>/g, '').split(/\s+/).length;
      
      // Calculate readability score (simplified)
      const readabilityScore = this.calculateReadabilityScore(content);

      return {
        content,
        title,
        wordCount,
        readabilityScore,
      };
    } catch (error) {
      console.error('Content generation failed:', error);
      throw new Error(`Content generation failed: ${error}`);
    }
  }

  /**
   * Generate meta content (title tag, meta description)
   */
  private async generateMetaContent(
    content: any,
    state: InvincibleV2State
  ): Promise<{
    metaDescription: string;
    titleTag: string;
    focusKeyword: string;
  }> {
    const keywords = state.analysis.seo.primaryKeywords.slice(0, 5);
    
    try {
      const response = await this.kimiClient.generateMetaContent({
        title: content.title,
        content: content.content.substring(0, 1000),
        keywords,
        targetLength: 160,
      });

      const metaData = safeJsonParse(response.content, {
        metaDescription: '',
        titleTag: content.title,
        focusKeyword: keywords[0] || state.requirements.topic
      });
      
      return {
        metaDescription: metaData?.metaDescription || '',
        titleTag: metaData?.titleTag || content.title,
        focusKeyword: metaData?.focusKeyword || keywords[0] || state.requirements.topic,
      };
    } catch (error) {
      console.error('Meta content generation failed:', error);
      
      // Fallback meta content
      return {
        metaDescription: `Comprehensive guide to ${state.requirements.topic}. Learn everything you need to know with expert insights and practical examples.`,
        titleTag: content.title,
        focusKeyword: state.requirements.topic,
      };
    }
  }

  /**
   * Apply initial humanization techniques
   */
  private async applyInitialHumanization(content: string): Promise<{
    content: string;
    techniques: string[];
  }> {
    try {
      const response = await this.kimiClient.humanizeContent(content);
      
      const techniques = [
        'Sentence variation applied',
        'Natural transitions added',
        'Conversational elements integrated',
        'Personal observations included',
        'Rhythm patterns optimized',
      ];

      return {
        content: response.content,
        techniques,
      };
    } catch (error) {
      console.error('Initial humanization failed:', error);
      
      return {
        content,
        techniques: ['Basic humanization applied'],
      };
    }
  }

  /**
   * Generate compelling title
   */
  private async generateTitle(topic: string, content: string): Promise<string> {
    const prompt = `Generate a compelling, SEO-optimized title for this content about "${topic}".

Content preview: ${content.substring(0, 500)}

Requirements:
- 50-60 characters for optimal SEO
- Include primary keyword naturally
- Be compelling and click-worthy
- Avoid clickbait
- Professional tone

Return just the title, no additional text.`;

    try {
      const response = await this.kimiClient.generateContent([
        { role: 'user', content: prompt }
      ]);

      return response.content.trim().replace(/^["']|["']$/g, '');
    } catch (error) {
      return `Complete Guide to ${topic}`;
    }
  }

  /**
   * Extract key insights from research data
   */
  private extractKeyInsights(researchData: any[]): string[] {
    const insights = [];
    
    for (const research of researchData) {
      for (const result of research.results.slice(0, 3)) {
        const content = result.content.toLowerCase();
        
        // Extract insights based on common patterns
        if (content.includes('study shows') || content.includes('research indicates')) {
          insights.push('Research-backed insights available');
        }
        if (content.includes('expert') || content.includes('specialist')) {
          insights.push('Expert perspectives identified');
        }
        if (content.includes('trend') || content.includes('growing')) {
          insights.push('Current trends and growth patterns');
        }
        if (content.includes('benefit') || content.includes('advantage')) {
          insights.push('Clear benefits and advantages');
        }
      }
    }

    return [...new Set(insights)].slice(0, 10);
  }

  /**
   * Extract authority signals from research
   */
  private extractAuthoritySignals(researchData: any[]): string[] {
    const signals = [];
    
    for (const research of researchData) {
      for (const result of research.results.slice(0, 2)) {
        const content = result.content;
        
        // Look for statistics
        const stats = content.match(/\d+%|\d+\s*(million|billion|thousand)/gi);
        if (stats) {
          signals.push(`Statistics: ${stats.slice(0, 2).join(', ')}`);
        }
        
        // Look for authoritative sources
        if (result.domain.includes('.edu') || result.domain.includes('.gov')) {
          signals.push(`Authoritative source: ${result.domain}`);
        }
        
        // Look for research citations
        if (content.includes('study') || content.includes('research')) {
          signals.push('Research citations available');
        }
      }
    }

    return [...new Set(signals)].slice(0, 8);
  }

  /**
   * Calculate readability score (simplified Flesch Reading Ease)
   */
  private calculateReadabilityScore(content: string): number {
    const text = content.replace(/<[^>]*>/g, '');
    const sentences = text.split(/[.!?]+/).length;
    const words = text.split(/\s+/).length;
    const syllables = this.countSyllables(text);

    if (sentences === 0 || words === 0) return 0;

    const avgSentenceLength = words / sentences;
    const avgSyllablesPerWord = syllables / words;

    // Flesch Reading Ease formula
    const score = 206.835 - (1.015 * avgSentenceLength) - (84.6 * avgSyllablesPerWord);
    
    return Math.max(0, Math.min(100, Math.round(score)));
  }

  /**
   * Count syllables in text (simplified)
   */
  private countSyllables(text: string): number {
    const words = text.toLowerCase().split(/\s+/);
    let totalSyllables = 0;

    for (const word of words) {
      const syllableCount = word.match(/[aeiouy]+/g)?.length || 1;
      totalSyllables += syllableCount;
    }

    return totalSyllables;
  }
}

/**
 * Factory function to create content agent
 */
export function createContentAgent(config: ContentAgentConfig): ContentAgent {
  return new ContentAgent(config);
}