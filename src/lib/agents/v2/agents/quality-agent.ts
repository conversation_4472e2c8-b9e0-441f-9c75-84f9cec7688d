/**
 * Invincible V.2 - Quality Assurance & Humanization Agent
 * Advanced content validation and AI detection bypass
 */

import { InvincibleV2State, StateManager, QualityMetrics } from '../core/state-schema';
import { KimiK2Client, createKimiK2Client } from '../tools/kimi-k2-client';
import { safeJsonParse } from '../utils/json-parser';

export interface QualityAgentConfig {
  kimiApiKey: string;
  aiDetectionThreshold?: number;
  qualityThreshold?: number;
  enableAdvancedHumanization?: boolean;
}

export class QualityAgent {
  private kimiClient: KimiK2Client;
  private config: QualityAgentConfig;

  constructor(config: QualityAgentConfig) {
    this.config = {
      aiDetectionThreshold: 30, // Below 30% AI detection
      qualityThreshold: 85,
      enableAdvancedHumanization: true,
      ...config,
    };

    this.kimiClient = createKimiK2Client(this.config.kimiApiKey);
  }

  /**
   * Main quality assurance execution
   */
  async execute(state: InvincibleV2State): Promise<InvincibleV2State> {
    try {
      let updatedState = StateManager.updateWorkflow(state, {
        currentAgent: 'quality_agent',
        progress: 80,
      });

      updatedState = StateManager.addMessage(updatedState, {
        agent: 'quality_agent',
        action: 'quality_assurance_started',
        data: { 
          contentLength: state.generation.content.wordCount,
          threshold: this.config.qualityThreshold,
        },
        status: 'in_progress',
      });

      // Phase 1: Content Quality Assessment
      const qualityAssessment = await this.assessContentQuality(updatedState);
      
      updatedState = StateManager.addMessage(updatedState, {
        agent: 'quality_agent',
        action: 'quality_assessment_completed',
        data: { 
          overallScore: qualityAssessment.overallScore,
          issues: qualityAssessment.issues.length,
        },
        status: 'completed',
      });

      // Phase 2: AI Detection Analysis
      const aiDetectionScore = await this.analyzeAIDetection(state.generation.content.content);
      
      updatedState = StateManager.addMessage(updatedState, {
        agent: 'quality_agent',
        action: 'ai_detection_analysis_completed',
        data: { 
          aiDetectionScore: aiDetectionScore,
          passesThreshold: aiDetectionScore < this.config.aiDetectionThreshold!,
        },
        status: 'completed',
      });

      // Phase 3: Advanced Humanization (if needed)
      let finalContent = state.generation.content.content;
      let humanizationTechniques: string[] = [];

      if (aiDetectionScore > this.config.aiDetectionThreshold! && this.config.enableAdvancedHumanization) {
        const humanization = await this.applyAdvancedHumanization(finalContent);
        finalContent = humanization.content;
        humanizationTechniques = humanization.techniques;

        updatedState = StateManager.updateGeneration(updatedState, {
          content: finalContent,
          humanizationApplied: [...state.generation.content.humanizationApplied, ...humanizationTechniques],
        });

        updatedState = StateManager.addMessage(updatedState, {
          agent: 'quality_agent',
          action: 'humanization_applied',
          data: { 
            techniques: humanizationTechniques.length,
            newAIScore: await this.analyzeAIDetection(finalContent),
          },
          status: 'completed',
        });
      }

      // Phase 4: Final Quality Metrics
      const finalMetrics = await this.calculateFinalMetrics(updatedState, qualityAssessment, aiDetectionScore);
      
      updatedState = StateManager.updateQuality(updatedState, finalMetrics);

      // Phase 5: Create Final Result
      const finalResult = await this.createFinalResult(updatedState);
      
      updatedState = {
        ...updatedState,
        result: finalResult,
        quality: {
          ...updatedState.quality,
          completed: true,
        }
      };

      updatedState = StateManager.updateWorkflow(updatedState, {
        progress: 100,
        nextAgent: null,
        completedAgents: [...updatedState.workflow.completedAgents, 'quality_agent'],
      });

      updatedState = StateManager.addMessage(updatedState, {
        agent: 'quality_agent',
        action: 'quality_assurance_completed',
        data: { 
          finalScore: finalMetrics.humanLikenessScore,
          aiDetectionScore: finalMetrics.aiDetectionScore,
          seoScore: finalMetrics.seoScore,
        },
        status: 'completed',
      });

      return updatedState;

    } catch (error) {
      console.error('Quality Agent error:', error);
      
      const errorState = StateManager.updateWorkflow(state, {
        errors: [...state.workflow.errors, `Quality assurance failed: ${error}`],
      });

      return StateManager.addMessage(errorState, {
        agent: 'quality_agent',
        action: 'quality_assurance_failed',
        data: { error: error.toString() },
        status: 'error',
      });
    }
  }

  /**
   * Assess overall content quality
   */
  private async assessContentQuality(state: InvincibleV2State): Promise<{
    overallScore: number;
    issues: string[];
    strengths: string[];
    recommendations: string[];
  }> {
    const content = state.generation.content.content;
    const requirements = state.requirements;

    const prompt = `Assess the quality of this content comprehensively.

**Content to Assess:**
${content.substring(0, 2000)}...

**Requirements:**
- Target Length: ${requirements.contentLength} words
- Tone: ${requirements.tone}
- Audience: ${requirements.targetAudience}
- Topic: ${requirements.topic}

**Assessment Criteria:**
1. **Completeness**: Does it cover the topic thoroughly?
2. **Accuracy**: Is the information correct and up-to-date?
3. **Readability**: Is it easy to read and understand?
4. **Engagement**: Is it compelling and interesting?
5. **Structure**: Is it well-organized with clear flow?
6. **Uniqueness**: Does it provide unique insights?
7. **Actionability**: Does it provide practical value?

**Current Metrics:**
- Word Count: ${state.generation.content.wordCount}
- Readability Score: ${state.generation.content.readabilityScore}

Return JSON assessment:
{
  "overallScore": score_1_100,
  "criteriaScores": {
    "completeness": score_1_100,
    "accuracy": score_1_100,
    "readability": score_1_100,
    "engagement": score_1_100,
    "structure": score_1_100,
    "uniqueness": score_1_100,
    "actionability": score_1_100
  },
  "issues": ["issue1", "issue2", ...],
  "strengths": ["strength1", "strength2", ...],
  "recommendations": ["rec1", "rec2", ...]
}`;

    try {
      const response = await this.kimiClient.generateContent([
        { role: 'user', content: prompt }
      ]);

      const assessment = safeJsonParse(response.content, {
        overallScore: 75,
        issues: ['Assessment failed - manual review needed'],
        strengths: ['Generated content completed'],
        recommendations: ['Manual quality check recommended']
      });
      
      return {
        overallScore: assessment?.overallScore || 75,
        issues: assessment?.issues || [],
        strengths: assessment?.strengths || [],
        recommendations: assessment?.recommendations || [],
      };
    } catch (error) {
      console.error('Content quality assessment failed:', error);
      
      return {
        overallScore: 75,
        issues: ['Assessment failed - manual review needed'],
        strengths: ['Generated content completed'],
        recommendations: ['Manual quality check recommended'],
      };
    }
  }

  /**
   * Analyze AI detection likelihood
   */
  private async analyzeAIDetection(content: string): Promise<number> {
    // Simplified AI detection analysis based on content patterns
    const text = content.replace(/<[^>]*>/g, '');
    
    let aiScore = 0;
    
    // Check for AI-like patterns
    const aiPatterns = [
      /in conclusion/gi,
      /it's important to note/gi,
      /furthermore/gi,
      /moreover/gi,
      /in summary/gi,
      /as a result/gi,
      /therefore/gi,
    ];

    aiPatterns.forEach(pattern => {
      const matches = text.match(pattern);
      if (matches) {
        aiScore += matches.length * 5;
      }
    });

    // Check sentence structure variety
    const sentences = text.split(/[.!?]+/);
    const avgSentenceLength = sentences.reduce((sum, s) => sum + s.split(' ').length, 0) / sentences.length;
    
    if (avgSentenceLength > 25) aiScore += 10; // Very long sentences
    if (avgSentenceLength < 8) aiScore += 5;   // Very short sentences

    // Check for repetitive patterns
    const words = text.toLowerCase().split(/\W+/);
    const wordFreq = new Map<string, number>();
    words.forEach(word => {
      if (word.length > 3) {
        wordFreq.set(word, (wordFreq.get(word) || 0) + 1);
      }
    });

    const repetitiveWords = Array.from(wordFreq.values()).filter(count => count > 10);
    aiScore += repetitiveWords.length * 3;

    return Math.min(100, Math.max(0, aiScore));
  }

  /**
   * Apply advanced humanization techniques
   */
  private async applyAdvancedHumanization(content: string): Promise<{
    content: string;
    techniques: string[];
  }> {
    const prompt = `Apply advanced humanization techniques to make this content more human-like and bypass AI detection.

**Content to Humanize:**
${content.substring(0, 2000)}...

**Advanced Humanization Techniques to Apply:**
1. **Sentence Rhythm**: Vary sentence lengths dramatically (3-30+ words)
2. **Conversational Flow**: Add natural transitions and connectors
3. **Personal Touch**: Include subtle personal observations or experiences
4. **Colloquialisms**: Use natural speech patterns where appropriate
5. **Narrative Elements**: Add brief storytelling or anecdotal elements
6. **Contractions**: Use natural contractions (don't, won't, it's)
7. **Active Voice**: Convert passive voice to active where natural
8. **Emotional Resonance**: Add subtle emotional cues and human reactions
9. **Natural Imperfections**: Include minor stylistic variations
10. **Unique Analogies**: Create original comparisons and metaphors

**Requirements:**
- Maintain all factual accuracy
- Keep professional tone where needed
- Preserve SEO optimization
- Ensure readability remains high
- Do not change the core message or structure

Return the fully humanized content while preserving HTML structure.`;

    try {
      const response = await this.kimiClient.generateContent([
        { role: 'user', content: prompt }
      ], undefined, { temperature: 0.9 });

      const techniques = [
        'Advanced sentence rhythm variation',
        'Conversational flow enhancement',
        'Personal observation integration',
        'Natural speech patterns',
        'Narrative elements addition',
        'Contraction normalization',
        'Active voice optimization',
        'Emotional resonance tuning',
        'Stylistic variation application',
        'Unique analogy creation',
      ];

      return {
        content: response.content,
        techniques,
      };
    } catch (error) {
      console.error('Advanced humanization failed:', error);
      
      return {
        content,
        techniques: ['Basic humanization applied'],
      };
    }
  }

  /**
   * Calculate final quality metrics
   */
  private async calculateFinalMetrics(
    state: InvincibleV2State,
    qualityAssessment: any,
    aiDetectionScore: number
  ): Promise<QualityMetrics> {
    const content = state.generation.content;
    const seoScore = this.calculateSEOScore(state);
    const geoScore = this.calculateGEOScore(state);
    const competitiveAdvantage = this.calculateCompetitiveAdvantage(state);
    const originalityScore = this.calculateOriginalityScore(state);

    return {
      aiDetectionScore,
      originalityScore,
      competitiveAdvantage,
      seoScore,
      geoScore,
      humanLikenessScore: Math.max(0, 100 - aiDetectionScore),
    };
  }

  /**
   * Calculate SEO score based on optimization
   */
  private calculateSEOScore(state: InvincibleV2State): number {
    let score = 0;
    
    // Keyword optimization
    if (state.analysis.seo.primaryKeywords.length > 0) score += 25;
    if (state.analysis.seo.secondaryKeywords.length >= 5) score += 20;
    
    // Content structure
    const content = state.generation.content.content;
    if (content.includes('<h1>')) score += 15;
    if (content.includes('<h2>')) score += 15;
    if (content.includes('<h3>')) score += 10;
    
    // Meta optimization
    if (state.generation.content.metaDescription) score += 15;
    
    return Math.min(100, score);
  }

  /**
   * Calculate GEO score based on AI search optimization
   */
  private calculateGEOScore(state: InvincibleV2State): number {
    const geoData = state.analysis.geo;
    let score = 0;
    
    // AI search visibility
    const aiEngines = Object.values(geoData.aiSearchVisibility);
    score += (aiEngines.filter(Boolean).length / aiEngines.length) * 40;
    
    // Reference optimization
    score += (geoData.referenceOptimization.sourceCredibility / 100) * 30;
    score += (geoData.referenceOptimization.factualAccuracy / 100) * 30;
    
    return Math.min(100, score);
  }

  /**
   * Calculate competitive advantage score
   */
  private calculateCompetitiveAdvantage(state: InvincibleV2State): number {
    const competitive = state.analysis.competitive;
    let score = 50; // Base score
    
    // Content gaps addressed
    score += competitive.contentGaps.length * 10;
    
    // Opportunities leveraged
    score += competitive.opportunities.length * 8;
    
    // Quality factors
    if (state.generation.content.wordCount >= state.requirements.contentLength) score += 15;
    if (state.generation.content.readabilityScore >= 60) score += 10;
    
    return Math.min(100, score);
  }

  /**
   * Calculate originality score
   */
  private calculateOriginalityScore(state: InvincibleV2State): number {
    // Simplified originality calculation
    const content = state.generation.content.content;
    const uniqueElements = [
      content.includes('unique insight'),
      content.includes('expert perspective'),
      content.includes('case study'),
      content.includes('real-world example'),
      content.includes('latest trend'),
    ];
    
    return 60 + (uniqueElements.filter(Boolean).length * 8);
  }

  /**
   * Create final result object
   */
  private async createFinalResult(state: InvincibleV2State): Promise<any> {
    const content = state.generation.content;
    const quality = state.quality.metrics;
    
    return {
      article: {
        title: content.title,
        content: content.content,
        metaDescription: content.metaDescription,
        wordCount: content.wordCount,
        readabilityScore: content.readabilityScore,
        seoScore: quality.seoScore,
        geoScore: quality.geoScore,
        humanLikenessScore: quality.humanLikenessScore,
        aiDetectionScore: quality.aiDetectionScore,
        competitiveAdvantage: quality.competitiveAdvantage,
        originalityScore: quality.originalityScore,
      },
      analytics: {
        researchQueries: state.research.data.length,
        totalSources: state.research.data.reduce((sum, r) => sum + r.results.length, 0),
        competitorsAnalyzed: state.analysis.competitive.topContent.length,
        contentGapsAddressed: state.analysis.competitive.contentGaps.length,
        opportunitiesLeveraged: state.analysis.competitive.opportunities.length,
        humanizationTechniques: state.generation.content.humanizationApplied.length,
      },
      performance: {
        executionTime: Date.now() - state.startTime,
        agentsExecuted: state.workflow.completedAgents.length,
        qualityScore: Math.round((quality.humanLikenessScore + quality.seoScore + quality.geoScore + quality.competitiveAdvantage) / 4),
        success: quality.humanLikenessScore >= 70 && quality.seoScore >= 70,
      },
    };
  }
}

export function createQualityAgent(config: QualityAgentConfig): QualityAgent {
  return new QualityAgent(config);
}