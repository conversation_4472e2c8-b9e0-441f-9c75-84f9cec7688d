/**
 * Mark1 Agent System - Basic Test
 * Simple test to verify system functionality
 */

import { createSupervisorAgent } from '../core/supervisor';
import { ContentRequirements } from '../core/state-schema';
import { createLogger } from '../utils/logger';

const logger = createLogger({
  agentName: 'Mark1Test',
  enableConsole: true,
});

/**
 * Basic functionality test
 */
export async function testMark1Basic(): Promise<void> {
  logger.info('Starting Mark1 basic test');

  try {
    // Mock API keys for testing (replace with real keys)
    const openRouterApiKey = process.env.OPENROUTER_API_KEY || 'test-key';
    const tavilyApiKey = process.env.TAVILY_API_KEY || 'test-key';

    if (openRouterApiKey === 'test-key' || tavilyApiKey === 'test-key') {
      logger.warn('Using mock API keys - test will use fallback mechanisms');
    }

    // Create supervisor
    const supervisor = createSupervisorAgent({
      openRouter<PERSON><PERSON><PERSON>ey,
      tavilyApiKey,
      qualityThreshold: 70, // Lower threshold for testing
      maxRetries: 1, // Fewer retries for faster testing
    });

    logger.info('Supervisor created successfully');

    // Test requirements
    const requirements: ContentRequirements = {
      topic: 'Artificial Intelligence Basics',
      contentLength: 1000, // Shorter for testing
      tone: 'professional',
      targetAudience: 'beginners',
      customInstructions: 'Focus on practical applications',
      contentType: 'guide',
      keywords: [],
    };

    logger.info('Test requirements created', requirements);

    // Execute workflow
    const startTime = Date.now();
    const result = await supervisor.executeWorkflow(requirements);
    const executionTime = Date.now() - startTime;

    logger.info('Workflow completed', {
      executionTime,
      success: !!result.result,
      agentsCompleted: result.workflow.completedAgents.length,
      totalErrors: result.research.errors.length + 
                   result.generation.errors.length + 
                   result.analysis.errors.length + 
                   result.quality.errors.length + 
                   result.workflow.errors.length,
    });

    // Validate results
    const validationResults = validateResults(result);
    
    if (validationResults.isValid) {
      logger.info('✅ Mark1 basic test PASSED', validationResults);
    } else {
      logger.error('❌ Mark1 basic test FAILED', validationResults);
    }

    return;

  } catch (error) {
    logger.error('❌ Mark1 basic test ERROR', { error: error.toString() });
    throw error;
  }
}

/**
 * Validate test results
 */
function validateResults(state: any): { isValid: boolean; details: any } {
  const details: any = {
    hasResult: !!state.result,
    hasContent: !!state.result?.article?.content,
    hasTitle: !!state.result?.article?.title,
    contentLength: state.result?.article?.content?.length || 0,
    wordCount: state.result?.article?.wordCount || 0,
    agentsCompleted: state.workflow.completedAgents.length,
    expectedAgents: 4,
    totalErrors: state.research.errors.length + 
                 state.generation.errors.length + 
                 state.analysis.errors.length + 
                 state.quality.errors.length + 
                 state.workflow.errors.length,
  };

  const isValid = details.hasResult && 
                  details.hasContent && 
                  details.hasTitle && 
                  details.contentLength > 100 && 
                  details.agentsCompleted >= 3 && // Allow for some agent failures
                  details.totalErrors < 20; // Allow for some errors with fallbacks

  return { isValid, details };
}

/**
 * Test individual components
 */
export async function testMark1Components(): Promise<void> {
  logger.info('Starting Mark1 components test');

  try {
    // Test logger
    const testLogger = createLogger({ agentName: 'ComponentTest' });
    testLogger.info('Logger test');
    testLogger.warn('Warning test');
    testLogger.error('Error test');
    logger.info('✅ Logger component test passed');

    // Test AI client
    const { createAIClient } = await import('../tools/ai-client');
    const aiClient = createAIClient('test-key', 'test-model');
    const aiStatus = aiClient.getStatus();
    logger.info('✅ AI client component test passed', aiStatus);

    // Test Tavily search
    const { TavilySearchTool } = await import('../tools/tavily-search');
    const searchTool = new TavilySearchTool({ apiKey: 'test-key' });
    const searchStatus = searchTool.getStatus();
    logger.info('✅ Tavily search component test passed', searchStatus);

    // Test state management
    const { createInitialState, StateManager } = await import('../core/state-schema');
    const initialState = createInitialState({
      topic: 'Test Topic',
      contentLength: 1000,
      tone: 'professional',
      targetAudience: 'test audience',
      contentType: 'article',
    });
    
    const updatedState = StateManager.addMessage(initialState, {
      agent: 'test',
      action: 'test_action',
      data: { test: true },
      status: 'completed',
    });
    
    logger.info('✅ State management component test passed', {
      initialMessages: initialState.messages.length,
      updatedMessages: updatedState.messages.length,
    });

    logger.info('✅ All Mark1 component tests passed');

  } catch (error) {
    logger.error('❌ Mark1 component test ERROR', { error: error.toString() });
    throw error;
  }
}

/**
 * Run all tests
 */
export async function runMark1Tests(): Promise<void> {
  logger.info('🚀 Starting Mark1 test suite');

  try {
    await testMark1Components();
    logger.info('✅ Component tests completed');

    // Only run full workflow test if API keys are available
    if (process.env.OPENROUTER_API_KEY && process.env.TAVILY_API_KEY) {
      await testMark1Basic();
      logger.info('✅ Basic workflow test completed');
    } else {
      logger.warn('⚠️ Skipping workflow test - API keys not available');
    }

    logger.info('🎉 Mark1 test suite completed successfully');

  } catch (error) {
    logger.error('❌ Mark1 test suite failed', { error: error.toString() });
    throw error;
  }
}

// Export for use in other test files
export default {
  testMark1Basic,
  testMark1Components,
  runMark1Tests,
};

// Run tests if this file is executed directly
if (require.main === module) {
  runMark1Tests().catch(console.error);
}
