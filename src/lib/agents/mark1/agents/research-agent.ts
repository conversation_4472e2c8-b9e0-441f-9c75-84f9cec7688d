/**
 * Mark1 Agent System - Research Agent
 * Simplified and robust research with comprehensive error handling
 */

import { Mark1State, StateManager, ResearchData } from '../core/state-schema';
import { TavilySearchTool, CompetitiveAnalysis, SEOIntelligence } from '../tools/tavily-search';
import { createLogger, Timer, logError } from '../utils/logger';

export interface ResearchAgentConfig {
  tavilyApiKey: string;
  maxSearchQueries?: number;
  searchDepth?: 'basic' | 'advanced';
  competitiveAnalysis?: boolean;
  trendAnalysis?: boolean;
  timeout?: number;
}

/**
 * Simplified Research Agent with robust error handling
 */
export class ResearchAgent {
  private searchTool: TavilySearchTool;
  private config: ResearchAgentConfig;
  private logger: ReturnType<typeof createLogger>;

  constructor(config: ResearchAgentConfig) {
    this.config = {
      maxSearchQueries: 8,
      searchDepth: 'advanced',
      competitiveAnalysis: true,
      trendAnalysis: true,
      timeout: 120000, // 2 minutes
      ...config,
    };

    this.searchTool = new TavilySearchTool({
      apiKey: this.config.tavilyApiKey,
      searchDepth: this.config.searchDepth,
      maxResults: 10,
      includeImages: false,
      includeAnswer: true,
      includeRawContent: true,
    });
    
    this.logger = createLogger({ 
      agentName: 'ResearchAgent',
      phase: 'research',
      enableConsole: true,
    });
    
    this.logger.info('Research agent initialized');
  }

  /**
   * Main execution method
   */
  async execute(state: Mark1State): Promise<Mark1State> {
    const timer = new Timer('Research Agent Execution');
    let updatedState = state;

    try {
      this.logger.info(`Starting research for: "${state.requirements.topic}"`);

      // Update workflow status
      updatedState = StateManager.updateWorkflow(updatedState, {
        currentAgent: 'research_agent',
        progress: 10,
      });

      updatedState = StateManager.addMessage(updatedState, {
        agent: 'research_agent',
        action: 'research_started',
        data: { topic: state.requirements.topic },
        status: 'in_progress',
      });

      // Phase 1: Core topic research
      this.logger.info('Phase 1: Core topic research');
      const coreResearch = await this.conductCoreResearch(state.requirements.topic);
      updatedState = StateManager.updateResearch(updatedState, coreResearch);
      
      updatedState = StateManager.updateWorkflow(updatedState, { progress: 30 });
      updatedState = StateManager.addMessage(updatedState, {
        agent: 'research_agent',
        action: 'core_research_completed',
        data: { resultsCount: coreResearch.results.length },
        status: 'completed',
      });

      // Phase 2: Competitive analysis (if enabled)
      if (this.config.competitiveAnalysis) {
        this.logger.info('Phase 2: Competitive analysis');
        const competitiveData = await this.conductCompetitiveAnalysis(state.requirements.topic);
        
        updatedState = StateManager.updateAnalysis(updatedState, {
          competitive: {
            topContent: competitiveData.topCompetitors.map(comp => ({ title: comp })),
            contentGaps: competitiveData.contentGaps,
            opportunities: competitiveData.marketOpportunities,
          },
        });

        updatedState = StateManager.updateWorkflow(updatedState, { progress: 50 });
        updatedState = StateManager.addMessage(updatedState, {
          agent: 'research_agent',
          action: 'competitive_analysis_completed',
          data: { 
            competitorsFound: competitiveData.topCompetitors.length,
            gapsIdentified: competitiveData.contentGaps.length,
          },
          status: 'completed',
        });
      }

      // Phase 3: Variation research
      this.logger.info('Phase 3: Variation research');
      const variationResearch = await this.conductVariationResearch(state.requirements.topic);
      updatedState = StateManager.updateResearch(updatedState, variationResearch);

      updatedState = StateManager.updateWorkflow(updatedState, { progress: 70 });
      updatedState = StateManager.addMessage(updatedState, {
        agent: 'research_agent',
        action: 'variation_research_completed',
        data: { variationsCount: variationResearch.length },
        status: 'completed',
      });

      // Phase 4: SEO intelligence (if enabled)
      if (this.config.trendAnalysis) {
        this.logger.info('Phase 4: SEO intelligence');
        const seoData = await this.gatherSEOIntelligence(state.requirements.topic);
        
        updatedState = StateManager.updateAnalysis(updatedState, {
          seo: {
            primaryKeywords: seoData.primaryKeywords,
            secondaryKeywords: seoData.secondaryKeywords,
            keywordDensity: {},
            competitorKeywords: seoData.competitorKeywords,
            seoScore: 75, // Base score
            recommendations: [
              'Include primary keywords in title and headings',
              'Use secondary keywords naturally throughout content',
              'Optimize for featured snippets',
            ],
          },
        });

        updatedState = StateManager.updateWorkflow(updatedState, { progress: 85 });
        updatedState = StateManager.addMessage(updatedState, {
          agent: 'research_agent',
          action: 'seo_intelligence_completed',
          data: { 
            primaryKeywords: seoData.primaryKeywords.length,
            secondaryKeywords: seoData.secondaryKeywords.length,
          },
          status: 'completed',
        });
      }

      // Mark research as completed
      updatedState = {
        ...updatedState,
        research: {
          ...updatedState.research,
          completed: true,
        },
      };

      updatedState = StateManager.updateWorkflow(updatedState, {
        progress: 100,
        nextAgent: 'content_agent',
        completedAgents: [...updatedState.workflow.completedAgents, 'research_agent'],
      });

      updatedState = StateManager.addMessage(updatedState, {
        agent: 'research_agent',
        action: 'research_completed',
        data: { 
          totalSources: updatedState.research.data.reduce((sum, r) => sum + r.results.length, 0),
          executionTime: timer.elapsed(),
        },
        status: 'completed',
      });

      timer.log(this.logger);
      this.logger.info('Research agent completed successfully');
      return updatedState;

    } catch (error) {
      logError(error, 'Research Agent', this.logger);
      
      const errorState = StateManager.updateWorkflow(updatedState, {
        errors: [...updatedState.workflow.errors, `Research failed: ${error}`],
      });

      return StateManager.addMessage(errorState, {
        agent: 'research_agent',
        action: 'research_failed',
        data: { error: error.toString(), executionTime: timer.elapsed() },
        status: 'error',
      });
    }
  }

  /**
   * Conduct core research on the main topic
   */
  private async conductCoreResearch(topic: string): Promise<ResearchData> {
    try {
      this.logger.info(`Conducting core research for: "${topic}"`);
      
      const searchResponse = await this.searchTool.enhancedSearch(
        `${topic} comprehensive guide complete overview 2025`,
        { maxResults: 12, searchDepth: 'advanced' }
      );

      return {
        query: `Core research: ${topic}`,
        results: searchResponse.results,
        competitorAnalysis: {
          topCompetitors: [],
          contentGaps: [],
          strengthsWeaknesses: [],
        },
        searchDepth: 'advanced',
        timestamp: Date.now(),
      };
    } catch (error) {
      logError(error, 'Core Research', this.logger);
      
      // Return minimal fallback data
      return {
        query: `Core research: ${topic}`,
        results: [{
          title: `${topic} - Research Unavailable`,
          url: 'https://fallback.example.com',
          content: `Research data for ${topic} is temporarily unavailable. Please try again later.`,
          score: 0.5,
        }],
        competitorAnalysis: {
          topCompetitors: [],
          contentGaps: ['Research service unavailable'],
          strengthsWeaknesses: [],
        },
        searchDepth: 'basic',
        timestamp: Date.now(),
      };
    }
  }

  /**
   * Conduct comprehensive competitive analysis
   */
  private async conductCompetitiveAnalysis(topic: string): Promise<CompetitiveAnalysis> {
    try {
      this.logger.info(`Conducting competitive analysis for: "${topic}"`);
      return await this.searchTool.competitiveAnalysis(topic);
    } catch (error) {
      logError(error, 'Competitive Analysis', this.logger);
      
      return {
        topCompetitors: [],
        contentGaps: ['Competitive analysis unavailable'],
        strengthsWeaknesses: [],
        marketOpportunities: [],
      };
    }
  }

  /**
   * Research topic variations and related queries
   */
  private async conductVariationResearch(topic: string): Promise<ResearchData[]> {
    try {
      this.logger.info(`Conducting variation research for: "${topic}"`);
      
      const variations = [
        `${topic} best practices tips 2025`,
        `${topic} benefits advantages`,
        `${topic} challenges problems solutions`,
        `${topic} examples case studies`,
        `${topic} tools software resources`,
        `${topic} future trends 2025`,
      ];

      const searchResults = await this.searchTool.multiQueryResearch(topic, variations);
      
      return searchResults.map((result, index) => ({
        query: result.query,
        results: result.results,
        competitorAnalysis: {
          topCompetitors: [],
          contentGaps: [],
          strengthsWeaknesses: [],
        },
        searchDepth: 'advanced' as const,
        timestamp: Date.now() + index, // Slight offset for uniqueness
      }));
    } catch (error) {
      logError(error, 'Variation Research', this.logger);
      
      return [{
        query: `${topic} variations`,
        results: [],
        competitorAnalysis: {
          topCompetitors: [],
          contentGaps: ['Variation research unavailable'],
          strengthsWeaknesses: [],
        },
        searchDepth: 'basic',
        timestamp: Date.now(),
      }];
    }
  }

  /**
   * Gather SEO intelligence
   */
  private async gatherSEOIntelligence(topic: string): Promise<SEOIntelligence> {
    try {
      this.logger.info(`Gathering SEO intelligence for: "${topic}"`);
      return await this.searchTool.seoIntelligence(topic);
    } catch (error) {
      logError(error, 'SEO Intelligence', this.logger);
      
      return {
        primaryKeywords: [topic],
        secondaryKeywords: [],
        competitorKeywords: [],
        searchVolume: {},
        difficulty: {},
      };
    }
  }

  /**
   * Get agent status
   */
  getStatus(): {
    isReady: boolean;
    searchToolStatus: any;
    config: ResearchAgentConfig;
  } {
    return {
      isReady: true,
      searchToolStatus: this.searchTool.getStatus(),
      config: this.config,
    };
  }
}
