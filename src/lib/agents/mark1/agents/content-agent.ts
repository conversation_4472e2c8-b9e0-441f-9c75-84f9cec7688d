/**
 * Mark1 Agent System - Content Agent
 * Simplified and robust content generation with comprehensive error handling
 */

import { Mark1State, StateManager, ContentGeneration } from '../core/state-schema';
import { AIClient, createAIClient, safeJsonParse } from '../tools/ai-client';
import { createLogger, Timer, logError } from '../utils/logger';

export interface ContentAgentConfig {
  openRouterApiKey: string;
  model?: string;
  temperature?: number;
  maxRetries?: number;
  enableStreaming?: boolean;
  timeout?: number;
}

export interface ContentStrategy {
  approach: 'comprehensive' | 'focused' | 'listicle' | 'guide';
  writingStyle: 'professional' | 'conversational' | 'academic' | 'casual';
  structureType: 'traditional' | 'problem-solution' | 'how-to' | 'comparison';
  humanizationLevel: 'minimal' | 'moderate' | 'high';
}

export interface ResearchAnalysis {
  qualityScore: number;
  dataPoints: number;
  competitiveOpportunities: string[];
  keyInsights: string[];
  contentGaps: string[];
}

/**
 * Simplified Content Agent with robust error handling
 */
export class ContentAgent {
  private aiClient: AIClient;
  private config: ContentAgentConfig;
  private logger: ReturnType<typeof createLogger>;

  constructor(config: ContentAgentConfig) {
    this.config = {
      model: 'anthropic/claude-3.5-sonnet',
      temperature: 0.7,
      maxRetries: 2,
      enableStreaming: false,
      timeout: 180000, // 3 minutes
      ...config,
    };

    this.aiClient = createAIClient(this.config.openRouterApiKey, this.config.model);
    
    this.logger = createLogger({ 
      agentName: 'ContentAgent',
      phase: 'content_generation',
      enableConsole: true,
    });
    
    this.logger.info('Content agent initialized');
  }

  /**
   * Main execution method
   */
  async execute(state: Mark1State): Promise<Mark1State> {
    const timer = new Timer('Content Agent Execution');
    let updatedState = state;

    try {
      this.logger.info(`Starting content generation for: "${state.requirements.topic}"`);

      // Update workflow status
      updatedState = StateManager.updateWorkflow(updatedState, {
        currentAgent: 'content_agent',
        progress: 10,
      });

      updatedState = StateManager.addMessage(updatedState, {
        agent: 'content_agent',
        action: 'content_generation_started',
        data: { topic: state.requirements.topic },
        status: 'in_progress',
      });

      // Phase 1: Analyze research data
      this.logger.info('Phase 1: Analyzing research data');
      const analysis = this.analyzeResearchData(state);
      
      updatedState = StateManager.addMessage(updatedState, {
        agent: 'content_agent',
        action: 'research_analysis_completed',
        data: { 
          qualityScore: analysis.qualityScore,
          competitiveOpportunities: analysis.competitiveOpportunities.length,
        },
        status: 'completed',
      });

      // Phase 2: Determine content strategy
      this.logger.info('Phase 2: Determining content strategy');
      const strategy = await this.determineContentStrategy(state, analysis);
      
      updatedState = StateManager.updateWorkflow(updatedState, { progress: 30 });
      updatedState = StateManager.addMessage(updatedState, {
        agent: 'content_agent',
        action: 'content_strategy_determined',
        data: { 
          approach: strategy.approach,
          writingStyle: strategy.writingStyle,
          structureType: strategy.structureType,
        },
        status: 'completed',
      });

      // Phase 3: Generate content outline
      this.logger.info('Phase 3: Generating content outline');
      const outline = await this.generateOutline(state, strategy, analysis);
      
      updatedState = StateManager.updateWorkflow(updatedState, { progress: 50 });
      updatedState = StateManager.addMessage(updatedState, {
        agent: 'content_agent',
        action: 'outline_generated',
        data: { outlinePoints: outline.length },
        status: 'completed',
      });

      // Phase 4: Generate main content
      this.logger.info('Phase 4: Generating main content');
      const contentResult = await this.generateMainContent(state, strategy, outline, analysis);
      
      updatedState = StateManager.updateWorkflow(updatedState, { progress: 80 });
      updatedState = StateManager.addMessage(updatedState, {
        agent: 'content_agent',
        action: 'main_content_generated',
        data: { 
          wordCount: contentResult.wordCount,
          readabilityScore: contentResult.readabilityScore,
        },
        status: 'completed',
      });

      // Phase 5: Apply humanization techniques
      this.logger.info('Phase 5: Applying humanization');
      const humanizedContent = await this.applyHumanization(contentResult.content, strategy);
      
      // Update generation state
      updatedState = StateManager.updateGeneration(updatedState, {
        content: {
          outline,
          content: humanizedContent.content,
          title: contentResult.title,
          metaDescription: await this.generateMetaDescription(state.requirements.topic, humanizedContent.content),
          wordCount: humanizedContent.wordCount,
          readabilityScore: contentResult.readabilityScore,
          humanizationApplied: humanizedContent.techniques,
        },
        iterations: 1,
        completed: true,
      });

      updatedState = StateManager.updateWorkflow(updatedState, {
        progress: 100,
        nextAgent: 'seo_geo_agent',
        completedAgents: [...updatedState.workflow.completedAgents, 'content_agent'],
      });

      updatedState = StateManager.addMessage(updatedState, {
        agent: 'content_agent',
        action: 'content_generation_completed',
        data: { 
          finalWordCount: humanizedContent.wordCount,
          humanizationTechniques: humanizedContent.techniques.length,
          executionTime: timer.elapsed(),
        },
        status: 'completed',
      });

      timer.log(this.logger);
      this.logger.info('Content agent completed successfully');
      return updatedState;

    } catch (error) {
      logError(error, 'Content Agent', this.logger);
      
      const errorState = StateManager.updateWorkflow(updatedState, {
        errors: [...updatedState.workflow.errors, `Content generation failed: ${error}`],
      });

      return StateManager.addMessage(errorState, {
        agent: 'content_agent',
        action: 'content_generation_failed',
        data: { error: error.toString(), executionTime: timer.elapsed() },
        status: 'error',
      });
    }
  }

  /**
   * Analyze research data quality and extract insights
   */
  private analyzeResearchData(state: Mark1State): ResearchAnalysis {
    try {
      const researchData = state.research.data;
      const totalResults = researchData.reduce((sum, r) => sum + r.results.length, 0);
      
      // Calculate quality score based on data availability
      let qualityScore = 50; // Base score
      if (totalResults > 20) qualityScore += 20;
      if (totalResults > 40) qualityScore += 15;
      if (researchData.length > 3) qualityScore += 10;
      if (state.analysis.competitive.contentGaps.length > 0) qualityScore += 5;

      // Extract key insights from research
      const keyInsights = [
        'Comprehensive research data available',
        'Multiple perspectives identified',
        'Current trends and best practices found',
      ];

      // Identify competitive opportunities
      const competitiveOpportunities = [
        'Unique angle on common topic',
        'More detailed implementation guide',
        'Updated information for 2025',
        ...state.analysis.competitive.opportunities.slice(0, 2),
      ];

      return {
        qualityScore: Math.min(qualityScore, 100),
        dataPoints: totalResults,
        competitiveOpportunities,
        keyInsights,
        contentGaps: state.analysis.competitive.contentGaps,
      };
    } catch (error) {
      logError(error, 'Research Analysis', this.logger);
      
      return {
        qualityScore: 60,
        dataPoints: 0,
        competitiveOpportunities: ['Basic content structure'],
        keyInsights: ['Limited research data available'],
        contentGaps: ['Research analysis failed'],
      };
    }
  }

  /**
   * Determine optimal content strategy
   */
  private async determineContentStrategy(state: Mark1State, analysis: ResearchAnalysis): Promise<ContentStrategy> {
    try {
      const prompt = `Based on the following information, determine the optimal content strategy:

Topic: ${state.requirements.topic}
Content Length: ${state.requirements.contentLength} words
Target Audience: ${state.requirements.targetAudience}
Tone: ${state.requirements.tone}
Content Type: ${state.requirements.contentType}

Research Quality Score: ${analysis.qualityScore}/100
Available Data Points: ${analysis.dataPoints}
Competitive Opportunities: ${analysis.competitiveOpportunities.join(', ')}

Return a JSON object with:
{
  "approach": "comprehensive|focused|listicle|guide",
  "writingStyle": "professional|conversational|academic|casual", 
  "structureType": "traditional|problem-solution|how-to|comparison",
  "humanizationLevel": "minimal|moderate|high"
}`;

      const response = await this.aiClient.generateJSON(
        [{ role: 'user', content: prompt }],
        {
          approach: state.requirements.contentLength > 2500 ? 'comprehensive' : 'focused',
          writingStyle: state.requirements.tone === 'casual' ? 'conversational' : 'professional',
          structureType: 'traditional',
          humanizationLevel: 'moderate'
        }
      );
      
      return response as ContentStrategy;
    } catch (error) {
      logError(error, 'Content Strategy', this.logger);
      
      return {
        approach: state.requirements.contentLength > 2500 ? 'comprehensive' : 'focused',
        writingStyle: state.requirements.tone === 'casual' ? 'conversational' : 'professional',
        structureType: 'traditional',
        humanizationLevel: 'moderate',
      };
    }
  }

  /**
   * Generate content outline
   */
  private async generateOutline(state: Mark1State, strategy: ContentStrategy, analysis: ResearchAnalysis): Promise<string[]> {
    try {
      const prompt = `Create a detailed outline for an article about "${state.requirements.topic}".

Requirements:
- Target length: ${state.requirements.contentLength} words
- Approach: ${strategy.approach}
- Structure: ${strategy.structureType}
- Audience: ${state.requirements.targetAudience}

Key insights to include:
${analysis.keyInsights.join('\n')}

Competitive opportunities:
${analysis.competitiveOpportunities.join('\n')}

Return a JSON array of outline points (strings only):
["Introduction", "Main Point 1", "Main Point 2", ...]`;

      const response = await this.aiClient.generateJSON(
        [{ role: 'user', content: prompt }],
        [
          'Introduction',
          `Understanding ${state.requirements.topic}`,
          `Benefits and Applications`,
          `Best Practices`,
          `Common Challenges`,
          'Conclusion'
        ]
      );
      
      return Array.isArray(response) ? response : [
        'Introduction',
        `Understanding ${state.requirements.topic}`,
        'Key Benefits',
        'Implementation Guide',
        'Best Practices',
        'Conclusion'
      ];
    } catch (error) {
      logError(error, 'Outline Generation', this.logger);
      
      return [
        'Introduction',
        `Understanding ${state.requirements.topic}`,
        'Key Benefits',
        'Implementation Guide',
        'Best Practices',
        'Conclusion'
      ];
    }
  }

  /**
   * Generate main content
   */
  private async generateMainContent(
    state: Mark1State,
    strategy: ContentStrategy,
    outline: string[],
    analysis: ResearchAnalysis
  ): Promise<{ content: string; title: string; wordCount: number; readabilityScore: number }> {
    try {
      const researchContext = state.research.data
        .slice(0, 3)
        .map(r => r.results.slice(0, 2).map(result => `${result.title}: ${result.content.substring(0, 200)}...`).join('\n'))
        .join('\n\n');

      const prompt = `Write a comprehensive ${state.requirements.contentLength}-word article about "${state.requirements.topic}".

REQUIREMENTS:
- Target audience: ${state.requirements.targetAudience}
- Tone: ${state.requirements.tone}
- Writing style: ${strategy.writingStyle}
- Structure approach: ${strategy.structureType}
- Word count: EXACTLY ${state.requirements.contentLength} words

OUTLINE TO FOLLOW:
${outline.map((point, i) => `${i + 1}. ${point}`).join('\n')}

RESEARCH CONTEXT:
${researchContext}

COMPETITIVE ADVANTAGES TO INCLUDE:
${analysis.competitiveOpportunities.slice(0, 3).join('\n')}

INSTRUCTIONS:
- Write in HTML format with proper headings (h1, h2, h3)
- Include the main title in an h1 tag
- Use natural, human-like language
- Include specific examples and actionable advice
- Reference current trends and 2025 insights
- Ensure content flows naturally between sections
- Target exactly ${state.requirements.contentLength} words

${state.requirements.customInstructions ? `ADDITIONAL INSTRUCTIONS:\n${state.requirements.customInstructions}` : ''}`;

      const response = await this.aiClient.generateWithRetry(
        [{ role: 'user', content: prompt }],
        this.config.maxRetries
      );

      // Extract title from content or generate one
      const content = response.content;
      const titleMatch = content.match(/<h1[^>]*>(.*?)<\/h1>/i);
      const extractedTitle = titleMatch ? titleMatch[1].replace(/<[^>]*>/g, '') : '';

      const title = extractedTitle || await this.generateTitle(state.requirements.topic, content);

      // Calculate word count (approximate)
      const wordCount = content.replace(/<[^>]*>/g, '').split(/\s+/).filter(word => word.length > 0).length;

      // Calculate readability score (simplified)
      const readabilityScore = this.calculateReadabilityScore(content);

      return {
        content,
        title,
        wordCount,
        readabilityScore,
      };
    } catch (error) {
      logError(error, 'Content Generation', this.logger);

      // Return fallback content
      const fallbackContent = this.generateFallbackContent(state.requirements.topic, state.requirements.contentLength);
      return {
        content: fallbackContent,
        title: `Complete Guide to ${state.requirements.topic}`,
        wordCount: state.requirements.contentLength,
        readabilityScore: 75,
      };
    }
  }

  /**
   * Apply humanization techniques to content
   */
  private async applyHumanization(content: string, strategy: ContentStrategy): Promise<{
    content: string;
    wordCount: number;
    techniques: string[];
  }> {
    try {
      if (strategy.humanizationLevel === 'minimal') {
        return {
          content,
          wordCount: content.replace(/<[^>]*>/g, '').split(/\s+/).filter(word => word.length > 0).length,
          techniques: ['minimal_processing'],
        };
      }

      const prompt = `Apply humanization techniques to make this content more natural and human-like:

${content}

Humanization level: ${strategy.humanizationLevel}

Apply these techniques:
1. Add natural transitions and connectors
2. Include personal touches and relatable examples
3. Vary sentence structure and length
4. Add conversational elements where appropriate
5. Include rhetorical questions
6. Use active voice where possible

Return the improved content maintaining the same HTML structure.`;

      const response = await this.aiClient.generateContent([
        { role: 'user', content: prompt }
      ]);

      const techniques = [
        'natural_transitions',
        'varied_sentence_structure',
        'conversational_elements',
        'active_voice_optimization',
      ];

      return {
        content: response.content,
        wordCount: response.content.replace(/<[^>]*>/g, '').split(/\s+/).filter(word => word.length > 0).length,
        techniques,
      };
    } catch (error) {
      logError(error, 'Humanization', this.logger);

      return {
        content,
        wordCount: content.replace(/<[^>]*>/g, '').split(/\s+/).filter(word => word.length > 0).length,
        techniques: ['humanization_failed'],
      };
    }
  }

  /**
   * Generate title if not extracted from content
   */
  private async generateTitle(topic: string, content: string): Promise<string> {
    try {
      const prompt = `Generate a compelling, SEO-friendly title for an article about "${topic}".

Content preview:
${content.substring(0, 500)}...

Requirements:
- 50-60 characters
- Include the main keyword
- Make it engaging and clickable
- Professional tone

Return only the title, no quotes or extra text.`;

      const response = await this.aiClient.generateContent([
        { role: 'user', content: prompt }
      ]);

      return response.content.trim().replace(/^["']|["']$/g, '');
    } catch (error) {
      logError(error, 'Title Generation', this.logger);
      return `Complete Guide to ${topic}`;
    }
  }

  /**
   * Generate meta description
   */
  private async generateMetaDescription(topic: string, content: string): Promise<string> {
    try {
      const prompt = `Generate a compelling meta description for an article about "${topic}".

Content preview:
${content.substring(0, 300)}...

Requirements:
- 150-160 characters
- Include main keyword
- Compelling and descriptive
- Call to action

Return only the meta description, no quotes.`;

      const response = await this.aiClient.generateContent([
        { role: 'user', content: prompt }
      ]);

      return response.content.trim().replace(/^["']|["']$/g, '');
    } catch (error) {
      logError(error, 'Meta Description Generation', this.logger);
      return `Learn everything about ${topic} with our comprehensive guide. Discover best practices, tips, and expert insights.`;
    }
  }

  /**
   * Calculate readability score (simplified)
   */
  private calculateReadabilityScore(content: string): number {
    try {
      const text = content.replace(/<[^>]*>/g, '');
      const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
      const words = text.split(/\s+/).filter(w => w.length > 0);
      const syllables = words.reduce((count, word) => count + this.countSyllables(word), 0);

      if (sentences.length === 0 || words.length === 0) return 75;

      const avgWordsPerSentence = words.length / sentences.length;
      const avgSyllablesPerWord = syllables / words.length;

      // Simplified Flesch Reading Ease formula
      const score = 206.835 - (1.015 * avgWordsPerSentence) - (84.6 * avgSyllablesPerWord);

      return Math.max(0, Math.min(100, Math.round(score)));
    } catch (error) {
      return 75; // Default score
    }
  }

  /**
   * Count syllables in a word (simplified)
   */
  private countSyllables(word: string): number {
    word = word.toLowerCase();
    if (word.length <= 3) return 1;

    const vowels = word.match(/[aeiouy]+/g);
    let count = vowels ? vowels.length : 1;

    if (word.endsWith('e')) count--;
    if (word.endsWith('le') && word.length > 2) count++;

    return Math.max(1, count);
  }

  /**
   * Generate fallback content when AI fails
   */
  private generateFallbackContent(topic: string, targetLength: number): string {
    const sections = [
      `<h1>Complete Guide to ${topic}</h1>`,
      `<h2>Introduction</h2>`,
      `<p>${topic} is an important subject that requires comprehensive understanding. This guide will provide you with essential information and practical insights.</p>`,
      `<h2>Understanding ${topic}</h2>`,
      `<p>To effectively work with ${topic}, it's crucial to understand the fundamental concepts and principles involved.</p>`,
      `<h2>Key Benefits</h2>`,
      `<p>Implementing ${topic} correctly can provide numerous advantages including improved efficiency, better results, and enhanced outcomes.</p>`,
      `<h2>Best Practices</h2>`,
      `<p>Following established best practices ensures optimal results when working with ${topic}. These guidelines have been proven effective across various scenarios.</p>`,
      `<h2>Common Challenges</h2>`,
      `<p>Like any complex subject, ${topic} comes with its own set of challenges. Understanding these potential issues helps in better preparation and implementation.</p>`,
      `<h2>Conclusion</h2>`,
      `<p>${topic} is a valuable area of knowledge that can provide significant benefits when properly understood and implemented. By following the guidelines in this comprehensive guide, you can achieve success in your endeavors.</p>`,
    ];

    return sections.join('\n\n');
  }

  /**
   * Get agent status
   */
  getStatus(): {
    isReady: boolean;
    aiClientStatus: any;
    config: ContentAgentConfig;
  } {
    return {
      isReady: true,
      aiClientStatus: this.aiClient.getStatus(),
      config: this.config,
    };
  }
}
