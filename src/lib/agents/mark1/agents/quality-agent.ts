/**
 * Mark1 Agent System - Quality Agent
 * Simplified quality assurance and validation with robust error handling
 */

import { Mark1State, StateManager, QualityMetrics } from '../core/state-schema';
import { AIClient, createAIClient, safeJsonParse } from '../tools/ai-client';
import { createLogger, Timer, logError } from '../utils/logger';

export interface QualityAgentConfig {
  openRouterApiKey: string;
  model?: string;
  aiDetectionThreshold?: number;
  qualityThreshold?: number;
  enableAdvancedHumanization?: boolean;
  timeout?: number;
}

export interface QualityAssessment {
  overallScore: number;
  issues: string[];
  strengths: string[];
  recommendations: string[];
}

export interface HumanizationResult {
  content: string;
  techniques: string[];
  humanLikenessScore: number;
}

/**
 * Simplified Quality Agent with robust error handling
 */
export class QualityAgent {
  private aiClient: AIClient;
  private config: QualityAgentConfig;
  private logger: ReturnType<typeof createLogger>;

  constructor(config: QualityAgentConfig) {
    this.config = {
      model: 'anthropic/claude-3.5-sonnet',
      aiDetectionThreshold: 30,
      qualityThreshold: 80,
      enableAdvancedHumanization: true,
      timeout: 120000, // 2 minutes
      ...config,
    };

    this.aiClient = createAIClient(this.config.openRouterApiKey, this.config.model);
    
    this.logger = createLogger({ 
      agentName: 'QualityAgent',
      phase: 'quality_assurance',
      enableConsole: true,
    });
    
    this.logger.info('Quality agent initialized');
  }

  /**
   * Main execution method
   */
  async execute(state: Mark1State): Promise<Mark1State> {
    const timer = new Timer('Quality Agent Execution');
    let updatedState = state;

    try {
      this.logger.info(`Starting quality assurance for: "${state.requirements.topic}"`);

      // Update workflow status
      updatedState = StateManager.updateWorkflow(updatedState, {
        currentAgent: 'quality_agent',
        progress: 10,
      });

      updatedState = StateManager.addMessage(updatedState, {
        agent: 'quality_agent',
        action: 'quality_assurance_started',
        data: { topic: state.requirements.topic },
        status: 'in_progress',
      });

      // Phase 1: Content Quality Assessment
      this.logger.info('Phase 1: Content quality assessment');
      const qualityAssessment = await this.assessContentQuality(updatedState);
      
      updatedState = StateManager.updateWorkflow(updatedState, { progress: 30 });
      updatedState = StateManager.addMessage(updatedState, {
        agent: 'quality_agent',
        action: 'quality_assessment_completed',
        data: { 
          overallScore: qualityAssessment.overallScore,
          issues: qualityAssessment.issues.length,
        },
        status: 'completed',
      });

      // Phase 2: AI Detection Analysis
      this.logger.info('Phase 2: AI detection analysis');
      const aiDetectionScore = await this.analyzeAIDetection(state.generation.content.content);
      
      updatedState = StateManager.updateWorkflow(updatedState, { progress: 50 });
      updatedState = StateManager.addMessage(updatedState, {
        agent: 'quality_agent',
        action: 'ai_detection_analysis_completed',
        data: { 
          aiDetectionScore: aiDetectionScore,
          passesThreshold: aiDetectionScore < this.config.aiDetectionThreshold!,
        },
        status: 'completed',
      });

      // Phase 3: Advanced Humanization (if enabled and needed)
      let finalContent = state.generation.content.content;
      let humanizationTechniques: string[] = [];
      
      if (this.config.enableAdvancedHumanization && aiDetectionScore > this.config.aiDetectionThreshold!) {
        this.logger.info('Phase 3: Advanced humanization');
        const humanizationResult = await this.applyAdvancedHumanization(finalContent);
        finalContent = humanizationResult.content;
        humanizationTechniques = humanizationResult.techniques;
        
        updatedState = StateManager.updateWorkflow(updatedState, { progress: 70 });
        updatedState = StateManager.addMessage(updatedState, {
          agent: 'quality_agent',
          action: 'advanced_humanization_completed',
          data: { 
            techniques: humanizationTechniques.length,
            humanLikenessScore: humanizationResult.humanLikenessScore,
          },
          status: 'completed',
        });
      }

      // Phase 4: Final Quality Metrics Calculation
      this.logger.info('Phase 4: Final quality metrics');
      const finalMetrics = await this.calculateFinalMetrics(updatedState, qualityAssessment, aiDetectionScore);
      
      // Update quality state
      updatedState = StateManager.updateQuality(updatedState, {
        metrics: finalMetrics,
        validationResults: [
          `Overall quality: ${qualityAssessment.overallScore}/100`,
          `AI detection score: ${aiDetectionScore}%`,
          `Human-likeness: ${finalMetrics.humanLikenessScore}/100`,
        ],
        humanizationApplied: humanizationTechniques,
        completed: true,
      });

      // Update final content
      updatedState = StateManager.updateGeneration(updatedState, {
        content: {
          ...updatedState.generation.content,
          content: finalContent,
          humanizationApplied: [...updatedState.generation.content.humanizationApplied, ...humanizationTechniques],
        },
      });

      // Generate final result
      const finalResult = this.generateFinalResult(updatedState, finalMetrics);
      updatedState = {
        ...updatedState,
        result: finalResult,
      };

      updatedState = StateManager.updateWorkflow(updatedState, {
        progress: 100,
        nextAgent: null,
        completedAgents: [...updatedState.workflow.completedAgents, 'quality_agent'],
      });

      updatedState = StateManager.addMessage(updatedState, {
        agent: 'quality_agent',
        action: 'quality_assurance_completed',
        data: { 
          finalScore: finalMetrics.humanLikenessScore,
          aiDetectionScore: finalMetrics.aiDetectionScore,
          seoScore: finalMetrics.seoScore,
          executionTime: timer.elapsed(),
        },
        status: 'completed',
      });

      timer.log(this.logger);
      this.logger.info('Quality agent completed successfully');
      return updatedState;

    } catch (error) {
      logError(error, 'Quality Agent', this.logger);
      
      const errorState = StateManager.updateWorkflow(updatedState, {
        errors: [...updatedState.workflow.errors, `Quality assurance failed: ${error}`],
      });

      return StateManager.addMessage(errorState, {
        agent: 'quality_agent',
        action: 'quality_assurance_failed',
        data: { error: error.toString(), executionTime: timer.elapsed() },
        status: 'error',
      });
    }
  }

  /**
   * Assess overall content quality
   */
  private async assessContentQuality(state: Mark1State): Promise<QualityAssessment> {
    try {
      const content = state.generation.content.content;
      const requirements = state.requirements;

      const prompt = `Assess the quality of this content based on the requirements:

CONTENT:
${content.substring(0, 1500)}...

REQUIREMENTS:
- Topic: ${requirements.topic}
- Target Length: ${requirements.contentLength} words
- Target Audience: ${requirements.targetAudience}
- Tone: ${requirements.tone}

Evaluate:
1. Content relevance and accuracy
2. Structure and organization
3. Readability and flow
4. Completeness and depth
5. Engagement and value

Return JSON with:
{
  "overallScore": 0-100,
  "issues": ["issue1", "issue2"],
  "strengths": ["strength1", "strength2"],
  "recommendations": ["rec1", "rec2"]
}`;

      const response = await this.aiClient.generateJSON(
        [{ role: 'user', content: prompt }],
        {
          overallScore: 75,
          issues: ['Assessment failed - manual review needed'],
          strengths: ['Generated content completed'],
          recommendations: ['Manual quality check recommended']
        }
      );
      
      return {
        overallScore: response.overallScore || 75,
        issues: response.issues || [],
        strengths: response.strengths || [],
        recommendations: response.recommendations || [],
      };
    } catch (error) {
      logError(error, 'Content Quality Assessment', this.logger);
      
      return {
        overallScore: 75,
        issues: ['Assessment failed - manual review needed'],
        strengths: ['Generated content completed'],
        recommendations: ['Manual quality check recommended'],
      };
    }
  }

  /**
   * Analyze AI detection likelihood
   */
  private async analyzeAIDetection(content: string): Promise<number> {
    try {
      // Simplified AI detection analysis
      const text = content.replace(/<[^>]*>/g, '');
      
      // Check for AI-like patterns
      let aiScore = 0;
      
      // Repetitive patterns
      const sentences = text.split(/[.!?]+/);
      const avgSentenceLength = sentences.reduce((sum, s) => sum + s.split(' ').length, 0) / sentences.length;
      if (avgSentenceLength > 25) aiScore += 10;
      if (avgSentenceLength < 10) aiScore += 5;
      
      // Common AI phrases
      const aiPhrases = [
        'it is important to note',
        'furthermore',
        'in conclusion',
        'it should be noted',
        'comprehensive guide',
        'best practices',
      ];
      
      const aiPhraseCount = aiPhrases.reduce((count, phrase) => {
        return count + (text.toLowerCase().includes(phrase) ? 1 : 0);
      }, 0);
      
      aiScore += aiPhraseCount * 5;
      
      // Lack of personal touches
      const personalWords = ['i', 'we', 'you', 'my', 'our', 'your'];
      const personalCount = personalWords.reduce((count, word) => {
        return count + (text.toLowerCase().split(' ').filter(w => w === word).length);
      }, 0);
      
      if (personalCount < 5) aiScore += 10;
      
      return Math.min(100, Math.max(0, aiScore));
    } catch (error) {
      logError(error, 'AI Detection Analysis', this.logger);
      return 50; // Default moderate score
    }
  }

  /**
   * Apply advanced humanization techniques
   */
  private async applyAdvancedHumanization(content: string): Promise<HumanizationResult> {
    try {
      const prompt = `Apply advanced humanization techniques to make this content more natural and human-like:

${content}

Apply these techniques:
1. Add personal anecdotes and experiences
2. Include conversational transitions
3. Use varied sentence structures
4. Add rhetorical questions
5. Include contractions and informal language where appropriate
6. Add emotional touches and personality
7. Use active voice and direct address
8. Include specific examples and stories

Return the humanized content maintaining the same HTML structure and information.`;

      const response = await this.aiClient.generateContent([
        { role: 'user', content: prompt }
      ]);

      const techniques = [
        'personal_anecdotes',
        'conversational_transitions',
        'varied_sentence_structure',
        'rhetorical_questions',
        'contractions_added',
        'emotional_touches',
        'active_voice',
        'specific_examples',
      ];

      // Calculate human-likeness score
      const humanLikenessScore = this.calculateHumanLikenessScore(response.content);

      return {
        content: response.content,
        techniques,
        humanLikenessScore,
      };
    } catch (error) {
      logError(error, 'Advanced Humanization', this.logger);
      
      return {
        content,
        techniques: ['humanization_failed'],
        humanLikenessScore: 60,
      };
    }
  }

  /**
   * Calculate human-likeness score
   */
  private calculateHumanLikenessScore(content: string): number {
    try {
      const text = content.replace(/<[^>]*>/g, '').toLowerCase();
      let score = 50; // Base score
      
      // Check for personal pronouns
      const personalPronouns = (text.match(/\b(i|we|you|my|our|your)\b/g) || []).length;
      score += Math.min(personalPronouns * 2, 20);
      
      // Check for contractions
      const contractions = (text.match(/\b\w+'\w+\b/g) || []).length;
      score += Math.min(contractions * 3, 15);
      
      // Check for questions
      const questions = (text.match(/\?/g) || []).length;
      score += Math.min(questions * 5, 15);
      
      return Math.min(100, Math.max(0, score));
    } catch (error) {
      return 70; // Default score
    }
  }

  /**
   * Calculate final quality metrics
   */
  private async calculateFinalMetrics(
    state: Mark1State, 
    qualityAssessment: QualityAssessment, 
    aiDetectionScore: number
  ): Promise<QualityMetrics> {
    try {
      const seoScore = state.analysis.seo.seoScore || 70;
      const geoScore = state.analysis.geo.geoScore || 70;
      const humanLikenessScore = Math.max(0, 100 - aiDetectionScore);
      
      return {
        aiDetectionScore,
        originalityScore: qualityAssessment.overallScore,
        competitiveAdvantage: Math.min(100, (state.analysis.competitive.opportunities.length * 10) + 60),
        seoScore,
        geoScore,
        humanLikenessScore,
      };
    } catch (error) {
      logError(error, 'Final Metrics Calculation', this.logger);
      
      return {
        aiDetectionScore: 50,
        originalityScore: 70,
        competitiveAdvantage: 70,
        seoScore: 70,
        geoScore: 70,
        humanLikenessScore: 70,
      };
    }
  }

  /**
   * Generate final result object
   */
  private generateFinalResult(state: Mark1State, quality: QualityMetrics): any {
    return {
      article: {
        title: state.generation.content.title,
        content: state.generation.content.content,
        metaDescription: state.generation.content.metaDescription,
        wordCount: state.generation.content.wordCount,
        readabilityScore: state.generation.content.readabilityScore,
        keywords: {
          primary: state.analysis.seo.primaryKeywords,
          secondary: state.analysis.seo.secondaryKeywords,
        },
      },
      analytics: {
        researchQueries: state.research.data.length,
        totalSources: state.research.data.reduce((sum, r) => sum + r.results.length, 0),
        competitorsAnalyzed: state.analysis.competitive.topContent.length,
        contentGapsAddressed: state.analysis.competitive.contentGaps.length,
        opportunitiesLeveraged: state.analysis.competitive.opportunities.length,
        humanizationTechniques: state.generation.content.humanizationApplied.length,
      },
      performance: {
        executionTime: Date.now() - state.startTime,
        agentsExecuted: state.workflow.completedAgents.length,
        qualityScore: Math.round((quality.humanLikenessScore + quality.seoScore + quality.geoScore + quality.competitiveAdvantage) / 4),
        success: quality.humanLikenessScore >= 70 && quality.seoScore >= 70,
      },
    };
  }

  /**
   * Get agent status
   */
  getStatus(): {
    isReady: boolean;
    aiClientStatus: any;
    config: QualityAgentConfig;
  } {
    return {
      isReady: true,
      aiClientStatus: this.aiClient.getStatus(),
      config: this.config,
    };
  }
}
