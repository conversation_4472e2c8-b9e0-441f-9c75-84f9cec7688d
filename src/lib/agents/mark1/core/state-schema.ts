/**
 * Mark1 Agent System - Simplified State Schema
 * Fixed version of v2 with robust error handling and simplified state management
 */

export interface ContentRequirements {
  topic: string;
  contentLength: number;
  tone: string;
  targetAudience: string;
  customInstructions?: string;
  contentType: string;
  keywords?: string[];
}

export interface ResearchData {
  query: string;
  results: Array<{
    title: string;
    url: string;
    content: string;
    publishedDate?: string;
    score?: number;
  }>;
  competitorAnalysis: {
    topCompetitors: string[];
    contentGaps: string[];
    strengthsWeaknesses: string[];
  };
  searchDepth: 'basic' | 'advanced';
  timestamp: number;
}

export interface SEOAnalysis {
  primaryKeywords: string[];
  secondaryKeywords: string[];
  keywordDensity: Record<string, number>;
  competitorKeywords: string[];
  seoScore: number;
  recommendations: string[];
}

export interface GEOOptimization {
  aiSearchVisibility: {
    perplexity: boolean;
    chatgpt: boolean;
    gemini: boolean;
    claude: boolean;
  };
  referenceOptimization: {
    citations: string[];
    sourceCredibility: number;
    factualAccuracy: number;
  };
  multimodalOptimization: {
    imageRecommendations: string[];
    videoSuggestions: string[];
    interactiveElements: string[];
  };
  geoScore: number;
}

export interface ContentGeneration {
  outline: string[];
  content: string;
  title: string;
  metaDescription: string;
  wordCount: number;
  readabilityScore: number;
  humanizationApplied: string[];
}

export interface QualityMetrics {
  aiDetectionScore: number;
  originalityScore: number;
  competitiveAdvantage: number;
  seoScore: number;
  geoScore: number;
  humanLikenessScore: number;
}

export interface AgentMessage {
  agent: string;
  action: string;
  data: any;
  timestamp: number;
  status: 'pending' | 'in_progress' | 'completed' | 'error';
}

/**
 * Simplified Mark1 State Interface
 * Reduced complexity while maintaining functionality
 */
export interface Mark1State {
  // Input Configuration
  requirements: ContentRequirements;
  
  // Research Phase - Simplified
  research: {
    data: ResearchData[];
    currentQuery: string;
    completed: boolean;
    errors: string[];
  };
  
  // Analysis Phase - Simplified
  analysis: {
    seo: SEOAnalysis;
    geo: GEOOptimization;
    competitive: {
      topContent: any[];
      contentGaps: string[];
      opportunities: string[];
    };
    completed: boolean;
    errors: string[];
  };
  
  // Content Generation Phase - Simplified
  generation: {
    content: ContentGeneration;
    iterations: number;
    completed: boolean;
    errors: string[];
  };
  
  // Quality Assurance Phase - Simplified
  quality: {
    metrics: QualityMetrics;
    validationResults: string[];
    humanizationApplied: string[];
    completed: boolean;
    errors: string[];
  };
  
  // Workflow Management - Simplified
  workflow: {
    currentAgent: string;
    nextAgent: string | null;
    completedAgents: string[];
    errors: string[];
    progress: number;
    retryCount: number;
  };
  
  // Communication - Simplified
  messages: AgentMessage[];
  
  // Results - Simplified
  result: {
    article: any;
    analytics: any;
    performance: any;
  } | null;
  
  // Metadata
  sessionId: string;
  startTime: number;
  endTime?: number;
  version: 'mark1';
}

/**
 * Create initial state with safe defaults
 */
export function createInitialState(requirements: ContentRequirements): Mark1State {
  return {
    requirements,
    research: {
      data: [],
      currentQuery: '',
      completed: false,
      errors: [],
    },
    analysis: {
      seo: {
        primaryKeywords: [],
        secondaryKeywords: [],
        keywordDensity: {},
        competitorKeywords: [],
        seoScore: 0,
        recommendations: [],
      },
      geo: {
        aiSearchVisibility: {
          perplexity: false,
          chatgpt: false,
          gemini: false,
          claude: false,
        },
        referenceOptimization: {
          citations: [],
          sourceCredibility: 0,
          factualAccuracy: 0,
        },
        multimodalOptimization: {
          imageRecommendations: [],
          videoSuggestions: [],
          interactiveElements: [],
        },
        geoScore: 0,
      },
      competitive: {
        topContent: [],
        contentGaps: [],
        opportunities: [],
      },
      completed: false,
      errors: [],
    },
    generation: {
      content: {
        outline: [],
        content: '',
        title: '',
        metaDescription: '',
        wordCount: 0,
        readabilityScore: 0,
        humanizationApplied: [],
      },
      iterations: 0,
      completed: false,
      errors: [],
    },
    quality: {
      metrics: {
        aiDetectionScore: 0,
        originalityScore: 0,
        competitiveAdvantage: 0,
        seoScore: 0,
        geoScore: 0,
        humanLikenessScore: 0,
      },
      validationResults: [],
      humanizationApplied: [],
      completed: false,
      errors: [],
    },
    workflow: {
      currentAgent: 'supervisor',
      nextAgent: 'research_agent',
      completedAgents: [],
      errors: [],
      progress: 0,
      retryCount: 0,
    },
    messages: [],
    result: null,
    sessionId: `mark1_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    startTime: Date.now(),
    version: 'mark1',
  };
}

/**
 * Simplified State Manager with robust error handling
 */
export class StateManager {
  static updateResearch(state: Mark1State, data: ResearchData | ResearchData[]): Mark1State {
    try {
      const newData = Array.isArray(data) ? data : [data];
      return {
        ...state,
        research: {
          ...state.research,
          data: [...state.research.data, ...newData],
        },
      };
    } catch (error) {
      console.error('StateManager.updateResearch error:', error);
      return {
        ...state,
        research: {
          ...state.research,
          errors: [...state.research.errors, `Research update failed: ${error}`],
        },
      };
    }
  }

  static updateAnalysis(state: Mark1State, updates: Partial<Mark1State['analysis']>): Mark1State {
    try {
      return {
        ...state,
        analysis: {
          ...state.analysis,
          ...updates,
        },
      };
    } catch (error) {
      console.error('StateManager.updateAnalysis error:', error);
      return {
        ...state,
        analysis: {
          ...state.analysis,
          errors: [...state.analysis.errors, `Analysis update failed: ${error}`],
        },
      };
    }
  }

  static updateGeneration(state: Mark1State, updates: Partial<Mark1State['generation']>): Mark1State {
    try {
      return {
        ...state,
        generation: {
          ...state.generation,
          ...updates,
        },
      };
    } catch (error) {
      console.error('StateManager.updateGeneration error:', error);
      return {
        ...state,
        generation: {
          ...state.generation,
          errors: [...state.generation.errors, `Generation update failed: ${error}`],
        },
      };
    }
  }

  static updateQuality(state: Mark1State, updates: Partial<Mark1State['quality']>): Mark1State {
    try {
      return {
        ...state,
        quality: {
          ...state.quality,
          ...updates,
        },
      };
    } catch (error) {
      console.error('StateManager.updateQuality error:', error);
      return {
        ...state,
        quality: {
          ...state.quality,
          errors: [...state.quality.errors, `Quality update failed: ${error}`],
        },
      };
    }
  }

  static updateWorkflow(state: Mark1State, updates: Partial<Mark1State['workflow']>): Mark1State {
    try {
      return {
        ...state,
        workflow: {
          ...state.workflow,
          ...updates,
        },
      };
    } catch (error) {
      console.error('StateManager.updateWorkflow error:', error);
      return {
        ...state,
        workflow: {
          ...state.workflow,
          errors: [...state.workflow.errors, `Workflow update failed: ${error}`],
        },
      };
    }
  }

  static addMessage(state: Mark1State, message: Omit<AgentMessage, 'timestamp'>): Mark1State {
    try {
      return {
        ...state,
        messages: [
          ...state.messages,
          {
            ...message,
            timestamp: Date.now(),
          },
        ],
      };
    } catch (error) {
      console.error('StateManager.addMessage error:', error);
      return state; // Return unchanged state on error
    }
  }
}
