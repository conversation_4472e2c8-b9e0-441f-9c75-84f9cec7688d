/**
 * Mark1 Agent System - Supervisor Agent
 * Simplified workflow coordination with robust error handling
 */

import { Mark1State, StateManager, createInitialState, ContentRequirements } from './state-schema';
import { AIClient, createAIClient, safeJsonParse } from '../tools/ai-client';
import { createLogger, Timer, logError } from '../utils/logger';

export interface SupervisorConfig {
  openRouterApiKey: string;
  tavilyApiKey: string;
  model?: string;
  streaming?: boolean;
  maxRetries?: number;
  qualityThreshold?: number;
  timeout?: number;
}

export interface AgentCapability {
  name: string;
  description: string;
  inputs: string[];
  outputs: string[];
  estimatedTime: number; // in seconds
  dependencies: string[];
}

export interface WorkflowPlan {
  agents: string[];
  reasoning: string;
  estimatedTime: number;
}

export interface ValidationResult {
  isValid: boolean;
  qualityScore: number;
  issues: string[];
}

/**
 * Simplified Supervisor Agent with robust error handling
 */
export class SupervisorAgent {
  private aiClient: AIClient;
  private config: SupervisorConfig;
  private agentCapabilities: Map<string, AgentCapability>;
  private currentWorkflow: string[] = [];
  private logger: ReturnType<typeof createLogger>;

  constructor(config: SupervisorConfig) {
    this.config = {
      model: 'qwen/qwen-2.5-72b-instruct',
      streaming: false,
      maxRetries: 2,
      qualityThreshold: 80,
      timeout: 300000, // 5 minutes
      ...config,
    };

    this.aiClient = createAIClient(this.config.openRouterApiKey, this.config.model);
    this.logger = createLogger({ 
      agentName: 'SupervisorAgent',
      workflowId: `workflow_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`
    });
    
    this.logger.info('Supervisor initialized');
    this.initializeAgentCapabilities();
  }

  /**
   * Initialize agent capabilities and dependencies
   */
  private initializeAgentCapabilities() {
    this.agentCapabilities = new Map([
      ['research_agent', {
        name: 'Research Agent',
        description: 'Conducts comprehensive research with competitive analysis and SEO intelligence',
        inputs: ['topic', 'contentType', 'targetAudience'],
        outputs: ['researchData', 'competitiveAnalysis', 'seoIntelligence'],
        estimatedTime: 45,
        dependencies: [],
      }],
      ['content_agent', {
        name: 'Content Generation Agent',
        description: 'Creates high-quality, human-like content using advanced AI techniques',
        inputs: ['researchData', 'outline', 'requirements', 'competitiveAnalysis'],
        outputs: ['content', 'title', 'metaDescription'],
        estimatedTime: 60,
        dependencies: ['research_agent'],
      }],
      ['seo_geo_agent', {
        name: 'SEO & GEO Optimization Agent',
        description: 'Optimizes content for traditional SEO and generative engine optimization',
        inputs: ['content', 'keywords', 'competitiveAnalysis'],
        outputs: ['optimizedContent', 'seoScore', 'geoOptimization'],
        estimatedTime: 30,
        dependencies: ['content_agent'],
      }],
      ['quality_agent', {
        name: 'Quality Assurance Agent',
        description: 'Validates content quality, applies humanization, and ensures AI detection bypass',
        inputs: ['content', 'requirements', 'qualityMetrics'],
        outputs: ['validatedContent', 'qualityScore', 'humanizationReport'],
        estimatedTime: 25,
        dependencies: ['seo_geo_agent'],
      }],
    ]);
  }

  /**
   * Main workflow execution method
   */
  async executeWorkflow(requirements: ContentRequirements): Promise<Mark1State> {
    const workflowTimer = new Timer('Complete Workflow');
    let state = createInitialState(requirements);
    
    this.logger.info(`Workflow started: "${requirements.topic}"`);
    
    try {
      // Update supervisor status
      state = StateManager.updateWorkflow(state, {
        currentAgent: 'supervisor',
        progress: 5,
      });

      // Plan workflow
      const plan = await this.planWorkflow(state);
      this.currentWorkflow = plan.agents;
      
      this.logger.info(`Workflow planned: ${this.currentWorkflow.join(' → ')} (~${plan.estimatedTime}s)`);

      state = StateManager.addMessage(state, {
        agent: 'supervisor',
        action: 'workflow_started',
        data: { 
          topic: requirements.topic,
          estimatedTime: plan.estimatedTime,
          agents: plan.agents,
        },
        status: 'in_progress',
      });

      // Execute each agent in the planned sequence
      for (let i = 0; i < this.currentWorkflow.length; i++) {
        const agentName = this.currentWorkflow[i];
        
        this.logger.info(`Executing agent ${i + 1}/${this.currentWorkflow.length}: ${agentName}`);
        
        state = await this.executeAgent(agentName, state);
        
        // Check for errors
        if (state.workflow.errors.length > 0) {
          this.logger.warn(`Agent ${agentName} reported errors:`, state.workflow.errors);
        }

        // Update progress
        const progress = Math.round(((i + 1) / this.currentWorkflow.length) * 90) + 5;
        state = StateManager.updateWorkflow(state, { progress });
      }

      // Final workflow validation
      const finalValidation = await this.validateFinalResult(state);
      
      if (finalValidation.isValid) {
        state = StateManager.updateWorkflow(state, {
          progress: 100,
          currentAgent: 'supervisor',
          nextAgent: null,
        });

        state = StateManager.addMessage(state, {
          agent: 'supervisor',
          action: 'workflow_completed',
          data: { 
            qualityScore: finalValidation.qualityScore,
            completionTime: Date.now() - state.startTime,
          },
          status: 'completed',
        });

        state.endTime = Date.now();
      } else {
        throw new Error(`Final validation failed: ${finalValidation.issues.join(', ')}`);
      }

      workflowTimer.log(this.logger);
      this.logger.info('Workflow completed successfully');
      return state;

    } catch (error) {
      logError(error, 'Supervisor Workflow', this.logger);
      
      return StateManager.addMessage(state, {
        agent: 'supervisor',
        action: 'workflow_failed',
        data: { error: error.toString(), executionTime: workflowTimer.elapsed() },
        status: 'error',
      });
    }
  }

  /**
   * Plan the optimal workflow based on requirements
   */
  private async planWorkflow(state: Mark1State): Promise<WorkflowPlan> {
    try {
      // For Mark1, we use a simplified, fixed workflow that works reliably
      const standardWorkflow = ['research_agent', 'content_agent', 'seo_geo_agent', 'quality_agent'];
      
      return {
        agents: standardWorkflow,
        reasoning: 'Standard Mark1 workflow optimized for reliability and performance',
        estimatedTime: this.calculateEstimatedTime(standardWorkflow),
      };
    } catch (error) {
      logError(error, 'Workflow Planning', this.logger);
      
      // Fallback to standard workflow
      return {
        agents: ['research_agent', 'content_agent', 'seo_geo_agent', 'quality_agent'],
        reasoning: 'Fallback to standard workflow due to planning error',
        estimatedTime: 160, // Default estimate
      };
    }
  }

  /**
   * Execute a specific agent
   */
  private async executeAgent(agentName: string, state: Mark1State): Promise<Mark1State> {
    const agentTimer = new Timer(`Agent: ${agentName}`);
    
    try {
      this.logger.info(`Starting agent: ${agentName}`);
      
      // Dynamic agent loading and execution
      switch (agentName) {
        case 'research_agent':
          const { ResearchAgent } = await import('../agents/research-agent');
          const researchAgent = new ResearchAgent({
            tavilyApiKey: this.config.tavilyApiKey,
          });
          return await researchAgent.execute(state);

        case 'content_agent':
          const { ContentAgent } = await import('../agents/content-agent');
          const contentAgent = new ContentAgent({
            openRouterApiKey: this.config.openRouterApiKey,
          });
          return await contentAgent.execute(state);

        case 'seo_geo_agent':
          const { SEOGEOAgent } = await import('../agents/seo-geo-agent');
          const seoAgent = new SEOGEOAgent({
            openRouterApiKey: this.config.openRouterApiKey,
          });
          return await seoAgent.execute(state);

        case 'quality_agent':
          const { QualityAgent } = await import('../agents/quality-agent');
          const qualityAgent = new QualityAgent({
            openRouterApiKey: this.config.openRouterApiKey,
          });
          return await qualityAgent.execute(state);

        default:
          throw new Error(`Agent execution not implemented: ${agentName}`);
      }
    } catch (error) {
      logError(error, `Agent ${agentName}`, this.logger);
      
      return StateManager.addMessage(state, {
        agent: 'supervisor',
        action: 'agent_execution_failed',
        data: { 
          agentName,
          error: error.toString(),
          executionTime: agentTimer.elapsed(),
        },
        status: 'error',
      });
    } finally {
      agentTimer.log(this.logger);
    }
  }

  /**
   * Calculate estimated execution time
   */
  private calculateEstimatedTime(agents?: string[]): number {
    const agentList = agents || this.currentWorkflow;
    return agentList.reduce((total, agentName) => {
      const capability = this.agentCapabilities.get(agentName);
      return total + (capability?.estimatedTime || 30);
    }, 10); // Add 10 seconds for supervisor overhead
  }

  /**
   * Validate final result
   */
  private async validateFinalResult(state: Mark1State): Promise<ValidationResult> {
    try {
      const issues: string[] = [];
      let qualityScore = 0;

      // Check if all phases completed
      if (!state.research.completed) {
        issues.push('Research phase not completed');
      } else {
        qualityScore += 25;
      }

      if (!state.generation.completed) {
        issues.push('Content generation not completed');
      } else {
        qualityScore += 25;
      }

      if (!state.analysis.completed) {
        issues.push('Analysis phase not completed');
      } else {
        qualityScore += 25;
      }

      if (!state.quality.completed) {
        issues.push('Quality assurance not completed');
      } else {
        qualityScore += 25;
      }

      // Check content quality
      if (state.generation.content.wordCount < state.requirements.contentLength * 0.8) {
        issues.push('Content length below target');
        qualityScore -= 10;
      }

      if (!state.generation.content.title) {
        issues.push('Missing title');
        qualityScore -= 5;
      }

      if (!state.generation.content.content) {
        issues.push('Missing content');
        qualityScore -= 20;
      }

      // Check for critical errors
      const totalErrors = state.research.errors.length + 
                         state.generation.errors.length + 
                         state.analysis.errors.length + 
                         state.quality.errors.length + 
                         state.workflow.errors.length;

      if (totalErrors > 5) {
        issues.push('Too many errors during execution');
        qualityScore -= 15;
      }

      qualityScore = Math.max(0, Math.min(100, qualityScore));
      const isValid = issues.length === 0 && qualityScore >= this.config.qualityThreshold!;

      return {
        isValid,
        qualityScore,
        issues,
      };
    } catch (error) {
      logError(error, 'Final Validation', this.logger);
      
      return {
        isValid: false,
        qualityScore: 0,
        issues: ['Validation process failed'],
      };
    }
  }

  /**
   * Get supervisor status
   */
  getStatus(): {
    isReady: boolean;
    currentWorkflow: string[];
    agentCapabilities: string[];
    config: SupervisorConfig;
  } {
    return {
      isReady: true,
      currentWorkflow: this.currentWorkflow,
      agentCapabilities: Array.from(this.agentCapabilities.keys()),
      config: this.config,
    };
  }
}

/**
 * Create supervisor agent with configuration
 */
export function createSupervisorAgent(config: SupervisorConfig): SupervisorAgent {
  return new SupervisorAgent(config);
}
