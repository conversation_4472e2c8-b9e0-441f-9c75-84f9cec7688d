/**
 * Mark1 Agent System - Main Export Index
 * Simplified and robust multi-agent content generation system
 */

// Core Components
export { 
  SupervisorAgent, 
  createSupervisorAgent,
  type SupervisorConfig,
  type AgentCapability,
  type WorkflowPlan,
  type ValidationResult,
} from './core/supervisor';

export {
  type Mark1State,
  type ContentRequirements,
  type ResearchData,
  type SEOAnalysis,
  type GEOOptimization,
  type ContentGeneration,
  type QualityMetrics,
  type AgentMessage,
  StateManager,
  createInitialState,
} from './core/state-schema';

// Individual Agents
export {
  ResearchAgent,
  type ResearchAgentConfig,
} from './agents/research-agent';

export {
  ContentAgent,
  type ContentAgentConfig,
  type ContentStrategy,
  type ResearchAnalysis,
} from './agents/content-agent';

export {
  SEOGEOAgent,
  type SEOGEOAgentConfig,
  type SEOOptimizationResult,
  type GEOOptimizationResult,
} from './agents/seo-geo-agent';

export {
  QualityAgent,
  type QualityAgentConfig,
  type QualityAssessment,
  type HumanizationResult,
} from './agents/quality-agent';

// Tools
export {
  AIClient,
  createAIClient,
  safeJsonParse,
  extractJsonFromText,
  type AIClientConfig,
  type AIMessage,
  type AIResponse,
} from './tools/ai-client';

export {
  TavilySearchTool,
  type TavilyConfig,
  type TavilyResult,
  type TavilyResponse,
  type CompetitiveAnalysis,
  type SEOIntelligence,
} from './tools/tavily-search';

// Utilities
export {
  Logger,
  logger,
  createLogger,
  logPerformance,
  Timer,
  logError,
  safeLog,
  type LogLevel,
  type LogEntry,
  type LoggerConfig,
} from './utils/logger';

// Test utilities (for development)
export {
  testMark1Basic,
  testMark1Components,
  runMark1Tests,
} from './test/basic-test';

// Version and metadata
export const MARK1_VERSION = '1.0.0';
export const MARK1_DESCRIPTION = 'Simplified and robust multi-agent content generation system';

/**
 * Quick start function for Mark1
 */
export async function createMark1Agent(config: {
  openRouterApiKey: string;
  tavilyApiKey: string;
  qualityThreshold?: number;
}) {
  const { createSupervisorAgent } = await import('./core/supervisor');
  
  return createSupervisorAgent({
    openRouterApiKey: config.openRouterApiKey,
    tavilyApiKey: config.tavilyApiKey,
    qualityThreshold: config.qualityThreshold || 75,
    streaming: false,
    maxRetries: 2,
  });
}

/**
 * Generate content using Mark1 (convenience function)
 */
export async function generateContent(
  requirements: ContentRequirements,
  config: {
    openRouterApiKey: string;
    tavilyApiKey: string;
    qualityThreshold?: number;
  }
) {
  const agent = await createMark1Agent(config);
  return await agent.executeWorkflow(requirements);
}

/**
 * Mark1 system information
 */
export const MARK1_INFO = {
  version: MARK1_VERSION,
  description: MARK1_DESCRIPTION,
  agents: ['research_agent', 'content_agent', 'seo_geo_agent', 'quality_agent'],
  improvements: [
    'Simplified state management with robust error handling',
    'Fixed API key rotation issues',
    'Improved error isolation and recovery',
    'Better fallback mechanisms',
    'Enhanced JSON parsing with safe defaults',
    'Streamlined logging and monitoring',
    'Reduced complexity while maintaining functionality',
    'More predictable execution flow',
  ],
  capabilities: [
    'Comprehensive research with Tavily search',
    'Competitive analysis and market intelligence',
    'Advanced content generation with AI',
    'SEO and GEO optimization',
    'Quality assurance and humanization',
    'Robust error handling and recovery',
    'Simplified workflow coordination',
  ],
  workflow: {
    type: 'sequential',
    agents: 4,
    estimatedTime: '2-3 minutes',
    errorHandling: 'comprehensive with fallbacks',
  },
};

// Default export
export default {
  createSupervisorAgent,
  createMark1Agent,
  generateContent,
  StateManager,
  createInitialState,
  MARK1_VERSION,
  MARK1_INFO,
};
