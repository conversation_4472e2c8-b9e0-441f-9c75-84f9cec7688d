/**
 * Mark1 Agent System - Simplified Logger
 * Robust logging with minimal complexity
 */

export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

export interface LogEntry {
  timestamp: number;
  level: LogLevel;
  message: string;
  data?: any;
  agentName?: string;
  phase?: string;
}

export interface LoggerConfig {
  agentName?: string;
  phase?: string;
  enableConsole?: boolean;
  enableStorage?: boolean;
  maxEntries?: number;
}

/**
 * Simplified Lo<PERSON> with robust error handling
 */
export class Logger {
  private config: LoggerConfig;
  private entries: LogEntry[] = [];

  constructor(config: LoggerConfig = {}) {
    this.config = {
      enableConsole: true,
      enableStorage: false,
      maxEntries: 1000,
      ...config,
    };
  }

  /**
   * Log debug message
   */
  debug(message: string, data?: any): void {
    this.log('debug', message, data);
  }

  /**
   * Log info message
   */
  info(message: string, data?: any): void {
    this.log('info', message, data);
  }

  /**
   * Log warning message
   */
  warn(message: string, data?: any): void {
    this.log('warn', message, data);
  }

  /**
   * Log error message
   */
  error(message: string, data?: any): void {
    this.log('error', message, data);
  }

  /**
   * Core logging method
   */
  private log(level: LogLevel, message: string, data?: any): void {
    try {
      const entry: LogEntry = {
        timestamp: Date.now(),
        level,
        message,
        data,
        agentName: this.config.agentName,
        phase: this.config.phase,
      };

      // Add to entries if storage enabled
      if (this.config.enableStorage) {
        this.entries.push(entry);
        
        // Trim entries if exceeding max
        if (this.entries.length > this.config.maxEntries!) {
          this.entries = this.entries.slice(-this.config.maxEntries!);
        }
      }

      // Console output if enabled
      if (this.config.enableConsole) {
        this.outputToConsole(entry);
      }
    } catch (error) {
      // Fallback console log if logger fails
      console.error('Logger failed:', error);
      console.log(`[${level.toUpperCase()}] ${message}`, data);
    }
  }

  /**
   * Output to console with formatting
   */
  private outputToConsole(entry: LogEntry): void {
    const timestamp = new Date(entry.timestamp).toISOString().substr(11, 8);
    const prefix = entry.agentName ? `[${entry.agentName}]` : '[Mark1]';
    const phase = entry.phase ? ` (${entry.phase})` : '';
    
    const formattedMessage = `${timestamp} ${prefix}${phase} ${entry.message}`;

    switch (entry.level) {
      case 'debug':
        console.debug(formattedMessage, entry.data || '');
        break;
      case 'info':
        console.info(formattedMessage, entry.data || '');
        break;
      case 'warn':
        console.warn(formattedMessage, entry.data || '');
        break;
      case 'error':
        console.error(formattedMessage, entry.data || '');
        break;
    }
  }

  /**
   * Get all log entries
   */
  getEntries(): LogEntry[] {
    return [...this.entries];
  }

  /**
   * Get entries by level
   */
  getEntriesByLevel(level: LogLevel): LogEntry[] {
    return this.entries.filter(entry => entry.level === level);
  }

  /**
   * Clear all entries
   */
  clear(): void {
    this.entries = [];
  }

  /**
   * Get logger statistics
   */
  getStats(): {
    totalEntries: number;
    entriesByLevel: Record<LogLevel, number>;
    oldestEntry?: number;
    newestEntry?: number;
  } {
    const entriesByLevel = this.entries.reduce((acc, entry) => {
      acc[entry.level] = (acc[entry.level] || 0) + 1;
      return acc;
    }, {} as Record<LogLevel, number>);

    return {
      totalEntries: this.entries.length,
      entriesByLevel,
      oldestEntry: this.entries[0]?.timestamp,
      newestEntry: this.entries[this.entries.length - 1]?.timestamp,
    };
  }
}

/**
 * Global logger instance
 */
export const logger = new Logger({
  agentName: 'Mark1',
  enableConsole: true,
  enableStorage: true,
});

/**
 * Create a logger for specific agent/phase
 */
export function createLogger(config: LoggerConfig): Logger {
  return new Logger(config);
}

/**
 * Performance logging utility
 */
export function logPerformance<T>(
  operation: string,
  fn: () => Promise<T>,
  logger?: Logger
): Promise<T> {
  return new Promise(async (resolve, reject) => {
    const startTime = Date.now();
    const log = logger || new Logger();
    
    try {
      log.info(`🚀 Starting: ${operation}`);
      const result = await fn();
      const duration = Date.now() - startTime;
      log.info(`✅ Completed: ${operation} (${duration}ms)`);
      resolve(result);
    } catch (error) {
      const duration = Date.now() - startTime;
      log.error(`❌ Failed: ${operation} (${duration}ms)`, error);
      reject(error);
    }
  });
}

/**
 * Simple timing utility
 */
export class Timer {
  private startTime: number;
  private label: string;

  constructor(label: string = 'Operation') {
    this.label = label;
    this.startTime = Date.now();
  }

  /**
   * Get elapsed time in milliseconds
   */
  elapsed(): number {
    return Date.now() - this.startTime;
  }

  /**
   * Log elapsed time
   */
  log(logger?: Logger): void {
    const elapsed = this.elapsed();
    const log = logger || new Logger();
    log.info(`⏱️ ${this.label}: ${elapsed}ms`);
  }

  /**
   * Reset timer
   */
  reset(): void {
    this.startTime = Date.now();
  }
}

/**
 * Error logging utility
 */
export function logError(error: any, context?: string, logger?: Logger): void {
  const log = logger || new Logger();
  const errorMessage = error instanceof Error ? error.message : String(error);
  const errorStack = error instanceof Error ? error.stack : undefined;
  
  log.error(`${context ? `[${context}] ` : ''}${errorMessage}`, {
    stack: errorStack,
    error: error,
  });
}

/**
 * Safe logging wrapper that never throws
 */
export function safeLog(level: LogLevel, message: string, data?: any, logger?: Logger): void {
  try {
    const log = logger || new Logger();
    log[level](message, data);
  } catch (error) {
    // Fallback to console if logger fails
    console.log(`[SAFE-LOG-${level.toUpperCase()}] ${message}`, data);
  }
}
