/**
 * Mark1 Agent System - Simplified AI Client
 * Robust OpenRouter client with simplified error handling
 */

import OpenAI from 'openai';

export interface AIClientConfig {
  apiKey: string;
  model: string;
  baseURL?: string;
  temperature?: number;
  maxTokens?: number;
  timeout?: number;
}

export interface AIMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface AIResponse {
  content: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  model?: string;
}

/**
 * Simplified AI Client with robust error handling
 */
export class AIClient {
  private client: OpenAI;
  private config: AIClientConfig;
  private requestCount: number = 0;
  private lastRequestTime: number = 0;

  constructor(config: AIClientConfig) {
    this.config = {
      baseURL: 'https://openrouter.ai/api/v1',
      temperature: 0.7,
      maxTokens: 4000,
      timeout: 60000,
      ...config,
    };

    this.client = new OpenAI({
      apiKey: this.config.apiKey,
      baseURL: this.config.baseURL,
      timeout: this.config.timeout,
    });

    console.log(`🤖 AI Client initialized with model: ${this.config.model}`);
  }

  /**
   * Generate content with robust error handling
   */
  async generateContent(messages: AIMessage[], options: Partial<AIClientConfig> = {}): Promise<AIResponse> {
    const requestId = `ai_${Date.now()}_${Math.random().toString(36).substr(2, 4)}`;
    const startTime = Date.now();

    try {
      console.log(`🤖 AI Request [${requestId}]: ${messages.length} messages`);

      const response = await this.client.chat.completions.create({
        model: options.model || this.config.model,
        messages: messages.map(msg => ({
          role: msg.role,
          content: msg.content,
        })),
        temperature: options.temperature ?? this.config.temperature,
        max_tokens: options.maxTokens ?? this.config.maxTokens,
      });

      const responseTime = Date.now() - startTime;
      const content = response.choices[0]?.message?.content || '';

      console.log(`✅ AI Success [${requestId}]: ${content.length} chars in ${responseTime}ms`);

      this.requestCount++;
      this.lastRequestTime = Date.now();

      return {
        content,
        usage: response.usage ? {
          promptTokens: response.usage.prompt_tokens,
          completionTokens: response.usage.completion_tokens,
          totalTokens: response.usage.total_tokens,
        } : undefined,
        model: response.model,
      };

    } catch (error) {
      const responseTime = Date.now() - startTime;
      console.error(`❌ AI Request Failed [${requestId}]: ${error} (${responseTime}ms)`);

      // Return fallback response instead of throwing
      return {
        content: this.generateFallbackContent(messages),
        usage: undefined,
        model: this.config.model,
      };
    }
  }

  /**
   * Generate fallback content when AI fails
   */
  private generateFallbackContent(messages: AIMessage[]): string {
    const lastUserMessage = messages.filter(m => m.role === 'user').pop();
    const topic = lastUserMessage?.content || 'the requested topic';

    console.log(`🔄 Using fallback content generation`);

    return `# ${topic.charAt(0).toUpperCase() + topic.slice(1)}

This content was generated using fallback methods due to AI service unavailability.

## Introduction

${topic} is an important subject that requires careful consideration and understanding. This comprehensive guide will provide you with the essential information you need to know.

## Key Points

1. **Understanding the Basics**: ${topic} involves several fundamental concepts that are crucial for success.

2. **Best Practices**: Following established best practices ensures optimal results when working with ${topic}.

3. **Common Challenges**: Like any complex subject, ${topic} comes with its own set of challenges that can be overcome with proper planning.

4. **Implementation**: Successful implementation of ${topic} requires a systematic approach and attention to detail.

## Benefits

- Improved understanding of core concepts
- Better decision-making capabilities
- Enhanced problem-solving skills
- Increased efficiency and effectiveness

## Conclusion

${topic} is a valuable area of knowledge that can provide significant benefits when properly understood and implemented. By following the guidelines and best practices outlined in this guide, you can achieve success in your endeavors.

*Note: This content was generated using fallback methods. For more detailed and specific information, please try again when the AI service is available.*`;
  }

  /**
   * Generate content with retry logic
   */
  async generateWithRetry(messages: AIMessage[], maxRetries: number = 2, options: Partial<AIClientConfig> = {}): Promise<AIResponse> {
    let lastError: Error | null = null;

    for (let attempt = 0; attempt < maxRetries; attempt++) {
      try {
        return await this.generateContent(messages, options);
      } catch (error) {
        lastError = error as Error;
        console.warn(`⚠️ AI generation attempt ${attempt + 1} failed: ${lastError.message}`);
        
        if (attempt < maxRetries - 1) {
          // Wait before retry with exponential backoff
          await new Promise(resolve => setTimeout(resolve, 1000 * (attempt + 1)));
        }
      }
    }

    // All attempts failed, return fallback
    console.error(`❌ All AI generation attempts failed`);
    return {
      content: this.generateFallbackContent(messages),
      usage: undefined,
      model: this.config.model,
    };
  }

  /**
   * Parse JSON response with fallback
   */
  async generateJSON<T>(messages: AIMessage[], fallback: T, options: Partial<AIClientConfig> = {}): Promise<T> {
    try {
      const response = await this.generateContent(messages, options);
      const parsed = JSON.parse(response.content);
      return parsed as T;
    } catch (error) {
      console.warn(`⚠️ JSON parsing failed, using fallback: ${error}`);
      return fallback;
    }
  }

  /**
   * Generate structured content with validation
   */
  async generateStructured(
    messages: AIMessage[], 
    validator: (content: string) => boolean,
    maxRetries: number = 2,
    options: Partial<AIClientConfig> = {}
  ): Promise<AIResponse> {
    for (let attempt = 0; attempt < maxRetries; attempt++) {
      const response = await this.generateContent(messages, options);
      
      if (validator(response.content)) {
        return response;
      }
      
      console.warn(`⚠️ Generated content failed validation, attempt ${attempt + 1}`);
      
      if (attempt < maxRetries - 1) {
        // Add validation feedback to messages
        messages.push({
          role: 'user',
          content: 'The previous response did not meet the required format. Please try again with the correct structure.',
        });
      }
    }

    // Return fallback if validation keeps failing
    return {
      content: this.generateFallbackContent(messages),
      usage: undefined,
      model: this.config.model,
    };
  }

  /**
   * Get client status
   */
  getStatus(): {
    requestCount: number;
    lastRequestTime: number;
    isReady: boolean;
    model: string;
  } {
    return {
      requestCount: this.requestCount,
      lastRequestTime: this.lastRequestTime,
      isReady: !!this.config.apiKey,
      model: this.config.model,
    };
  }
}

/**
 * Create AI client with default configuration
 */
export function createAIClient(apiKey: string, model: string = 'qwen/qwen-2.5-72b-instruct'): AIClient {
  return new AIClient({
    apiKey,
    model,
    temperature: 0.7,
    maxTokens: 4000,
  });
}

/**
 * Safe JSON parsing with fallback
 */
export function safeJsonParse<T>(jsonString: string, fallback: T): T {
  try {
    const parsed = JSON.parse(jsonString);
    return parsed as T;
  } catch (error) {
    console.warn(`⚠️ JSON parsing failed: ${error}`);
    return fallback;
  }
}

/**
 * Extract JSON from text that might contain other content
 */
export function extractJsonFromText<T>(text: string, fallback: T): T {
  try {
    // Try to find JSON in the text
    const jsonMatch = text.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      return JSON.parse(jsonMatch[0]) as T;
    }
    
    // Try parsing the entire text
    return JSON.parse(text) as T;
  } catch (error) {
    console.warn(`⚠️ JSON extraction failed: ${error}`);
    return fallback;
  }
}
