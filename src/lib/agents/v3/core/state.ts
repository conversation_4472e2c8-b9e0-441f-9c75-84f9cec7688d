/**
 * Invincible V3 - LangGraph State Schema
 * Comprehensive state management for LangGraph workflow orchestration
 */

import { Annotation } from '@langchain/langgraph';

// Content Requirements Interface
export interface ContentRequirements {
  topic: string;
  contentLength: number;
  tone: 'professional' | 'casual' | 'technical' | 'conversational';
  targetAudience: string;
  contentType: 'article' | 'blog' | 'guide' | 'review';
  customInstructions?: string;
}

// Research Data Structure
export interface ResearchResult {
  query: string;
  results: Array<{
    title: string;
    url: string;
    content: string;
    score?: number;
    publishedDate?: string;
  }>;
  answer?: string;
  timestamp: number;
}

// Content Generation Structure
export interface GeneratedContent {
  title: string;
  content: string;
  metaDescription: string;
  wordCount: number;
  outline: string[];
  readabilityScore: number;
}

// Quality Metrics
export interface QualityMetrics {
  aiDetectionScore: number;
  humanLikenessScore: number;
  seoScore: number;
  geoScore: number;
  originalityScore: number;
  competitiveAdvantage: number;
}

// Final Article Result
export interface ArticleResult {
  article: GeneratedContent & {
    seoOptimized: boolean;
    geoOptimized: boolean;
    humanizationApplied: string[];
  };
  metrics: QualityMetrics;
  analytics: {
    researchQueries: number;
    totalSources: number;
    executionTime: number;
    agentsExecuted: string[];
  };
  performance: {
    success: boolean;
    qualityScore: number;
  };
}

// Progress Tracking
export interface WorkflowProgress {
  currentNode: string;
  completedNodes: string[];
  progress: number;
  errors: string[];
  messages: Array<{
    node: string;
    action: string;
    data: any;
    timestamp: number;
    status: 'started' | 'completed' | 'error';
  }>;
}

/**
 * Main LangGraph State Interface
 * This state flows through all nodes in the workflow
 */
export interface InvincibleV3State {
  // Input Configuration
  requirements: ContentRequirements;
  
  // Research Phase Results
  research: {
    coreResearch: ResearchResult[];
    competitiveAnalysis: {
      topCompetitors: string[];
      contentGaps: string[];
      opportunities: string[];
    };
    seoKeywords: {
      primary: string[];
      secondary: string[];
      searchIntent: string;
    };
    completed: boolean;
  };
  
  // Content Generation Results
  content: {
    generated: GeneratedContent | null;
    strategy: {
      approach: string;
      writingStyle: string;
      structureType: string;
    } | null;
    completed: boolean;
  };
  
  // Optimization Results
  optimization: {
    seoOptimized: string | null;
    geoOptimized: boolean;
    keywords: {
      primary: string[];
      secondary: string[];
    };
    completed: boolean;
  };
  
  // Quality Assurance Results
  quality: {
    metrics: QualityMetrics | null;
    humanized: string | null;
    validationResults: string[];
    completed: boolean;
  };
  
  // Final Results
  result: ArticleResult | null;
  
  // Workflow Management
  workflow: WorkflowProgress;
  
  // Session Metadata
  sessionId: string;
  startTime: number;
  endTime?: number;
  version: 'v3';
}

/**
 * LangGraph State Annotation
 * Defines how state is managed and updated in LangGraph
 */
export const StateAnnotation = Annotation.Root({
  requirements: Annotation<ContentRequirements>(),
  
  research: Annotation<InvincibleV3State['research']>({
    reducer: (current, update) => ({ ...current, ...update }),
  }),
  
  content: Annotation<InvincibleV3State['content']>({
    reducer: (current, update) => ({ ...current, ...update }),
  }),
  
  optimization: Annotation<InvincibleV3State['optimization']>({
    reducer: (current, update) => ({ ...current, ...update }),
  }),
  
  quality: Annotation<InvincibleV3State['quality']>({
    reducer: (current, update) => ({ ...current, ...update }),
  }),
  
  result: Annotation<ArticleResult | null>(),
  
  workflow: Annotation<WorkflowProgress>({
    reducer: (current, update) => ({
      ...current,
      ...update,
      messages: [
        ...current.messages,
        ...(update.messages || []),
      ],
      errors: [
        ...current.errors,
        ...(update.errors || []),
      ],
      completedNodes: [
        ...new Set([
          ...current.completedNodes,
          ...(update.completedNodes || []),
        ]),
      ],
    }),
  }),
  
  sessionId: Annotation<string>(),
  startTime: Annotation<number>(),
  endTime: Annotation<number>(),
  version: Annotation<'v3'>(),
});

/**
 * State Utilities
 */
export class StateUtils {
  /**
   * Create initial state for workflow
   */
  static createInitialState(requirements: ContentRequirements): InvincibleV3State {
    const sessionId = `v3_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    return {
      requirements,
      research: {
        coreResearch: [],
        competitiveAnalysis: {
          topCompetitors: [],
          contentGaps: [],
          opportunities: [],
        },
        seoKeywords: {
          primary: [],
          secondary: [],
          searchIntent: 'informational',
        },
        completed: false,
      },
      content: {
        generated: null,
        strategy: null,
        completed: false,
      },
      optimization: {
        seoOptimized: null,
        geoOptimized: false,
        keywords: {
          primary: [],
          secondary: [],
        },
        completed: false,
      },
      quality: {
        metrics: null,
        humanized: null,
        validationResults: [],
        completed: false,
      },
      result: null,
      workflow: {
        currentNode: 'start',
        completedNodes: [],
        progress: 0,
        errors: [],
        messages: [],
      },
      sessionId,
      startTime: Date.now(),
      version: 'v3',
    };
  }
  
  /**
   * Add workflow message
   */
  static addMessage(
    state: InvincibleV3State,
    node: string,
    action: string,
    data: any,
    status: 'started' | 'completed' | 'error' = 'completed'
  ): Partial<InvincibleV3State> {
    return {
      workflow: {
        ...state.workflow,
        messages: [
          ...state.workflow.messages,
          {
            node,
            action,
            data,
            timestamp: Date.now(),
            status,
          },
        ],
      },
    };
  }
  
  /**
   * Update progress
   */
  static updateProgress(
    state: InvincibleV3State,
    currentNode: string,
    progress: number,
    completed?: string[]
  ): Partial<InvincibleV3State> {
    return {
      workflow: {
        ...state.workflow,
        currentNode,
        progress,
        completedNodes: completed || state.workflow.completedNodes,
      },
    };
  }
  
  /**
   * Add error
   */
  static addError(
    state: InvincibleV3State,
    error: string
  ): Partial<InvincibleV3State> {
    return {
      workflow: {
        ...state.workflow,
        errors: [...state.workflow.errors, error],
      },
    };
  }
  
  /**
   * Check if workflow should continue
   */
  static shouldContinue(state: InvincibleV3State): boolean {
    return state.workflow.errors.length === 0;
  }
  
  /**
   * Calculate overall quality score
   */
  static calculateQualityScore(metrics: QualityMetrics): number {
    const weights = {
      humanLikenessScore: 0.3,
      seoScore: 0.25,
      geoScore: 0.2,
      originalityScore: 0.15,
      competitiveAdvantage: 0.1,
    };
    
    return Math.round(
      metrics.humanLikenessScore * weights.humanLikenessScore +
      metrics.seoScore * weights.seoScore +
      metrics.geoScore * weights.geoScore +
      metrics.originalityScore * weights.originalityScore +
      metrics.competitiveAdvantage * weights.competitiveAdvantage
    );
  }
}

/**
 * Node Names Constants
 */
export const NodeNames = {
  START: 'start',
  RESEARCH: 'research_node',
  CONTENT: 'content_node', 
  OPTIMIZATION: 'optimization_node',
  QUALITY: 'quality_node',
  END: 'end',
} as const;

export type NodeName = typeof NodeNames[keyof typeof NodeNames]; 