/**
 * Invincible V3 - Optimization Node
 * LangGraph node for SEO and GEO optimization
 */

import { InvincibleV3State, StateUtils, NodeNames } from '../core/state';
import { AIClient } from '../tools/ai-client';

/**
 * Optimization Node Configuration
 */
export interface OptimizationNodeConfig {
  openRouterApiKey: string;
  enableSEO?: boolean;
  enableGEO?: boolean;
}

/**
 * Optimization Node - Optimizes content for search engines and AI systems
 */
export async function optimizationNode(
  state: InvincibleV3State,
  config: OptimizationNodeConfig
): Promise<Partial<InvincibleV3State>> {
  console.log(`🎯 Optimization Node: Starting SEO/GEO optimization for "${state.requirements.topic}"`);

  try {
    if (!state.content.generated) {
      throw new Error('No content available for optimization');
    }

    // Initialize AI client
    const aiClient = new AIClient({
      apiKey: config.openRouterApiKey,
    });

    // Update progress
    const progressUpdate = StateUtils.updateProgress(
      state,
      NodeNames.OPTIMIZATION,
      10,
      [NodeNames.RESEARCH, NodeNames.CONTENT]
    );

    const messageUpdate = StateUtils.addMessage(
      state,
      NodeNames.OPTIMIZATION,
      'optimization_started',
      { 
        enableSEO: config.enableSEO !== false,
        enableGEO: config.enableGEO !== false,
        wordCount: state.content.generated.wordCount,
      }
    );

    let optimizedContent = state.content.generated.content;
    let seoOptimized = false;
    let geoOptimized = false;

    // Phase 1: SEO Optimization
    if (config.enableSEO !== false) {
      console.log('🔍 Phase 1: SEO optimization...');
      
      const allKeywords = [
        ...state.research.seoKeywords.primary,
        ...state.research.seoKeywords.secondary,
      ];

      optimizedContent = await aiClient.optimizeForSEO(
        optimizedContent,
        allKeywords
      );

      seoOptimized = true;

      const seoProgressUpdate = StateUtils.updateProgress(
        state,
        NodeNames.OPTIMIZATION,
        50,
        [NodeNames.RESEARCH, NodeNames.CONTENT]
      );

      const seoMessageUpdate = StateUtils.addMessage(
        state,
        NodeNames.OPTIMIZATION,
        'seo_optimization_completed',
        {
          keywordsOptimized: allKeywords.length,
          searchIntent: state.research.seoKeywords.searchIntent,
        }
      );
    }

    // Phase 2: GEO Optimization (Generative Engine Optimization)
    if (config.enableGEO !== false) {
      console.log('🤖 Phase 2: GEO optimization for AI search...');
      
      // GEO optimization focuses on structured data, clear answers, and AI-friendly formatting
      const geoPrompt = `Optimize this content for generative AI systems (ChatGPT, Perplexity, Claude, etc.).

**Content to optimize:**
${optimizedContent.substring(0, 3000)}...

**GEO Optimization tasks:**
1. Add clear, direct answers to likely questions
2. Structure information for easy extraction by AI systems
3. Include relevant data points and statistics
4. Add semantic markup and clear hierarchies
5. Optimize for voice search and conversational queries
6. Ensure factual accuracy and credibility signals

Return the GEO-optimized content maintaining HTML structure.`;

      try {
        const geoResponse = await aiClient['makeRequest']([
          { role: 'user', content: geoPrompt }
        ]);
        
        optimizedContent = geoResponse.content;
        geoOptimized = true;
      } catch (error) {
        console.warn('GEO optimization failed, using SEO-optimized content:', error);
        geoOptimized = false;
      }

      const geoProgressUpdate = StateUtils.updateProgress(
        state,
        NodeNames.OPTIMIZATION,
        80,
        [NodeNames.RESEARCH, NodeNames.CONTENT]
      );

      const geoMessageUpdate = StateUtils.addMessage(
        state,
        NodeNames.OPTIMIZATION,
        'geo_optimization_completed',
        {
          geoOptimized,
          aiSearchReady: geoOptimized,
        }
      );
    }

    // Final optimization completion
    const finalProgressUpdate = StateUtils.updateProgress(
      state,
      NodeNames.OPTIMIZATION,
      100,
      [NodeNames.RESEARCH, NodeNames.CONTENT, NodeNames.OPTIMIZATION]
    );

    const completionMessageUpdate = StateUtils.addMessage(
      state,
      NodeNames.OPTIMIZATION,
      'optimization_completed',
      {
        seoOptimized,
        geoOptimized,
        finalWordCount: optimizedContent.replace(/<[^>]*>/g, '').split(/\s+/).length,
      }
    );

    console.log(`✅ Optimization Node: SEO=${seoOptimized}, GEO=${geoOptimized}`);

    // Return state updates
    return {
      optimization: {
        seoOptimized: optimizedContent,
        geoOptimized,
        keywords: {
          primary: state.research.seoKeywords.primary,
          secondary: state.research.seoKeywords.secondary,
        },
        completed: true,
      },
      ...progressUpdate,
      ...messageUpdate,
      ...finalProgressUpdate,
      ...completionMessageUpdate,
    };

  } catch (error) {
    console.error('❌ Optimization Node failed:', error);

    const errorUpdate = StateUtils.addError(
      state,
      `Optimization failed: ${error instanceof Error ? error.message : String(error)}`
    );

    const errorMessageUpdate = StateUtils.addMessage(
      state,
      NodeNames.OPTIMIZATION,
      'optimization_failed',
      { error: error instanceof Error ? error.message : String(error) },
      'error'
    );

    return {
      ...errorUpdate,
      ...errorMessageUpdate,
    };
  }
}

/**
 * Optimization Node Condition - Determines if optimization was successful
 */
export function optimizationNodeCondition(state: InvincibleV3State): string {
  if (!StateUtils.shouldContinue(state)) {
    return NodeNames.END;
  }

  if (state.optimization.completed && state.optimization.seoOptimized) {
    return NodeNames.QUALITY;
  }

  return NodeNames.END;
} 