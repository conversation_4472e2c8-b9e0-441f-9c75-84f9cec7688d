/**
 * Invincible V3 - Content Generation Node
 * LangGraph node for superior content creation with AI
 */

import { InvincibleV3State, StateUtils, NodeNames } from '../core/state';
import { AIClient, ContentStrategy } from '../tools/ai-client';

/**
 * Content Node Configuration
 */
export interface ContentNodeConfig {
  openRouterApiKey: string;
  model?: string;
  temperature?: number;
}

/**
 * Content Node - Generates high-quality content based on research
 */
export async function contentNode(
  state: InvincibleV3State,
  config: ContentNodeConfig
): Promise<Partial<InvincibleV3State>> {
  console.log(`✍️ Content Node: Starting content generation for "${state.requirements.topic}"`);

  try {
    // Initialize AI client
    const aiClient = new AIClient({
      apiKey: config.openRouterApiKey,
      model: config.model || 'moonshotai/kimi-k2',
      temperature: config.temperature || 0.7,
    });

    // Update progress
    const progressUpdate = StateUtils.updateProgress(
      state,
      NodeNames.CONTENT,
      10,
      [NodeNames.RESEARCH]
    );

    const messageUpdate = StateUtils.addMessage(
      state,
      NodeNames.CONTENT,
      'content_generation_started',
      { 
        topic: state.requirements.topic,
        targetLength: state.requirements.contentLength,
        researchSources: state.research.coreResearch.length,
      }
    );

    // Phase 1: Determine Content Strategy
    console.log('🎯 Phase 1: Determining content strategy...');
    const strategy = await aiClient.determineStrategy(
      state.requirements.topic,
      state.requirements.contentLength,
      state.requirements.tone,
      state.requirements.targetAudience,
      state.research.coreResearch
    );

    const strategyProgressUpdate = StateUtils.updateProgress(
      state,
      NodeNames.CONTENT,
      30,
      [NodeNames.RESEARCH]
    );

    const strategyMessageUpdate = StateUtils.addMessage(
      state,
      NodeNames.CONTENT,
      'strategy_determined',
      {
        approach: strategy.approach,
        writingStyle: strategy.writingStyle,
        structureType: strategy.structureType,
      }
    );

    // Phase 2: Generate Content Outline
    console.log('📝 Phase 2: Generating content outline...');
    const outline = await aiClient.generateOutline(
      state.requirements.topic,
      state.requirements.contentLength,
      strategy,
      state.research.coreResearch
    );

    const outlineProgressUpdate = StateUtils.updateProgress(
      state,
      NodeNames.CONTENT,
      50,
      [NodeNames.RESEARCH]
    );

    const outlineMessageUpdate = StateUtils.addMessage(
      state,
      NodeNames.CONTENT,
      'outline_generated',
      {
        sections: outline.length,
        estimatedWords: Math.round(state.requirements.contentLength / outline.length),
      }
    );

    // Phase 3: Generate Main Content
    console.log('🚀 Phase 3: Generating main content...');
    const generatedContent = await aiClient.generateContent({
      topic: state.requirements.topic,
      contentLength: state.requirements.contentLength,
      tone: state.requirements.tone,
      targetAudience: state.requirements.targetAudience,
      researchData: state.research.coreResearch,
      strategy,
      outline,
    });

    const contentProgressUpdate = StateUtils.updateProgress(
      state,
      NodeNames.CONTENT,
      80,
      [NodeNames.RESEARCH]
    );

    const contentMessageUpdate = StateUtils.addMessage(
      state,
      NodeNames.CONTENT,
      'content_generated',
      {
        title: generatedContent.title,
        wordCount: generatedContent.wordCount,
        readabilityScore: generatedContent.readabilityScore,
      }
    );

    // Phase 4: Initial Quality Check
    console.log('🔍 Phase 4: Initial quality assessment...');
    const initialQuality = await aiClient.assessQuality(
      generatedContent.content,
      state.requirements
    );

    // Final content completion
    const finalProgressUpdate = StateUtils.updateProgress(
      state,
      NodeNames.CONTENT,
      100,
      [NodeNames.RESEARCH, NodeNames.CONTENT]
    );

    const completionMessageUpdate = StateUtils.addMessage(
      state,
      NodeNames.CONTENT,
      'content_completed',
      {
        wordCount: generatedContent.wordCount,
        sections: outline.length,
        initialQualityScore: StateUtils.calculateQualityScore(initialQuality),
      }
    );

    console.log(`✅ Content Node: Generated ${generatedContent.wordCount} words with ${outline.length} sections`);

    // Return state updates
    return {
      content: {
        generated: generatedContent,
        strategy,
        completed: true,
      },
      ...progressUpdate,
      ...messageUpdate,
      ...strategyProgressUpdate,
      ...strategyMessageUpdate,
      ...outlineProgressUpdate,
      ...outlineMessageUpdate,
      ...contentProgressUpdate,
      ...contentMessageUpdate,
      ...finalProgressUpdate,
      ...completionMessageUpdate,
    };

  } catch (error) {
    console.error('❌ Content Node failed:', error);

    const errorUpdate = StateUtils.addError(
      state,
      `Content generation failed: ${error instanceof Error ? error.message : String(error)}`
    );

    const errorMessageUpdate = StateUtils.addMessage(
      state,
      NodeNames.CONTENT,
      'content_generation_failed',
      { error: error instanceof Error ? error.message : String(error) },
      'error'
    );

    return {
      ...errorUpdate,
      ...errorMessageUpdate,
    };
  }
}

/**
 * Content Node Condition - Determines if content generation was successful
 */
export function contentNodeCondition(state: InvincibleV3State): string {
  if (!StateUtils.shouldContinue(state)) {
    return NodeNames.END;
  }

  if (state.content.completed && state.content.generated) {
    return NodeNames.OPTIMIZATION;
  }

  return NodeNames.END;
} 