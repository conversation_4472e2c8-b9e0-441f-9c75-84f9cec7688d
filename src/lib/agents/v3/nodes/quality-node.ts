/**
 * Invincible V3 - Quality Assurance Node
 * LangGraph node for final quality validation and humanization
 */

import { InvincibleV3State, StateUtils, NodeNames, ArticleResult } from '../core/state';
import { AIClient } from '../tools/ai-client';

/**
 * Quality Node Configuration
 */
export interface QualityNodeConfig {
  openRouterApiKey: string;
  qualityThreshold?: number;
  aiDetectionThreshold?: number;
  enableHumanization?: boolean;
}

/**
 * Quality Node - Final quality assurance and humanization
 */
export async function qualityNode(
  state: InvincibleV3State,
  config: QualityNodeConfig
): Promise<Partial<InvincibleV3State>> {
  console.log(`✨ Quality Node: Starting quality assurance for "${state.requirements.topic}"`);

  try {
    if (!state.content.generated || !state.optimization.seoOptimized) {
      throw new Error('No optimized content available for quality assurance');
    }

    // Initialize AI client
    const aiClient = new AIClient({
      apiKey: config.openRouterApiKey,
    });

    const qualityThreshold = config.qualityThreshold || 75;
    const aiDetectionThreshold = config.aiDetectionThreshold || 30;

    // Update progress
    const progressUpdate = StateUtils.updateProgress(
      state,
      NodeNames.QUALITY,
      10,
      [NodeNames.RESEARCH, NodeNames.CONTENT, NodeNames.OPTIMIZATION]
    );

    const messageUpdate = StateUtils.addMessage(
      state,
      NodeNames.QUALITY,
      'quality_assurance_started',
      { 
        qualityThreshold,
        aiDetectionThreshold,
        enableHumanization: config.enableHumanization !== false,
      }
    );

    // Phase 1: Comprehensive Quality Assessment
    console.log('🔍 Phase 1: Comprehensive quality assessment...');
    const qualityMetrics = await aiClient.assessQuality(
      state.optimization.seoOptimized,
      state.requirements
    );

    const assessmentProgressUpdate = StateUtils.updateProgress(
      state,
      NodeNames.QUALITY,
      40,
      [NodeNames.RESEARCH, NodeNames.CONTENT, NodeNames.OPTIMIZATION]
    );

    const assessmentMessageUpdate = StateUtils.addMessage(
      state,
      NodeNames.QUALITY,
      'quality_assessment_completed',
      {
        humanLikenessScore: qualityMetrics.humanLikenessScore,
        aiDetectionScore: qualityMetrics.aiDetectionScore,
        seoScore: qualityMetrics.seoScore,
        geoScore: qualityMetrics.geoScore,
        overallScore: StateUtils.calculateQualityScore(qualityMetrics),
      }
    );

    let finalContent = state.optimization.seoOptimized;
    let humanizationTechniques: string[] = [];

    // Phase 2: Humanization (if needed or enabled)
    const needsHumanization = 
      qualityMetrics.aiDetectionScore > aiDetectionThreshold ||
      qualityMetrics.humanLikenessScore < qualityThreshold ||
      config.enableHumanization === true;

    if (needsHumanization && config.enableHumanization !== false) {
      console.log('🤖 Phase 2: Humanizing content...');
      
      try {
        finalContent = await aiClient.humanizeContent(finalContent);
        humanizationTechniques = [
          'Varied sentence structures',
          'Natural transitions',
          'Conversational tone adjustments',
          'Human-like imperfections',
          'Direct reader engagement',
        ];

        const humanizationProgressUpdate = StateUtils.updateProgress(
          state,
          NodeNames.QUALITY,
          70,
          [NodeNames.RESEARCH, NodeNames.CONTENT, NodeNames.OPTIMIZATION]
        );

        const humanizationMessageUpdate = StateUtils.addMessage(
          state,
          NodeNames.QUALITY,
          'humanization_completed',
          {
            techniquesApplied: humanizationTechniques.length,
            improvedAiDetection: true,
          }
        );

        // Re-assess quality after humanization
        const finalQualityMetrics = await aiClient.assessQuality(
          finalContent,
          state.requirements
        );

        // Update metrics with improved scores
        qualityMetrics.aiDetectionScore = Math.min(
          qualityMetrics.aiDetectionScore,
          finalQualityMetrics.aiDetectionScore
        );
        qualityMetrics.humanLikenessScore = Math.max(
          qualityMetrics.humanLikenessScore,
          finalQualityMetrics.humanLikenessScore
        );

      } catch (error) {
        console.warn('Humanization failed, using optimized content:', error);
        humanizationTechniques = ['Humanization failed - using optimized content'];
      }
    }

    // Phase 3: Final Validation
    console.log('✅ Phase 3: Final validation...');
    const finalQualityScore = StateUtils.calculateQualityScore(qualityMetrics);
    const validationResults: string[] = [];

    // Quality checks
    if (finalQualityScore >= qualityThreshold) {
      validationResults.push('✅ Quality threshold met');
    } else {
      validationResults.push('⚠️ Quality below threshold');
    }

    if (qualityMetrics.aiDetectionScore <= aiDetectionThreshold) {
      validationResults.push('✅ AI detection threshold met');
    } else {
      validationResults.push('⚠️ AI detection score high');
    }

    if (qualityMetrics.seoScore >= 70) {
      validationResults.push('✅ SEO optimized');
    }

    if (qualityMetrics.geoScore >= 60) {
      validationResults.push('✅ GEO optimized for AI search');
    }

    // Phase 4: Create Final Result
    console.log('📦 Phase 4: Creating final result...');
    const finalWordCount = finalContent.replace(/<[^>]*>/g, '').split(/\s+/).length;

    const articleResult: ArticleResult = {
      article: {
        ...state.content.generated,
        content: finalContent,
        wordCount: finalWordCount,
        seoOptimized: true,
        geoOptimized: state.optimization.geoOptimized,
        humanizationApplied: humanizationTechniques,
      },
      metrics: qualityMetrics,
      analytics: {
        researchQueries: state.research.coreResearch.length,
        totalSources: state.research.coreResearch.reduce((sum, r) => sum + r.results.length, 0),
        executionTime: Date.now() - state.startTime,
        agentsExecuted: [NodeNames.RESEARCH, NodeNames.CONTENT, NodeNames.OPTIMIZATION, NodeNames.QUALITY],
      },
      performance: {
        success: finalQualityScore >= qualityThreshold,
        qualityScore: finalQualityScore,
      },
    };

    // Final completion
    const finalProgressUpdate = StateUtils.updateProgress(
      state,
      NodeNames.QUALITY,
      100,
      [NodeNames.RESEARCH, NodeNames.CONTENT, NodeNames.OPTIMIZATION, NodeNames.QUALITY]
    );

    const completionMessageUpdate = StateUtils.addMessage(
      state,
      NodeNames.QUALITY,
      'quality_assurance_completed',
      {
        finalQualityScore,
        success: articleResult.performance.success,
        wordCount: finalWordCount,
        humanizationApplied: humanizationTechniques.length > 0,
      }
    );

    console.log(`✅ Quality Node: Final score ${finalQualityScore}/100, Success: ${articleResult.performance.success}`);

    // Return state updates
    return {
      quality: {
        metrics: qualityMetrics,
        humanized: finalContent,
        validationResults,
        completed: true,
      },
      result: articleResult,
      endTime: Date.now(),
      ...progressUpdate,
      ...messageUpdate,
      ...assessmentProgressUpdate,
      ...assessmentMessageUpdate,
      ...finalProgressUpdate,
      ...completionMessageUpdate,
    };

  } catch (error) {
    console.error('❌ Quality Node failed:', error);

    const errorUpdate = StateUtils.addError(
      state,
      `Quality assurance failed: ${error instanceof Error ? error.message : String(error)}`
    );

    const errorMessageUpdate = StateUtils.addMessage(
      state,
      NodeNames.QUALITY,
      'quality_assurance_failed',
      { error: error instanceof Error ? error.message : String(error) },
      'error'
    );

    return {
      ...errorUpdate,
      ...errorMessageUpdate,
    };
  }
}

/**
 * Quality Node Condition - Determines workflow completion
 */
export function qualityNodeCondition(state: InvincibleV3State): string {
  // Quality node always ends the workflow
  return NodeNames.END;
} 