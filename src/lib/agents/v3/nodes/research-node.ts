/**
 * Invincible V3 - Research Node
 * LangGraph node for comprehensive research and competitive analysis
 */

import { InvincibleV3State, StateUtils, NodeNames } from '../core/state';
import { SearchTool } from '../tools/search-tool';

/**
 * Research Node Configuration
 */
export interface ResearchNodeConfig {
  tavilyApiKey?: string;
  maxQueries?: number;
  enableCompetitiveAnalysis?: boolean;
}

/**
 * Research Node - Conducts comprehensive research on the topic
 */
export async function researchNode(
  state: InvincibleV3State,
  config: ResearchNodeConfig = {}
): Promise<Partial<InvincibleV3State>> {
  console.log(`🔍 Research Node: Starting research for "${state.requirements.topic}"`);

  try {
    // Initialize search tool
    const searchTool = new SearchTool({
      apiKey: config.tavilyApiKey,
      maxResults: 5,
      searchDepth: 'basic',
    });

    // Update progress
    const progressUpdate = StateUtils.updateProgress(
      state,
      NodeNames.RESEARCH,
      10,
      []
    );

    const messageUpdate = StateUtils.addMessage(
      state,
      NodeNames.RESEARCH,
      'research_started',
      { topic: state.requirements.topic }
    );

    // Phase 1: Core Research
    console.log('📚 Phase 1: Conducting core research...');
    const coreResearch = await searchTool.conductResearch(state.requirements.topic);

    const coreProgressUpdate = StateUtils.updateProgress(
      state,
      NodeNames.RESEARCH,
      30,
      []
    );

    const coreMessageUpdate = StateUtils.addMessage(
      state,
      NodeNames.RESEARCH,
      'core_research_completed',
      { 
        totalQueries: coreResearch.length,
        totalResults: coreResearch.reduce((sum, r) => sum + r.results.length, 0)
      }
    );

    // Phase 2: Competitive Analysis
    console.log('🏆 Phase 2: Analyzing competitors...');
    let competitiveAnalysis = {
      topCompetitors: [] as string[],
      contentGaps: [] as string[],
      opportunities: [] as string[],
    };

    if (config.enableCompetitiveAnalysis !== false) {
      competitiveAnalysis = await searchTool.analyzeCompetitors(state.requirements.topic);
    }

    const competitiveProgressUpdate = StateUtils.updateProgress(
      state,
      NodeNames.RESEARCH,
      60,
      []
    );

    const competitiveMessageUpdate = StateUtils.addMessage(
      state,
      NodeNames.RESEARCH,
      'competitive_analysis_completed',
      {
        competitors: competitiveAnalysis.topCompetitors.length,
        opportunities: competitiveAnalysis.opportunities.length,
        contentGaps: competitiveAnalysis.contentGaps.length,
      }
    );

    // Phase 3: SEO Keywords Extraction
    console.log('🎯 Phase 3: Extracting SEO keywords...');
    const seoKeywords = await searchTool.extractSEOKeywords(state.requirements.topic);

    const seoProgressUpdate = StateUtils.updateProgress(
      state,
      NodeNames.RESEARCH,
      90,
      []
    );

    const seoMessageUpdate = StateUtils.addMessage(
      state,
      NodeNames.RESEARCH,
      'seo_keywords_extracted',
      {
        primaryKeywords: seoKeywords.primary.length,
        secondaryKeywords: seoKeywords.secondary.length,
        searchIntent: seoKeywords.searchIntent,
      }
    );

    // Final research completion
    const finalProgressUpdate = StateUtils.updateProgress(
      state,
      NodeNames.RESEARCH,
      100,
      [NodeNames.RESEARCH]
    );

    const completionMessageUpdate = StateUtils.addMessage(
      state,
      NodeNames.RESEARCH,
      'research_completed',
      {
        totalResearchQueries: coreResearch.length,
        totalSources: coreResearch.reduce((sum, r) => sum + r.results.length, 0),
        competitorsAnalyzed: competitiveAnalysis.topCompetitors.length,
        keywordsExtracted: seoKeywords.primary.length + seoKeywords.secondary.length,
      }
    );

    console.log(`✅ Research Node: Completed with ${coreResearch.length} research queries`);

    // Return state updates
    return {
      research: {
        coreResearch,
        competitiveAnalysis,
        seoKeywords,
        completed: true,
      },
      ...progressUpdate,
      ...messageUpdate,
      ...coreProgressUpdate,
      ...coreMessageUpdate,
      ...competitiveProgressUpdate,
      ...competitiveMessageUpdate,
      ...seoProgressUpdate,
      ...seoMessageUpdate,
      ...finalProgressUpdate,
      ...completionMessageUpdate,
    };

  } catch (error) {
    console.error('❌ Research Node failed:', error);

    const errorUpdate = StateUtils.addError(
      state,
      `Research failed: ${error instanceof Error ? error.message : String(error)}`
    );

    const errorMessageUpdate = StateUtils.addMessage(
      state,
      NodeNames.RESEARCH,
      'research_failed',
      { error: error instanceof Error ? error.message : String(error) },
      'error'
    );

    return {
      ...errorUpdate,
      ...errorMessageUpdate,
    };
  }
}

/**
 * Research Node Condition - Determines if research was successful
 */
export function researchNodeCondition(state: InvincibleV3State): string {
  if (!StateUtils.shouldContinue(state)) {
    return NodeNames.END;
  }

  if (state.research.completed && state.research.coreResearch.length > 0) {
    return NodeNames.CONTENT;
  }

  return NodeNames.END;
} 