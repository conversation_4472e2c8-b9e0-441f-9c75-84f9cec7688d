/**
 * Invincible V3 - Enhanced AI Client
 * Robust OpenRouter integration with comprehensive content generation
 */

import { GeneratedContent, QualityMetrics } from '../core/state';

export interface AIConfig {
  apiKey: string;
  model?: string;
  temperature?: number;
  maxTokens?: number;
}

export interface ContentStrategy {
  approach: string;
  writingStyle: string;
  structureType: string;
}

export interface ContentRequest {
  topic: string;
  contentLength: number;
  tone: string;
  targetAudience: string;
  researchData: any[];
  strategy: ContentStrategy;
  outline: string[];
}

export class AIClient {
  private config: Required<AIConfig>;
  private baseURL = 'https://openrouter.ai/api/v1';

  constructor(config: AIConfig) {
    this.config = {
      model: 'moonshotai/kimi-k2',
      temperature: 0.7,
      maxTokens: 4000,
      ...config,
    };

    console.log(`🤖 AIClient initialized with model: ${this.config.model}`);
  }

  /**
   * Determine content strategy based on requirements
   */
  async determineStrategy(
    topic: string,
    contentLength: number,
    tone: string,
    targetAudience: string,
    researchData: any[]
  ): Promise<ContentStrategy> {
    const prompt = `Analyze the content requirements and determine the optimal content generation strategy.

**Requirements:**
- Topic: "${topic}"
- Length: ${contentLength} words
- Tone: ${tone}
- Audience: ${targetAudience}

**Research Insights:**
${researchData.map(r => `- ${r.query}: ${r.results.length} sources`).join('\n')}

Determine the best:
1. **Approach**: comprehensive, focused, narrative, or analytical
2. **Writing Style**: conversational, professional, authoritative, or engaging  
3. **Structure Type**: traditional, modern, problem-solution, or comparison

Return JSON:
{
  "approach": "approach_type",
  "writingStyle": "style_type", 
  "structureType": "structure_type"
}`;

    try {
      const response = await this.makeRequest([
        { role: 'user', content: prompt }
      ]);

      const strategy = this.parseJSON(response.content, {
        approach: contentLength > 2000 ? 'comprehensive' : 'focused',
        writingStyle: tone === 'casual' ? 'conversational' : 'professional',
        structureType: 'traditional',
      });

      return {
        approach: strategy.approach || 'comprehensive',
        writingStyle: strategy.writingStyle || 'professional',
        structureType: strategy.structureType || 'traditional',
      };

    } catch (error) {
      console.error('Strategy determination failed:', error);
      return {
        approach: contentLength > 2000 ? 'comprehensive' : 'focused',
        writingStyle: tone === 'casual' ? 'conversational' : 'professional',
        structureType: 'traditional',
      };
    }
  }

  /**
   * Generate content outline
   */
  async generateOutline(
    topic: string,
    contentLength: number,
    strategy: ContentStrategy,
    researchData: any[]
  ): Promise<string[]> {
    const researchContext = researchData
      .map(r => `${r.query}: ${r.results.slice(0, 2).map((res: any) => res.title).join(', ')}`)
      .join('\n');

    const prompt = `Create a comprehensive content outline for "${topic}".

**Strategy:**
- Approach: ${strategy.approach}
- Writing Style: ${strategy.writingStyle}
- Structure: ${strategy.structureType}
- Target Length: ${contentLength} words

**Research Context:**
${researchContext}

Create 6-10 section titles that will form a complete ${contentLength}-word article.
Include:
- Engaging introduction
- Core content sections
- Practical examples
- Conclusion with action items

Return as JSON array:
["Section 1 Title", "Section 2 Title", ...]`;

    try {
      const response = await this.makeRequest([
        { role: 'user', content: prompt }
      ]);

      const outline = this.parseJSON(response.content, []);
      
      if (Array.isArray(outline) && outline.length > 0) {
        return outline;
      }

      // Fallback outline
      return this.generateFallbackOutline(topic, contentLength);

    } catch (error) {
      console.error('Outline generation failed:', error);
      return this.generateFallbackOutline(topic, contentLength);
    }
  }

  /**
   * Generate main content
   */
  async generateContent(request: ContentRequest): Promise<GeneratedContent> {
    const researchContext = request.researchData
      .map(r => `${r.query}: ${r.results.slice(0, 3).map((res: any) => res.content.substring(0, 200)).join(' | ')}`)
      .join('\n\n');

    const prompt = `Create superior content on "${request.topic}" that outperforms competitor content.

**Requirements:**
- Length: ${request.contentLength} words
- Tone: ${request.tone}
- Audience: ${request.targetAudience}
- Strategy: ${request.strategy.approach} approach, ${request.strategy.writingStyle} style

**Outline:**
${request.outline.map((section, i) => `${i + 1}. ${section}`).join('\n')}

**Research Context:**
${researchContext.substring(0, 2000)}

**Content Guidelines:**
1. Write naturally with varied sentence structures
2. Include unique insights not found in competitor content
3. Use compelling examples and practical applications
4. Structure with semantic HTML (h1, h2, h3, p, ul, ol)
5. Optimize for both human readers and AI systems
6. Include transition sentences between sections
7. End with clear conclusions and next steps

Create the complete article in HTML format with proper headings and structure.`;

    try {
      const response = await this.makeRequest([
        { role: 'user', content: prompt }
      ], { maxTokens: 6000 });

      const content = response.content;
      
      // Extract title
      const titleMatch = content.match(/<h1[^>]*>(.*?)<\/h1>/i);
      const title = titleMatch 
        ? titleMatch[1].replace(/<[^>]*>/g, '').trim()
        : await this.generateTitle(request.topic);

      // Calculate metrics
      const wordCount = content.replace(/<[^>]*>/g, '').split(/\s+/).length;
      const readabilityScore = this.calculateReadabilityScore(content);

      // Generate meta description
      const metaDescription = await this.generateMetaDescription(title, content);

      return {
        title,
        content,
        metaDescription,
        wordCount,
        outline: request.outline,
        readabilityScore,
      };

    } catch (error) {
      console.error('Content generation failed:', error);
      throw new Error(`Content generation failed: ${error}`);
    }
  }

  /**
   * Optimize content for SEO
   */
  async optimizeForSEO(
    content: string,
    keywords: string[]
  ): Promise<string> {
    const prompt = `Optimize this content for SEO while maintaining natural readability.

**Content to optimize:**
${content.substring(0, 3000)}...

**Target Keywords:**
${keywords.join(', ')}

**Optimization tasks:**
1. Naturally integrate keywords into headings and content
2. Improve meta descriptions and title tags
3. Add internal linking opportunities
4. Enhance semantic structure
5. Optimize for featured snippets

Return the optimized HTML content with improvements highlighted.`;

    try {
      const response = await this.makeRequest([
        { role: 'user', content: prompt }
      ]);

      return response.content;

    } catch (error) {
      console.error('SEO optimization failed:', error);
      return content; // Return original if optimization fails
    }
  }

  /**
   * Humanize content to bypass AI detection
   */
  async humanizeContent(content: string): Promise<string> {
    const prompt = `Humanize this content to make it more natural and engaging while maintaining all information.

**Content to humanize:**
${content.substring(0, 4000)}...

**Humanization techniques:**
1. Vary sentence structures and lengths
2. Add conversational transitions
3. Include personal observations
4. Use natural speech patterns
5. Add subtle imperfections that humans would make
6. Include contractions and casual language where appropriate
7. Add rhetorical questions and direct reader address

Return the humanized content maintaining the HTML structure.`;

    try {
      const response = await this.makeRequest([
        { role: 'user', content: prompt }
      ], { temperature: 0.9 });

      return response.content;

    } catch (error) {
      console.error('Content humanization failed:', error);
      return content;
    }
  }

  /**
   * Assess content quality
   */
  async assessQuality(
    content: string,
    requirements: any
  ): Promise<QualityMetrics> {
    const prompt = `Assess the quality of this content comprehensively.

**Content:**
${content.substring(0, 2000)}...

**Requirements:**
- Target Length: ${requirements.contentLength} words
- Tone: ${requirements.tone}
- Audience: ${requirements.targetAudience}

Rate each aspect from 1-100:
1. AI Detection Score (lower is better - how AI-like it sounds)
2. Human Likeness Score (how natural and engaging)
3. SEO Score (search engine optimization)
4. GEO Score (generative engine optimization for AI search)
5. Originality Score (uniqueness and fresh insights)
6. Competitive Advantage (superiority over typical content)

Return JSON:
{
  "aiDetectionScore": 1-100,
  "humanLikenessScore": 1-100,
  "seoScore": 1-100,
  "geoScore": 1-100,
  "originalityScore": 1-100,
  "competitiveAdvantage": 1-100
}`;

    try {
      const response = await this.makeRequest([
        { role: 'user', content: prompt }
      ]);

      const metrics = this.parseJSON(response.content, {
        aiDetectionScore: 30,
        humanLikenessScore: 75,
        seoScore: 70,
        geoScore: 70,
        originalityScore: 60,
        competitiveAdvantage: 65,
      });

      return {
        aiDetectionScore: metrics.aiDetectionScore || 30,
        humanLikenessScore: metrics.humanLikenessScore || 75,
        seoScore: metrics.seoScore || 70,
        geoScore: metrics.geoScore || 70,
        originalityScore: metrics.originalityScore || 60,
        competitiveAdvantage: metrics.competitiveAdvantage || 65,
      };

    } catch (error) {
      console.error('Quality assessment failed:', error);
      return {
        aiDetectionScore: 30,
        humanLikenessScore: 75,
        seoScore: 70,
        geoScore: 70,
        originalityScore: 60,
        competitiveAdvantage: 65,
      };
    }
  }

  // Private helper methods
  private async makeRequest(
    messages: Array<{ role: string; content: string }>,
    options: Partial<AIConfig> = {}
  ): Promise<{ content: string; usage?: any }> {
    const requestConfig = { ...this.config, ...options };

    console.log(`🚀 Making AI request to ${requestConfig.model}`);

    const response = await fetch(`${this.baseURL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${requestConfig.apiKey}`,
        'HTTP-Referer': 'http://localhost:3000',
        'X-Title': 'Invincible V3 Agent System',
      },
      body: JSON.stringify({
        model: requestConfig.model,
        messages,
        temperature: requestConfig.temperature,
        max_tokens: requestConfig.maxTokens,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`OpenRouter API error ${response.status}: ${errorText}`);
    }

    const data = await response.json();
    
    console.log(`✅ AI request completed - ${data.usage?.total_tokens || 0} tokens`);

    return {
      content: data.choices[0].message.content,
      usage: data.usage,
    };
  }

  private parseJSON(text: string, fallback: any): any {
    try {
      // Remove markdown formatting if present
      const cleanText = text.replace(/```json\n?|\n?```/g, '').trim();
      return JSON.parse(cleanText);
    } catch {
      return fallback;
    }
  }

  private generateFallbackOutline(topic: string, contentLength: number): string[] {
    const baseOutline = [
      `Introduction to ${topic}`,
      `What is ${topic}? - Complete Guide`,
      `Benefits and Advantages of ${topic}`,
      `How to Get Started with ${topic}`,
      `Best Practices and Tips`,
      `Common Challenges and Solutions`,
      `Conclusion and Next Steps`,
    ];

    if (contentLength >= 2000) {
      baseOutline.splice(5, 0, `Advanced Techniques for ${topic}`);
      baseOutline.splice(6, 0, `Real-World Examples and Case Studies`);
    }

    return baseOutline;
  }

  private async generateTitle(topic: string): Promise<string> {
    try {
      const response = await this.makeRequest([
        { 
          role: 'user', 
          content: `Generate a compelling, SEO-optimized title for content about "${topic}". 
                   Keep it 50-60 characters, include the main keyword, and make it click-worthy but not clickbait.
                   Return just the title.` 
        }
      ]);
      
      return response.content.trim().replace(/^["']|["']$/g, '');
    } catch {
      return `Complete Guide to ${topic}`;
    }
  }

  private async generateMetaDescription(title: string, content: string): Promise<string> {
    try {
      const contentPreview = content.replace(/<[^>]*>/g, '').substring(0, 500);
      
      const response = await this.makeRequest([
        { 
          role: 'user', 
          content: `Generate a compelling meta description for this content:
                   Title: ${title}
                   Content preview: ${contentPreview}
                   
                   Keep it 150-160 characters, include main keywords, and make it compelling for search results.
                   Return just the meta description.` 
        }
      ]);
      
      return response.content.trim().replace(/^["']|["']$/g, '');
    } catch {
      return `Comprehensive guide to ${title.toLowerCase()}. Learn everything you need to know with expert insights and practical examples.`;
    }
  }

  private calculateReadabilityScore(content: string): number {
    const text = content.replace(/<[^>]*>/g, '');
    const sentences = text.split(/[.!?]+/).length;
    const words = text.split(/\s+/).length;
    const avgWordsPerSentence = words / sentences;
    
    // Simple readability score (lower is better)
    let score = 100 - (avgWordsPerSentence - 15) * 2;
    return Math.max(0, Math.min(100, score));
  }
} 