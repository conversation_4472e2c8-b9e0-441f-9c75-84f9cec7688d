/**
 * Invincible V3 - LangGraph Workflow
 * Complete workflow orchestration for content generation
 */

import { StateGraph, START, END } from '@langchain/langgraph';
import { 
  InvincibleV3State, 
  StateAnnotation, 
  StateUtils, 
  NodeNames, 
  ContentRequirements 
} from '../core/state';
import { researchNode, researchNodeCondition } from '../nodes/research-node';
import { contentNode, contentNodeCondition } from '../nodes/content-node';
import { optimizationNode, optimizationNodeCondition } from '../nodes/optimization-node';
import { qualityNode, qualityNodeCondition } from '../nodes/quality-node';

/**
 * Workflow Configuration
 */
export interface InvincibleWorkflowConfig {
  openRouterApiKey: string;
  tavilyApiKey?: string;
  enableCompetitiveAnalysis?: boolean;
  enableSEO?: boolean;
  enableGEO?: boolean;
  enableHumanization?: boolean;
  qualityThreshold?: number;
  aiDetectionThreshold?: number;
}

/**
 * Create the complete Invincible V3 workflow
 */
export function createInvincibleWorkflow(config: InvincibleWorkflowConfig) {
  console.log('🏗️ Creating Invincible V3 LangGraph workflow...');

  // Create the state graph
  const workflow = new StateGraph(StateAnnotation)
    // Add all nodes
    .addNode(NodeNames.RESEARCH, async (state: InvincibleV3State) => {
      return await researchNode(state, {
        tavilyApiKey: config.tavilyApiKey,
        enableCompetitiveAnalysis: config.enableCompetitiveAnalysis,
      });
    })
    .addNode(NodeNames.CONTENT, async (state: InvincibleV3State) => {
      return await contentNode(state, {
        openRouterApiKey: config.openRouterApiKey,
      });
    })
    .addNode(NodeNames.OPTIMIZATION, async (state: InvincibleV3State) => {
      return await optimizationNode(state, {
        openRouterApiKey: config.openRouterApiKey,
        enableSEO: config.enableSEO,
        enableGEO: config.enableGEO,
      });
    })
    .addNode(NodeNames.QUALITY, async (state: InvincibleV3State) => {
      return await qualityNode(state, {
        openRouterApiKey: config.openRouterApiKey,
        qualityThreshold: config.qualityThreshold,
        aiDetectionThreshold: config.aiDetectionThreshold,
        enableHumanization: config.enableHumanization,
      });
    })
    
    // Define workflow edges
    .addEdge(START, NodeNames.RESEARCH)
    .addConditionalEdges(NodeNames.RESEARCH, researchNodeCondition)
    .addConditionalEdges(NodeNames.CONTENT, contentNodeCondition)
    .addConditionalEdges(NodeNames.OPTIMIZATION, optimizationNodeCondition)
    .addConditionalEdges(NodeNames.QUALITY, qualityNodeCondition);

  return workflow.compile();
}

/**
 * Execute workflow with initial requirements
 */
export async function executeWorkflow(
  requirements: ContentRequirements,
  config: InvincibleWorkflowConfig
): Promise<InvincibleV3State> {
  console.log(`🚀 Executing Invincible V3 workflow for: "${requirements.topic}"`);

  try {
    // Create the workflow
    const workflow = createInvincibleWorkflow(config);

    // Create initial state
    const initialState = StateUtils.createInitialState(requirements);

    console.log(`📊 Initial state created for session: ${initialState.sessionId}`);

    // Execute the workflow
    const result = await workflow.invoke(initialState);

    console.log(`✅ Workflow completed in ${Date.now() - initialState.startTime}ms`);

    return result;

  } catch (error) {
    console.error('❌ Workflow execution failed:', error);
    throw new Error(`Workflow execution failed: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * Execute workflow with progress callbacks (simplified streaming)
 */
export async function executeWorkflowWithProgress(
  requirements: ContentRequirements,
  config: InvincibleWorkflowConfig,
  onProgress?: (update: { type: string; data: any; timestamp: number }) => void
): Promise<InvincibleV3State> {
  console.log(`🌊 Executing Invincible V3 workflow with progress for: "${requirements.topic}"`);

  try {
    // Create the workflow
    const workflow = createInvincibleWorkflow(config);

    // Create initial state
    const initialState = StateUtils.createInitialState(requirements);

    // Notify workflow start
    onProgress?.({
      type: 'workflow_started',
      data: {
        sessionId: initialState.sessionId,
        topic: requirements.topic,
        estimatedTime: 180,
      },
      timestamp: Date.now(),
    });

    // Execute the workflow
    const result = await workflow.invoke(initialState);

    // Notify completion
    onProgress?.({
      type: 'workflow_completed',
      data: {
        success: result.result?.performance.success || false,
        result: result.result,
        totalTime: (result.endTime || Date.now()) - result.startTime,
      },
      timestamp: Date.now(),
    });

    console.log(`✅ Workflow with progress completed in ${Date.now() - initialState.startTime}ms`);
    return result;

  } catch (error) {
    console.error('❌ Workflow with progress failed:', error);
    
    onProgress?.({
      type: 'workflow_error',
      data: { 
        error: error instanceof Error ? error.message : String(error),
      },
      timestamp: Date.now(),
    });

    throw error;
  }
}

/**
 * Get workflow status for a running workflow
 */
export function getWorkflowStatus(state: InvincibleV3State): {
  currentNode: string;
  progress: number;
  completedNodes: string[];
  errors: string[];
  isComplete: boolean;
  hasErrors: boolean;
} {
  return {
    currentNode: state.workflow.currentNode,
    progress: state.workflow.progress,
    completedNodes: state.workflow.completedNodes,
    errors: state.workflow.errors,
    isComplete: !!state.result,
    hasErrors: state.workflow.errors.length > 0,
  };
}

/**
 * Workflow configuration presets
 */
export const WorkflowPresets = {
  /**
   * Standard preset for most content generation tasks
   */
  standard: (apiKeys: { openRouter: string; tavily?: string }): InvincibleWorkflowConfig => ({
    openRouterApiKey: apiKeys.openRouter,
    tavilyApiKey: apiKeys.tavily,
    enableCompetitiveAnalysis: true,
    enableSEO: true,
    enableGEO: true,
    enableHumanization: true,
    qualityThreshold: 75,
    aiDetectionThreshold: 30,
  }),

  /**
   * Fast preset for quick content generation
   */
  fast: (apiKeys: { openRouter: string; tavily?: string }): InvincibleWorkflowConfig => ({
    openRouterApiKey: apiKeys.openRouter,
    tavilyApiKey: apiKeys.tavily,
    enableCompetitiveAnalysis: false,
    enableSEO: true,
    enableGEO: false,
    enableHumanization: false,
    qualityThreshold: 60,
    aiDetectionThreshold: 40,
  }),

  /**
   * Premium preset for highest quality content
   */
  premium: (apiKeys: { openRouter: string; tavily?: string }): InvincibleWorkflowConfig => ({
    openRouterApiKey: apiKeys.openRouter,
    tavilyApiKey: apiKeys.tavily,
    enableCompetitiveAnalysis: true,
    enableSEO: true,
    enableGEO: true,
    enableHumanization: true,
    qualityThreshold: 85,
    aiDetectionThreshold: 20,
  }),
}; 