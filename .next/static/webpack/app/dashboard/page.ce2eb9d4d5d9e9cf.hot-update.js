"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_ChevronLeft_Clock_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,ChevronLeft,Clock,Crown,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_ChevronLeft_Clock_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,ChevronLeft,Clock,Crown,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_ChevronLeft_Clock_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,ChevronLeft,Clock,Crown,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layers.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_ChevronLeft_Clock_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,ChevronLeft,Clock,Crown,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_ChevronLeft_Clock_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,ChevronLeft,Clock,Crown,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-help.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_ChevronLeft_Clock_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,ChevronLeft,Clock,Crown,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_ChevronLeft_Clock_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,ChevronLeft,Clock,Crown,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_ChevronLeft_Clock_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,ChevronLeft,Clock,Crown,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_ChevronLeft_Clock_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,ChevronLeft,Clock,Crown,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_ChevronLeft_Clock_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,ChevronLeft,Clock,Crown,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/video.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_ChevronLeft_Clock_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,ChevronLeft,Clock,Crown,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_ChevronLeft_Clock_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,ChevronLeft,Clock,Crown,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_ChevronLeft_Clock_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,ChevronLeft,Clock,Crown,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_ChevronLeft_Clock_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,ChevronLeft,Clock,Crown,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_ChevronLeft_Clock_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,ChevronLeft,Clock,Crown,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_ChevronLeft_Clock_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,ChevronLeft,Clock,Crown,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_ChevronLeft_Clock_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,ChevronLeft,Clock,Crown,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_ChevronLeft_Clock_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,ChevronLeft,Clock,Crown,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_ChevronLeft_Clock_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,ChevronLeft,Clock,Crown,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_ChevronLeft_Clock_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,ChevronLeft,Clock,Crown,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_ChevronLeft_Clock_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,ChevronLeft,Clock,Crown,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ProfileButton__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ProfileButton */ \"(app-pages-browser)/./src/components/ProfileButton.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Lazy load RecentContent to reduce initial bundle size\nconst OptimizedRecentContent = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_dashboard_RecentContent_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/dashboard/RecentContent */ \"(app-pages-browser)/./src/components/dashboard/RecentContent.tsx\")).then((mod)=>({\n            default: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(mod.default)\n        })), {\n    loadableGenerated: {\n        modules: [\n            \"app/dashboard/page.tsx -> \" + \"@/components/dashboard/RecentContent\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: Array.from({\n                length: 3\n            }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white/10 rounded-lg p-4 animate-pulse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 bg-white/10 rounded mb-2 w-3/4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-3 bg-white/10 rounded w-1/2\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, i, true, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n            lineNumber: 62,\n            columnNumber: 5\n        }, undefined)\n});\n_c = OptimizedRecentContent;\n// Optimized lazy loading with React.memo and reduced bundle size\nconst OptimizedInvincibleOrb = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_InvincibleOrb_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/InvincibleOrb */ \"(app-pages-browser)/./src/components/InvincibleOrb.tsx\")).then((mod)=>({\n            default: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(mod.default)\n        })), {\n    loadableGenerated: {\n        modules: [\n            \"app/dashboard/page.tsx -> \" + \"@/components/InvincibleOrb\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-16 h-16 border-2 border-violet-500/30 border-t-violet-500 rounded-full animate-spin mx-auto\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n            lineNumber: 76,\n            columnNumber: 18\n        }, undefined)\n});\n_c1 = OptimizedInvincibleOrb;\nconst OptimizedBlogPreview = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_c2 = ()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_BlogPreview_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/BlogPreview */ \"(app-pages-browser)/./src/components/BlogPreview.tsx\")).then((mod)=>({\n            default: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(mod.default)\n        })), {\n    loadableGenerated: {\n        modules: [\n            \"app/dashboard/page.tsx -> \" + \"@/components/BlogPreview\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-16 h-16 border-2 border-pink-500/30 border-t-pink-500 rounded-full animate-spin mx-auto\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n            lineNumber: 81,\n            columnNumber: 18\n        }, undefined)\n});\n_c3 = OptimizedBlogPreview;\nconst OptimizedEmailPreview = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_c4 = ()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_EmailPreview_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/EmailPreview */ \"(app-pages-browser)/./src/components/EmailPreview.tsx\")).then((mod)=>({\n            default: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(mod.default)\n        })), {\n    loadableGenerated: {\n        modules: [\n            \"app/dashboard/page.tsx -> \" + \"@/components/EmailPreview\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-16 h-16 border-2 border-emerald-500/30 border-t-emerald-500 rounded-full animate-spin mx-auto\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n            lineNumber: 86,\n            columnNumber: 18\n        }, undefined)\n});\n_c5 = OptimizedEmailPreview;\nconst OptimizedSocialMediaPreview = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_c6 = ()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_SocialMediaPreview_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/SocialMediaPreview */ \"(app-pages-browser)/./src/components/SocialMediaPreview.tsx\")).then((mod)=>({\n            default: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(mod.default)\n        })), {\n    loadableGenerated: {\n        modules: [\n            \"app/dashboard/page.tsx -> \" + \"@/components/SocialMediaPreview\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-16 h-16 border-2 border-blue-500/30 border-t-blue-500 rounded-full animate-spin mx-auto\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n            lineNumber: 91,\n            columnNumber: 18\n        }, undefined)\n});\n_c7 = OptimizedSocialMediaPreview;\nconst OptimizedVideoScriptPreview = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_c8 = ()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_VideoScriptPreview_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/VideoScriptPreview */ \"(app-pages-browser)/./src/components/VideoScriptPreview.tsx\")).then((mod)=>({\n            default: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(mod.default)\n        })), {\n    loadableGenerated: {\n        modules: [\n            \"app/dashboard/page.tsx -> \" + \"@/components/VideoScriptPreview\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-16 h-16 border-2 border-red-500/30 border-t-red-500 rounded-full animate-spin mx-auto\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n            lineNumber: 96,\n            columnNumber: 18\n        }, undefined)\n});\n_c9 = OptimizedVideoScriptPreview;\nconst OptimizedVideoAlchemyPreview = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_c10 = ()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_VideoAlchemyPreview_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/VideoAlchemyPreview */ \"(app-pages-browser)/./src/components/VideoAlchemyPreview.tsx\")).then((mod)=>({\n            default: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(mod.default)\n        })), {\n    loadableGenerated: {\n        modules: [\n            \"app/dashboard/page.tsx -> \" + \"@/components/VideoAlchemyPreview\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-16 h-16 border-2 border-purple-500/30 border-t-purple-500 rounded-full animate-spin mx-auto\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n            lineNumber: 101,\n            columnNumber: 18\n        }, undefined)\n});\n_c11 = OptimizedVideoAlchemyPreview;\nconst OptimizedMegatronPreview = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_c12 = ()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_MegatronPreview_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/MegatronPreview */ \"(app-pages-browser)/./src/components/MegatronPreview.tsx\")).then((mod)=>({\n            default: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(mod.default)\n        })), {\n    loadableGenerated: {\n        modules: [\n            \"app/dashboard/page.tsx -> \" + \"@/components/MegatronPreview\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-16 h-16 border-2 border-red-500/30 border-t-red-500 rounded-full animate-spin mx-auto\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n            lineNumber: 106,\n            columnNumber: 18\n        }, undefined)\n});\n_c13 = OptimizedMegatronPreview;\n// Optimized Sidebar Component with Memoization\nconst EnhancedSidebar = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(_s((param)=>{\n    let { isOpen, onClose, tools, selectedTool, setSelectedTool, hoveredTool, setHoveredTool } = param;\n    _s();\n    // Memoized animation variants for better performance\n    const sidebarVariants = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"EnhancedSidebar.useMemo[sidebarVariants]\": ()=>({\n                open: {\n                    x: 0,\n                    transition: {\n                        type: \"spring\",\n                        stiffness: 300,\n                        damping: 30\n                    }\n                },\n                closed: {\n                    x: -320,\n                    transition: {\n                        type: \"spring\",\n                        stiffness: 300,\n                        damping: 30\n                    }\n                }\n            })\n    }[\"EnhancedSidebar.useMemo[sidebarVariants]\"], []);\n    // Memoized handlers to prevent re-renders\n    const handleToolSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedSidebar.useCallback[handleToolSelect]\": (toolId)=>{\n            setSelectedTool(toolId);\n        }\n    }[\"EnhancedSidebar.useCallback[handleToolSelect]\"], [\n        setSelectedTool\n    ]);\n    const handleToolHover = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedSidebar.useCallback[handleToolHover]\": (toolId)=>{\n            setHoveredTool(toolId);\n        }\n    }[\"EnhancedSidebar.useCallback[handleToolHover]\"], [\n        setHoveredTool\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    transition: {\n                        duration: 0.2\n                    },\n                    onClick: onClose,\n                    className: \"fixed inset-0 bg-black/60 backdrop-blur-sm z-40 lg:hidden\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.aside, {\n                initial: \"closed\",\n                animate: isOpen ? \"open\" : \"closed\",\n                variants: sidebarVariants,\n                className: \"fixed left-0 top-0 h-screen w-[320px] z-50 lg:z-10\",\n                style: {\n                    willChange: 'transform'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black/85 backdrop-blur-xl border-r border-white/10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-br from-violet-950/15 via-black/60 to-indigo-950/15\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10 h-full flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 border-b border-white/10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"flex items-center space-x-4 group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-r from-violet-500 to-indigo-500 rounded-xl blur-lg opacity-50\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                                    whileHover: {\n                                                        scale: 1.05\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.95\n                                                    },\n                                                    transition: {\n                                                        duration: 0.2\n                                                    },\n                                                    className: \"relative bg-gradient-to-r from-violet-800 to-indigo-800 rounded-xl p-3 border border-white/20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_ChevronLeft_Clock_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-7 h-7 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-2xl font-bold text-white\",\n                                                    children: \"Invincible\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: \"Creative AI Suite\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"flex-1 p-4 space-y-2 overflow-y-auto custom-scrollbar\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                        onClick: ()=>handleToolSelect(''),\n                                        whileHover: {\n                                            x: 2\n                                        },\n                                        whileTap: {\n                                            scale: 0.98\n                                        },\n                                        transition: {\n                                            duration: 0.1\n                                        },\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"w-full flex items-center space-x-4 px-4 py-4 rounded-xl transition-all duration-200 relative\", !selectedTool ? \"bg-white/15 text-white shadow-lg border border-white/20\" : \"text-gray-400 hover:text-white hover:bg-white/8\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-2 rounded-lg bg-white/10\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_ChevronLeft_Clock_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 220,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-left\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-semibold\",\n                                                                children: \"Dashboard\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 223,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs opacity-70\",\n                                                                children: \"Overview & Stats\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 224,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            !selectedTool && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute right-3 w-1 h-8 bg-white rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"pt-6 pb-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 px-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-px flex-1 bg-white/20\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs font-semibold text-gray-400 uppercase tracking-wider\",\n                                                    children: \"AI Tools\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-px flex-1 bg-white/20\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/content\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                            whileHover: {\n                                                x: 2\n                                            },\n                                            whileTap: {\n                                                scale: 0.98\n                                            },\n                                            transition: {\n                                                duration: 0.2\n                                            },\n                                            className: \"w-full flex items-center space-x-4 px-4 py-4 text-gray-400 hover:text-white hover:bg-white/8 rounded-xl transition-all duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 rounded-lg bg-white/5\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_ChevronLeft_Clock_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-left\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-semibold\",\n                                                            children: \"Content Library\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 256,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs opacity-70\",\n                                                            children: \"View Past Content\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 257,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    tools.map((tool)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                            onClick: ()=>handleToolSelect(tool.id),\n                                            onMouseEnter: ()=>handleToolHover(tool.id),\n                                            onMouseLeave: ()=>handleToolHover(null),\n                                            whileHover: {\n                                                x: 2\n                                            },\n                                            whileTap: {\n                                                scale: 0.98\n                                            },\n                                            transition: {\n                                                duration: 0.1\n                                            },\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"w-full flex items-center space-x-4 px-4 py-4 rounded-xl transition-all duration-200 relative\", selectedTool === tool.id ? \"bg-gradient-to-r text-white shadow-lg border border-white/20\" : \"text-gray-400 hover:text-white hover:bg-white/8\", selectedTool === tool.id && tool.color),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"p-2 rounded-lg transition-colors duration-200\", selectedTool === tool.id ? \"bg-white/20\" : \"bg-white/10\"),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tool.icon, {\n                                                                className: \"w-5 h-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 287,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 281,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-left\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-semibold\",\n                                                                    children: tool.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 290,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs opacity-70\",\n                                                                    children: tool.subtitle\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 291,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 289,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                selectedTool === tool.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute right-3 w-1 h-8 bg-white rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                hoveredTool === tool.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute right-3 top-3 bg-black/60 rounded-full px-2 py-1 text-xs text-white\",\n                                                    children: tool.stats.generated\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, tool.id, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 15\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border-t border-white/10 space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/settings\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                            whileHover: {\n                                                x: 2\n                                            },\n                                            whileTap: {\n                                                scale: 0.98\n                                            },\n                                            transition: {\n                                                duration: 0.2\n                                            },\n                                            className: \"w-full flex items-center space-x-4 px-4 py-3 text-gray-400 hover:text-white hover:bg-white/8 rounded-xl transition-all duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 rounded-lg bg-white/5\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_ChevronLeft_Clock_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 318,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: \"Settings\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                        whileHover: {\n                                            x: 2\n                                        },\n                                        whileTap: {\n                                            scale: 0.98\n                                        },\n                                        transition: {\n                                            duration: 0.2\n                                        },\n                                        className: \"w-full flex items-center space-x-4 px-4 py-3 text-gray-400 hover:text-white hover:bg-white/8 rounded-xl transition-all duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 rounded-lg bg-white/5\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_ChevronLeft_Clock_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"Help & Support\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                lineNumber: 167,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n}, \"rL9lWfnAkLdZcznve+ofH+FlFk4=\"));\n_c14 = EnhancedSidebar;\n// Optimized Dashboard with Performance Enhancements\nfunction DashboardPage() {\n    _s1();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_4__.useSession)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    const [selectedTool, setSelectedTool] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('invincible-agent');\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showNotifications, setShowNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hoveredTool, setHoveredTool] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userProfile, setUserProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoadingProfile, setIsLoadingProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [userStats, setUserStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoadingStats, setIsLoadingStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // ALL useCallback hooks must be at the top level and in consistent order\n    const toggleSidebar = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DashboardPage.useCallback[toggleSidebar]\": ()=>setSidebarOpen({\n                \"DashboardPage.useCallback[toggleSidebar]\": (prev)=>!prev\n            }[\"DashboardPage.useCallback[toggleSidebar]\"])\n    }[\"DashboardPage.useCallback[toggleSidebar]\"], []);\n    const toggleNotifications = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DashboardPage.useCallback[toggleNotifications]\": ()=>setShowNotifications({\n                \"DashboardPage.useCallback[toggleNotifications]\": (prev)=>!prev\n            }[\"DashboardPage.useCallback[toggleNotifications]\"])\n    }[\"DashboardPage.useCallback[toggleNotifications]\"], []);\n    const handleSearchChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DashboardPage.useCallback[handleSearchChange]\": (e)=>{\n            setSearchQuery(e.target.value);\n        }\n    }[\"DashboardPage.useCallback[handleSearchChange]\"], []);\n    // Memoized stats calculation for tools to prevent recalculation on every render\n    const getToolStats = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DashboardPage.useCallback[getToolStats]\": (toolType)=>{\n            if (!(userStats === null || userStats === void 0 ? void 0 : userStats.contentBreakdown)) {\n                return {\n                    generated: 0,\n                    quality: 9.0,\n                    avgTime: '0 min'\n                };\n            }\n            const generated = userStats.contentBreakdown[toolType] || 0;\n            const quality = Math.min(9.8, 8.5 + generated * 0.05);\n            const avgTimes = {\n                'invincible_research': '4 min',\n                'blog': '3 min',\n                'email': '1 min',\n                'social_media': '30 sec',\n                'youtube_script': '4 min',\n                'video_alchemy': '3 min'\n            };\n            return {\n                generated,\n                quality: Math.round(quality * 10) / 10,\n                avgTime: avgTimes[toolType] || '2 min'\n            };\n        }\n    }[\"DashboardPage.useCallback[getToolStats]\"], [\n        userStats === null || userStats === void 0 ? void 0 : userStats.contentBreakdown\n    ]);\n    // Memoized tools configuration to prevent recreation - MUST be before any conditional returns\n    const tools = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"DashboardPage.useMemo[tools]\": ()=>[\n                {\n                    id: 'invincible-agent',\n                    title: 'Invincible V.1',\n                    subtitle: 'Superior Content',\n                    description: 'RAG-based content generation that analyzes competition, understands human writing patterns, and creates superior articles that dominate search results.',\n                    icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_ChevronLeft_Clock_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                    color: 'from-violet-800/80 to-indigo-800/80',\n                    bgGradient: 'from-violet-950/30 to-indigo-950/30',\n                    accentColor: 'violet',\n                    stats: getToolStats('invincible_research'),\n                    features: [\n                        'RAG Research',\n                        'Competitive Analysis',\n                        'Human Writing Style',\n                        'SEO Optimization',\n                        'Superior Quality',\n                        'Knowledge Base'\n                    ],\n                    href: '/invincible',\n                    preview: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(OptimizedInvincibleOrb, {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 442,\n                        columnNumber: 16\n                    }, this)\n                },\n                {\n                    id: 'email-generator',\n                    title: 'Email Generator',\n                    subtitle: 'Professional Emails',\n                    description: 'Generate compelling email campaigns, newsletters, and professional communications with AI-powered personalization.',\n                    icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_ChevronLeft_Clock_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                    color: 'from-emerald-500 to-teal-600',\n                    bgGradient: 'from-emerald-950/30 to-teal-950/30',\n                    accentColor: 'emerald',\n                    stats: getToolStats('email'),\n                    features: [\n                        'Personalization',\n                        'A/B Testing',\n                        'Professional Tone',\n                        'Quick Generation'\n                    ],\n                    href: '/email-generator'\n                },\n                {\n                    id: 'social-media-generator',\n                    title: 'Social Media',\n                    subtitle: 'Viral Content',\n                    description: 'Create engaging social media posts, captions, and content strategies that resonate with your audience across all platforms.',\n                    icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_ChevronLeft_Clock_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                    color: 'from-pink-500 to-rose-600',\n                    bgGradient: 'from-pink-950/30 to-rose-950/30',\n                    accentColor: 'pink',\n                    stats: getToolStats('social_media'),\n                    features: [\n                        'Multi-Platform',\n                        'Trending Hashtags',\n                        'Engagement Optimization',\n                        'Quick Generation'\n                    ],\n                    href: '/social-media-generator'\n                },\n                {\n                    id: 'blog-generator',\n                    title: 'Blog Generator',\n                    subtitle: 'SEO Optimized',\n                    description: 'Generate comprehensive, SEO-optimized blog posts with research, proper structure, and engaging content that ranks well.',\n                    icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_ChevronLeft_Clock_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                    color: 'from-blue-500 to-indigo-600',\n                    bgGradient: 'from-blue-950/30 to-indigo-950/30',\n                    accentColor: 'blue',\n                    stats: getToolStats('blog'),\n                    features: [\n                        'SEO Optimization',\n                        'Research Integration',\n                        'Long-form Content',\n                        'Professional Structure'\n                    ],\n                    href: '/blog-generator'\n                },\n                {\n                    id: 'youtube-script',\n                    title: 'YouTube Scripts',\n                    subtitle: 'Video Content',\n                    description: 'Create compelling YouTube video scripts with hooks, engagement techniques, and structured content for maximum viewer retention.',\n                    icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_ChevronLeft_Clock_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n                    color: 'from-red-500 to-orange-600',\n                    bgGradient: 'from-red-950/30 to-orange-950/30',\n                    accentColor: 'red',\n                    stats: getToolStats('youtube_script'),\n                    features: [\n                        'Hook Generation',\n                        'Retention Optimization',\n                        'CTA Integration',\n                        'Script Structure'\n                    ],\n                    href: '/youtube-script'\n                },\n                {\n                    id: 'video-alchemy',\n                    title: 'Video Alchemy',\n                    subtitle: 'Coming Soon',\n                    description: 'Transform your ideas into stunning video content with AI-powered video generation and editing capabilities.',\n                    icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_ChevronLeft_Clock_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                    color: 'from-purple-500 to-violet-600',\n                    bgGradient: 'from-purple-950/30 to-violet-950/30',\n                    accentColor: 'purple',\n                    stats: getToolStats('video_alchemy'),\n                    features: [\n                        'AI Video Generation',\n                        'Auto Editing',\n                        'Style Transfer',\n                        'Quick Export'\n                    ],\n                    href: '#',\n                    comingSoon: true\n                },\n                {\n                    id: 'megatron',\n                    title: 'Megatron',\n                    subtitle: 'Ultimate AI',\n                    description: 'The most powerful AI assistant for complex tasks, research, analysis, and creative projects with unlimited potential.',\n                    icon: Bot,\n                    color: 'from-gray-600 to-slate-700',\n                    bgGradient: 'from-gray-950/30 to-slate-950/30',\n                    accentColor: 'gray',\n                    stats: getToolStats('megatron'),\n                    features: [\n                        'Advanced Reasoning',\n                        'Multi-task Handling',\n                        'Research Capabilities',\n                        'Creative Solutions'\n                    ],\n                    href: '#',\n                    comingSoon: true\n                }\n            ]\n    }[\"DashboardPage.useMemo[tools]\"], [\n        getToolStats\n    ]);\n    // Redirect to login if not authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            if (status === 'unauthenticated') {\n                router.push('/login');\n            }\n        }\n    }[\"DashboardPage.useEffect\"], [\n        status,\n        router\n    ]);\n    // Fetch user profile data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            if (session === null || session === void 0 ? void 0 : session.user) {\n                fetchUserProfile();\n                fetchUserStats();\n            }\n        }\n    }[\"DashboardPage.useEffect\"], [\n        session\n    ]);\n    const fetchUserProfile = async ()=>{\n        try {\n            const response = await fetch('/api/user/profile');\n            if (response.ok) {\n                const data = await response.json();\n                setUserProfile(data);\n            }\n        } catch (error) {\n            console.error('Error fetching user profile:', error);\n        } finally{\n            setIsLoadingProfile(false);\n        }\n    };\n    const fetchUserStats = async ()=>{\n        try {\n            const response = await fetch('/api/stats');\n            if (response.ok) {\n                const data = await response.json();\n                setUserStats(data.stats);\n            }\n        } catch (error) {\n            console.error('Error fetching user stats:', error);\n        } finally{\n            setIsLoadingStats(false);\n        }\n    };\n    // Generate user initials for avatar\n    const getUserInitials = ()=>{\n        if ((userProfile === null || userProfile === void 0 ? void 0 : userProfile.firstName) && (userProfile === null || userProfile === void 0 ? void 0 : userProfile.lastName)) {\n            return \"\".concat(userProfile.firstName[0]).concat(userProfile.lastName[0]);\n        } else if (userProfile === null || userProfile === void 0 ? void 0 : userProfile.name) {\n            const names = userProfile.name.split(' ');\n            return names.length > 1 ? \"\".concat(names[0][0]).concat(names[names.length - 1][0]) : names[0][0];\n        } else if (userProfile === null || userProfile === void 0 ? void 0 : userProfile.email) {\n            return userProfile.email[0].toUpperCase();\n        }\n        return 'U';\n    };\n    // Get display name\n    const getDisplayName = ()=>{\n        if ((userProfile === null || userProfile === void 0 ? void 0 : userProfile.firstName) && (userProfile === null || userProfile === void 0 ? void 0 : userProfile.lastName)) {\n            return \"\".concat(userProfile.firstName, \" \").concat(userProfile.lastName);\n        } else if (userProfile === null || userProfile === void 0 ? void 0 : userProfile.name) {\n            return userProfile.name;\n        } else if (userProfile === null || userProfile === void 0 ? void 0 : userProfile.email) {\n            return userProfile.email.split('@')[0];\n        }\n        return 'User';\n    };\n    // Loading state\n    if (status === 'loading' || isLoadingProfile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin w-12 h-12 border-2 border-violet-400 border-t-transparent rounded-full mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 599,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: \"Loading your dashboard...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 600,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                lineNumber: 598,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n            lineNumber: 597,\n            columnNumber: 7\n        }, this);\n    }\n    // Don't render if not authenticated\n    if (status === 'unauthenticated') {\n        return null;\n    }\n    const activeTool = tools.find((t)=>t.id === selectedTool) || tools[0];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-black\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-violet-950/10 via-black to-indigo-950/10\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 617,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        transition: {\n                            duration: 0.5,\n                            ease: [\n                                0.4,\n                                0,\n                                0.2,\n                                1\n                            ]\n                        },\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"absolute inset-0 bg-gradient-to-br opacity-20\", activeTool.bgGradient)\n                    }, activeTool.id, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 620,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-1/4 left-1/4 w-[500px] h-[500px] bg-violet-700/10 rounded-full blur-[100px] animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 632,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-1/4 right-1/4 w-[600px] h-[600px] bg-indigo-700/10 rounded-full blur-[120px] animate-pulse\",\n                        style: {\n                            animationDelay: '1s'\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 633,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                lineNumber: 616,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 flex min-h-screen\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EnhancedSidebar, {\n                        isOpen: sidebarOpen,\n                        onClose: ()=>setSidebarOpen(false),\n                        tools: tools,\n                        selectedTool: selectedTool,\n                        setSelectedTool: setSelectedTool,\n                        hoveredTool: hoveredTool,\n                        setHoveredTool: setHoveredTool\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 639,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                        onClick: toggleSidebar,\n                        animate: {\n                            left: sidebarOpen ? 308 : 20\n                        },\n                        transition: {\n                            type: \"spring\",\n                            stiffness: 300,\n                            damping: 30\n                        },\n                        whileHover: {\n                            scale: 1.02\n                        },\n                        whileTap: {\n                            scale: 0.98\n                        },\n                        className: \"fixed top-6 z-50 p-3 bg-black/80 backdrop-blur-sm border border-white/20 rounded-xl hover:bg-black/90 transition-colors duration-200\",\n                        style: {\n                            willChange: 'transform'\n                        },\n                        children: sidebarOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_ChevronLeft_Clock_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            className: \"w-5 h-5 text-white\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                            lineNumber: 665,\n                            columnNumber: 26\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_ChevronLeft_Clock_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                            className: \"w-5 h-5 text-white\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                            lineNumber: 665,\n                            columnNumber: 75\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 650,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.main, {\n                        animate: {\n                            marginLeft: sidebarOpen ? 320 : 0\n                        },\n                        transition: {\n                            type: \"tween\",\n                            duration: 0.3,\n                            ease: [\n                                0.4,\n                                0,\n                                0.2,\n                                1\n                            ]\n                        },\n                        className: \"flex-1 min-h-screen\",\n                        style: {\n                            willChange: 'margin-left'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                                className: \"sticky top-0 z-40 backdrop-blur-xl bg-black/60 border-b border-white/10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                    animate: {\n                                        paddingLeft: sidebarOpen ? 32 : 80\n                                    },\n                                    transition: {\n                                        type: \"tween\",\n                                        duration: 0.3,\n                                        ease: [\n                                            0.4,\n                                            0,\n                                            0.2,\n                                            1\n                                        ]\n                                    },\n                                    className: \"px-8 py-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-white\",\n                                                        children: selectedTool ? activeTool.title : 'Dashboard Overview'\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 694,\n                                                        columnNumber: 39\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_ChevronLeft_Clock_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                className: \"absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 700,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                placeholder: \"Search tools, features...\",\n                                                                value: searchQuery,\n                                                                onChange: handleSearchChange,\n                                                                className: \"w-96 pl-12 pr-4 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/15 focus:border-violet-500/50 focus:outline-none transition-colors duration-200\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 701,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 699,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 693,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"relative p-3 text-gray-400 hover:text-white transition-colors duration-200 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl hover:bg-white/15\",\n                                                        onClick: toggleNotifications,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_ChevronLeft_Clock_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                className: \"w-5 h-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 717,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"absolute top-2 right-2 w-2 h-2 bg-violet-500 rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 718,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 713,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-8 w-px bg-white/20\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 721,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProfileButton__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        userProfile: userProfile,\n                                                        className: \"pl-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 723,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 712,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 692,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                    lineNumber: 683,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 682,\n                                columnNumber: 23\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                animate: {\n                                    paddingLeft: sidebarOpen ? 32 : 80\n                                },\n                                transition: {\n                                    type: \"tween\",\n                                    duration: 0.3,\n                                    ease: [\n                                        0.4,\n                                        0,\n                                        0.2,\n                                        1\n                                    ]\n                                },\n                                className: \"p-8 pb-16 pr-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                                    mode: \"wait\",\n                                    children: selectedTool ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToolDetails, {\n                                        tool: activeTool\n                                    }, selectedTool, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 746,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DashboardOverview, {\n                                        tools: tools,\n                                        userProfile: userProfile,\n                                        getDisplayName: getDisplayName,\n                                        userStats: userStats,\n                                        isLoadingStats: isLoadingStats\n                                    }, \"overview\", false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 748,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                    lineNumber: 744,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 733,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 669,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                lineNumber: 637,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n        lineNumber: 614,\n        columnNumber: 5\n    }, this);\n}\n_s1(DashboardPage, \"VYZLqcLgLdGHvLM+6PQ9g7+ikh0=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_4__.useSession,\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter\n    ];\n});\n_c15 = DashboardPage;\n// Optimized Tool Details Component with Memoization\nconst ToolDetails = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo((param)=>{\n    let { tool } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n        initial: {\n            opacity: 0,\n            scale: 0.95\n        },\n        animate: {\n            opacity: 1,\n            scale: 1\n        },\n        exit: {\n            opacity: 0,\n            scale: 0.95\n        },\n        transition: {\n            duration: 0.2,\n            ease: \"easeOut\"\n        },\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.3,\n                    delay: 0.1\n                },\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"relative overflow-hidden rounded-3xl backdrop-blur-xl border p-8\", tool.id === 'invincible-agent' ? \"bg-gradient-to-br from-white/10 to-white/5 border-white/20 shadow-2xl\" : \"bg-gradient-to-br from-white/5 to-white/0 border-white/10\"),\n                children: [\n                    tool.id === 'invincible-agent' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-white/20 via-transparent to-transparent rounded-3xl\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 783,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"absolute inset-0 bg-gradient-to-br opacity-20\", tool.color)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 786,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10 grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"p-4 rounded-2xl text-white shadow-2xl\", tool.id === 'invincible-agent' ? \"bg-gradient-to-br from-violet-800/70 to-indigo-800/70 backdrop-blur-sm border border-white/30\" : (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"bg-gradient-to-br\", tool.color)),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tool.icon, {\n                                                    className: \"w-8 h-8\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 800,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 794,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-3xl font-bold text-white\",\n                                                        children: tool.title\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 803,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg text-gray-300\",\n                                                        children: tool.subtitle\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 804,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 802,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 793,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300 leading-relaxed\",\n                                        children: tool.description\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 808,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-3\",\n                                        children: tool.features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"px-4 py-2 rounded-full text-sm text-white border\", tool.id === 'invincible-agent' ? \"bg-white/15 backdrop-blur-sm border-white/30\" : \"bg-white/10 backdrop-blur-sm border-white/20\"),\n                                                children: feature\n                                            }, index, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 814,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 812,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: tool.href,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                                whileHover: {\n                                                    scale: 1.05\n                                                },\n                                                whileTap: {\n                                                    scale: 0.95\n                                                },\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"px-12 py-6 rounded-2xl font-bold text-lg text-white shadow-2xl transition-all flex items-center space-x-3\", tool.id === 'invincible-agent' ? \"bg-gradient-to-r from-violet-800/80 to-indigo-800/80 backdrop-blur-sm border border-white/30 hover:from-violet-700/90 hover:to-indigo-700/90\" : (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"bg-gradient-to-r\", tool.color)),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_ChevronLeft_Clock_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                        className: \"w-6 h-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 840,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Launch \",\n                                                            tool.title\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 841,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 830,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 829,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 828,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 792,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"relative h-[400px] rounded-2xl overflow-hidden border\", tool.id === 'invincible-agent' ? \"bg-black/30 backdrop-blur-sm border-white/20\" : \"bg-black/40 border-white/10\"),\n                                children: tool.preview || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tool.icon, {\n                                                className: \"w-24 h-24 text-white/20 mx-auto\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 857,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400\",\n                                                children: \"Interactive preview coming soon\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 858,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 856,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                    lineNumber: 855,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 848,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 791,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                lineNumber: 771,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.3,\n                    delay: 0.2\n                },\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/content?type=\".concat(tool.id === 'invincible-agent' ? 'invincible_research' : tool.id === 'blog-generator' ? 'blog' : tool.id === 'email-generator' ? 'email' : tool.id === 'youtube-script' ? 'youtube_script' : tool.id.replace('-', '_')),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                delay: 0.25\n                            },\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"backdrop-blur-xl border rounded-2xl p-6 cursor-pointer hover:bg-white/10 transition-all duration-200 group\", tool.id === 'invincible-agent' ? \"bg-white/10 border-white/20 shadow-xl hover:border-white/30\" : \"bg-white/5 border-white/10 hover:border-white/20\"),\n                            title: \"View all \".concat(tool.title, \" content in your library\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_ChevronLeft_Clock_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"w-8 h-8 group-hover:scale-110 transition-transform duration-200\", \"text-\".concat(tool.accentColor, \"-400\"))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 887,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_ChevronLeft_Clock_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    className: \"w-5 h-5 text-emerald-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 889,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_ChevronLeft_Clock_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    className: \"w-4 h-4 text-gray-400 group-hover:text-white transition-colors\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 890,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 888,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                    lineNumber: 886,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-3xl font-bold text-white mb-1 group-hover:text-violet-200 transition-colors\",\n                                    children: tool.stats.generated\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                    lineNumber: 893,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-400 group-hover:text-gray-300 transition-colors\",\n                                    children: \"Content Generated - Click to view\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                    lineNumber: 894,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center text-xs text-violet-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_ChevronLeft_Clock_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                className: \"w-3 h-3 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 899,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"View in Content Library\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 900,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 898,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                    lineNumber: 897,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                            lineNumber: 874,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 873,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 0.35\n                        },\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"backdrop-blur-xl border rounded-2xl p-6\", tool.id === 'invincible-agent' ? \"bg-white/10 border-white/20 shadow-xl\" : \"bg-white/5 border-white/10\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_ChevronLeft_Clock_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"w-8 h-8\", \"text-\".concat(tool.accentColor, \"-400\"))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 918,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            1,\n                                            2,\n                                            3,\n                                            4,\n                                            5\n                                        ].map((star)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"w-3 h-3 rounded-full\", star <= Math.round(tool.stats.quality / 2) ? \"bg-yellow-400\" : \"bg-gray-600\")\n                                            }, star, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 921,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 919,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 917,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-3xl font-bold text-white mb-1\",\n                                children: [\n                                    tool.stats.quality,\n                                    \"/10\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 933,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-400\",\n                                children: \"Quality Score\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 934,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 906,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 0.45\n                        },\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"backdrop-blur-xl border rounded-2xl p-6\", tool.id === 'invincible-agent' ? \"bg-white/10 border-white/20 shadow-xl\" : \"bg-white/5 border-white/10\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_ChevronLeft_Clock_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"w-8 h-8\", \"text-\".concat(tool.accentColor, \"-400\"))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 949,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_ChevronLeft_Clock_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                        className: \"w-5 h-5 text-blue-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 950,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 948,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-3xl font-bold text-white mb-1\",\n                                children: tool.stats.avgTime\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 952,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-400\",\n                                children: \"Average Time\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 953,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 937,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                lineNumber: 867,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n        lineNumber: 763,\n        columnNumber: 5\n    }, undefined);\n});\n_c16 = ToolDetails;\n// Optimized Dashboard Overview Component with Memoization\nconst DashboardOverview = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo((param)=>{\n    let { tools, userProfile, getDisplayName, userStats, isLoadingStats } = param;\n    var _userStats_totalContent, _userStats_trends, _userStats_trends1, _userStats_trends2, _userStats_trends_toolsActive, _userStats_trends3, _userStats_trends4;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n        initial: {\n            opacity: 0,\n            scale: 0.95\n        },\n        animate: {\n            opacity: 1,\n            scale: 1\n        },\n        exit: {\n            opacity: 0,\n            scale: 0.95\n        },\n        transition: {\n            duration: 0.2,\n            ease: \"easeOut\"\n        },\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-violet-800/20 to-indigo-800/20 backdrop-blur-xl border border-white/10 rounded-3xl p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl font-bold text-white mb-4\",\n                        children: [\n                            \"Welcome back, \",\n                            getDisplayName(),\n                            \"! ✨\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 978,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-300 mb-6\",\n                        children: \"Your creative AI toolkit is ready. What will you create today?\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 979,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                        children: isLoadingStats ? // Loading skeleton\n                        Array.from({\n                            length: 4\n                        }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: index * 0.1\n                                },\n                                className: \"bg-black/40 backdrop-blur-sm border border-white/10 rounded-xl p-4 animate-pulse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-5 bg-white/10 rounded mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 992,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-8 bg-white/10 rounded mb-1\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 993,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-white/10 rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 994,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, index, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 985,\n                                columnNumber: 15\n                            }, undefined)) : [\n                            {\n                                label: 'Total Created',\n                                value: (userStats === null || userStats === void 0 ? void 0 : (_userStats_totalContent = userStats.totalContent) === null || _userStats_totalContent === void 0 ? void 0 : _userStats_totalContent.toString()) || '0',\n                                icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_ChevronLeft_Clock_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                                change: (userStats === null || userStats === void 0 ? void 0 : (_userStats_trends = userStats.trends) === null || _userStats_trends === void 0 ? void 0 : _userStats_trends.contentGrowth) || '+0%'\n                            },\n                            {\n                                label: 'Time Saved',\n                                value: \"\".concat((userStats === null || userStats === void 0 ? void 0 : userStats.timeSavedHours) || 0, \" hrs\"),\n                                icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_ChevronLeft_Clock_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"],\n                                change: (userStats === null || userStats === void 0 ? void 0 : (_userStats_trends1 = userStats.trends) === null || _userStats_trends1 === void 0 ? void 0 : _userStats_trends1.timeEfficiency) || '+0%'\n                            },\n                            {\n                                label: 'Quality Score',\n                                value: \"\".concat((userStats === null || userStats === void 0 ? void 0 : userStats.qualityScore) || 9.0, \"/10\"),\n                                icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_ChevronLeft_Clock_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n                                change: (userStats === null || userStats === void 0 ? void 0 : (_userStats_trends2 = userStats.trends) === null || _userStats_trends2 === void 0 ? void 0 : _userStats_trends2.qualityImprovement) || '+0.0'\n                            },\n                            {\n                                label: 'Active Tools',\n                                value: (userStats === null || userStats === void 0 ? void 0 : (_userStats_trends3 = userStats.trends) === null || _userStats_trends3 === void 0 ? void 0 : (_userStats_trends_toolsActive = _userStats_trends3.toolsActive) === null || _userStats_trends_toolsActive === void 0 ? void 0 : _userStats_trends_toolsActive.toString()) || '0',\n                                icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_ChevronLeft_Clock_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                                change: '+' + ((userStats === null || userStats === void 0 ? void 0 : (_userStats_trends4 = userStats.trends) === null || _userStats_trends4 === void 0 ? void 0 : _userStats_trends4.toolsActive) || 0)\n                            }\n                        ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: index * 0.1\n                                },\n                                className: \"bg-black/40 backdrop-blur-sm border border-white/10 rounded-xl p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                                className: \"w-5 h-5 text-violet-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 1032,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-emerald-400\",\n                                                children: stat.change\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 1033,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 1031,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-white\",\n                                        children: stat.value\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 1035,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-400\",\n                                        children: stat.label\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 1036,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, stat.label, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 1024,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 981,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                lineNumber: 977,\n                columnNumber: 15\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-white mb-6\",\n                        children: \"Your AI Tools\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 1045,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                        children: tools.map((tool, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: tool.href,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        scale: 0.9\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        scale: 1\n                                    },\n                                    transition: {\n                                        delay: index * 0.1\n                                    },\n                                    whileHover: {\n                                        y: -8\n                                    },\n                                    className: \"group relative cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"absolute inset-0 bg-gradient-to-br opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl blur-xl\", tool.color)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 1056,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"relative backdrop-blur-xl border transition-all\", tool.id === 'invincible-agent' ? \"bg-black/40 border-white/20 hover:border-white/30 rounded-2xl shadow-2xl\" : \"bg-black/60 border-white/10 hover:border-white/20 rounded-2xl\"),\n                                            children: [\n                                                tool.id === 'invincible-agent' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-transparent rounded-2xl\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 1069,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start justify-between mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"p-3 rounded-xl text-white\", tool.id === 'invincible-agent' ? \"bg-gradient-to-br from-violet-800/60 to-indigo-800/60 backdrop-blur-sm border border-white/20\" : (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"bg-gradient-to-br\", tool.color)),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tool.icon, {\n                                                                        className: \"w-6 h-6\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                        lineNumber: 1080,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 1074,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_ChevronLeft_Clock_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    className: \"w-5 h-5 text-gray-400 group-hover:text-white transition-colors\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 1082,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 1073,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-white mb-1\",\n                                                            children: tool.title\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 1085,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-400 mb-4\",\n                                                            children: tool.subtitle\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 1086,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between text-xs\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                    href: \"/content?type=\".concat(tool.id === 'invincible-agent' ? 'invincible_research' : tool.id === 'blog-generator' ? 'blog' : tool.id === 'email-generator' ? 'email' : tool.id === 'youtube-script' ? 'youtube_script' : tool.id.replace('-', '_')),\n                                                                    onClick: (e)=>e.stopPropagation(),\n                                                                    className: \"hover:bg-white/10 rounded px-2 py-1 transition-colors group/stat\",\n                                                                    title: \"View all \".concat(tool.title, \" content\"),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-500 group-hover/stat:text-gray-400\",\n                                                                            children: \"Generated\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                            lineNumber: 1095,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-white font-medium group-hover/stat:text-violet-200\",\n                                                                                    children: tool.stats.generated\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                                    lineNumber: 1097,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_ChevronLeft_Clock_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                    className: \"w-3 h-3 text-gray-400 group-hover/stat:text-white opacity-0 group-hover/stat:opacity-100 transition-all\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                                    lineNumber: 1098,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                            lineNumber: 1096,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-black/90 text-white text-xs rounded opacity-0 group-hover/stat:opacity-100 transition-opacity pointer-events-none whitespace-nowrap\",\n                                                                            children: \"Click to view content\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                            lineNumber: 1101,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 1089,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-right\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-500\",\n                                                                            children: \"Quality\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                            lineNumber: 1106,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-white font-medium\",\n                                                                            children: [\n                                                                                tool.stats.quality,\n                                                                                \"/10\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                            lineNumber: 1107,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 1105,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 1088,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 1072,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 1061,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                    lineNumber: 1049,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, tool.id, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 1048,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 1046,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                lineNumber: 1044,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(OptimizedRecentContent, {\n                    limit: 5,\n                    showFilters: true\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                    lineNumber: 1120,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                lineNumber: 1119,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n        lineNumber: 969,\n        columnNumber: 5\n    }, undefined);\n});\n_c17 = DashboardOverview;\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17;\n$RefreshReg$(_c, \"OptimizedRecentContent\");\n$RefreshReg$(_c1, \"OptimizedInvincibleOrb\");\n$RefreshReg$(_c2, \"OptimizedBlogPreview$dynamic\");\n$RefreshReg$(_c3, \"OptimizedBlogPreview\");\n$RefreshReg$(_c4, \"OptimizedEmailPreview$dynamic\");\n$RefreshReg$(_c5, \"OptimizedEmailPreview\");\n$RefreshReg$(_c6, \"OptimizedSocialMediaPreview$dynamic\");\n$RefreshReg$(_c7, \"OptimizedSocialMediaPreview\");\n$RefreshReg$(_c8, \"OptimizedVideoScriptPreview$dynamic\");\n$RefreshReg$(_c9, \"OptimizedVideoScriptPreview\");\n$RefreshReg$(_c10, \"OptimizedVideoAlchemyPreview$dynamic\");\n$RefreshReg$(_c11, \"OptimizedVideoAlchemyPreview\");\n$RefreshReg$(_c12, \"OptimizedMegatronPreview$dynamic\");\n$RefreshReg$(_c13, \"OptimizedMegatronPreview\");\n$RefreshReg$(_c14, \"EnhancedSidebar\");\n$RefreshReg$(_c15, \"DashboardPage\");\n$RefreshReg$(_c16, \"ToolDetails\");\n$RefreshReg$(_c17, \"DashboardOverview\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});