"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/invincible/page",{

/***/ "(app-pages-browser)/./src/app/invincible/page.tsx":
/*!*************************************!*\
  !*** ./src/app/invincible/page.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ InvinciblePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart,Brain,Crown,Monitor,Rocket,Search,Settings,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart,Brain,Crown,Monitor,Rocket,Search,Settings,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart,Brain,Crown,Monitor,Rocket,Search,Settings,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart,Brain,Crown,Monitor,Rocket,Search,Settings,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart,Brain,Crown,Monitor,Rocket,Search,Settings,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart,Brain,Crown,Monitor,Rocket,Search,Settings,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart,Brain,Crown,Monitor,Rocket,Search,Settings,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart,Brain,Crown,Monitor,Rocket,Search,Settings,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart,Brain,Crown,Monitor,Rocket,Search,Settings,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart,Brain,Crown,Monitor,Rocket,Search,Settings,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart,Brain,Crown,Monitor,Rocket,Search,Settings,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart,Brain,Crown,Monitor,Rocket,Search,Settings,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart,Brain,Crown,Monitor,Rocket,Search,Settings,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_InvincibleStreamingUI__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/InvincibleStreamingUI */ \"(app-pages-browser)/./src/components/InvincibleStreamingUI.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction InvinciblePage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_4__.useSession)();\n    const [config, setConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        topic: '',\n        customInstructions: '',\n        targetAudience: '',\n        contentLength: 2000,\n        tone: 'professional',\n        keywords: [],\n        searchDepth: 7,\n        competitorCount: 5,\n        deepSearchQueriesPerTopic: 7,\n        uniquenessLevel: 'high',\n        version: 'v2'\n    });\n    const [keywordInput, setKeywordInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showStreamingUI, setShowStreamingUI] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [streamingResult, setStreamingResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Handle URL parameters from Megatron\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"InvinciblePage.useEffect\": ()=>{\n            const topic = searchParams.get('topic');\n            const customInstructions = searchParams.get('customInstructions');\n            const source = searchParams.get('source');\n            const videoUrl = searchParams.get('videoUrl');\n            const videoTitle = searchParams.get('videoTitle');\n            if (source === 'megatron' && topic) {\n                setConfig({\n                    \"InvinciblePage.useEffect\": (prevConfig)=>({\n                            ...prevConfig,\n                            topic: topic,\n                            customInstructions: customInstructions || prevConfig.customInstructions,\n                            // Add metadata about the source\n                            ...videoUrl && {\n                                sourceVideoUrl: videoUrl\n                            },\n                            ...videoTitle && {\n                                sourceVideoTitle: videoTitle\n                            }\n                        })\n                }[\"InvinciblePage.useEffect\"]);\n            }\n        }\n    }[\"InvinciblePage.useEffect\"], [\n        searchParams\n    ]);\n    // Redirect to login if not authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"InvinciblePage.useEffect\": ()=>{\n            if (status === 'unauthenticated') {\n                window.location.href = '/login';\n            }\n        }\n    }[\"InvinciblePage.useEffect\"], [\n        status\n    ]);\n    // Loading state\n    if (status === 'loading') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-black flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin w-12 h-12 border-2 border-violet-400 border-t-transparent rounded-full mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                lineNumber: 106,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n            lineNumber: 105,\n            columnNumber: 7\n        }, this);\n    }\n    // Don't render if not authenticated\n    if (status === 'unauthenticated') {\n        return null;\n    }\n    const handleStartStreaming = async ()=>{\n        if (!config.topic.trim()) {\n            setError('Please enter a topic');\n            return;\n        }\n        setError('');\n        setIsStreaming(true);\n        // For all modes including autonomous, use the streaming UI\n        setShowStreamingUI(true);\n    };\n    const handleStreamingComplete = async (result)=>{\n        setStreamingResult(result);\n        setIsStreaming(false);\n        setIsSaving(true);\n        try {\n            var _result_stats, _result_insights, _result_stats1, _result_stats2, _result_article, _result_article1, _result_insights1, _result_insights2, _result_insights3;\n            console.log('🔍 Raw result structure:', {\n                hasResult: !!result.result,\n                hasArticle: !!result.article,\n                hasContent: !!result.content,\n                resultKeys: Object.keys(result || {}),\n                resultType: typeof result.result,\n                resultContent: result.result ? Object.keys(result.result) : 'N/A'\n            });\n            // Extract article data with comprehensive fallback logic\n            let articleData = null;\n            let articleTitle = '';\n            let articleContent = '';\n            // Handle different result structures\n            if (result.result) {\n                // result.result path\n                articleData = result.result;\n                articleTitle = result.result.title || '';\n                articleContent = result.result.content || '';\n                console.log('📄 Using result.result path');\n            } else if (result.article) {\n                // Alternative: result.article path\n                articleData = result.article;\n                articleTitle = result.article.title || '';\n                articleContent = result.article.content || '';\n                console.log('📄 Using result.article path');\n            } else if (result.content) {\n                // Direct content path\n                articleContent = result.content;\n                articleTitle = result.title || \"Article about \".concat(config.topic);\n                console.log('📄 Using result.content path');\n            } else {\n                // Last resort: use result directly\n                articleData = result;\n                articleTitle = result.title || \"Article about \".concat(config.topic);\n                articleContent = result.content || '';\n                console.log('📄 Using result direct path');\n            }\n            console.log('📊 Extracted article data:', {\n                hasArticleData: !!articleData,\n                title: articleTitle,\n                titleLength: (articleTitle === null || articleTitle === void 0 ? void 0 : articleTitle.length) || 0,\n                contentLength: (articleContent === null || articleContent === void 0 ? void 0 : articleContent.length) || 0,\n                contentPreview: (articleContent === null || articleContent === void 0 ? void 0 : articleContent.substring(0, 200)) || 'EMPTY',\n                contentType: typeof articleContent\n            });\n            // Validate title\n            if (!articleTitle || articleTitle.trim().length === 0) {\n                articleTitle = \"Complete Guide to \".concat(config.topic);\n                console.log('⚠️ Using fallback title:', articleTitle);\n            }\n            // Validate content with detailed error reporting\n            if (!articleContent || articleContent.trim().length === 0) {\n                console.error('❌ Content validation failed:', {\n                    contentExists: !!articleContent,\n                    contentType: typeof articleContent,\n                    contentLength: (articleContent === null || articleContent === void 0 ? void 0 : articleContent.length) || 0,\n                    rawContent: articleContent,\n                    resultStructure: JSON.stringify(result, null, 2).substring(0, 1000),\n                    configVersion: config.version,\n                    extractionPath: 'fallback paths'\n                });\n                // Error message for content generation failure\n                const errorMessage = \"Content generation failed - no content was generated. This might be due to API issues or content filtering. Please try again or contact support if the issue persists.\";\n                throw new Error(errorMessage);\n            }\n            // Final validation\n            if (articleContent.trim().length < 50) {\n                throw new Error(\"Generated content is too short (\".concat(articleContent.length, \" characters). Please try again.\"));\n            }\n            // Save the article to the database\n            const response = await fetch('/api/articles/store', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    title: articleTitle,\n                    content: articleContent,\n                    type: 'invincible',\n                    metadata: {\n                        topic: config.topic,\n                        tone: config.tone,\n                        targetAudience: config.targetAudience,\n                        contentLength: config.contentLength,\n                        customInstructions: config.customInstructions,\n                        keywords: config.keywords,\n                        executionTime: result.executionTime || ((_result_stats = result.stats) === null || _result_stats === void 0 ? void 0 : _result_stats.executionTime),\n                        totalSources: ((_result_insights = result.insights) === null || _result_insights === void 0 ? void 0 : _result_insights.totalSources) || ((_result_stats1 = result.stats) === null || _result_stats1 === void 0 ? void 0 : _result_stats1.totalSources),\n                        uniquenessScore: (_result_stats2 = result.stats) === null || _result_stats2 === void 0 ? void 0 : _result_stats2.uniquenessScore,\n                        seoScore: (articleData === null || articleData === void 0 ? void 0 : articleData.seoScore) || ((_result_article = result.article) === null || _result_article === void 0 ? void 0 : _result_article.seoScore),\n                        readabilityScore: (articleData === null || articleData === void 0 ? void 0 : articleData.readabilityScore) || ((_result_article1 = result.article) === null || _result_article1 === void 0 ? void 0 : _result_article1.readabilityScore),\n                        qualityScore: result.qualityScore || ((_result_insights1 = result.insights) === null || _result_insights1 === void 0 ? void 0 : _result_insights1.qualityScore),\n                        competitorsAnalyzed: (_result_insights2 = result.insights) === null || _result_insights2 === void 0 ? void 0 : _result_insights2.competitorsAnalyzed,\n                        iterationsCompleted: (_result_insights3 = result.insights) === null || _result_insights3 === void 0 ? void 0 : _result_insights3.iterationsCompleted,\n                        factCheckReport: result.factCheckReport,\n                        generatedAt: new Date().toISOString()\n                    },\n                    tone: config.tone,\n                    language: 'en'\n                })\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                throw new Error(\"Failed to save article: \".concat(errorText));\n            }\n            const saveResult = await response.json();\n            if (saveResult.success) {\n                // Small delay to show saving completion\n                await new Promise((resolve)=>setTimeout(resolve, 1000));\n                // Redirect to the article view page with the saved article ID\n                router.push(\"/article-view/\".concat(saveResult.article.id));\n            } else {\n                throw new Error(saveResult.error || 'Failed to save article');\n            }\n        } catch (error) {\n            console.error('Error saving article:', error);\n            setIsSaving(false);\n            setError(\"Article generated successfully but failed to save: \".concat(error instanceof Error ? error.message : 'Unknown error', \". Please try again.\"));\n        }\n    };\n    const handleStreamingError = (error)=>{\n        setError(error);\n        setIsStreaming(false);\n    };\n    const addKeyword = ()=>{\n        var _config_keywords;\n        if (keywordInput.trim() && !((_config_keywords = config.keywords) === null || _config_keywords === void 0 ? void 0 : _config_keywords.includes(keywordInput.trim()))) {\n            setConfig({\n                ...config,\n                keywords: [\n                    ...config.keywords || [],\n                    keywordInput.trim()\n                ]\n            });\n            setKeywordInput('');\n        }\n    };\n    const removeKeyword = (keyword)=>{\n        var _config_keywords;\n        setConfig({\n            ...config,\n            keywords: ((_config_keywords = config.keywords) === null || _config_keywords === void 0 ? void 0 : _config_keywords.filter((k)=>k !== keyword)) || []\n        });\n    };\n    const resetToConfiguration = ()=>{\n        setShowStreamingUI(false);\n        setIsStreaming(false);\n        setStreamingResult(null);\n        setError('');\n        setIsSaving(false);\n    };\n    const viewGeneratedArticle = ()=>{\n        router.push('/article-view');\n    };\n    // If showing streaming UI, render the appropriate component based on version\n    if (showStreamingUI) {\n        if (config.version === 'v2') {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InvincibleV2StreamingUI, {\n                topic: config.topic,\n                contentLength: config.contentLength,\n                tone: config.tone,\n                targetAudience: config.targetAudience,\n                customInstructions: config.customInstructions,\n                contentType: \"article\",\n                onComplete: handleStreamingComplete,\n                onError: handleStreamingError,\n                isSaving: isSaving\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                lineNumber: 310,\n                columnNumber: 9\n            }, this);\n        } else {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_InvincibleStreamingUI__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                topic: config.topic,\n                contentLength: config.contentLength,\n                tone: config.tone,\n                targetAudience: config.targetAudience,\n                customInstructions: config.customInstructions,\n                onComplete: handleStreamingComplete,\n                onError: handleStreamingError,\n                isSaving: isSaving\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                lineNumber: 324,\n                columnNumber: 9\n            }, this);\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-black\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-violet-900/10 via-black to-indigo-900/10\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                        lineNumber: 342,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        animate: {\n                            x: [\n                                0,\n                                100,\n                                0\n                            ],\n                            y: [\n                                0,\n                                -100,\n                                0\n                            ]\n                        },\n                        transition: {\n                            duration: 20,\n                            repeat: Infinity,\n                            ease: \"linear\"\n                        },\n                        className: \"absolute top-1/4 left-1/4 w-[500px] h-[500px] bg-violet-500/10 rounded-full blur-[100px]\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                        lineNumber: 345,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        animate: {\n                            x: [\n                                0,\n                                -100,\n                                0\n                            ],\n                            y: [\n                                0,\n                                100,\n                                0\n                            ]\n                        },\n                        transition: {\n                            duration: 15,\n                            repeat: Infinity,\n                            ease: \"linear\"\n                        },\n                        className: \"absolute bottom-1/4 right-1/4 w-[600px] h-[600px] bg-indigo-500/10 rounded-full blur-[120px]\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                lineNumber: 341,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.header, {\n                initial: {\n                    opacity: 0,\n                    y: -20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                className: \"relative z-10 border-b border-white/10 backdrop-blur-xl bg-black/40\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-6 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/dashboard\",\n                                        className: \"flex items-center space-x-3 text-gray-400 hover:text-white transition-colors group\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-5 h-5 group-hover:-translate-x-1 transition-transform\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"Back to Dashboard\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-6 w-px bg-white/20\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 rounded-xl blur-lg opacity-70 bg-gradient-to-r from-violet-600 to-indigo-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative bg-black rounded-xl p-2.5 border border-white/20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-6 h-6 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                            lineNumber: 394,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 391,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-xl font-bold text-white\",\n                                                        children: \"Invincible\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 398,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-400\",\n                                                        children: \"Advanced AI Content Generation\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 401,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 397,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                        lineNumber: 390,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                lineNumber: 379,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden md:flex items-center space-x-6 text-sm text-gray-400\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-4 h-4 text-emerald-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 411,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"AI Detection Bypass\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 412,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                            lineNumber: 410,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-4 h-4 text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 415,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Live Streaming\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                            lineNumber: 414,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-4 h-4 text-yellow-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Real-time Analysis\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                            lineNumber: 418,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                    lineNumber: 409,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                lineNumber: 408,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                        lineNumber: 378,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                    lineNumber: 377,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                lineNumber: 372,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"relative z-10 max-w-6xl mx-auto px-6 py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-flex items-center space-x-2 px-4 py-2 rounded-full bg-white/10 backdrop-blur-md border border-white/20 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"w-4 h-4 text-green-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                        lineNumber: 437,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-200\",\n                                        children: \"Live Streaming Generation\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                        lineNumber: 438,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"w-4 h-4 text-violet-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-200\",\n                                        children: \"AI Detection Bypass\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-4 h-4 text-yellow-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                        lineNumber: 441,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                lineNumber: 436,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-5xl md:text-6xl font-bold text-white mb-6 leading-tight\",\n                                children: [\n                                    \"Watch Your Content\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"block bg-gradient-to-r from-violet-400 via-purple-400 to-indigo-400 bg-clip-text text-transparent\",\n                                        children: \"Come to Life\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                lineNumber: 444,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-300 max-w-3xl mx-auto mb-8 leading-relaxed\",\n                                children: \"Experience the most advanced content generation with real-time streaming. Watch as our AI analyzes competitors, processes research, applies humanization techniques, and creates superior articles that bypass AI detection.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                lineNumber: 451,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto mt-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center mb-4 mx-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-6 h-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 458,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-white mb-2\",\n                                                children: \"Live Streaming\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 461,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"Watch queries, analysis, and generation in real-time\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 464,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                        lineNumber: 457,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center mb-4 mx-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-6 h-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 471,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 470,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-white mb-2\",\n                                                children: \"AI Detection Bypass\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 473,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"Advanced humanization with date variation and jargon removal\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 476,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                        lineNumber: 469,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center mb-4 mx-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-6 h-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 483,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 482,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-white mb-2\",\n                                                children: \"Superior Quality\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"Content that surpasses all competitors with comprehensive research\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 488,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                        lineNumber: 481,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                lineNumber: 456,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                        lineNumber: 431,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: -20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    delay: 0.1\n                                },\n                                className: \"lg:col-span-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 mb-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 bg-gradient-to-r from-violet-600 to-indigo-600 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"w-5 h-5 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 507,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 506,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-2xl font-bold text-white\",\n                                                    children: \"Configuration\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 509,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                            lineNumber: 505,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-white/90\",\n                                                            children: \"Invincible Version\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                            lineNumber: 515,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-2 gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: ()=>setConfig({\n                                                                            ...config,\n                                                                            version: 'v1'\n                                                                        }),\n                                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"px-6 py-4 rounded-xl border transition-all flex items-center space-x-3\", config.version === 'v1' ? \"bg-green-600/20 border-green-500/50 text-green-300\" : \"bg-white/5 border-white/10 text-gray-300 hover:bg-white/10 hover:border-white/20\"),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"w-5 h-5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                            lineNumber: 529,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-left\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"font-semibold\",\n                                                                                    children: \"V.1 Classic\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                                    lineNumber: 531,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-xs opacity-80\",\n                                                                                    children: \"Original streaming system\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                                    lineNumber: 532,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                            lineNumber: 530,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                    lineNumber: 519,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: ()=>setConfig({\n                                                                            ...config,\n                                                                            version: 'v2'\n                                                                        }),\n                                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"px-6 py-4 rounded-xl border transition-all flex items-center space-x-3 relative\", config.version === 'v2' ? \"bg-violet-600/20 border-violet-500/50 text-violet-300\" : \"bg-white/5 border-white/10 text-gray-300 hover:bg-white/10 hover:border-white/20\"),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                            className: \"w-5 h-5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                            lineNumber: 545,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-left\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"font-semibold flex items-center space-x-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: \"V.2 Autonomous\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                                            lineNumber: 548,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"px-2 py-1 bg-violet-600/30 text-xs rounded-full\",\n                                                                                            children: \"BETA\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                                            lineNumber: 549,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                                    lineNumber: 547,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-xs opacity-80\",\n                                                                                    children: \"Kimi K2 + LangGraph + Tavily\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                                    lineNumber: 551,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                            lineNumber: 546,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                    lineNumber: 535,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                            lineNumber: 518,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 514,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-white/90\",\n                                                            children: [\n                                                                \"Topic \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-violet-400\",\n                                                                    children: \"*\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                    lineNumber: 560,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                            lineNumber: 559,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: config.topic,\n                                                            onChange: (e)=>setConfig({\n                                                                    ...config,\n                                                                    topic: e.target.value\n                                                                }),\n                                                            className: \"w-full px-4 py-4 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:bg-white/10 focus:border-violet-500/50 transition-all\",\n                                                            placeholder: \"e.g., The 5 Best CLI Agents of 2025\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                            lineNumber: 562,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 558,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-white/90\",\n                                                                    children: \"Target Audience\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                    lineNumber: 575,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: config.targetAudience,\n                                                                    onChange: (e)=>setConfig({\n                                                                            ...config,\n                                                                            targetAudience: e.target.value\n                                                                        }),\n                                                                    className: \"w-full px-4 py-4 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:bg-white/10 focus:border-violet-500/50 transition-all\",\n                                                                    placeholder: \"e.g., Developers and tech enthusiasts\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                    lineNumber: 578,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                            lineNumber: 574,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-white/90\",\n                                                                    children: \"Content Length (words)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                    lineNumber: 588,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: config.contentLength,\n                                                                    onChange: (e)=>setConfig({\n                                                                            ...config,\n                                                                            contentLength: parseInt(e.target.value) || 2000\n                                                                        }),\n                                                                    className: \"w-full px-4 py-4 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:bg-white/10 focus:border-violet-500/50 transition-all\",\n                                                                    min: \"500\",\n                                                                    max: \"5000\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                    lineNumber: 591,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                            lineNumber: 587,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 573,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-white/90\",\n                                                            children: \"Custom Instructions\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                            lineNumber: 604,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            value: config.customInstructions,\n                                                            onChange: (e)=>setConfig({\n                                                                    ...config,\n                                                                    customInstructions: e.target.value\n                                                                }),\n                                                            className: \"w-full px-4 py-4 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:bg-white/10 focus:border-violet-500/50 transition-all resize-none\",\n                                                            rows: 4,\n                                                            placeholder: \"e.g., Focus on practical value and real-world usage. Include specific examples and avoid generic advice.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                            lineNumber: 607,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 603,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-white/90\",\n                                                            children: \"Writing Tone\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                            lineNumber: 618,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-2 md:grid-cols-3 gap-3\",\n                                                            children: [\n                                                                'professional',\n                                                                'conversational',\n                                                                'casual',\n                                                                'authoritative',\n                                                                'friendly',\n                                                                'technical'\n                                                            ].map((tone)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: ()=>setConfig({\n                                                                            ...config,\n                                                                            tone\n                                                                        }),\n                                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"px-4 py-3 rounded-xl border transition-all capitalize\", config.tone === tone ? \"bg-violet-600/20 border-violet-500/50 text-violet-300\" : \"bg-white/5 border-white/10 text-gray-300 hover:bg-white/10 hover:border-white/20\"),\n                                                                    children: tone\n                                                                }, tone, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                    lineNumber: 623,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                            lineNumber: 621,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 617,\n                                                    columnNumber: 17\n                                                }, this),\n                                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                                    initial: {\n                                                        opacity: 0,\n                                                        scale: 0.9\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        scale: 1\n                                                    },\n                                                    className: \"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-red-700 dark:text-red-300 text-sm\",\n                                                        children: error\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 649,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 644,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                                    onClick: handleStartStreaming,\n                                                    whileHover: {\n                                                        scale: 1.02\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.98\n                                                    },\n                                                    className: \"w-full py-4 bg-gradient-to-r from-violet-600 via-purple-600 to-indigo-600 text-white rounded-xl font-semibold text-lg hover:shadow-2xl transition-all duration-300 flex items-center justify-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"w-6 h-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                            lineNumber: 660,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Start Live Generation\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                            lineNumber: 661,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-6 h-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                            lineNumber: 662,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 654,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                            lineNumber: 512,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                    lineNumber: 504,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                lineNumber: 498,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    delay: 0.2\n                                },\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-2 bg-gradient-to-r from-green-600 to-emerald-600 rounded-lg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"w-5 h-5 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                            lineNumber: 679,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 678,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-bold text-white\",\n                                                        children: \"What You'll See\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 681,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 677,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 686,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-white font-medium\",\n                                                                        children: \"Live Search Queries\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                        lineNumber: 688,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-400 text-sm\",\n                                                                        children: \"Watch as AI generates and executes targeted research queries\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                        lineNumber: 689,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 687,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 685,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 694,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-white font-medium\",\n                                                                        children: \"Real-time Analysis\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                        lineNumber: 696,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-400 text-sm\",\n                                                                        children: \"See competitor analysis and content understanding in action\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                        lineNumber: 697,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 695,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 693,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-purple-400 rounded-full mt-2 flex-shrink-0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 702,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-white font-medium\",\n                                                                        children: \"AI Humanization\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                        lineNumber: 704,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-400 text-sm\",\n                                                                        children: \"Watch date variation, jargon removal, and bypass techniques\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                        lineNumber: 705,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 703,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 701,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-orange-400 rounded-full mt-2 flex-shrink-0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 710,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-white font-medium\",\n                                                                        children: \"Content Generation\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                        lineNumber: 712,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-400 text-sm\",\n                                                                        children: \"See superior content creation with full context\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                        lineNumber: 713,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 711,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 709,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 684,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                        lineNumber: 676,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-2 bg-gradient-to-r from-blue-600 to-cyan-600 rounded-lg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"w-5 h-5 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                            lineNumber: 723,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 722,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-bold text-white\",\n                                                        children: \"Generation Stats\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 725,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 721,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-green-400\",\n                                                                children: \"10+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 730,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: \"Search Queries\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 731,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 729,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-blue-400\",\n                                                                children: \"15+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 734,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: \"Sources Analyzed\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 735,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 733,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-purple-400\",\n                                                                children: \"4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 738,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: \"Analysis Phases\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 739,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 737,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-orange-400\",\n                                                                children: \"8+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 742,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: \"AI Bypass Techniques\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 743,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 741,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 728,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                        lineNumber: 720,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-violet-900/20 to-indigo-900/20 border border-violet-500/20 rounded-2xl p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-6 h-6 text-yellow-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 751,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-bold text-white\",\n                                                        children: \"Enhanced Features\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 752,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 750,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"w-4 h-4 text-emerald-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 757,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-300\",\n                                                                children: \"Advanced AI Detection Bypass\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 758,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 756,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"w-4 h-4 text-purple-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 761,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-300\",\n                                                                children: \"Human Writing Pattern Analysis\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 762,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 760,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"w-4 h-4 text-blue-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 765,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-300\",\n                                                                children: \"Comprehensive Research\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 766,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 764,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                className: \"w-4 h-4 text-red-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 769,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-300\",\n                                                                children: \"Superior Competition Analysis\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 770,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 768,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 755,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                        lineNumber: 749,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                lineNumber: 669,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                        lineNumber: 496,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                lineNumber: 429,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n        lineNumber: 339,\n        columnNumber: 5\n    }, this);\n}\n_s(InvinciblePage, \"lxN+kII/J8YaFoGOOhNqeM0QhOg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        next_auth_react__WEBPACK_IMPORTED_MODULE_4__.useSession\n    ];\n});\n_c = InvinciblePage;\nvar _c;\n$RefreshReg$(_c, \"InvinciblePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvaW52aW5jaWJsZS9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFNEM7QUFDaUI7QUFDTDtBQUMzQjtBQUNnQjtBQTBCdkI7QUFDVztBQUNzQztBQWdCeEQsU0FBU3NCOztJQUN0QixNQUFNQyxTQUFTckIsMERBQVNBO0lBQ3hCLE1BQU1zQixlQUFlckIsZ0VBQWVBO0lBQ3BDLE1BQU0sRUFBRXNCLE1BQU1DLE9BQU8sRUFBRUMsTUFBTSxFQUFFLEdBQUdyQiwyREFBVUE7SUFDNUMsTUFBTSxDQUFDc0IsUUFBUUMsVUFBVSxHQUFHN0IsK0NBQVFBLENBQW1CO1FBQ3JEOEIsT0FBTztRQUNQQyxvQkFBb0I7UUFDcEJDLGdCQUFnQjtRQUNoQkMsZUFBZTtRQUNmQyxNQUFNO1FBQ05DLFVBQVUsRUFBRTtRQUNaQyxhQUFhO1FBQ2JDLGlCQUFpQjtRQUNqQkMsMkJBQTJCO1FBQzNCQyxpQkFBaUI7UUFDakJDLFNBQVM7SUFDWDtJQUNBLE1BQU0sQ0FBQ0MsY0FBY0MsZ0JBQWdCLEdBQUcxQywrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUMyQyxhQUFhQyxlQUFlLEdBQUc1QywrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUM2QyxpQkFBaUJDLG1CQUFtQixHQUFHOUMsK0NBQVFBLENBQUM7SUFDdkQsTUFBTSxDQUFDK0MsaUJBQWlCQyxtQkFBbUIsR0FBR2hELCtDQUFRQSxDQUFNO0lBQzVELE1BQU0sQ0FBQ2lELE9BQU9DLFNBQVMsR0FBR2xELCtDQUFRQSxDQUFDO0lBQ25DLE1BQU0sQ0FBQ21ELFVBQVVDLFlBQVksR0FBR3BELCtDQUFRQSxDQUFDO0lBRXpDLHNDQUFzQztJQUN0Q0MsZ0RBQVNBO29DQUFDO1lBQ1IsTUFBTTZCLFFBQVFOLGFBQWE2QixHQUFHLENBQUM7WUFDL0IsTUFBTXRCLHFCQUFxQlAsYUFBYTZCLEdBQUcsQ0FBQztZQUM1QyxNQUFNQyxTQUFTOUIsYUFBYTZCLEdBQUcsQ0FBQztZQUNoQyxNQUFNRSxXQUFXL0IsYUFBYTZCLEdBQUcsQ0FBQztZQUNsQyxNQUFNRyxhQUFhaEMsYUFBYTZCLEdBQUcsQ0FBQztZQUVwQyxJQUFJQyxXQUFXLGNBQWN4QixPQUFPO2dCQUNsQ0Q7Z0RBQVU0QixDQUFBQSxhQUFlOzRCQUN2QixHQUFHQSxVQUFVOzRCQUNiM0IsT0FBT0E7NEJBQ1BDLG9CQUFvQkEsc0JBQXNCMEIsV0FBVzFCLGtCQUFrQjs0QkFDdkUsZ0NBQWdDOzRCQUNoQyxHQUFJd0IsWUFBWTtnQ0FBRUcsZ0JBQWdCSDs0QkFBUyxDQUFDOzRCQUM1QyxHQUFJQyxjQUFjO2dDQUFFRyxrQkFBa0JIOzRCQUFXLENBQUM7d0JBQ3BEOztZQUNGO1FBQ0Y7bUNBQUc7UUFBQ2hDO0tBQWE7SUFFakIseUNBQXlDO0lBQ3pDdkIsZ0RBQVNBO29DQUFDO1lBQ1IsSUFBSTBCLFdBQVcsbUJBQW1CO2dCQUNoQ2lDLE9BQU9DLFFBQVEsQ0FBQ0MsSUFBSSxHQUFHO1lBQ3pCO1FBQ0Y7bUNBQUc7UUFBQ25DO0tBQU87SUFFWCxnQkFBZ0I7SUFDaEIsSUFBSUEsV0FBVyxXQUFXO1FBQ3hCLHFCQUNFLDhEQUFDb0M7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRDt3QkFBSUMsV0FBVTs7Ozs7O2tDQUNmLDhEQUFDQzt3QkFBRUQsV0FBVTtrQ0FBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBSXJDO0lBRUEsb0NBQW9DO0lBQ3BDLElBQUlyQyxXQUFXLG1CQUFtQjtRQUNoQyxPQUFPO0lBQ1Q7SUFFQSxNQUFNdUMsdUJBQXVCO1FBQzNCLElBQUksQ0FBQ3RDLE9BQU9FLEtBQUssQ0FBQ3FDLElBQUksSUFBSTtZQUN4QmpCLFNBQVM7WUFDVDtRQUNGO1FBRUFBLFNBQVM7UUFDVE4sZUFBZTtRQUVmLDJEQUEyRDtRQUMzREUsbUJBQW1CO0lBQ3JCO0lBRUEsTUFBTXNCLDBCQUEwQixPQUFPQztRQUNyQ3JCLG1CQUFtQnFCO1FBQ25CekIsZUFBZTtRQUNmUSxZQUFZO1FBRVosSUFBSTtnQkFnRzJDaUIsZUFDekJBLGtCQUFpQ0EsZ0JBQzlCQSxnQkFDa0JBLGlCQUNnQkEsa0JBQ2RBLG1CQUNoQkEsbUJBQ0FBO1lBdEczQkMsUUFBUUMsR0FBRyxDQUFDLDRCQUE0QjtnQkFDdENDLFdBQVcsQ0FBQyxDQUFDSCxPQUFPQSxNQUFNO2dCQUMxQkksWUFBWSxDQUFDLENBQUNKLE9BQU9LLE9BQU87Z0JBQzVCQyxZQUFZLENBQUMsQ0FBQ04sT0FBT08sT0FBTztnQkFDNUJDLFlBQVlDLE9BQU9DLElBQUksQ0FBQ1YsVUFBVSxDQUFDO2dCQUNuQ1csWUFBWSxPQUFPWCxPQUFPQSxNQUFNO2dCQUNoQ1ksZUFBZVosT0FBT0EsTUFBTSxHQUFHUyxPQUFPQyxJQUFJLENBQUNWLE9BQU9BLE1BQU0sSUFBSTtZQUM5RDtZQUVBLHlEQUF5RDtZQUN6RCxJQUFJYSxjQUFjO1lBQ2xCLElBQUlDLGVBQWU7WUFDbkIsSUFBSUMsaUJBQWlCO1lBRXJCLHFDQUFxQztZQUNyQyxJQUFJZixPQUFPQSxNQUFNLEVBQUU7Z0JBQ2pCLHFCQUFxQjtnQkFDckJhLGNBQWNiLE9BQU9BLE1BQU07Z0JBQzNCYyxlQUFlZCxPQUFPQSxNQUFNLENBQUNnQixLQUFLLElBQUk7Z0JBQ3RDRCxpQkFBaUJmLE9BQU9BLE1BQU0sQ0FBQ08sT0FBTyxJQUFJO2dCQUMxQ04sUUFBUUMsR0FBRyxDQUFDO1lBQ2QsT0FBTyxJQUFJRixPQUFPSyxPQUFPLEVBQUU7Z0JBQ3pCLG1DQUFtQztnQkFDbkNRLGNBQWNiLE9BQU9LLE9BQU87Z0JBQzVCUyxlQUFlZCxPQUFPSyxPQUFPLENBQUNXLEtBQUssSUFBSTtnQkFDdkNELGlCQUFpQmYsT0FBT0ssT0FBTyxDQUFDRSxPQUFPLElBQUk7Z0JBQzNDTixRQUFRQyxHQUFHLENBQUM7WUFDZCxPQUFPLElBQUlGLE9BQU9PLE9BQU8sRUFBRTtnQkFDekIsc0JBQXNCO2dCQUN0QlEsaUJBQWlCZixPQUFPTyxPQUFPO2dCQUMvQk8sZUFBZWQsT0FBT2dCLEtBQUssSUFBSSxpQkFBOEIsT0FBYnpELE9BQU9FLEtBQUs7Z0JBQzVEd0MsUUFBUUMsR0FBRyxDQUFDO1lBQ2QsT0FBTztnQkFDTCxtQ0FBbUM7Z0JBQ25DVyxjQUFjYjtnQkFDZGMsZUFBZWQsT0FBT2dCLEtBQUssSUFBSSxpQkFBOEIsT0FBYnpELE9BQU9FLEtBQUs7Z0JBQzVEc0QsaUJBQWlCZixPQUFPTyxPQUFPLElBQUk7Z0JBQ25DTixRQUFRQyxHQUFHLENBQUM7WUFDZDtZQUVBRCxRQUFRQyxHQUFHLENBQUMsOEJBQThCO2dCQUN4Q2UsZ0JBQWdCLENBQUMsQ0FBQ0o7Z0JBQ2xCRyxPQUFPRjtnQkFDUEksYUFBYUosQ0FBQUEseUJBQUFBLG1DQUFBQSxhQUFjSyxNQUFNLEtBQUk7Z0JBQ3JDdkQsZUFBZW1ELENBQUFBLDJCQUFBQSxxQ0FBQUEsZUFBZ0JJLE1BQU0sS0FBSTtnQkFDekNDLGdCQUFnQkwsQ0FBQUEsMkJBQUFBLHFDQUFBQSxlQUFnQk0sU0FBUyxDQUFDLEdBQUcsU0FBUTtnQkFDckRDLGFBQWEsT0FBT1A7WUFDdEI7WUFFQSxpQkFBaUI7WUFDakIsSUFBSSxDQUFDRCxnQkFBZ0JBLGFBQWFoQixJQUFJLEdBQUdxQixNQUFNLEtBQUssR0FBRztnQkFDckRMLGVBQWUscUJBQWtDLE9BQWJ2RCxPQUFPRSxLQUFLO2dCQUNoRHdDLFFBQVFDLEdBQUcsQ0FBQyw0QkFBNEJZO1lBQzFDO1lBRUEsaURBQWlEO1lBQ2pELElBQUksQ0FBQ0Msa0JBQWtCQSxlQUFlakIsSUFBSSxHQUFHcUIsTUFBTSxLQUFLLEdBQUc7Z0JBQ3pEbEIsUUFBUXJCLEtBQUssQ0FBQyxnQ0FBZ0M7b0JBQzVDMkMsZUFBZSxDQUFDLENBQUNSO29CQUNqQk8sYUFBYSxPQUFPUDtvQkFDcEJuRCxlQUFlbUQsQ0FBQUEsMkJBQUFBLHFDQUFBQSxlQUFnQkksTUFBTSxLQUFJO29CQUN6Q0ssWUFBWVQ7b0JBQ1pVLGlCQUFpQkMsS0FBS0MsU0FBUyxDQUFDM0IsUUFBUSxNQUFNLEdBQUdxQixTQUFTLENBQUMsR0FBRztvQkFDOURPLGVBQWVyRSxPQUFPWSxPQUFPO29CQUM3QjBELGdCQUFnQjtnQkFDbEI7Z0JBRUEsK0NBQStDO2dCQUMvQyxNQUFNQyxlQUFnQjtnQkFFdEIsTUFBTSxJQUFJQyxNQUFNRDtZQUNsQjtZQUVBLG1CQUFtQjtZQUNuQixJQUFJZixlQUFlakIsSUFBSSxHQUFHcUIsTUFBTSxHQUFHLElBQUk7Z0JBQ3JDLE1BQU0sSUFBSVksTUFBTSxtQ0FBeUQsT0FBdEJoQixlQUFlSSxNQUFNLEVBQUM7WUFDM0U7WUFFQSxtQ0FBbUM7WUFDbkMsTUFBTWEsV0FBVyxNQUFNQyxNQUFNLHVCQUF1QjtnQkFDbERDLFFBQVE7Z0JBQ1JDLFNBQVM7b0JBQ1AsZ0JBQWdCO2dCQUNsQjtnQkFDQUMsTUFBTVYsS0FBS0MsU0FBUyxDQUFDO29CQUNuQlgsT0FBT0Y7b0JBQ1BQLFNBQVNRO29CQUNUc0IsTUFBTTtvQkFDTkMsVUFBVTt3QkFDUjdFLE9BQU9GLE9BQU9FLEtBQUs7d0JBQ25CSSxNQUFNTixPQUFPTSxJQUFJO3dCQUNqQkYsZ0JBQWdCSixPQUFPSSxjQUFjO3dCQUNyQ0MsZUFBZUwsT0FBT0ssYUFBYTt3QkFDbkNGLG9CQUFvQkgsT0FBT0csa0JBQWtCO3dCQUM3Q0ksVUFBVVAsT0FBT08sUUFBUTt3QkFDekJ5RSxlQUFldkMsT0FBT3VDLGFBQWEsTUFBSXZDLGdCQUFBQSxPQUFPd0MsS0FBSyxjQUFaeEMsb0NBQUFBLGNBQWN1QyxhQUFhO3dCQUNsRUUsY0FBY3pDLEVBQUFBLG1CQUFBQSxPQUFPMEMsUUFBUSxjQUFmMUMsdUNBQUFBLGlCQUFpQnlDLFlBQVksT0FBSXpDLGlCQUFBQSxPQUFPd0MsS0FBSyxjQUFaeEMscUNBQUFBLGVBQWN5QyxZQUFZO3dCQUN6RUUsZUFBZSxHQUFFM0MsaUJBQUFBLE9BQU93QyxLQUFLLGNBQVp4QyxxQ0FBQUEsZUFBYzJDLGVBQWU7d0JBQzlDQyxVQUFVL0IsQ0FBQUEsd0JBQUFBLGtDQUFBQSxZQUFhK0IsUUFBUSxPQUFJNUMsa0JBQUFBLE9BQU9LLE9BQU8sY0FBZEwsc0NBQUFBLGdCQUFnQjRDLFFBQVE7d0JBQzNEQyxrQkFBa0JoQyxDQUFBQSx3QkFBQUEsa0NBQUFBLFlBQWFnQyxnQkFBZ0IsT0FBSTdDLG1CQUFBQSxPQUFPSyxPQUFPLGNBQWRMLHVDQUFBQSxpQkFBZ0I2QyxnQkFBZ0I7d0JBQ25GQyxjQUFjOUMsT0FBTzhDLFlBQVksTUFBSTlDLG9CQUFBQSxPQUFPMEMsUUFBUSxjQUFmMUMsd0NBQUFBLGtCQUFpQjhDLFlBQVk7d0JBQ2xFQyxtQkFBbUIsR0FBRS9DLG9CQUFBQSxPQUFPMEMsUUFBUSxjQUFmMUMsd0NBQUFBLGtCQUFpQitDLG1CQUFtQjt3QkFDekRDLG1CQUFtQixHQUFFaEQsb0JBQUFBLE9BQU8wQyxRQUFRLGNBQWYxQyx3Q0FBQUEsa0JBQWlCZ0QsbUJBQW1CO3dCQUN6REMsaUJBQWlCakQsT0FBT2lELGVBQWU7d0JBQ3ZDQyxhQUFhLElBQUlDLE9BQU9DLFdBQVc7b0JBQ3JDO29CQUNBdkYsTUFBTU4sT0FBT00sSUFBSTtvQkFDakJ3RixVQUFVO2dCQUNaO1lBQ0Y7WUFFQSxJQUFJLENBQUNyQixTQUFTc0IsRUFBRSxFQUFFO2dCQUNoQixNQUFNQyxZQUFZLE1BQU12QixTQUFTd0IsSUFBSTtnQkFDckMsTUFBTSxJQUFJekIsTUFBTSwyQkFBcUMsT0FBVndCO1lBQzdDO1lBRUEsTUFBTUUsYUFBYSxNQUFNekIsU0FBUzBCLElBQUk7WUFFdEMsSUFBSUQsV0FBV0UsT0FBTyxFQUFFO2dCQUN0Qix3Q0FBd0M7Z0JBQ3hDLE1BQU0sSUFBSUMsUUFBUUMsQ0FBQUEsVUFBV0MsV0FBV0QsU0FBUztnQkFFakQsOERBQThEO2dCQUM5RDNHLE9BQU82RyxJQUFJLENBQUMsaUJBQXVDLE9BQXRCTixXQUFXcEQsT0FBTyxDQUFDMkQsRUFBRTtZQUNwRCxPQUFPO2dCQUNMLE1BQU0sSUFBSWpDLE1BQU0wQixXQUFXN0UsS0FBSyxJQUFJO1lBQ3RDO1FBQ0YsRUFBRSxPQUFPQSxPQUFPO1lBQ2RxQixRQUFRckIsS0FBSyxDQUFDLHlCQUF5QkE7WUFDdkNHLFlBQVk7WUFDWkYsU0FBUyxzREFBK0csT0FBekRELGlCQUFpQm1ELFFBQVFuRCxNQUFNcUYsT0FBTyxHQUFHLGlCQUFnQjtRQUMxSDtJQUNGO0lBRUEsTUFBTUMsdUJBQXVCLENBQUN0RjtRQUM1QkMsU0FBU0Q7UUFDVEwsZUFBZTtJQUNqQjtJQUVBLE1BQU00RixhQUFhO1lBQ1c1RztRQUE1QixJQUFJYSxhQUFhMEIsSUFBSSxNQUFNLEdBQUN2QyxtQkFBQUEsT0FBT08sUUFBUSxjQUFmUCx1Q0FBQUEsaUJBQWlCNkcsUUFBUSxDQUFDaEcsYUFBYTBCLElBQUksTUFBSztZQUMxRXRDLFVBQVU7Z0JBQ1IsR0FBR0QsTUFBTTtnQkFDVE8sVUFBVTt1QkFBS1AsT0FBT08sUUFBUSxJQUFJLEVBQUU7b0JBQUdNLGFBQWEwQixJQUFJO2lCQUFHO1lBQzdEO1lBQ0F6QixnQkFBZ0I7UUFDbEI7SUFDRjtJQUVBLE1BQU1nRyxnQkFBZ0IsQ0FBQ0M7WUFHVC9HO1FBRlpDLFVBQVU7WUFDUixHQUFHRCxNQUFNO1lBQ1RPLFVBQVVQLEVBQUFBLG1CQUFBQSxPQUFPTyxRQUFRLGNBQWZQLHVDQUFBQSxpQkFBaUJnSCxNQUFNLENBQUNDLENBQUFBLElBQUtBLE1BQU1GLGFBQVksRUFBRTtRQUM3RDtJQUNGO0lBRUEsTUFBTUcsdUJBQXVCO1FBQzNCaEcsbUJBQW1CO1FBQ25CRixlQUFlO1FBQ2ZJLG1CQUFtQjtRQUNuQkUsU0FBUztRQUNURSxZQUFZO0lBQ2Q7SUFFQSxNQUFNMkYsdUJBQXVCO1FBQzNCeEgsT0FBTzZHLElBQUksQ0FBQztJQUNkO0lBRUEsNkVBQTZFO0lBQzdFLElBQUl2RixpQkFBaUI7UUFDbkIsSUFBSWpCLE9BQU9ZLE9BQU8sS0FBSyxNQUFNO1lBQzNCLHFCQUNFLDhEQUFDd0c7Z0JBQ0NsSCxPQUFPRixPQUFPRSxLQUFLO2dCQUNuQkcsZUFBZUwsT0FBT0ssYUFBYTtnQkFDbkNDLE1BQU1OLE9BQU9NLElBQUk7Z0JBQ2pCRixnQkFBZ0JKLE9BQU9JLGNBQWM7Z0JBQ3JDRCxvQkFBb0JILE9BQU9HLGtCQUFrQjtnQkFDN0M0RCxhQUFZO2dCQUNac0QsWUFBWTdFO2dCQUNaOEUsU0FBU1g7Z0JBQ1RwRixVQUFVQTs7Ozs7O1FBR2hCLE9BQU87WUFDTCxxQkFDRSw4REFBQzlCLHlFQUFxQkE7Z0JBQ3BCUyxPQUFPRixPQUFPRSxLQUFLO2dCQUNuQkcsZUFBZUwsT0FBT0ssYUFBYTtnQkFDbkNDLE1BQU1OLE9BQU9NLElBQUk7Z0JBQ2pCRixnQkFBZ0JKLE9BQU9JLGNBQWM7Z0JBQ3JDRCxvQkFBb0JILE9BQU9HLGtCQUFrQjtnQkFDN0NrSCxZQUFZN0U7Z0JBQ1o4RSxTQUFTWDtnQkFDVHBGLFVBQVVBOzs7Ozs7UUFHaEI7SUFDRjtJQUVBLHFCQUNFLDhEQUFDWTtRQUFJQyxXQUFVOzswQkFFYiw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRDt3QkFBSUMsV0FBVTs7Ozs7O2tDQUdmLDhEQUFDNUQsaURBQU1BLENBQUMyRCxHQUFHO3dCQUNUb0YsU0FBUzs0QkFDUEMsR0FBRztnQ0FBQztnQ0FBRztnQ0FBSzs2QkFBRTs0QkFDZEMsR0FBRztnQ0FBQztnQ0FBRyxDQUFDO2dDQUFLOzZCQUFFO3dCQUNqQjt3QkFDQUMsWUFBWTs0QkFDVkMsVUFBVTs0QkFDVkMsUUFBUUM7NEJBQ1JDLE1BQU07d0JBQ1I7d0JBQ0ExRixXQUFVOzs7Ozs7a0NBRVosOERBQUM1RCxpREFBTUEsQ0FBQzJELEdBQUc7d0JBQ1RvRixTQUFTOzRCQUNQQyxHQUFHO2dDQUFDO2dDQUFHLENBQUM7Z0NBQUs7NkJBQUU7NEJBQ2ZDLEdBQUc7Z0NBQUM7Z0NBQUc7Z0NBQUs7NkJBQUU7d0JBQ2hCO3dCQUNBQyxZQUFZOzRCQUNWQyxVQUFVOzRCQUNWQyxRQUFRQzs0QkFDUkMsTUFBTTt3QkFDUjt3QkFDQTFGLFdBQVU7Ozs7Ozs7Ozs7OzswQkFLZCw4REFBQzVELGlEQUFNQSxDQUFDdUosTUFBTTtnQkFDWkMsU0FBUztvQkFBRUMsU0FBUztvQkFBR1IsR0FBRyxDQUFDO2dCQUFHO2dCQUM5QkYsU0FBUztvQkFBRVUsU0FBUztvQkFBR1IsR0FBRztnQkFBRTtnQkFDNUJyRixXQUFVOzBCQUVWLDRFQUFDRDtvQkFBSUMsV0FBVTs4QkFDYiw0RUFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUMzRCxrREFBSUE7d0NBQ0h5RCxNQUFLO3dDQUNMRSxXQUFVOzswREFFViw4REFBQ3pELDZLQUFTQTtnREFBQ3lELFdBQVU7Ozs7OzswREFDckIsOERBQUM4RjtnREFBSzlGLFdBQVU7MERBQWM7Ozs7Ozs7Ozs7OztrREFHaEMsOERBQUNEO3dDQUFJQyxXQUFVOzs7Ozs7a0RBRWYsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDRDt3REFBSUMsV0FBVTs7Ozs7O2tFQUNmLDhEQUFDRDt3REFBSUMsV0FBVTtrRUFDWCw0RUFBQ2hELDZLQUFLQTs0REFBQ2dELFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7OzBEQUd2Qiw4REFBQ0Q7O2tFQUNDLDhEQUFDZ0c7d0RBQUcvRixXQUFVO2tFQUErQjs7Ozs7O2tFQUc3Qyw4REFBQ0M7d0RBQUVELFdBQVU7a0VBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBTzNDLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNqRCw4S0FBTUE7b0RBQUNpRCxXQUFVOzs7Ozs7OERBQ2xCLDhEQUFDOEY7OERBQUs7Ozs7Ozs7Ozs7OztzREFFUiw4REFBQy9GOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQzlDLDhLQUFPQTtvREFBQzhDLFdBQVU7Ozs7Ozs4REFDbkIsOERBQUM4Rjs4REFBSzs7Ozs7Ozs7Ozs7O3NEQUVSLDhEQUFDL0Y7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDdEQsOEtBQUdBO29EQUFDc0QsV0FBVTs7Ozs7OzhEQUNmLDhEQUFDOEY7OERBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFTbEIsOERBQUNFO2dCQUFLaEcsV0FBVTs7a0NBRWQsOERBQUM1RCxpREFBTUEsQ0FBQzJELEdBQUc7d0JBQ1Q2RixTQUFTOzRCQUFFQyxTQUFTOzRCQUFHUixHQUFHO3dCQUFHO3dCQUM3QkYsU0FBUzs0QkFBRVUsU0FBUzs0QkFBR1IsR0FBRzt3QkFBRTt3QkFDNUJyRixXQUFVOzswQ0FFViw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDN0MsOEtBQVFBO3dDQUFDNkMsV0FBVTs7Ozs7O2tEQUNwQiw4REFBQzhGO3dDQUFLOUYsV0FBVTtrREFBd0I7Ozs7OztrREFDeEMsOERBQUN4RCw4S0FBUUE7d0NBQUN3RCxXQUFVOzs7Ozs7a0RBQ3BCLDhEQUFDOEY7d0NBQUs5RixXQUFVO2tEQUF3Qjs7Ozs7O2tEQUN4Qyw4REFBQ2hELDZLQUFLQTt3Q0FBQ2dELFdBQVU7Ozs7Ozs7Ozs7OzswQ0FHbkIsOERBQUNpRztnQ0FBR2pHLFdBQVU7O29DQUErRDtrREFFM0UsOERBQUM4Rjt3Q0FBSzlGLFdBQVU7a0RBQW9HOzs7Ozs7Ozs7Ozs7MENBS3RILDhEQUFDQztnQ0FBRUQsV0FBVTswQ0FBK0Q7Ozs7OzswQ0FLNUUsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDtnREFBSUMsV0FBVTswREFDYiw0RUFBQzdDLDhLQUFRQTtvREFBQzZDLFdBQVU7Ozs7Ozs7Ozs7OzBEQUV0Qiw4REFBQ2tHO2dEQUFHbEcsV0FBVTswREFBZ0M7Ozs7OzswREFHOUMsOERBQUNDO2dEQUFFRCxXQUFVOzBEQUF3Qjs7Ozs7Ozs7Ozs7O2tEQUt2Qyw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDtnREFBSUMsV0FBVTswREFDYiw0RUFBQ2pELDhLQUFNQTtvREFBQ2lELFdBQVU7Ozs7Ozs7Ozs7OzBEQUVwQiw4REFBQ2tHO2dEQUFHbEcsV0FBVTswREFBZ0M7Ozs7OzswREFHOUMsOERBQUNDO2dEQUFFRCxXQUFVOzBEQUF3Qjs7Ozs7Ozs7Ozs7O2tEQUt2Qyw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDtnREFBSUMsV0FBVTswREFDYiw0RUFBQ2hELDZLQUFLQTtvREFBQ2dELFdBQVU7Ozs7Ozs7Ozs7OzBEQUVuQiw4REFBQ2tHO2dEQUFHbEcsV0FBVTswREFBZ0M7Ozs7OzswREFHOUMsOERBQUNDO2dEQUFFRCxXQUFVOzBEQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQVEzQyw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUViLDhEQUFDNUQsaURBQU1BLENBQUMyRCxHQUFHO2dDQUNUNkYsU0FBUztvQ0FBRUMsU0FBUztvQ0FBR1QsR0FBRyxDQUFDO2dDQUFHO2dDQUM5QkQsU0FBUztvQ0FBRVUsU0FBUztvQ0FBR1QsR0FBRztnQ0FBRTtnQ0FDNUJFLFlBQVk7b0NBQUVhLE9BQU87Z0NBQUk7Z0NBQ3pCbkcsV0FBVTswQ0FFViw0RUFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEO29EQUFJQyxXQUFVOzhEQUNiLDRFQUFDbEQsOEtBQVFBO3dEQUFDa0QsV0FBVTs7Ozs7Ozs7Ozs7OERBRXRCLDhEQUFDa0c7b0RBQUdsRyxXQUFVOzhEQUFnQzs7Ozs7Ozs7Ozs7O3NEQUdoRCw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUViLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNvRzs0REFBTXBHLFdBQVU7c0VBQTBDOzs7Ozs7c0VBRzNELDhEQUFDRDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUNxRztvRUFDQzNELE1BQUs7b0VBQ0w0RCxTQUFTLElBQU16SSxVQUFVOzRFQUFFLEdBQUdELE1BQU07NEVBQUVZLFNBQVM7d0VBQUs7b0VBQ3BEd0IsV0FBVzVDLDhDQUFFQSxDQUNYLDBFQUNBUSxPQUFPWSxPQUFPLEtBQUssT0FDZix1REFDQTs7c0ZBR04sOERBQUNyQiw4S0FBUUE7NEVBQUM2QyxXQUFVOzs7Ozs7c0ZBQ3BCLDhEQUFDRDs0RUFBSUMsV0FBVTs7OEZBQ2IsOERBQUNEO29GQUFJQyxXQUFVOzhGQUFnQjs7Ozs7OzhGQUMvQiw4REFBQ0Q7b0ZBQUlDLFdBQVU7OEZBQXFCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OEVBR3hDLDhEQUFDcUc7b0VBQ0MzRCxNQUFLO29FQUNMNEQsU0FBUyxJQUFNekksVUFBVTs0RUFBRSxHQUFHRCxNQUFNOzRFQUFFWSxTQUFTO3dFQUFLO29FQUNwRHdCLFdBQVc1Qyw4Q0FBRUEsQ0FDWCxtRkFDQVEsT0FBT1ksT0FBTyxLQUFLLE9BQ2YsMERBQ0E7O3NGQUdOLDhEQUFDeEIsNktBQUtBOzRFQUFDZ0QsV0FBVTs7Ozs7O3NGQUNqQiw4REFBQ0Q7NEVBQUlDLFdBQVU7OzhGQUNiLDhEQUFDRDtvRkFBSUMsV0FBVTs7c0dBQ2IsOERBQUM4RjtzR0FBSzs7Ozs7O3NHQUNOLDhEQUFDQTs0RkFBSzlGLFdBQVU7c0dBQWtEOzs7Ozs7Ozs7Ozs7OEZBRXBFLDhEQUFDRDtvRkFBSUMsV0FBVTs4RkFBcUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4REFPNUMsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ29HOzREQUFNcEcsV0FBVTs7Z0VBQTBDOzhFQUNuRCw4REFBQzhGO29FQUFLOUYsV0FBVTs4RUFBa0I7Ozs7Ozs7Ozs7OztzRUFFMUMsOERBQUN1Rzs0REFDQzdELE1BQUs7NERBQ0w4RCxPQUFPNUksT0FBT0UsS0FBSzs0REFDbkIySSxVQUFVLENBQUNDLElBQU03SSxVQUFVO29FQUFFLEdBQUdELE1BQU07b0VBQUVFLE9BQU80SSxFQUFFQyxNQUFNLENBQUNILEtBQUs7Z0VBQUM7NERBQzlEeEcsV0FBVTs0REFDVjRHLGFBQVk7Ozs7Ozs7Ozs7Ozs4REFNaEIsOERBQUM3RztvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ29HO29FQUFNcEcsV0FBVTs4RUFBMEM7Ozs7Ozs4RUFHM0QsOERBQUN1RztvRUFDQzdELE1BQUs7b0VBQ0w4RCxPQUFPNUksT0FBT0ksY0FBYztvRUFDNUJ5SSxVQUFVLENBQUNDLElBQU03SSxVQUFVOzRFQUFFLEdBQUdELE1BQU07NEVBQUVJLGdCQUFnQjBJLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSzt3RUFBQztvRUFDdkV4RyxXQUFVO29FQUNWNEcsYUFBWTs7Ozs7Ozs7Ozs7O3NFQUloQiw4REFBQzdHOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ29HO29FQUFNcEcsV0FBVTs4RUFBMEM7Ozs7Ozs4RUFHM0QsOERBQUN1RztvRUFDQzdELE1BQUs7b0VBQ0w4RCxPQUFPNUksT0FBT0ssYUFBYTtvRUFDM0J3SSxVQUFVLENBQUNDLElBQU03SSxVQUFVOzRFQUFFLEdBQUdELE1BQU07NEVBQUVLLGVBQWU0SSxTQUFTSCxFQUFFQyxNQUFNLENBQUNILEtBQUssS0FBSzt3RUFBSztvRUFDeEZ4RyxXQUFVO29FQUNWOEcsS0FBSTtvRUFDSkMsS0FBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhEQU1WLDhEQUFDaEg7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDb0c7NERBQU1wRyxXQUFVO3NFQUEwQzs7Ozs7O3NFQUczRCw4REFBQ2dIOzREQUNDUixPQUFPNUksT0FBT0csa0JBQWtCOzREQUNoQzBJLFVBQVUsQ0FBQ0MsSUFBTTdJLFVBQVU7b0VBQUUsR0FBR0QsTUFBTTtvRUFBRUcsb0JBQW9CMkksRUFBRUMsTUFBTSxDQUFDSCxLQUFLO2dFQUFDOzREQUMzRXhHLFdBQVU7NERBQ1ZpSCxNQUFNOzREQUNOTCxhQUFZOzs7Ozs7Ozs7Ozs7OERBS2hCLDhEQUFDN0c7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDb0c7NERBQU1wRyxXQUFVO3NFQUEwQzs7Ozs7O3NFQUczRCw4REFBQ0Q7NERBQUlDLFdBQVU7c0VBQ1o7Z0VBQUM7Z0VBQWdCO2dFQUFrQjtnRUFBVTtnRUFBaUI7Z0VBQVk7NkRBQVksQ0FBQ2tILEdBQUcsQ0FBQyxDQUFDaEoscUJBQzNGLDhEQUFDbUk7b0VBRUMzRCxNQUFLO29FQUNMNEQsU0FBUyxJQUFNekksVUFBVTs0RUFBRSxHQUFHRCxNQUFNOzRFQUFFTTt3RUFBSztvRUFDM0M4QixXQUFXNUMsOENBQUVBLENBQ1gseURBQ0FRLE9BQU9NLElBQUksS0FBS0EsT0FDWiwwREFDQTs4RUFHTEE7bUVBVklBOzs7Ozs7Ozs7Ozs7Ozs7O2dEQW1CVmUsdUJBQ0MsOERBQUM3QyxpREFBTUEsQ0FBQzJELEdBQUc7b0RBQ1g2RixTQUFTO3dEQUFFQyxTQUFTO3dEQUFHc0IsT0FBTztvREFBSTtvREFDbENoQyxTQUFTO3dEQUFFVSxTQUFTO3dEQUFHc0IsT0FBTztvREFBRTtvREFDaENuSCxXQUFVOzhEQUVWLDRFQUFDQzt3REFBRUQsV0FBVTtrRUFBMENmOzs7Ozs7Ozs7Ozs4REFLM0QsOERBQUM3QyxpREFBTUEsQ0FBQ2lLLE1BQU07b0RBQ1pDLFNBQVNwRztvREFDVGtILFlBQVk7d0RBQUVELE9BQU87b0RBQUs7b0RBQzFCRSxVQUFVO3dEQUFFRixPQUFPO29EQUFLO29EQUN4Qm5ILFdBQVU7O3NFQUVWLDhEQUFDN0MsOEtBQVFBOzREQUFDNkMsV0FBVTs7Ozs7O3NFQUNwQiw4REFBQzhGO3NFQUFLOzs7Ozs7c0VBQ04sOERBQUNqSiw4S0FBTUE7NERBQUNtRCxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FPMUIsOERBQUM1RCxpREFBTUEsQ0FBQzJELEdBQUc7Z0NBQ1Q2RixTQUFTO29DQUFFQyxTQUFTO29DQUFHVCxHQUFHO2dDQUFHO2dDQUM3QkQsU0FBUztvQ0FBRVUsU0FBUztvQ0FBR1QsR0FBRztnQ0FBRTtnQ0FDNUJFLFlBQVk7b0NBQUVhLE9BQU87Z0NBQUk7Z0NBQ3pCbkcsV0FBVTs7a0RBR1YsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDRDt3REFBSUMsV0FBVTtrRUFDYiw0RUFBQ3hELDhLQUFRQTs0REFBQ3dELFdBQVU7Ozs7Ozs7Ozs7O2tFQUV0Qiw4REFBQ2tHO3dEQUFHbEcsV0FBVTtrRUFBK0I7Ozs7Ozs7Ozs7OzswREFHL0MsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0Q7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDRDtnRUFBSUMsV0FBVTs7Ozs7OzBFQUNmLDhEQUFDRDs7a0ZBQ0MsOERBQUNFO3dFQUFFRCxXQUFVO2tGQUF5Qjs7Ozs7O2tGQUN0Qyw4REFBQ0M7d0VBQUVELFdBQVU7a0ZBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0VBSXpDLDhEQUFDRDt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNEO2dFQUFJQyxXQUFVOzs7Ozs7MEVBQ2YsOERBQUNEOztrRkFDQyw4REFBQ0U7d0VBQUVELFdBQVU7a0ZBQXlCOzs7Ozs7a0ZBQ3RDLDhEQUFDQzt3RUFBRUQsV0FBVTtrRkFBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7OztrRUFJekMsOERBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0Q7Z0VBQUlDLFdBQVU7Ozs7OzswRUFDZiw4REFBQ0Q7O2tGQUNDLDhEQUFDRTt3RUFBRUQsV0FBVTtrRkFBeUI7Ozs7OztrRkFDdEMsOERBQUNDO3dFQUFFRCxXQUFVO2tGQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tFQUl6Qyw4REFBQ0Q7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDRDtnRUFBSUMsV0FBVTs7Ozs7OzBFQUNmLDhEQUFDRDs7a0ZBQ0MsOERBQUNFO3dFQUFFRCxXQUFVO2tGQUF5Qjs7Ozs7O2tGQUN0Qyw4REFBQ0M7d0VBQUVELFdBQVU7a0ZBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBTzdDLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0Q7d0RBQUlDLFdBQVU7a0VBQ2IsNEVBQUMvQyw4S0FBUUE7NERBQUMrQyxXQUFVOzs7Ozs7Ozs7OztrRUFFdEIsOERBQUNrRzt3REFBR2xHLFdBQVU7a0VBQStCOzs7Ozs7Ozs7Ozs7MERBRy9DLDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0Q7Z0VBQUlDLFdBQVU7MEVBQW9DOzs7Ozs7MEVBQ25ELDhEQUFDRDtnRUFBSUMsV0FBVTswRUFBd0I7Ozs7Ozs7Ozs7OztrRUFFekMsOERBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0Q7Z0VBQUlDLFdBQVU7MEVBQW1DOzs7Ozs7MEVBQ2xELDhEQUFDRDtnRUFBSUMsV0FBVTswRUFBd0I7Ozs7Ozs7Ozs7OztrRUFFekMsOERBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0Q7Z0VBQUlDLFdBQVU7MEVBQXFDOzs7Ozs7MEVBQ3BELDhEQUFDRDtnRUFBSUMsV0FBVTswRUFBd0I7Ozs7Ozs7Ozs7OztrRUFFekMsOERBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0Q7Z0VBQUlDLFdBQVU7MEVBQXFDOzs7Ozs7MEVBQ3BELDhEQUFDRDtnRUFBSUMsV0FBVTswRUFBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFNN0MsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDaEQsNktBQUtBO3dEQUFDZ0QsV0FBVTs7Ozs7O2tFQUNqQiw4REFBQ2tHO3dEQUFHbEcsV0FBVTtrRUFBK0I7Ozs7Ozs7Ozs7OzswREFHL0MsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0Q7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDakQsOEtBQU1BO2dFQUFDaUQsV0FBVTs7Ozs7OzBFQUNsQiw4REFBQzhGO2dFQUFLOUYsV0FBVTswRUFBZ0I7Ozs7Ozs7Ozs7OztrRUFFbEMsOERBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ3ZELDhLQUFLQTtnRUFBQ3VELFdBQVU7Ozs7OzswRUFDakIsOERBQUM4RjtnRUFBSzlGLFdBQVU7MEVBQWdCOzs7Ozs7Ozs7Ozs7a0VBRWxDLDhEQUFDRDt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNwRCw4S0FBTUE7Z0VBQUNvRCxXQUFVOzs7Ozs7MEVBQ2xCLDhEQUFDOEY7Z0VBQUs5RixXQUFVOzBFQUFnQjs7Ozs7Ozs7Ozs7O2tFQUVsQyw4REFBQ0Q7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDckQsOEtBQU1BO2dFQUFDcUQsV0FBVTs7Ozs7OzBFQUNsQiw4REFBQzhGO2dFQUFLOUYsV0FBVTswRUFBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVNsRDtHQXh0QndCMUM7O1FBQ1BwQixzREFBU0E7UUFDSEMsNERBQWVBO1FBQ0ZHLHVEQUFVQTs7O0tBSHRCZ0IiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hYXl1c2htaXNocmEvRGVza3RvcC9vbGQgaW52aW5jaWJsZSB3aXRoIGRlZXByZXNlYXJjaC9zcmMvYXBwL2ludmluY2libGUvcGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlUm91dGVyLCB1c2VTZWFyY2hQYXJhbXMgfSBmcm9tICduZXh0L25hdmlnYXRpb24nO1xuaW1wb3J0IHsgbW90aW9uLCBBbmltYXRlUHJlc2VuY2UgfSBmcm9tICdmcmFtZXItbW90aW9uJztcbmltcG9ydCBMaW5rIGZyb20gJ25leHQvbGluayc7XG5pbXBvcnQgeyB1c2VTZXNzaW9uIH0gZnJvbSAnbmV4dC1hdXRoL3JlYWN0JztcbmltcG9ydCB7IFxuICBBcnJvd0xlZnQsIFxuICBTcGFya2xlcywgXG4gIEJyYWluLCBcbiAgWmFwLCBcbiAgVGFyZ2V0LCBcbiAgU2VhcmNoLCBcbiAgVHJlbmRpbmdVcCwgXG4gIEV5ZSwgXG4gIFJvY2tldCxcbiAgUGx1cyxcbiAgWCxcbiAgU2V0dGluZ3MsXG4gIFBsYXksXG4gIENoZXZyb25SaWdodCxcbiAgU2hpZWxkLFxuICBDcm93bixcbiAgTGlnaHRidWxiLFxuICBGaWxlVGV4dCxcbiAgVXNlcnMsXG4gIEdsb2JlLFxuICBDbG9jayxcbiAgQmFyQ2hhcnQsXG4gIE1vbml0b3IsXG4gIEFjdGl2aXR5XG59IGZyb20gJ2x1Y2lkZS1yZWFjdCc7XG5pbXBvcnQgeyBjbiB9IGZyb20gJ0AvbGliL3V0aWxzJztcbmltcG9ydCBJbnZpbmNpYmxlU3RyZWFtaW5nVUkgZnJvbSAnQC9jb21wb25lbnRzL0ludmluY2libGVTdHJlYW1pbmdVSSc7XG5cbmludGVyZmFjZSBJbnZpbmNpYmxlQ29uZmlnIHtcbiAgdG9waWM6IHN0cmluZztcbiAgY3VzdG9tSW5zdHJ1Y3Rpb25zPzogc3RyaW5nO1xuICB0YXJnZXRBdWRpZW5jZT86IHN0cmluZztcbiAgY29udGVudExlbmd0aD86IG51bWJlcjtcbiAgdG9uZT86IHN0cmluZztcbiAga2V5d29yZHM/OiBzdHJpbmdbXTtcbiAgc2VhcmNoRGVwdGg/OiBudW1iZXI7XG4gIGNvbXBldGl0b3JDb3VudD86IG51bWJlcjtcbiAgZGVlcFNlYXJjaFF1ZXJpZXNQZXJUb3BpYz86IG51bWJlcjtcbiAgdW5pcXVlbmVzc0xldmVsPzogJ3N0YW5kYXJkJyB8ICdoaWdoJyB8ICdtYXhpbXVtJztcbiAgdmVyc2lvbj86ICd2MScgfCAndjInO1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBJbnZpbmNpYmxlUGFnZSgpIHtcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XG4gIGNvbnN0IHNlYXJjaFBhcmFtcyA9IHVzZVNlYXJjaFBhcmFtcygpO1xuICBjb25zdCB7IGRhdGE6IHNlc3Npb24sIHN0YXR1cyB9ID0gdXNlU2Vzc2lvbigpO1xuICBjb25zdCBbY29uZmlnLCBzZXRDb25maWddID0gdXNlU3RhdGU8SW52aW5jaWJsZUNvbmZpZz4oe1xuICAgIHRvcGljOiAnJyxcbiAgICBjdXN0b21JbnN0cnVjdGlvbnM6ICcnLFxuICAgIHRhcmdldEF1ZGllbmNlOiAnJyxcbiAgICBjb250ZW50TGVuZ3RoOiAyMDAwLFxuICAgIHRvbmU6ICdwcm9mZXNzaW9uYWwnLFxuICAgIGtleXdvcmRzOiBbXSxcbiAgICBzZWFyY2hEZXB0aDogNyxcbiAgICBjb21wZXRpdG9yQ291bnQ6IDUsXG4gICAgZGVlcFNlYXJjaFF1ZXJpZXNQZXJUb3BpYzogNyxcbiAgICB1bmlxdWVuZXNzTGV2ZWw6ICdoaWdoJyxcbiAgICB2ZXJzaW9uOiAndjInLFxuICB9KTtcbiAgY29uc3QgW2tleXdvcmRJbnB1dCwgc2V0S2V5d29yZElucHV0XSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW2lzU3RyZWFtaW5nLCBzZXRJc1N0cmVhbWluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtzaG93U3RyZWFtaW5nVUksIHNldFNob3dTdHJlYW1pbmdVSV0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtzdHJlYW1pbmdSZXN1bHQsIHNldFN0cmVhbWluZ1Jlc3VsdF0gPSB1c2VTdGF0ZTxhbnk+KG51bGwpO1xuICBjb25zdCBbZXJyb3IsIHNldEVycm9yXSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW2lzU2F2aW5nLCBzZXRJc1NhdmluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgLy8gSGFuZGxlIFVSTCBwYXJhbWV0ZXJzIGZyb20gTWVnYXRyb25cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCB0b3BpYyA9IHNlYXJjaFBhcmFtcy5nZXQoJ3RvcGljJyk7XG4gICAgY29uc3QgY3VzdG9tSW5zdHJ1Y3Rpb25zID0gc2VhcmNoUGFyYW1zLmdldCgnY3VzdG9tSW5zdHJ1Y3Rpb25zJyk7XG4gICAgY29uc3Qgc291cmNlID0gc2VhcmNoUGFyYW1zLmdldCgnc291cmNlJyk7XG4gICAgY29uc3QgdmlkZW9VcmwgPSBzZWFyY2hQYXJhbXMuZ2V0KCd2aWRlb1VybCcpO1xuICAgIGNvbnN0IHZpZGVvVGl0bGUgPSBzZWFyY2hQYXJhbXMuZ2V0KCd2aWRlb1RpdGxlJyk7XG5cbiAgICBpZiAoc291cmNlID09PSAnbWVnYXRyb24nICYmIHRvcGljKSB7XG4gICAgICBzZXRDb25maWcocHJldkNvbmZpZyA9PiAoe1xuICAgICAgICAuLi5wcmV2Q29uZmlnLFxuICAgICAgICB0b3BpYzogdG9waWMsXG4gICAgICAgIGN1c3RvbUluc3RydWN0aW9uczogY3VzdG9tSW5zdHJ1Y3Rpb25zIHx8IHByZXZDb25maWcuY3VzdG9tSW5zdHJ1Y3Rpb25zLFxuICAgICAgICAvLyBBZGQgbWV0YWRhdGEgYWJvdXQgdGhlIHNvdXJjZVxuICAgICAgICAuLi4odmlkZW9VcmwgJiYgeyBzb3VyY2VWaWRlb1VybDogdmlkZW9VcmwgfSksXG4gICAgICAgIC4uLih2aWRlb1RpdGxlICYmIHsgc291cmNlVmlkZW9UaXRsZTogdmlkZW9UaXRsZSB9KVxuICAgICAgfSkpO1xuICAgIH1cbiAgfSwgW3NlYXJjaFBhcmFtc10pO1xuXG4gIC8vIFJlZGlyZWN0IHRvIGxvZ2luIGlmIG5vdCBhdXRoZW50aWNhdGVkXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHN0YXR1cyA9PT0gJ3VuYXV0aGVudGljYXRlZCcpIHtcbiAgICAgIHdpbmRvdy5sb2NhdGlvbi5ocmVmID0gJy9sb2dpbidcbiAgICB9XG4gIH0sIFtzdGF0dXNdKVxuXG4gIC8vIExvYWRpbmcgc3RhdGVcbiAgaWYgKHN0YXR1cyA9PT0gJ2xvYWRpbmcnKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWJsYWNrIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgc3BhY2UteS00XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gdy0xMiBoLTEyIGJvcmRlci0yIGJvcmRlci12aW9sZXQtNDAwIGJvcmRlci10LXRyYW5zcGFyZW50IHJvdW5kZWQtZnVsbCBteC1hdXRvXCI+PC9kaXY+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMFwiPkxvYWRpbmcuLi48L3A+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKVxuICB9XG5cbiAgLy8gRG9uJ3QgcmVuZGVyIGlmIG5vdCBhdXRoZW50aWNhdGVkXG4gIGlmIChzdGF0dXMgPT09ICd1bmF1dGhlbnRpY2F0ZWQnKSB7XG4gICAgcmV0dXJuIG51bGxcbiAgfVxuXG4gIGNvbnN0IGhhbmRsZVN0YXJ0U3RyZWFtaW5nID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmICghY29uZmlnLnRvcGljLnRyaW0oKSkge1xuICAgICAgc2V0RXJyb3IoJ1BsZWFzZSBlbnRlciBhIHRvcGljJyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuICAgIFxuICAgIHNldEVycm9yKCcnKTtcbiAgICBzZXRJc1N0cmVhbWluZyh0cnVlKTtcbiAgICBcbiAgICAvLyBGb3IgYWxsIG1vZGVzIGluY2x1ZGluZyBhdXRvbm9tb3VzLCB1c2UgdGhlIHN0cmVhbWluZyBVSVxuICAgIHNldFNob3dTdHJlYW1pbmdVSSh0cnVlKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVTdHJlYW1pbmdDb21wbGV0ZSA9IGFzeW5jIChyZXN1bHQ6IGFueSkgPT4ge1xuICAgIHNldFN0cmVhbWluZ1Jlc3VsdChyZXN1bHQpO1xuICAgIHNldElzU3RyZWFtaW5nKGZhbHNlKTtcbiAgICBzZXRJc1NhdmluZyh0cnVlKTtcblxuICAgIHRyeSB7XG4gICAgICBjb25zb2xlLmxvZygn8J+UjSBSYXcgcmVzdWx0IHN0cnVjdHVyZTonLCB7XG4gICAgICAgIGhhc1Jlc3VsdDogISFyZXN1bHQucmVzdWx0LFxuICAgICAgICBoYXNBcnRpY2xlOiAhIXJlc3VsdC5hcnRpY2xlLFxuICAgICAgICBoYXNDb250ZW50OiAhIXJlc3VsdC5jb250ZW50LFxuICAgICAgICByZXN1bHRLZXlzOiBPYmplY3Qua2V5cyhyZXN1bHQgfHwge30pLFxuICAgICAgICByZXN1bHRUeXBlOiB0eXBlb2YgcmVzdWx0LnJlc3VsdCxcbiAgICAgICAgcmVzdWx0Q29udGVudDogcmVzdWx0LnJlc3VsdCA/IE9iamVjdC5rZXlzKHJlc3VsdC5yZXN1bHQpIDogJ04vQScsXG4gICAgICB9KTtcblxuICAgICAgLy8gRXh0cmFjdCBhcnRpY2xlIGRhdGEgd2l0aCBjb21wcmVoZW5zaXZlIGZhbGxiYWNrIGxvZ2ljXG4gICAgICBsZXQgYXJ0aWNsZURhdGEgPSBudWxsO1xuICAgICAgbGV0IGFydGljbGVUaXRsZSA9ICcnO1xuICAgICAgbGV0IGFydGljbGVDb250ZW50ID0gJyc7XG5cbiAgICAgIC8vIEhhbmRsZSBkaWZmZXJlbnQgcmVzdWx0IHN0cnVjdHVyZXNcbiAgICAgIGlmIChyZXN1bHQucmVzdWx0KSB7XG4gICAgICAgIC8vIHJlc3VsdC5yZXN1bHQgcGF0aFxuICAgICAgICBhcnRpY2xlRGF0YSA9IHJlc3VsdC5yZXN1bHQ7XG4gICAgICAgIGFydGljbGVUaXRsZSA9IHJlc3VsdC5yZXN1bHQudGl0bGUgfHwgJyc7XG4gICAgICAgIGFydGljbGVDb250ZW50ID0gcmVzdWx0LnJlc3VsdC5jb250ZW50IHx8ICcnO1xuICAgICAgICBjb25zb2xlLmxvZygn8J+ThCBVc2luZyByZXN1bHQucmVzdWx0IHBhdGgnKTtcbiAgICAgIH0gZWxzZSBpZiAocmVzdWx0LmFydGljbGUpIHtcbiAgICAgICAgLy8gQWx0ZXJuYXRpdmU6IHJlc3VsdC5hcnRpY2xlIHBhdGhcbiAgICAgICAgYXJ0aWNsZURhdGEgPSByZXN1bHQuYXJ0aWNsZTtcbiAgICAgICAgYXJ0aWNsZVRpdGxlID0gcmVzdWx0LmFydGljbGUudGl0bGUgfHwgJyc7XG4gICAgICAgIGFydGljbGVDb250ZW50ID0gcmVzdWx0LmFydGljbGUuY29udGVudCB8fCAnJztcbiAgICAgICAgY29uc29sZS5sb2coJ/Cfk4QgVXNpbmcgcmVzdWx0LmFydGljbGUgcGF0aCcpO1xuICAgICAgfSBlbHNlIGlmIChyZXN1bHQuY29udGVudCkge1xuICAgICAgICAvLyBEaXJlY3QgY29udGVudCBwYXRoXG4gICAgICAgIGFydGljbGVDb250ZW50ID0gcmVzdWx0LmNvbnRlbnQ7XG4gICAgICAgIGFydGljbGVUaXRsZSA9IHJlc3VsdC50aXRsZSB8fCBgQXJ0aWNsZSBhYm91dCAke2NvbmZpZy50b3BpY31gO1xuICAgICAgICBjb25zb2xlLmxvZygn8J+ThCBVc2luZyByZXN1bHQuY29udGVudCBwYXRoJyk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICAvLyBMYXN0IHJlc29ydDogdXNlIHJlc3VsdCBkaXJlY3RseVxuICAgICAgICBhcnRpY2xlRGF0YSA9IHJlc3VsdDtcbiAgICAgICAgYXJ0aWNsZVRpdGxlID0gcmVzdWx0LnRpdGxlIHx8IGBBcnRpY2xlIGFib3V0ICR7Y29uZmlnLnRvcGljfWA7XG4gICAgICAgIGFydGljbGVDb250ZW50ID0gcmVzdWx0LmNvbnRlbnQgfHwgJyc7XG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5OEIFVzaW5nIHJlc3VsdCBkaXJlY3QgcGF0aCcpO1xuICAgICAgfVxuXG4gICAgICBjb25zb2xlLmxvZygn8J+TiiBFeHRyYWN0ZWQgYXJ0aWNsZSBkYXRhOicsIHtcbiAgICAgICAgaGFzQXJ0aWNsZURhdGE6ICEhYXJ0aWNsZURhdGEsXG4gICAgICAgIHRpdGxlOiBhcnRpY2xlVGl0bGUsXG4gICAgICAgIHRpdGxlTGVuZ3RoOiBhcnRpY2xlVGl0bGU/Lmxlbmd0aCB8fCAwLFxuICAgICAgICBjb250ZW50TGVuZ3RoOiBhcnRpY2xlQ29udGVudD8ubGVuZ3RoIHx8IDAsXG4gICAgICAgIGNvbnRlbnRQcmV2aWV3OiBhcnRpY2xlQ29udGVudD8uc3Vic3RyaW5nKDAsIDIwMCkgfHwgJ0VNUFRZJyxcbiAgICAgICAgY29udGVudFR5cGU6IHR5cGVvZiBhcnRpY2xlQ29udGVudFxuICAgICAgfSk7XG5cbiAgICAgIC8vIFZhbGlkYXRlIHRpdGxlXG4gICAgICBpZiAoIWFydGljbGVUaXRsZSB8fCBhcnRpY2xlVGl0bGUudHJpbSgpLmxlbmd0aCA9PT0gMCkge1xuICAgICAgICBhcnRpY2xlVGl0bGUgPSBgQ29tcGxldGUgR3VpZGUgdG8gJHtjb25maWcudG9waWN9YDtcbiAgICAgICAgY29uc29sZS5sb2coJ+KaoO+4jyBVc2luZyBmYWxsYmFjayB0aXRsZTonLCBhcnRpY2xlVGl0bGUpO1xuICAgICAgfVxuXG4gICAgICAvLyBWYWxpZGF0ZSBjb250ZW50IHdpdGggZGV0YWlsZWQgZXJyb3IgcmVwb3J0aW5nXG4gICAgICBpZiAoIWFydGljbGVDb250ZW50IHx8IGFydGljbGVDb250ZW50LnRyaW0oKS5sZW5ndGggPT09IDApIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIENvbnRlbnQgdmFsaWRhdGlvbiBmYWlsZWQ6Jywge1xuICAgICAgICAgIGNvbnRlbnRFeGlzdHM6ICEhYXJ0aWNsZUNvbnRlbnQsXG4gICAgICAgICAgY29udGVudFR5cGU6IHR5cGVvZiBhcnRpY2xlQ29udGVudCxcbiAgICAgICAgICBjb250ZW50TGVuZ3RoOiBhcnRpY2xlQ29udGVudD8ubGVuZ3RoIHx8IDAsXG4gICAgICAgICAgcmF3Q29udGVudDogYXJ0aWNsZUNvbnRlbnQsXG4gICAgICAgICAgcmVzdWx0U3RydWN0dXJlOiBKU09OLnN0cmluZ2lmeShyZXN1bHQsIG51bGwsIDIpLnN1YnN0cmluZygwLCAxMDAwKSxcbiAgICAgICAgICBjb25maWdWZXJzaW9uOiBjb25maWcudmVyc2lvbixcbiAgICAgICAgICBleHRyYWN0aW9uUGF0aDogJ2ZhbGxiYWNrIHBhdGhzJ1xuICAgICAgICB9KTtcblxuICAgICAgICAvLyBFcnJvciBtZXNzYWdlIGZvciBjb250ZW50IGdlbmVyYXRpb24gZmFpbHVyZVxuICAgICAgICBjb25zdCBlcnJvck1lc3NhZ2UgPSBgQ29udGVudCBnZW5lcmF0aW9uIGZhaWxlZCAtIG5vIGNvbnRlbnQgd2FzIGdlbmVyYXRlZC4gVGhpcyBtaWdodCBiZSBkdWUgdG8gQVBJIGlzc3VlcyBvciBjb250ZW50IGZpbHRlcmluZy4gUGxlYXNlIHRyeSBhZ2FpbiBvciBjb250YWN0IHN1cHBvcnQgaWYgdGhlIGlzc3VlIHBlcnNpc3RzLmA7XG5cbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGVycm9yTWVzc2FnZSk7XG4gICAgICB9XG5cbiAgICAgIC8vIEZpbmFsIHZhbGlkYXRpb25cbiAgICAgIGlmIChhcnRpY2xlQ29udGVudC50cmltKCkubGVuZ3RoIDwgNTApIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBHZW5lcmF0ZWQgY29udGVudCBpcyB0b28gc2hvcnQgKCR7YXJ0aWNsZUNvbnRlbnQubGVuZ3RofSBjaGFyYWN0ZXJzKS4gUGxlYXNlIHRyeSBhZ2Fpbi5gKTtcbiAgICAgIH1cblxuICAgICAgLy8gU2F2ZSB0aGUgYXJ0aWNsZSB0byB0aGUgZGF0YWJhc2VcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvYXJ0aWNsZXMvc3RvcmUnLCB7XG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfSxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoe1xuICAgICAgICAgIHRpdGxlOiBhcnRpY2xlVGl0bGUsXG4gICAgICAgICAgY29udGVudDogYXJ0aWNsZUNvbnRlbnQsXG4gICAgICAgICAgdHlwZTogJ2ludmluY2libGUnLFxuICAgICAgICAgIG1ldGFkYXRhOiB7XG4gICAgICAgICAgICB0b3BpYzogY29uZmlnLnRvcGljLFxuICAgICAgICAgICAgdG9uZTogY29uZmlnLnRvbmUsXG4gICAgICAgICAgICB0YXJnZXRBdWRpZW5jZTogY29uZmlnLnRhcmdldEF1ZGllbmNlLFxuICAgICAgICAgICAgY29udGVudExlbmd0aDogY29uZmlnLmNvbnRlbnRMZW5ndGgsXG4gICAgICAgICAgICBjdXN0b21JbnN0cnVjdGlvbnM6IGNvbmZpZy5jdXN0b21JbnN0cnVjdGlvbnMsXG4gICAgICAgICAgICBrZXl3b3JkczogY29uZmlnLmtleXdvcmRzLFxuICAgICAgICAgICAgZXhlY3V0aW9uVGltZTogcmVzdWx0LmV4ZWN1dGlvblRpbWUgfHwgcmVzdWx0LnN0YXRzPy5leGVjdXRpb25UaW1lLFxuICAgICAgICAgICAgdG90YWxTb3VyY2VzOiByZXN1bHQuaW5zaWdodHM/LnRvdGFsU291cmNlcyB8fCByZXN1bHQuc3RhdHM/LnRvdGFsU291cmNlcyxcbiAgICAgICAgICAgIHVuaXF1ZW5lc3NTY29yZTogcmVzdWx0LnN0YXRzPy51bmlxdWVuZXNzU2NvcmUsXG4gICAgICAgICAgICBzZW9TY29yZTogYXJ0aWNsZURhdGE/LnNlb1Njb3JlIHx8IHJlc3VsdC5hcnRpY2xlPy5zZW9TY29yZSxcbiAgICAgICAgICAgIHJlYWRhYmlsaXR5U2NvcmU6IGFydGljbGVEYXRhPy5yZWFkYWJpbGl0eVNjb3JlIHx8IHJlc3VsdC5hcnRpY2xlPy5yZWFkYWJpbGl0eVNjb3JlLFxuICAgICAgICAgICAgcXVhbGl0eVNjb3JlOiByZXN1bHQucXVhbGl0eVNjb3JlIHx8IHJlc3VsdC5pbnNpZ2h0cz8ucXVhbGl0eVNjb3JlLFxuICAgICAgICAgICAgY29tcGV0aXRvcnNBbmFseXplZDogcmVzdWx0Lmluc2lnaHRzPy5jb21wZXRpdG9yc0FuYWx5emVkLFxuICAgICAgICAgICAgaXRlcmF0aW9uc0NvbXBsZXRlZDogcmVzdWx0Lmluc2lnaHRzPy5pdGVyYXRpb25zQ29tcGxldGVkLFxuICAgICAgICAgICAgZmFjdENoZWNrUmVwb3J0OiByZXN1bHQuZmFjdENoZWNrUmVwb3J0LFxuICAgICAgICAgICAgZ2VuZXJhdGVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgICAgIH0sXG4gICAgICAgICAgdG9uZTogY29uZmlnLnRvbmUsXG4gICAgICAgICAgbGFuZ3VhZ2U6ICdlbidcbiAgICAgICAgfSlcbiAgICAgIH0pO1xuXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIGNvbnN0IGVycm9yVGV4dCA9IGF3YWl0IHJlc3BvbnNlLnRleHQoKTtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gc2F2ZSBhcnRpY2xlOiAke2Vycm9yVGV4dH1gKTtcbiAgICAgIH1cblxuICAgICAgY29uc3Qgc2F2ZVJlc3VsdCA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgIFxuICAgICAgaWYgKHNhdmVSZXN1bHQuc3VjY2Vzcykge1xuICAgICAgICAvLyBTbWFsbCBkZWxheSB0byBzaG93IHNhdmluZyBjb21wbGV0aW9uXG4gICAgICAgIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCAxMDAwKSk7XG4gICAgICAgIFxuICAgICAgICAvLyBSZWRpcmVjdCB0byB0aGUgYXJ0aWNsZSB2aWV3IHBhZ2Ugd2l0aCB0aGUgc2F2ZWQgYXJ0aWNsZSBJRFxuICAgICAgICByb3V0ZXIucHVzaChgL2FydGljbGUtdmlldy8ke3NhdmVSZXN1bHQuYXJ0aWNsZS5pZH1gKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihzYXZlUmVzdWx0LmVycm9yIHx8ICdGYWlsZWQgdG8gc2F2ZSBhcnRpY2xlJyk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHNhdmluZyBhcnRpY2xlOicsIGVycm9yKTtcbiAgICAgIHNldElzU2F2aW5nKGZhbHNlKTtcbiAgICAgIHNldEVycm9yKGBBcnRpY2xlIGdlbmVyYXRlZCBzdWNjZXNzZnVsbHkgYnV0IGZhaWxlZCB0byBzYXZlOiAke2Vycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ1Vua25vd24gZXJyb3InfS4gUGxlYXNlIHRyeSBhZ2Fpbi5gKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlU3RyZWFtaW5nRXJyb3IgPSAoZXJyb3I6IHN0cmluZykgPT4ge1xuICAgIHNldEVycm9yKGVycm9yKTtcbiAgICBzZXRJc1N0cmVhbWluZyhmYWxzZSk7XG4gIH07XG5cbiAgY29uc3QgYWRkS2V5d29yZCA9ICgpID0+IHtcbiAgICBpZiAoa2V5d29yZElucHV0LnRyaW0oKSAmJiAhY29uZmlnLmtleXdvcmRzPy5pbmNsdWRlcyhrZXl3b3JkSW5wdXQudHJpbSgpKSkge1xuICAgICAgc2V0Q29uZmlnKHtcbiAgICAgICAgLi4uY29uZmlnLFxuICAgICAgICBrZXl3b3JkczogWy4uLihjb25maWcua2V5d29yZHMgfHwgW10pLCBrZXl3b3JkSW5wdXQudHJpbSgpXVxuICAgICAgfSk7XG4gICAgICBzZXRLZXl3b3JkSW5wdXQoJycpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCByZW1vdmVLZXl3b3JkID0gKGtleXdvcmQ6IHN0cmluZykgPT4ge1xuICAgIHNldENvbmZpZyh7XG4gICAgICAuLi5jb25maWcsXG4gICAgICBrZXl3b3JkczogY29uZmlnLmtleXdvcmRzPy5maWx0ZXIoayA9PiBrICE9PSBrZXl3b3JkKSB8fCBbXVxuICAgIH0pO1xuICB9O1xuXG4gIGNvbnN0IHJlc2V0VG9Db25maWd1cmF0aW9uID0gKCkgPT4ge1xuICAgIHNldFNob3dTdHJlYW1pbmdVSShmYWxzZSk7XG4gICAgc2V0SXNTdHJlYW1pbmcoZmFsc2UpO1xuICAgIHNldFN0cmVhbWluZ1Jlc3VsdChudWxsKTtcbiAgICBzZXRFcnJvcignJyk7XG4gICAgc2V0SXNTYXZpbmcoZmFsc2UpO1xuICB9O1xuXG4gIGNvbnN0IHZpZXdHZW5lcmF0ZWRBcnRpY2xlID0gKCkgPT4ge1xuICAgIHJvdXRlci5wdXNoKCcvYXJ0aWNsZS12aWV3Jyk7XG4gIH07XG5cbiAgLy8gSWYgc2hvd2luZyBzdHJlYW1pbmcgVUksIHJlbmRlciB0aGUgYXBwcm9wcmlhdGUgY29tcG9uZW50IGJhc2VkIG9uIHZlcnNpb25cbiAgaWYgKHNob3dTdHJlYW1pbmdVSSkge1xuICAgIGlmIChjb25maWcudmVyc2lvbiA9PT0gJ3YyJykge1xuICAgICAgcmV0dXJuIChcbiAgICAgICAgPEludmluY2libGVWMlN0cmVhbWluZ1VJXG4gICAgICAgICAgdG9waWM9e2NvbmZpZy50b3BpY31cbiAgICAgICAgICBjb250ZW50TGVuZ3RoPXtjb25maWcuY29udGVudExlbmd0aH1cbiAgICAgICAgICB0b25lPXtjb25maWcudG9uZX1cbiAgICAgICAgICB0YXJnZXRBdWRpZW5jZT17Y29uZmlnLnRhcmdldEF1ZGllbmNlfVxuICAgICAgICAgIGN1c3RvbUluc3RydWN0aW9ucz17Y29uZmlnLmN1c3RvbUluc3RydWN0aW9uc31cbiAgICAgICAgICBjb250ZW50VHlwZT1cImFydGljbGVcIlxuICAgICAgICAgIG9uQ29tcGxldGU9e2hhbmRsZVN0cmVhbWluZ0NvbXBsZXRlfVxuICAgICAgICAgIG9uRXJyb3I9e2hhbmRsZVN0cmVhbWluZ0Vycm9yfVxuICAgICAgICAgIGlzU2F2aW5nPXtpc1NhdmluZ31cbiAgICAgICAgLz5cbiAgICAgICk7XG4gICAgfSBlbHNlIHtcbiAgICAgIHJldHVybiAoXG4gICAgICAgIDxJbnZpbmNpYmxlU3RyZWFtaW5nVUlcbiAgICAgICAgICB0b3BpYz17Y29uZmlnLnRvcGljfVxuICAgICAgICAgIGNvbnRlbnRMZW5ndGg9e2NvbmZpZy5jb250ZW50TGVuZ3RofVxuICAgICAgICAgIHRvbmU9e2NvbmZpZy50b25lfVxuICAgICAgICAgIHRhcmdldEF1ZGllbmNlPXtjb25maWcudGFyZ2V0QXVkaWVuY2V9XG4gICAgICAgICAgY3VzdG9tSW5zdHJ1Y3Rpb25zPXtjb25maWcuY3VzdG9tSW5zdHJ1Y3Rpb25zfVxuICAgICAgICAgIG9uQ29tcGxldGU9e2hhbmRsZVN0cmVhbWluZ0NvbXBsZXRlfVxuICAgICAgICAgIG9uRXJyb3I9e2hhbmRsZVN0cmVhbWluZ0Vycm9yfVxuICAgICAgICAgIGlzU2F2aW5nPXtpc1NhdmluZ31cbiAgICAgICAgLz5cbiAgICAgICk7XG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ibGFja1wiPlxuICAgICAgey8qIEFuaW1hdGVkIEJhY2tncm91bmQgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZpeGVkIGluc2V0LTAgei0wXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBiZy1ncmFkaWVudC10by1iciBmcm9tLXZpb2xldC05MDAvMTAgdmlhLWJsYWNrIHRvLWluZGlnby05MDAvMTBcIiAvPlxuICAgICAgICBcbiAgICAgICAgey8qIEFuaW1hdGVkIG9yYnMgKi99XG4gICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgYW5pbWF0ZT17e1xuICAgICAgICAgICAgeDogWzAsIDEwMCwgMF0sXG4gICAgICAgICAgICB5OiBbMCwgLTEwMCwgMF0sXG4gICAgICAgICAgfX1cbiAgICAgICAgICB0cmFuc2l0aW9uPXt7XG4gICAgICAgICAgICBkdXJhdGlvbjogMjAsXG4gICAgICAgICAgICByZXBlYXQ6IEluZmluaXR5LFxuICAgICAgICAgICAgZWFzZTogXCJsaW5lYXJcIlxuICAgICAgICAgIH19XG4gICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTEvNCBsZWZ0LTEvNCB3LVs1MDBweF0gaC1bNTAwcHhdIGJnLXZpb2xldC01MDAvMTAgcm91bmRlZC1mdWxsIGJsdXItWzEwMHB4XVwiXG4gICAgICAgIC8+XG4gICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgYW5pbWF0ZT17e1xuICAgICAgICAgICAgeDogWzAsIC0xMDAsIDBdLFxuICAgICAgICAgICAgeTogWzAsIDEwMCwgMF0sXG4gICAgICAgICAgfX1cbiAgICAgICAgICB0cmFuc2l0aW9uPXt7XG4gICAgICAgICAgICBkdXJhdGlvbjogMTUsXG4gICAgICAgICAgICByZXBlYXQ6IEluZmluaXR5LFxuICAgICAgICAgICAgZWFzZTogXCJsaW5lYXJcIlxuICAgICAgICAgIH19XG4gICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgYm90dG9tLTEvNCByaWdodC0xLzQgdy1bNjAwcHhdIGgtWzYwMHB4XSBiZy1pbmRpZ28tNTAwLzEwIHJvdW5kZWQtZnVsbCBibHVyLVsxMjBweF1cIlxuICAgICAgICAvPlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICA8bW90aW9uLmhlYWRlciBcbiAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAtMjAgfX1cbiAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICAgIGNsYXNzTmFtZT1cInJlbGF0aXZlIHotMTAgYm9yZGVyLWIgYm9yZGVyLXdoaXRlLzEwIGJhY2tkcm9wLWJsdXIteGwgYmctYmxhY2svNDBcIlxuICAgICAgPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvIHB4LTYgcHktNlwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNlwiPlxuICAgICAgICAgICAgICA8TGluayBcbiAgICAgICAgICAgICAgICBocmVmPVwiL2Rhc2hib2FyZFwiIFxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMyB0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtd2hpdGUgdHJhbnNpdGlvbi1jb2xvcnMgZ3JvdXBcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPEFycm93TGVmdCBjbGFzc05hbWU9XCJ3LTUgaC01IGdyb3VwLWhvdmVyOi10cmFuc2xhdGUteC0xIHRyYW5zaXRpb24tdHJhbnNmb3JtXCIgLz5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPkJhY2sgdG8gRGFzaGJvYXJkPC9zcGFuPlxuICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtNiB3LXB4IGJnLXdoaXRlLzIwXCIgLz5cbiAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIHJvdW5kZWQteGwgYmx1ci1sZyBvcGFjaXR5LTcwIGJnLWdyYWRpZW50LXRvLXIgZnJvbS12aW9sZXQtNjAwIHRvLWluZGlnby02MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBiZy1ibGFjayByb3VuZGVkLXhsIHAtMi41IGJvcmRlciBib3JkZXItd2hpdGUvMjBcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8Q3Jvd24gY2xhc3NOYW1lPVwidy02IGgtNiB0ZXh0LXdoaXRlXCIgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LWJvbGQgdGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgICAgICAgICBJbnZpbmNpYmxlXG4gICAgICAgICAgICAgICAgICA8L2gxPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNDAwXCI+XG4gICAgICAgICAgICAgICAgICAgIEFkdmFuY2VkIEFJIENvbnRlbnQgR2VuZXJhdGlvblxuICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImhpZGRlbiBtZDpmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTYgdGV4dC1zbSB0ZXh0LWdyYXktNDAwXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgIDxTaGllbGQgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LWVtZXJhbGQtNDAwXCIgLz5cbiAgICAgICAgICAgICAgICAgIDxzcGFuPkFJIERldGVjdGlvbiBCeXBhc3M8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgIDxNb25pdG9yIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1ibHVlLTQwMFwiIC8+XG4gICAgICAgICAgICAgICAgICA8c3Bhbj5MaXZlIFN0cmVhbWluZzwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgICAgPFphcCBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQteWVsbG93LTQwMFwiIC8+XG4gICAgICAgICAgICAgICAgICA8c3Bhbj5SZWFsLXRpbWUgQW5hbHlzaXM8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9tb3Rpb24uaGVhZGVyPlxuXG4gICAgICB7LyogTWFpbiBDb250ZW50ICovfVxuICAgICAgPG1haW4gY2xhc3NOYW1lPVwicmVsYXRpdmUgei0xMCBtYXgtdy02eGwgbXgtYXV0byBweC02IHB5LTEyXCI+XG4gICAgICAgIHsvKiBIZXJvIFNlY3Rpb24gKi99XG4gICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAyMCB9fVxuICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxuICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1iLTE2XCJcbiAgICAgICAgPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBweC00IHB5LTIgcm91bmRlZC1mdWxsIGJnLXdoaXRlLzEwIGJhY2tkcm9wLWJsdXItbWQgYm9yZGVyIGJvcmRlci13aGl0ZS8yMCBtYi04XCI+XG4gICAgICAgICAgICA8QWN0aXZpdHkgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LWdyZWVuLTQwMFwiIC8+XG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS0yMDBcIj5MaXZlIFN0cmVhbWluZyBHZW5lcmF0aW9uPC9zcGFuPlxuICAgICAgICAgICAgPFNwYXJrbGVzIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC12aW9sZXQtNDAwXCIgLz5cbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTIwMFwiPkFJIERldGVjdGlvbiBCeXBhc3M8L3NwYW4+XG4gICAgICAgICAgICA8Q3Jvd24gY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LXllbGxvdy00MDBcIiAvPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIFxuICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTV4bCBtZDp0ZXh0LTZ4bCBmb250LWJvbGQgdGV4dC13aGl0ZSBtYi02IGxlYWRpbmctdGlnaHRcIj5cbiAgICAgICAgICAgIFdhdGNoIFlvdXIgQ29udGVudFxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiYmxvY2sgYmctZ3JhZGllbnQtdG8tciBmcm9tLXZpb2xldC00MDAgdmlhLXB1cnBsZS00MDAgdG8taW5kaWdvLTQwMCBiZy1jbGlwLXRleHQgdGV4dC10cmFuc3BhcmVudFwiPlxuICAgICAgICAgICAgICBDb21lIHRvIExpZmVcbiAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICA8L2gyPlxuICAgICAgICAgIFxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteGwgdGV4dC1ncmF5LTMwMCBtYXgtdy0zeGwgbXgtYXV0byBtYi04IGxlYWRpbmctcmVsYXhlZFwiPlxuICAgICAgICAgICAgRXhwZXJpZW5jZSB0aGUgbW9zdCBhZHZhbmNlZCBjb250ZW50IGdlbmVyYXRpb24gd2l0aCByZWFsLXRpbWUgc3RyZWFtaW5nLiBXYXRjaCBhcyBvdXIgQUkgYW5hbHl6ZXMgY29tcGV0aXRvcnMsIFxuICAgICAgICAgICAgcHJvY2Vzc2VzIHJlc2VhcmNoLCBhcHBsaWVzIGh1bWFuaXphdGlvbiB0ZWNobmlxdWVzLCBhbmQgY3JlYXRlcyBzdXBlcmlvciBhcnRpY2xlcyB0aGF0IGJ5cGFzcyBBSSBkZXRlY3Rpb24uXG4gICAgICAgICAgPC9wPlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0zIGdhcC02IG1heC13LTR4bCBteC1hdXRvIG10LTEyXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlLzUgYmFja2Ryb3AtYmx1ci1zbSBib3JkZXIgYm9yZGVyLXdoaXRlLzEwIHJvdW5kZWQteGwgcC02XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMiBoLTEyIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1ncmVlbi01MDAgdG8tZW1lcmFsZC01MDAgcm91bmRlZC1sZyBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBtYi00IG14LWF1dG9cIj5cbiAgICAgICAgICAgICAgICA8QWN0aXZpdHkgY2xhc3NOYW1lPVwidy02IGgtNiB0ZXh0LXdoaXRlXCIgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtd2hpdGUgbWItMlwiPlxuICAgICAgICAgICAgICAgIExpdmUgU3RyZWFtaW5nXG4gICAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTQwMFwiPlxuICAgICAgICAgICAgICAgIFdhdGNoIHF1ZXJpZXMsIGFuYWx5c2lzLCBhbmQgZ2VuZXJhdGlvbiBpbiByZWFsLXRpbWVcbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUvNSBiYWNrZHJvcC1ibHVyLXNtIGJvcmRlciBib3JkZXItd2hpdGUvMTAgcm91bmRlZC14bCBwLTZcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTEyIGgtMTIgYmctZ3JhZGllbnQtdG8tciBmcm9tLWJsdWUtNTAwIHRvLWN5YW4tNTAwIHJvdW5kZWQtbGcgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbWItNCBteC1hdXRvXCI+XG4gICAgICAgICAgICAgICAgPFNoaWVsZCBjbGFzc05hbWU9XCJ3LTYgaC02IHRleHQtd2hpdGVcIiAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZSBtYi0yXCI+XG4gICAgICAgICAgICAgICAgQUkgRGV0ZWN0aW9uIEJ5cGFzc1xuICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS00MDBcIj5cbiAgICAgICAgICAgICAgICBBZHZhbmNlZCBodW1hbml6YXRpb24gd2l0aCBkYXRlIHZhcmlhdGlvbiBhbmQgamFyZ29uIHJlbW92YWxcbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUvNSBiYWNrZHJvcC1ibHVyLXNtIGJvcmRlciBib3JkZXItd2hpdGUvMTAgcm91bmRlZC14bCBwLTZcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTEyIGgtMTIgYmctZ3JhZGllbnQtdG8tciBmcm9tLXB1cnBsZS01MDAgdG8tcGluay01MDAgcm91bmRlZC1sZyBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBtYi00IG14LWF1dG9cIj5cbiAgICAgICAgICAgICAgICA8Q3Jvd24gY2xhc3NOYW1lPVwidy02IGgtNiB0ZXh0LXdoaXRlXCIgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtd2hpdGUgbWItMlwiPlxuICAgICAgICAgICAgICAgIFN1cGVyaW9yIFF1YWxpdHlcbiAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNDAwXCI+XG4gICAgICAgICAgICAgICAgQ29udGVudCB0aGF0IHN1cnBhc3NlcyBhbGwgY29tcGV0aXRvcnMgd2l0aCBjb21wcmVoZW5zaXZlIHJlc2VhcmNoXG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L21vdGlvbi5kaXY+XG5cbiAgICAgICAgey8qIE1haW4gQ29uZmlndXJhdGlvbiBGb3JtICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbGc6Z3JpZC1jb2xzLTMgZ2FwLThcIj5cbiAgICAgICAgICB7LyogQ29uZmlndXJhdGlvbiBGb3JtICovfVxuICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHg6IC0yMCB9fVxuICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB4OiAwIH19XG4gICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGRlbGF5OiAwLjEgfX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImxnOmNvbC1zcGFuLTJcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUvNSBiYWNrZHJvcC1ibHVyLXhsIGJvcmRlciBib3JkZXItd2hpdGUvMTAgcm91bmRlZC0yeGwgcC04XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zIG1iLThcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtMiBiZy1ncmFkaWVudC10by1yIGZyb20tdmlvbGV0LTYwMCB0by1pbmRpZ28tNjAwIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgICAgIDxTZXR0aW5ncyBjbGFzc05hbWU9XCJ3LTUgaC01IHRleHQtd2hpdGVcIiAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC13aGl0ZVwiPkNvbmZpZ3VyYXRpb248L2gzPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktOFwiPlxuICAgICAgICAgICAgICAgIHsvKiBWZXJzaW9uIFNlbGVjdG9yICovfVxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXdoaXRlLzkwXCI+XG4gICAgICAgICAgICAgICAgICAgIEludmluY2libGUgVmVyc2lvblxuICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMiBnYXAtNFwiPlxuICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0Q29uZmlnKHsgLi4uY29uZmlnLCB2ZXJzaW9uOiAndjEnIH0pfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgICAgICAgICAgICAgICBcInB4LTYgcHktNCByb3VuZGVkLXhsIGJvcmRlciB0cmFuc2l0aW9uLWFsbCBmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIixcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbmZpZy52ZXJzaW9uID09PSAndjEnXG4gICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJiZy1ncmVlbi02MDAvMjAgYm9yZGVyLWdyZWVuLTUwMC81MCB0ZXh0LWdyZWVuLTMwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDogXCJiZy13aGl0ZS81IGJvcmRlci13aGl0ZS8xMCB0ZXh0LWdyYXktMzAwIGhvdmVyOmJnLXdoaXRlLzEwIGhvdmVyOmJvcmRlci13aGl0ZS8yMFwiXG4gICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIDxBY3Rpdml0eSBjbGFzc05hbWU9XCJ3LTUgaC01XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtbGVmdFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkXCI+Vi4xIENsYXNzaWM8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyBvcGFjaXR5LTgwXCI+T3JpZ2luYWwgc3RyZWFtaW5nIHN5c3RlbTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldENvbmZpZyh7IC4uLmNvbmZpZywgdmVyc2lvbjogJ3YyJyB9KX1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgICAgICAgICAgICAgICAgXCJweC02IHB5LTQgcm91bmRlZC14bCBib3JkZXIgdHJhbnNpdGlvbi1hbGwgZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zIHJlbGF0aXZlXCIsXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25maWcudmVyc2lvbiA9PT0gJ3YyJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwiYmctdmlvbGV0LTYwMC8yMCBib3JkZXItdmlvbGV0LTUwMC81MCB0ZXh0LXZpb2xldC0zMDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICA6IFwiYmctd2hpdGUvNSBib3JkZXItd2hpdGUvMTAgdGV4dC1ncmF5LTMwMCBob3ZlcjpiZy13aGl0ZS8xMCBob3Zlcjpib3JkZXItd2hpdGUvMjBcIlxuICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICA8Q3Jvd24gY2xhc3NOYW1lPVwidy01IGgtNVwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWxlZnRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCBmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+Vi4yIEF1dG9ub21vdXM8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInB4LTIgcHktMSBiZy12aW9sZXQtNjAwLzMwIHRleHQteHMgcm91bmRlZC1mdWxsXCI+QkVUQTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIG9wYWNpdHktODBcIj5LaW1pIEsyICsgTGFuZ0dyYXBoICsgVGF2aWx5PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICB7LyogVG9waWMgKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtd2hpdGUvOTBcIj5cbiAgICAgICAgICAgICAgICAgICAgVG9waWMgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC12aW9sZXQtNDAwXCI+Kjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Y29uZmlnLnRvcGljfVxuICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldENvbmZpZyh7IC4uLmNvbmZpZywgdG9waWM6IGUudGFyZ2V0LnZhbHVlIH0pfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtNCBweS00IGJnLXdoaXRlLzUgYm9yZGVyIGJvcmRlci13aGl0ZS8xMCByb3VuZGVkLXhsIHRleHQtd2hpdGUgcGxhY2Vob2xkZXItZ3JheS00MDAgZm9jdXM6Ymctd2hpdGUvMTAgZm9jdXM6Ym9yZGVyLXZpb2xldC01MDAvNTAgdHJhbnNpdGlvbi1hbGxcIlxuICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cImUuZy4sIFRoZSA1IEJlc3QgQ0xJIEFnZW50cyBvZiAyMDI1XCJcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cblxuICAgICAgICAgICAgICAgIHsvKiBUYXJnZXQgQXVkaWVuY2UgJiBDb250ZW50IExlbmd0aCAqL31cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTZcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtd2hpdGUvOTBcIj5cbiAgICAgICAgICAgICAgICAgICAgICBUYXJnZXQgQXVkaWVuY2VcbiAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtjb25maWcudGFyZ2V0QXVkaWVuY2V9XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRDb25maWcoeyAuLi5jb25maWcsIHRhcmdldEF1ZGllbmNlOiBlLnRhcmdldC52YWx1ZSB9KX1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtNCBweS00IGJnLXdoaXRlLzUgYm9yZGVyIGJvcmRlci13aGl0ZS8xMCByb3VuZGVkLXhsIHRleHQtd2hpdGUgcGxhY2Vob2xkZXItZ3JheS00MDAgZm9jdXM6Ymctd2hpdGUvMTAgZm9jdXM6Ym9yZGVyLXZpb2xldC01MDAvNTAgdHJhbnNpdGlvbi1hbGxcIlxuICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiZS5nLiwgRGV2ZWxvcGVycyBhbmQgdGVjaCBlbnRodXNpYXN0c1wiXG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC13aGl0ZS85MFwiPlxuICAgICAgICAgICAgICAgICAgICAgIENvbnRlbnQgTGVuZ3RoICh3b3JkcylcbiAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgdHlwZT1cIm51bWJlclwiXG4gICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2NvbmZpZy5jb250ZW50TGVuZ3RofVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Q29uZmlnKHsgLi4uY29uZmlnLCBjb250ZW50TGVuZ3RoOiBwYXJzZUludChlLnRhcmdldC52YWx1ZSkgfHwgMjAwMCB9KX1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtNCBweS00IGJnLXdoaXRlLzUgYm9yZGVyIGJvcmRlci13aGl0ZS8xMCByb3VuZGVkLXhsIHRleHQtd2hpdGUgcGxhY2Vob2xkZXItZ3JheS00MDAgZm9jdXM6Ymctd2hpdGUvMTAgZm9jdXM6Ym9yZGVyLXZpb2xldC01MDAvNTAgdHJhbnNpdGlvbi1hbGxcIlxuICAgICAgICAgICAgICAgICAgICAgIG1pbj1cIjUwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgbWF4PVwiNTAwMFwiXG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIHsvKiBDdXN0b20gSW5zdHJ1Y3Rpb25zICovfVxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXdoaXRlLzkwXCI+XG4gICAgICAgICAgICAgICAgICAgIEN1c3RvbSBJbnN0cnVjdGlvbnNcbiAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICA8dGV4dGFyZWFcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2NvbmZpZy5jdXN0b21JbnN0cnVjdGlvbnN9XG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Q29uZmlnKHsgLi4uY29uZmlnLCBjdXN0b21JbnN0cnVjdGlvbnM6IGUudGFyZ2V0LnZhbHVlIH0pfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtNCBweS00IGJnLXdoaXRlLzUgYm9yZGVyIGJvcmRlci13aGl0ZS8xMCByb3VuZGVkLXhsIHRleHQtd2hpdGUgcGxhY2Vob2xkZXItZ3JheS00MDAgZm9jdXM6Ymctd2hpdGUvMTAgZm9jdXM6Ym9yZGVyLXZpb2xldC01MDAvNTAgdHJhbnNpdGlvbi1hbGwgcmVzaXplLW5vbmVcIlxuICAgICAgICAgICAgICAgICAgICByb3dzPXs0fVxuICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cImUuZy4sIEZvY3VzIG9uIHByYWN0aWNhbCB2YWx1ZSBhbmQgcmVhbC13b3JsZCB1c2FnZS4gSW5jbHVkZSBzcGVjaWZpYyBleGFtcGxlcyBhbmQgYXZvaWQgZ2VuZXJpYyBhZHZpY2UuXCJcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICB7LyogVG9uZSBTZWxlY3Rpb24gKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtd2hpdGUvOTBcIj5cbiAgICAgICAgICAgICAgICAgICAgV3JpdGluZyBUb25lXG4gICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0yIG1kOmdyaWQtY29scy0zIGdhcC0zXCI+XG4gICAgICAgICAgICAgICAgICAgIHtbJ3Byb2Zlc3Npb25hbCcsICdjb252ZXJzYXRpb25hbCcsICdjYXN1YWwnLCAnYXV0aG9yaXRhdGl2ZScsICdmcmllbmRseScsICd0ZWNobmljYWwnXS5tYXAoKHRvbmUpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICBrZXk9e3RvbmV9XG4gICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldENvbmZpZyh7IC4uLmNvbmZpZywgdG9uZSB9KX1cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgICAgICAgICAgICAgICAgIFwicHgtNCBweS0zIHJvdW5kZWQteGwgYm9yZGVyIHRyYW5zaXRpb24tYWxsIGNhcGl0YWxpemVcIixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY29uZmlnLnRvbmUgPT09IHRvbmVcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwiYmctdmlvbGV0LTYwMC8yMCBib3JkZXItdmlvbGV0LTUwMC81MCB0ZXh0LXZpb2xldC0zMDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogXCJiZy13aGl0ZS81IGJvcmRlci13aGl0ZS8xMCB0ZXh0LWdyYXktMzAwIGhvdmVyOmJnLXdoaXRlLzEwIGhvdmVyOmJvcmRlci13aGl0ZS8yMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIHt0b25lfVxuICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG5cblxuICAgICAgICAgICAgICAgIHsvKiBFcnJvciBEaXNwbGF5ICovfVxuICAgICAgICAgICAgICAgICAge2Vycm9yICYmIChcbiAgICAgICAgICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCBzY2FsZTogMC45IH19XG4gICAgICAgICAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgc2NhbGU6IDEgfX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctcmVkLTUwIGRhcms6YmctcmVkLTkwMC8yMCBib3JkZXIgYm9yZGVyLXJlZC0yMDAgZGFyazpib3JkZXItcmVkLTgwMCByb3VuZGVkLWxnIHAtNFwiXG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1yZWQtNzAwIGRhcms6dGV4dC1yZWQtMzAwIHRleHQtc21cIj57ZXJyb3J9PC9wPlxuICAgICAgICAgICAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgICAgey8qIFN0YXJ0IEdlbmVyYXRpb24gQnV0dG9uICovfVxuICAgICAgICAgICAgICAgIDxtb3Rpb24uYnV0dG9uXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVTdGFydFN0cmVhbWluZ31cbiAgICAgICAgICAgICAgICAgIHdoaWxlSG92ZXI9e3sgc2NhbGU6IDEuMDIgfX1cbiAgICAgICAgICAgICAgICAgIHdoaWxlVGFwPXt7IHNjYWxlOiAwLjk4IH19XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHktNCBiZy1ncmFkaWVudC10by1yIGZyb20tdmlvbGV0LTYwMCB2aWEtcHVycGxlLTYwMCB0by1pbmRpZ28tNjAwIHRleHQtd2hpdGUgcm91bmRlZC14bCBmb250LXNlbWlib2xkIHRleHQtbGcgaG92ZXI6c2hhZG93LTJ4bCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgc3BhY2UteC0zXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8QWN0aXZpdHkgY2xhc3NOYW1lPVwidy02IGgtNlwiIC8+XG4gICAgICAgICAgICAgICAgICA8c3Bhbj5TdGFydCBMaXZlIEdlbmVyYXRpb248L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8Um9ja2V0IGNsYXNzTmFtZT1cInctNiBoLTZcIiAvPlxuICAgICAgICAgICAgICAgIDwvbW90aW9uLmJ1dHRvbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L21vdGlvbi5kaXY+XG5cbiAgICAgICAgICB7LyogTGl2ZSBQcmV2aWV3IFBhbmVsICovfVxuICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHg6IDIwIH19XG4gICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHg6IDAgfX1cbiAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZGVsYXk6IDAuMiB9fVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwic3BhY2UteS02XCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICB7LyogRmVhdHVyZXMgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlLzUgYmFja2Ryb3AtYmx1ci14bCBib3JkZXIgYm9yZGVyLXdoaXRlLzEwIHJvdW5kZWQtMnhsIHAtNlwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMyBtYi02XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTIgYmctZ3JhZGllbnQtdG8tciBmcm9tLWdyZWVuLTYwMCB0by1lbWVyYWxkLTYwMCByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgICAgICA8U3BhcmtsZXMgY2xhc3NOYW1lPVwidy01IGgtNSB0ZXh0LXdoaXRlXCIgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LWJvbGQgdGV4dC13aGl0ZVwiPldoYXQgWW91J2xsIFNlZTwvaDM+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTIgaC0yIGJnLWdyZWVuLTQwMCByb3VuZGVkLWZ1bGwgbXQtMiBmbGV4LXNocmluay0wXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtbWVkaXVtXCI+TGl2ZSBTZWFyY2ggUXVlcmllczwvcD5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCB0ZXh0LXNtXCI+V2F0Y2ggYXMgQUkgZ2VuZXJhdGVzIGFuZCBleGVjdXRlcyB0YXJnZXRlZCByZXNlYXJjaCBxdWVyaWVzPC9wPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMiBoLTIgYmctYmx1ZS00MDAgcm91bmRlZC1mdWxsIG10LTIgZmxleC1zaHJpbmstMFwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBmb250LW1lZGl1bVwiPlJlYWwtdGltZSBBbmFseXNpczwvcD5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCB0ZXh0LXNtXCI+U2VlIGNvbXBldGl0b3IgYW5hbHlzaXMgYW5kIGNvbnRlbnQgdW5kZXJzdGFuZGluZyBpbiBhY3Rpb248L3A+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMiBoLTIgYmctcHVycGxlLTQwMCByb3VuZGVkLWZ1bGwgbXQtMiBmbGV4LXNocmluay0wXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtbWVkaXVtXCI+QUkgSHVtYW5pemF0aW9uPC9wPlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIHRleHQtc21cIj5XYXRjaCBkYXRlIHZhcmlhdGlvbiwgamFyZ29uIHJlbW92YWwsIGFuZCBieXBhc3MgdGVjaG5pcXVlczwvcD5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTIgaC0yIGJnLW9yYW5nZS00MDAgcm91bmRlZC1mdWxsIG10LTIgZmxleC1zaHJpbmstMFwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBmb250LW1lZGl1bVwiPkNvbnRlbnQgR2VuZXJhdGlvbjwvcD5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCB0ZXh0LXNtXCI+U2VlIHN1cGVyaW9yIGNvbnRlbnQgY3JlYXRpb24gd2l0aCBmdWxsIGNvbnRleHQ8L3A+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIFN0YXRzIFByZXZpZXcgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlLzUgYmFja2Ryb3AtYmx1ci14bCBib3JkZXIgYm9yZGVyLXdoaXRlLzEwIHJvdW5kZWQtMnhsIHAtNlwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMyBtYi02XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTIgYmctZ3JhZGllbnQtdG8tciBmcm9tLWJsdWUtNjAwIHRvLWN5YW4tNjAwIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgICAgIDxCYXJDaGFydCBjbGFzc05hbWU9XCJ3LTUgaC01IHRleHQtd2hpdGVcIiAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlXCI+R2VuZXJhdGlvbiBTdGF0czwvaDM+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMiBnYXAtNFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZ3JlZW4tNDAwXCI+MTArPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTQwMFwiPlNlYXJjaCBRdWVyaWVzPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ibHVlLTQwMFwiPjE1KzwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS00MDBcIj5Tb3VyY2VzIEFuYWx5emVkPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1wdXJwbGUtNDAwXCI+NDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS00MDBcIj5BbmFseXNpcyBQaGFzZXM8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LW9yYW5nZS00MDBcIj44KzwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS00MDBcIj5BSSBCeXBhc3MgVGVjaG5pcXVlczwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogRW5oYW5jZWQgRmVhdHVyZXMgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLWJyIGZyb20tdmlvbGV0LTkwMC8yMCB0by1pbmRpZ28tOTAwLzIwIGJvcmRlciBib3JkZXItdmlvbGV0LTUwMC8yMCByb3VuZGVkLTJ4bCBwLTZcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTMgbWItNFwiPlxuICAgICAgICAgICAgICAgIDxDcm93biBjbGFzc05hbWU9XCJ3LTYgaC02IHRleHQteWVsbG93LTQwMFwiIC8+XG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1ib2xkIHRleHQtd2hpdGVcIj5FbmhhbmNlZCBGZWF0dXJlczwvaDM+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgICAgPFNoaWVsZCBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtZW1lcmFsZC00MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMFwiPkFkdmFuY2VkIEFJIERldGVjdGlvbiBCeXBhc3M8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgIDxCcmFpbiBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtcHVycGxlLTQwMFwiIC8+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktMzAwXCI+SHVtYW4gV3JpdGluZyBQYXR0ZXJuIEFuYWx5c2lzPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICA8U2VhcmNoIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1ibHVlLTQwMFwiIC8+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktMzAwXCI+Q29tcHJlaGVuc2l2ZSBSZXNlYXJjaDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgICAgPFRhcmdldCBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtcmVkLTQwMFwiIC8+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktMzAwXCI+U3VwZXJpb3IgQ29tcGV0aXRpb24gQW5hbHlzaXM8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9tYWluPlxuICAgIDwvZGl2PlxuICApO1xufSAiXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VSb3V0ZXIiLCJ1c2VTZWFyY2hQYXJhbXMiLCJtb3Rpb24iLCJMaW5rIiwidXNlU2Vzc2lvbiIsIkFycm93TGVmdCIsIlNwYXJrbGVzIiwiQnJhaW4iLCJaYXAiLCJUYXJnZXQiLCJTZWFyY2giLCJSb2NrZXQiLCJTZXR0aW5ncyIsIlNoaWVsZCIsIkNyb3duIiwiQmFyQ2hhcnQiLCJNb25pdG9yIiwiQWN0aXZpdHkiLCJjbiIsIkludmluY2libGVTdHJlYW1pbmdVSSIsIkludmluY2libGVQYWdlIiwicm91dGVyIiwic2VhcmNoUGFyYW1zIiwiZGF0YSIsInNlc3Npb24iLCJzdGF0dXMiLCJjb25maWciLCJzZXRDb25maWciLCJ0b3BpYyIsImN1c3RvbUluc3RydWN0aW9ucyIsInRhcmdldEF1ZGllbmNlIiwiY29udGVudExlbmd0aCIsInRvbmUiLCJrZXl3b3JkcyIsInNlYXJjaERlcHRoIiwiY29tcGV0aXRvckNvdW50IiwiZGVlcFNlYXJjaFF1ZXJpZXNQZXJUb3BpYyIsInVuaXF1ZW5lc3NMZXZlbCIsInZlcnNpb24iLCJrZXl3b3JkSW5wdXQiLCJzZXRLZXl3b3JkSW5wdXQiLCJpc1N0cmVhbWluZyIsInNldElzU3RyZWFtaW5nIiwic2hvd1N0cmVhbWluZ1VJIiwic2V0U2hvd1N0cmVhbWluZ1VJIiwic3RyZWFtaW5nUmVzdWx0Iiwic2V0U3RyZWFtaW5nUmVzdWx0IiwiZXJyb3IiLCJzZXRFcnJvciIsImlzU2F2aW5nIiwic2V0SXNTYXZpbmciLCJnZXQiLCJzb3VyY2UiLCJ2aWRlb1VybCIsInZpZGVvVGl0bGUiLCJwcmV2Q29uZmlnIiwic291cmNlVmlkZW9VcmwiLCJzb3VyY2VWaWRlb1RpdGxlIiwid2luZG93IiwibG9jYXRpb24iLCJocmVmIiwiZGl2IiwiY2xhc3NOYW1lIiwicCIsImhhbmRsZVN0YXJ0U3RyZWFtaW5nIiwidHJpbSIsImhhbmRsZVN0cmVhbWluZ0NvbXBsZXRlIiwicmVzdWx0IiwiY29uc29sZSIsImxvZyIsImhhc1Jlc3VsdCIsImhhc0FydGljbGUiLCJhcnRpY2xlIiwiaGFzQ29udGVudCIsImNvbnRlbnQiLCJyZXN1bHRLZXlzIiwiT2JqZWN0Iiwia2V5cyIsInJlc3VsdFR5cGUiLCJyZXN1bHRDb250ZW50IiwiYXJ0aWNsZURhdGEiLCJhcnRpY2xlVGl0bGUiLCJhcnRpY2xlQ29udGVudCIsInRpdGxlIiwiaGFzQXJ0aWNsZURhdGEiLCJ0aXRsZUxlbmd0aCIsImxlbmd0aCIsImNvbnRlbnRQcmV2aWV3Iiwic3Vic3RyaW5nIiwiY29udGVudFR5cGUiLCJjb250ZW50RXhpc3RzIiwicmF3Q29udGVudCIsInJlc3VsdFN0cnVjdHVyZSIsIkpTT04iLCJzdHJpbmdpZnkiLCJjb25maWdWZXJzaW9uIiwiZXh0cmFjdGlvblBhdGgiLCJlcnJvck1lc3NhZ2UiLCJFcnJvciIsInJlc3BvbnNlIiwiZmV0Y2giLCJtZXRob2QiLCJoZWFkZXJzIiwiYm9keSIsInR5cGUiLCJtZXRhZGF0YSIsImV4ZWN1dGlvblRpbWUiLCJzdGF0cyIsInRvdGFsU291cmNlcyIsImluc2lnaHRzIiwidW5pcXVlbmVzc1Njb3JlIiwic2VvU2NvcmUiLCJyZWFkYWJpbGl0eVNjb3JlIiwicXVhbGl0eVNjb3JlIiwiY29tcGV0aXRvcnNBbmFseXplZCIsIml0ZXJhdGlvbnNDb21wbGV0ZWQiLCJmYWN0Q2hlY2tSZXBvcnQiLCJnZW5lcmF0ZWRBdCIsIkRhdGUiLCJ0b0lTT1N0cmluZyIsImxhbmd1YWdlIiwib2siLCJlcnJvclRleHQiLCJ0ZXh0Iiwic2F2ZVJlc3VsdCIsImpzb24iLCJzdWNjZXNzIiwiUHJvbWlzZSIsInJlc29sdmUiLCJzZXRUaW1lb3V0IiwicHVzaCIsImlkIiwibWVzc2FnZSIsImhhbmRsZVN0cmVhbWluZ0Vycm9yIiwiYWRkS2V5d29yZCIsImluY2x1ZGVzIiwicmVtb3ZlS2V5d29yZCIsImtleXdvcmQiLCJmaWx0ZXIiLCJrIiwicmVzZXRUb0NvbmZpZ3VyYXRpb24iLCJ2aWV3R2VuZXJhdGVkQXJ0aWNsZSIsIkludmluY2libGVWMlN0cmVhbWluZ1VJIiwib25Db21wbGV0ZSIsIm9uRXJyb3IiLCJhbmltYXRlIiwieCIsInkiLCJ0cmFuc2l0aW9uIiwiZHVyYXRpb24iLCJyZXBlYXQiLCJJbmZpbml0eSIsImVhc2UiLCJoZWFkZXIiLCJpbml0aWFsIiwib3BhY2l0eSIsInNwYW4iLCJoMSIsIm1haW4iLCJoMiIsImgzIiwiZGVsYXkiLCJsYWJlbCIsImJ1dHRvbiIsIm9uQ2xpY2siLCJpbnB1dCIsInZhbHVlIiwib25DaGFuZ2UiLCJlIiwidGFyZ2V0IiwicGxhY2Vob2xkZXIiLCJwYXJzZUludCIsIm1pbiIsIm1heCIsInRleHRhcmVhIiwicm93cyIsIm1hcCIsInNjYWxlIiwid2hpbGVIb3ZlciIsIndoaWxlVGFwIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/invincible/page.tsx\n"));

/***/ })

});