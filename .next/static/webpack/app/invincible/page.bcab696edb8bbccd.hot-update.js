"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/invincible/page",{

/***/ "(app-pages-browser)/./src/app/invincible/page.tsx":
/*!*************************************!*\
  !*** ./src/app/invincible/page.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ InvinciblePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart,Brain,Crown,Monitor,Rocket,Search,Settings,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart,Brain,Crown,Monitor,Rocket,Search,Settings,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart,Brain,Crown,Monitor,Rocket,Search,Settings,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart,Brain,Crown,Monitor,Rocket,Search,Settings,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart,Brain,Crown,Monitor,Rocket,Search,Settings,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart,Brain,Crown,Monitor,Rocket,Search,Settings,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart,Brain,Crown,Monitor,Rocket,Search,Settings,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart,Brain,Crown,Monitor,Rocket,Search,Settings,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart,Brain,Crown,Monitor,Rocket,Search,Settings,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart,Brain,Crown,Monitor,Rocket,Search,Settings,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart,Brain,Crown,Monitor,Rocket,Search,Settings,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart,Brain,Crown,Monitor,Rocket,Search,Settings,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart,Brain,Crown,Monitor,Rocket,Search,Settings,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_InvincibleStreamingUI__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/InvincibleStreamingUI */ \"(app-pages-browser)/./src/components/InvincibleStreamingUI.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction InvinciblePage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_4__.useSession)();\n    const [config, setConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        topic: '',\n        customInstructions: '',\n        targetAudience: '',\n        contentLength: 2000,\n        tone: 'professional',\n        keywords: [],\n        searchDepth: 7,\n        competitorCount: 5,\n        deepSearchQueriesPerTopic: 7,\n        uniquenessLevel: 'high',\n        version: 'v2'\n    });\n    const [keywordInput, setKeywordInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showStreamingUI, setShowStreamingUI] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [streamingResult, setStreamingResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Handle URL parameters from Megatron\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"InvinciblePage.useEffect\": ()=>{\n            const topic = searchParams.get('topic');\n            const customInstructions = searchParams.get('customInstructions');\n            const source = searchParams.get('source');\n            const videoUrl = searchParams.get('videoUrl');\n            const videoTitle = searchParams.get('videoTitle');\n            if (source === 'megatron' && topic) {\n                setConfig({\n                    \"InvinciblePage.useEffect\": (prevConfig)=>({\n                            ...prevConfig,\n                            topic: topic,\n                            customInstructions: customInstructions || prevConfig.customInstructions,\n                            // Add metadata about the source\n                            ...videoUrl && {\n                                sourceVideoUrl: videoUrl\n                            },\n                            ...videoTitle && {\n                                sourceVideoTitle: videoTitle\n                            }\n                        })\n                }[\"InvinciblePage.useEffect\"]);\n            }\n        }\n    }[\"InvinciblePage.useEffect\"], [\n        searchParams\n    ]);\n    // Redirect to login if not authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"InvinciblePage.useEffect\": ()=>{\n            if (status === 'unauthenticated') {\n                window.location.href = '/login';\n            }\n        }\n    }[\"InvinciblePage.useEffect\"], [\n        status\n    ]);\n    // Loading state\n    if (status === 'loading') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-black flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin w-12 h-12 border-2 border-violet-400 border-t-transparent rounded-full mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                lineNumber: 106,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n            lineNumber: 105,\n            columnNumber: 7\n        }, this);\n    }\n    // Don't render if not authenticated\n    if (status === 'unauthenticated') {\n        return null;\n    }\n    const handleStartStreaming = async ()=>{\n        if (!config.topic.trim()) {\n            setError('Please enter a topic');\n            return;\n        }\n        setError('');\n        setIsStreaming(true);\n        // For all modes including autonomous, use the streaming UI\n        setShowStreamingUI(true);\n    };\n    const handleStreamingComplete = async (result)=>{\n        setStreamingResult(result);\n        setIsStreaming(false);\n        setIsSaving(true);\n        try {\n            var _result_stats, _result_insights, _result_stats1, _result_stats2, _result_article, _result_article1, _result_insights1, _result_insights2, _result_insights3;\n            console.log('🔍 Raw result structure:', {\n                hasResult: !!result.result,\n                hasArticle: !!result.article,\n                hasContent: !!result.content,\n                resultKeys: Object.keys(result || {}),\n                resultType: typeof result.result,\n                resultContent: result.result ? Object.keys(result.result) : 'N/A'\n            });\n            // Extract article data with comprehensive fallback logic\n            let articleData = null;\n            let articleTitle = '';\n            let articleContent = '';\n            // Handle different result structures\n            if (result.result) {\n                // result.result path\n                articleData = result.result;\n                articleTitle = result.result.title || '';\n                articleContent = result.result.content || '';\n                console.log('📄 Using result.result path');\n            } else if (result.article) {\n                // Alternative: result.article path\n                articleData = result.article;\n                articleTitle = result.article.title || '';\n                articleContent = result.article.content || '';\n                console.log('📄 Using result.article path');\n            } else if (result.content) {\n                // Direct content path\n                articleContent = result.content;\n                articleTitle = result.title || \"Article about \".concat(config.topic);\n                console.log('📄 Using result.content path');\n            } else {\n                // Last resort: use result directly\n                articleData = result;\n                articleTitle = result.title || \"Article about \".concat(config.topic);\n                articleContent = result.content || '';\n                console.log('📄 Using result direct path');\n            }\n            console.log('📊 Extracted article data:', {\n                hasArticleData: !!articleData,\n                title: articleTitle,\n                titleLength: (articleTitle === null || articleTitle === void 0 ? void 0 : articleTitle.length) || 0,\n                contentLength: (articleContent === null || articleContent === void 0 ? void 0 : articleContent.length) || 0,\n                contentPreview: (articleContent === null || articleContent === void 0 ? void 0 : articleContent.substring(0, 200)) || 'EMPTY',\n                contentType: typeof articleContent\n            });\n            // Validate title\n            if (!articleTitle || articleTitle.trim().length === 0) {\n                articleTitle = \"Complete Guide to \".concat(config.topic);\n                console.log('⚠️ Using fallback title:', articleTitle);\n            }\n            // Validate content with detailed error reporting\n            if (!articleContent || articleContent.trim().length === 0) {\n                console.error('❌ Content validation failed:', {\n                    contentExists: !!articleContent,\n                    contentType: typeof articleContent,\n                    contentLength: (articleContent === null || articleContent === void 0 ? void 0 : articleContent.length) || 0,\n                    rawContent: articleContent,\n                    resultStructure: JSON.stringify(result, null, 2).substring(0, 1000),\n                    configVersion: config.version,\n                    extractionPath: 'fallback paths'\n                });\n                // Error message for content generation failure\n                const errorMessage = \"Content generation failed - no content was generated. This might be due to API issues or content filtering. Please try again or contact support if the issue persists.\";\n                throw new Error(errorMessage);\n            }\n            // Final validation\n            if (articleContent.trim().length < 50) {\n                throw new Error(\"Generated content is too short (\".concat(articleContent.length, \" characters). Please try again.\"));\n            }\n            // Save the article to the database\n            const response = await fetch('/api/articles/store', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    title: articleTitle,\n                    content: articleContent,\n                    type: 'invincible',\n                    metadata: {\n                        topic: config.topic,\n                        tone: config.tone,\n                        targetAudience: config.targetAudience,\n                        contentLength: config.contentLength,\n                        customInstructions: config.customInstructions,\n                        keywords: config.keywords,\n                        executionTime: result.executionTime || ((_result_stats = result.stats) === null || _result_stats === void 0 ? void 0 : _result_stats.executionTime),\n                        totalSources: ((_result_insights = result.insights) === null || _result_insights === void 0 ? void 0 : _result_insights.totalSources) || ((_result_stats1 = result.stats) === null || _result_stats1 === void 0 ? void 0 : _result_stats1.totalSources),\n                        uniquenessScore: (_result_stats2 = result.stats) === null || _result_stats2 === void 0 ? void 0 : _result_stats2.uniquenessScore,\n                        seoScore: (articleData === null || articleData === void 0 ? void 0 : articleData.seoScore) || ((_result_article = result.article) === null || _result_article === void 0 ? void 0 : _result_article.seoScore),\n                        readabilityScore: (articleData === null || articleData === void 0 ? void 0 : articleData.readabilityScore) || ((_result_article1 = result.article) === null || _result_article1 === void 0 ? void 0 : _result_article1.readabilityScore),\n                        qualityScore: result.qualityScore || ((_result_insights1 = result.insights) === null || _result_insights1 === void 0 ? void 0 : _result_insights1.qualityScore),\n                        competitorsAnalyzed: (_result_insights2 = result.insights) === null || _result_insights2 === void 0 ? void 0 : _result_insights2.competitorsAnalyzed,\n                        iterationsCompleted: (_result_insights3 = result.insights) === null || _result_insights3 === void 0 ? void 0 : _result_insights3.iterationsCompleted,\n                        factCheckReport: result.factCheckReport,\n                        generatedAt: new Date().toISOString()\n                    },\n                    tone: config.tone,\n                    language: 'en'\n                })\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                throw new Error(\"Failed to save article: \".concat(errorText));\n            }\n            const saveResult = await response.json();\n            if (saveResult.success) {\n                // Small delay to show saving completion\n                await new Promise((resolve)=>setTimeout(resolve, 1000));\n                // Redirect to the article view page with the saved article ID\n                router.push(\"/article-view/\".concat(saveResult.article.id));\n            } else {\n                throw new Error(saveResult.error || 'Failed to save article');\n            }\n        } catch (error) {\n            console.error('Error saving article:', error);\n            setIsSaving(false);\n            setError(\"Article generated successfully but failed to save: \".concat(error instanceof Error ? error.message : 'Unknown error', \". Please try again.\"));\n        }\n    };\n    const handleStreamingError = (error)=>{\n        setError(error);\n        setIsStreaming(false);\n    };\n    const addKeyword = ()=>{\n        var _config_keywords;\n        if (keywordInput.trim() && !((_config_keywords = config.keywords) === null || _config_keywords === void 0 ? void 0 : _config_keywords.includes(keywordInput.trim()))) {\n            setConfig({\n                ...config,\n                keywords: [\n                    ...config.keywords || [],\n                    keywordInput.trim()\n                ]\n            });\n            setKeywordInput('');\n        }\n    };\n    const removeKeyword = (keyword)=>{\n        var _config_keywords;\n        setConfig({\n            ...config,\n            keywords: ((_config_keywords = config.keywords) === null || _config_keywords === void 0 ? void 0 : _config_keywords.filter((k)=>k !== keyword)) || []\n        });\n    };\n    const resetToConfiguration = ()=>{\n        setShowStreamingUI(false);\n        setIsStreaming(false);\n        setStreamingResult(null);\n        setError('');\n        setIsSaving(false);\n    };\n    const viewGeneratedArticle = ()=>{\n        router.push('/article-view');\n    };\n    // If showing streaming UI, render the appropriate component based on version\n    if (showStreamingUI) {\n        if (config.version === 'v2') {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InvincibleV2StreamingUI, {\n                topic: config.topic,\n                contentLength: config.contentLength,\n                tone: config.tone,\n                targetAudience: config.targetAudience,\n                customInstructions: config.customInstructions,\n                contentType: \"article\",\n                onComplete: handleStreamingComplete,\n                onError: handleStreamingError,\n                isSaving: isSaving\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                lineNumber: 310,\n                columnNumber: 9\n            }, this);\n        } else {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_InvincibleStreamingUI__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                topic: config.topic,\n                contentLength: config.contentLength,\n                tone: config.tone,\n                targetAudience: config.targetAudience,\n                customInstructions: config.customInstructions,\n                onComplete: handleStreamingComplete,\n                onError: handleStreamingError,\n                isSaving: isSaving\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                lineNumber: 324,\n                columnNumber: 9\n            }, this);\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-black\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-violet-900/10 via-black to-indigo-900/10\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                        lineNumber: 342,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        animate: {\n                            x: [\n                                0,\n                                100,\n                                0\n                            ],\n                            y: [\n                                0,\n                                -100,\n                                0\n                            ]\n                        },\n                        transition: {\n                            duration: 20,\n                            repeat: Infinity,\n                            ease: \"linear\"\n                        },\n                        className: \"absolute top-1/4 left-1/4 w-[500px] h-[500px] bg-violet-500/10 rounded-full blur-[100px]\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                        lineNumber: 345,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        animate: {\n                            x: [\n                                0,\n                                -100,\n                                0\n                            ],\n                            y: [\n                                0,\n                                100,\n                                0\n                            ]\n                        },\n                        transition: {\n                            duration: 15,\n                            repeat: Infinity,\n                            ease: \"linear\"\n                        },\n                        className: \"absolute bottom-1/4 right-1/4 w-[600px] h-[600px] bg-indigo-500/10 rounded-full blur-[120px]\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                lineNumber: 341,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.header, {\n                initial: {\n                    opacity: 0,\n                    y: -20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                className: \"relative z-10 border-b border-white/10 backdrop-blur-xl bg-black/40\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-6 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/dashboard\",\n                                        className: \"flex items-center space-x-3 text-gray-400 hover:text-white transition-colors group\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-5 h-5 group-hover:-translate-x-1 transition-transform\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"Back to Dashboard\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-6 w-px bg-white/20\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 rounded-xl blur-lg opacity-70 bg-gradient-to-r from-violet-600 to-indigo-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative bg-black rounded-xl p-2.5 border border-white/20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-6 h-6 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                            lineNumber: 394,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 391,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-xl font-bold text-white\",\n                                                        children: \"Invincible\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 398,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-400\",\n                                                        children: \"Advanced AI Content Generation\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 401,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 397,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                        lineNumber: 390,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                lineNumber: 379,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden md:flex items-center space-x-6 text-sm text-gray-400\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-4 h-4 text-emerald-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 411,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"AI Detection Bypass\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 412,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                            lineNumber: 410,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-4 h-4 text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 415,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Live Streaming\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                            lineNumber: 414,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-4 h-4 text-yellow-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Real-time Analysis\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                            lineNumber: 418,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                    lineNumber: 409,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                lineNumber: 408,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                        lineNumber: 378,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                    lineNumber: 377,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                lineNumber: 372,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"relative z-10 max-w-6xl mx-auto px-6 py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-flex items-center space-x-2 px-4 py-2 rounded-full bg-white/10 backdrop-blur-md border border-white/20 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"w-4 h-4 text-green-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                        lineNumber: 437,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-200\",\n                                        children: \"Live Streaming Generation\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                        lineNumber: 438,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"w-4 h-4 text-violet-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-200\",\n                                        children: \"AI Detection Bypass\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-4 h-4 text-yellow-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                        lineNumber: 441,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                lineNumber: 436,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-5xl md:text-6xl font-bold text-white mb-6 leading-tight\",\n                                children: [\n                                    \"Watch Your Content\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"block bg-gradient-to-r from-violet-400 via-purple-400 to-indigo-400 bg-clip-text text-transparent\",\n                                        children: \"Come to Life\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                lineNumber: 444,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-300 max-w-3xl mx-auto mb-8 leading-relaxed\",\n                                children: \"Experience the most advanced content generation with real-time streaming. Watch as our AI analyzes competitors, processes research, applies humanization techniques, and creates superior articles that bypass AI detection.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                lineNumber: 451,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto mt-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center mb-4 mx-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-6 h-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 458,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-white mb-2\",\n                                                children: \"Live Streaming\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 461,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"Watch queries, analysis, and generation in real-time\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 464,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                        lineNumber: 457,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center mb-4 mx-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-6 h-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 471,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 470,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-white mb-2\",\n                                                children: \"AI Detection Bypass\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 473,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"Advanced humanization with date variation and jargon removal\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 476,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                        lineNumber: 469,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center mb-4 mx-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-6 h-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 483,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 482,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-white mb-2\",\n                                                children: \"Superior Quality\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"Content that surpasses all competitors with comprehensive research\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 488,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                        lineNumber: 481,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                lineNumber: 456,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                        lineNumber: 431,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: -20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    delay: 0.1\n                                },\n                                className: \"lg:col-span-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 mb-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 bg-gradient-to-r from-violet-600 to-indigo-600 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"w-5 h-5 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 507,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 506,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-2xl font-bold text-white\",\n                                                    children: \"Configuration\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 509,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                            lineNumber: 505,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-white/90\",\n                                                            children: \"Invincible Version\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                            lineNumber: 515,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-2 gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: ()=>setConfig({\n                                                                            ...config,\n                                                                            version: 'v1'\n                                                                        }),\n                                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"px-6 py-4 rounded-xl border transition-all flex items-center space-x-3\", config.version === 'v1' ? \"bg-green-600/20 border-green-500/50 text-green-300\" : \"bg-white/5 border-white/10 text-gray-300 hover:bg-white/10 hover:border-white/20\"),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"w-5 h-5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                            lineNumber: 529,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-left\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"font-semibold\",\n                                                                                    children: \"V.1 Classic\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                                    lineNumber: 531,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-xs opacity-80\",\n                                                                                    children: \"Original streaming system\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                                    lineNumber: 532,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                            lineNumber: 530,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                    lineNumber: 519,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: ()=>setConfig({\n                                                                            ...config,\n                                                                            version: 'v2'\n                                                                        }),\n                                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"px-6 py-4 rounded-xl border transition-all flex items-center space-x-3 relative\", config.version === 'v2' ? \"bg-violet-600/20 border-violet-500/50 text-violet-300\" : \"bg-white/5 border-white/10 text-gray-300 hover:bg-white/10 hover:border-white/20\"),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                            className: \"w-5 h-5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                            lineNumber: 545,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-left\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"font-semibold flex items-center space-x-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: \"V.2 Autonomous\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                                            lineNumber: 548,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"px-2 py-1 bg-violet-600/30 text-xs rounded-full\",\n                                                                                            children: \"BETA\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                                            lineNumber: 549,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                                    lineNumber: 547,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-xs opacity-80\",\n                                                                                    children: \"Kimi K2 + LangGraph + Tavily\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                                    lineNumber: 551,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                            lineNumber: 546,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                    lineNumber: 535,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                            lineNumber: 518,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 514,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-white/90\",\n                                                            children: [\n                                                                \"Topic \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-violet-400\",\n                                                                    children: \"*\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                    lineNumber: 560,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                            lineNumber: 559,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: config.topic,\n                                                            onChange: (e)=>setConfig({\n                                                                    ...config,\n                                                                    topic: e.target.value\n                                                                }),\n                                                            className: \"w-full px-4 py-4 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:bg-white/10 focus:border-violet-500/50 transition-all\",\n                                                            placeholder: \"e.g., The 5 Best CLI Agents of 2025\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                            lineNumber: 562,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 558,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-white/90\",\n                                                                    children: \"Target Audience\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                    lineNumber: 575,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: config.targetAudience,\n                                                                    onChange: (e)=>setConfig({\n                                                                            ...config,\n                                                                            targetAudience: e.target.value\n                                                                        }),\n                                                                    className: \"w-full px-4 py-4 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:bg-white/10 focus:border-violet-500/50 transition-all\",\n                                                                    placeholder: \"e.g., Developers and tech enthusiasts\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                    lineNumber: 578,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                            lineNumber: 574,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-white/90\",\n                                                                    children: \"Content Length (words)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                    lineNumber: 588,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: config.contentLength,\n                                                                    onChange: (e)=>setConfig({\n                                                                            ...config,\n                                                                            contentLength: parseInt(e.target.value) || 2000\n                                                                        }),\n                                                                    className: \"w-full px-4 py-4 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:bg-white/10 focus:border-violet-500/50 transition-all\",\n                                                                    min: \"500\",\n                                                                    max: \"5000\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                    lineNumber: 591,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                            lineNumber: 587,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 573,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-white/90\",\n                                                            children: \"Custom Instructions\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                            lineNumber: 604,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            value: config.customInstructions,\n                                                            onChange: (e)=>setConfig({\n                                                                    ...config,\n                                                                    customInstructions: e.target.value\n                                                                }),\n                                                            className: \"w-full px-4 py-4 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:bg-white/10 focus:border-violet-500/50 transition-all resize-none\",\n                                                            rows: 4,\n                                                            placeholder: \"e.g., Focus on practical value and real-world usage. Include specific examples and avoid generic advice.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                            lineNumber: 607,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 603,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-white/90\",\n                                                            children: \"Writing Tone\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                            lineNumber: 618,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-2 md:grid-cols-3 gap-3\",\n                                                            children: [\n                                                                'professional',\n                                                                'conversational',\n                                                                'casual',\n                                                                'authoritative',\n                                                                'friendly',\n                                                                'technical'\n                                                            ].map((tone)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: ()=>setConfig({\n                                                                            ...config,\n                                                                            tone\n                                                                        }),\n                                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"px-4 py-3 rounded-xl border transition-all capitalize\", config.tone === tone ? \"bg-violet-600/20 border-violet-500/50 text-violet-300\" : \"bg-white/5 border-white/10 text-gray-300 hover:bg-white/10 hover:border-white/20\"),\n                                                                    children: tone\n                                                                }, tone, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                    lineNumber: 623,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                            lineNumber: 621,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 617,\n                                                    columnNumber: 17\n                                                }, this),\n                                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                                    initial: {\n                                                        opacity: 0,\n                                                        scale: 0.9\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        scale: 1\n                                                    },\n                                                    className: \"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-red-700 dark:text-red-300 text-sm\",\n                                                        children: error\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 649,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 644,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                                    onClick: handleStartStreaming,\n                                                    whileHover: {\n                                                        scale: 1.02\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.98\n                                                    },\n                                                    className: \"w-full py-4 bg-gradient-to-r from-violet-600 via-purple-600 to-indigo-600 text-white rounded-xl font-semibold text-lg hover:shadow-2xl transition-all duration-300 flex items-center justify-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"w-6 h-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                            lineNumber: 660,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Start Live Generation\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                            lineNumber: 661,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-6 h-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                            lineNumber: 662,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 654,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                            lineNumber: 512,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                    lineNumber: 504,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                lineNumber: 498,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    delay: 0.2\n                                },\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-2 bg-gradient-to-r from-green-600 to-emerald-600 rounded-lg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"w-5 h-5 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                            lineNumber: 679,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 678,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-bold text-white\",\n                                                        children: \"What You'll See\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 681,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 677,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 686,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-white font-medium\",\n                                                                        children: \"Live Search Queries\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                        lineNumber: 688,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-400 text-sm\",\n                                                                        children: \"Watch as AI generates and executes targeted research queries\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                        lineNumber: 689,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 687,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 685,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 694,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-white font-medium\",\n                                                                        children: \"Real-time Analysis\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                        lineNumber: 696,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-400 text-sm\",\n                                                                        children: \"See competitor analysis and content understanding in action\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                        lineNumber: 697,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 695,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 693,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-purple-400 rounded-full mt-2 flex-shrink-0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 702,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-white font-medium\",\n                                                                        children: \"AI Humanization\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                        lineNumber: 704,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-400 text-sm\",\n                                                                        children: \"Watch date variation, jargon removal, and bypass techniques\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                        lineNumber: 705,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 703,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 701,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-orange-400 rounded-full mt-2 flex-shrink-0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 710,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-white font-medium\",\n                                                                        children: \"Content Generation\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                        lineNumber: 712,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-400 text-sm\",\n                                                                        children: \"See superior content creation with full context\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                        lineNumber: 713,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 711,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 709,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 684,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                        lineNumber: 676,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-2 bg-gradient-to-r from-blue-600 to-cyan-600 rounded-lg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"w-5 h-5 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                            lineNumber: 723,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 722,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-bold text-white\",\n                                                        children: \"Generation Stats\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 725,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 721,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-green-400\",\n                                                                children: \"10+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 730,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: \"Search Queries\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 731,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 729,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-blue-400\",\n                                                                children: \"15+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 734,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: \"Sources Analyzed\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 735,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 733,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-purple-400\",\n                                                                children: \"4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 738,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: \"Analysis Phases\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 739,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 737,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-orange-400\",\n                                                                children: \"8+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 742,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: \"AI Bypass Techniques\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 743,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 741,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 728,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                        lineNumber: 720,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-violet-900/20 to-indigo-900/20 border border-violet-500/20 rounded-2xl p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-6 h-6 text-yellow-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 751,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-bold text-white\",\n                                                        children: \"Enhanced Features\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 752,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 750,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"w-4 h-4 text-emerald-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 757,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-300\",\n                                                                children: \"Advanced AI Detection Bypass\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 758,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 756,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"w-4 h-4 text-purple-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 761,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-300\",\n                                                                children: \"Human Writing Pattern Analysis\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 762,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 760,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"w-4 h-4 text-blue-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 765,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-300\",\n                                                                children: \"Comprehensive Research\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 766,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 764,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                className: \"w-4 h-4 text-red-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 769,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-300\",\n                                                                children: \"Superior Competition Analysis\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 770,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 768,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 755,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                        lineNumber: 749,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                lineNumber: 669,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                        lineNumber: 496,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                lineNumber: 429,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n        lineNumber: 339,\n        columnNumber: 5\n    }, this);\n}\n_s(InvinciblePage, \"lxN+kII/J8YaFoGOOhNqeM0QhOg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        next_auth_react__WEBPACK_IMPORTED_MODULE_4__.useSession\n    ];\n});\n_c = InvinciblePage;\nvar _c;\n$RefreshReg$(_c, \"InvinciblePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/invincible/page.tsx\n"));

/***/ })

});