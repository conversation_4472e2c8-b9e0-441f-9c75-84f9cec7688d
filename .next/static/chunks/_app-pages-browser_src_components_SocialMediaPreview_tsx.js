/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_components_SocialMediaPreview_tsx"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/client-only/index.js":
/*!**************************************************************!*\
  !*** ./node_modules/next/dist/compiled/client-only/index.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {



/***/ }),

/***/ "(app-pages-browser)/./node_modules/styled-jsx/dist/index/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/styled-jsx/dist/index/index.js ***!
  \*****************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/process/browser.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n__webpack_require__(/*! client-only */ \"(app-pages-browser)/./node_modules/next/dist/compiled/client-only/index.js\");\nvar React = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nfunction _interopDefaultLegacy(e) {\n    return e && typeof e === 'object' && 'default' in e ? e : {\n        'default': e\n    };\n}\nvar React__default = /*#__PURE__*/ _interopDefaultLegacy(React);\n_c = React__default;\n/*\nBased on Glamor's sheet\nhttps://github.com/threepointone/glamor/blob/667b480d31b3721a905021b26e1290ce92ca2879/src/sheet.js\n*/ function _defineProperties(target, props) {\n    for(var i = 0; i < props.length; i++){\n        var descriptor = props[i];\n        descriptor.enumerable = descriptor.enumerable || false;\n        descriptor.configurable = true;\n        if (\"value\" in descriptor) descriptor.writable = true;\n        Object.defineProperty(target, descriptor.key, descriptor);\n    }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n    if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) _defineProperties(Constructor, staticProps);\n    return Constructor;\n}\nvar isProd = typeof process !== \"undefined\" && process.env && \"development\" === \"production\";\nvar isString = function(o) {\n    return Object.prototype.toString.call(o) === \"[object String]\";\n};\nvar StyleSheet = /*#__PURE__*/ function() {\n    function StyleSheet(param) {\n        var ref = param === void 0 ? {} : param, _name = ref.name, name = _name === void 0 ? \"stylesheet\" : _name, _optimizeForSpeed = ref.optimizeForSpeed, optimizeForSpeed = _optimizeForSpeed === void 0 ? isProd : _optimizeForSpeed;\n        invariant$1(isString(name), \"`name` must be a string\");\n        this._name = name;\n        this._deletedRulePlaceholder = \"#\" + name + \"-deleted-rule____{}\";\n        invariant$1(typeof optimizeForSpeed === \"boolean\", \"`optimizeForSpeed` must be a boolean\");\n        this._optimizeForSpeed = optimizeForSpeed;\n        this._serverSheet = undefined;\n        this._tags = [];\n        this._injected = false;\n        this._rulesCount = 0;\n        var node = typeof window !== \"undefined\" && document.querySelector('meta[property=\"csp-nonce\"]');\n        this._nonce = node ? node.getAttribute(\"content\") : null;\n    }\n    var _proto = StyleSheet.prototype;\n    _proto.setOptimizeForSpeed = function setOptimizeForSpeed(bool) {\n        invariant$1(typeof bool === \"boolean\", \"`setOptimizeForSpeed` accepts a boolean\");\n        invariant$1(this._rulesCount === 0, \"optimizeForSpeed cannot be when rules have already been inserted\");\n        this.flush();\n        this._optimizeForSpeed = bool;\n        this.inject();\n    };\n    _proto.isOptimizeForSpeed = function isOptimizeForSpeed() {\n        return this._optimizeForSpeed;\n    };\n    _proto.inject = function inject() {\n        var _this = this;\n        invariant$1(!this._injected, \"sheet already injected\");\n        this._injected = true;\n        if (typeof window !== \"undefined\" && this._optimizeForSpeed) {\n            this._tags[0] = this.makeStyleTag(this._name);\n            this._optimizeForSpeed = \"insertRule\" in this.getSheet();\n            if (!this._optimizeForSpeed) {\n                if (!isProd) {\n                    console.warn(\"StyleSheet: optimizeForSpeed mode not supported falling back to standard mode.\");\n                }\n                this.flush();\n                this._injected = true;\n            }\n            return;\n        }\n        this._serverSheet = {\n            cssRules: [],\n            insertRule: function(rule, index) {\n                if (typeof index === \"number\") {\n                    _this._serverSheet.cssRules[index] = {\n                        cssText: rule\n                    };\n                } else {\n                    _this._serverSheet.cssRules.push({\n                        cssText: rule\n                    });\n                }\n                return index;\n            },\n            deleteRule: function(index) {\n                _this._serverSheet.cssRules[index] = null;\n            }\n        };\n    };\n    _proto.getSheetForTag = function getSheetForTag(tag) {\n        if (tag.sheet) {\n            return tag.sheet;\n        }\n        // this weirdness brought to you by firefox\n        for(var i = 0; i < document.styleSheets.length; i++){\n            if (document.styleSheets[i].ownerNode === tag) {\n                return document.styleSheets[i];\n            }\n        }\n    };\n    _proto.getSheet = function getSheet() {\n        return this.getSheetForTag(this._tags[this._tags.length - 1]);\n    };\n    _proto.insertRule = function insertRule(rule, index) {\n        invariant$1(isString(rule), \"`insertRule` accepts only strings\");\n        if (typeof window === \"undefined\") {\n            if (typeof index !== \"number\") {\n                index = this._serverSheet.cssRules.length;\n            }\n            this._serverSheet.insertRule(rule, index);\n            return this._rulesCount++;\n        }\n        if (this._optimizeForSpeed) {\n            var sheet = this.getSheet();\n            if (typeof index !== \"number\") {\n                index = sheet.cssRules.length;\n            }\n            // this weirdness for perf, and chrome's weird bug\n            // https://stackoverflow.com/questions/20007992/chrome-suddenly-stopped-accepting-insertrule\n            try {\n                sheet.insertRule(rule, index);\n            } catch (error) {\n                if (!isProd) {\n                    console.warn(\"StyleSheet: illegal rule: \\n\\n\" + rule + \"\\n\\nSee https://stackoverflow.com/q/20007992 for more info\");\n                }\n                return -1;\n            }\n        } else {\n            var insertionPoint = this._tags[index];\n            this._tags.push(this.makeStyleTag(this._name, rule, insertionPoint));\n        }\n        return this._rulesCount++;\n    };\n    _proto.replaceRule = function replaceRule(index, rule) {\n        if (this._optimizeForSpeed || typeof window === \"undefined\") {\n            var sheet = typeof window !== \"undefined\" ? this.getSheet() : this._serverSheet;\n            if (!rule.trim()) {\n                rule = this._deletedRulePlaceholder;\n            }\n            if (!sheet.cssRules[index]) {\n                // @TBD Should we throw an error?\n                return index;\n            }\n            sheet.deleteRule(index);\n            try {\n                sheet.insertRule(rule, index);\n            } catch (error) {\n                if (!isProd) {\n                    console.warn(\"StyleSheet: illegal rule: \\n\\n\" + rule + \"\\n\\nSee https://stackoverflow.com/q/20007992 for more info\");\n                }\n                // In order to preserve the indices we insert a deleteRulePlaceholder\n                sheet.insertRule(this._deletedRulePlaceholder, index);\n            }\n        } else {\n            var tag = this._tags[index];\n            invariant$1(tag, \"old rule at index `\" + index + \"` not found\");\n            tag.textContent = rule;\n        }\n        return index;\n    };\n    _proto.deleteRule = function deleteRule(index) {\n        if (typeof window === \"undefined\") {\n            this._serverSheet.deleteRule(index);\n            return;\n        }\n        if (this._optimizeForSpeed) {\n            this.replaceRule(index, \"\");\n        } else {\n            var tag = this._tags[index];\n            invariant$1(tag, \"rule at index `\" + index + \"` not found\");\n            tag.parentNode.removeChild(tag);\n            this._tags[index] = null;\n        }\n    };\n    _proto.flush = function flush() {\n        this._injected = false;\n        this._rulesCount = 0;\n        if (typeof window !== \"undefined\") {\n            this._tags.forEach(function(tag) {\n                return tag && tag.parentNode.removeChild(tag);\n            });\n            this._tags = [];\n        } else {\n            // simpler on server\n            this._serverSheet.cssRules = [];\n        }\n    };\n    _proto.cssRules = function cssRules() {\n        var _this = this;\n        if (typeof window === \"undefined\") {\n            return this._serverSheet.cssRules;\n        }\n        return this._tags.reduce(function(rules, tag) {\n            if (tag) {\n                rules = rules.concat(Array.prototype.map.call(_this.getSheetForTag(tag).cssRules, function(rule) {\n                    return rule.cssText === _this._deletedRulePlaceholder ? null : rule;\n                }));\n            } else {\n                rules.push(null);\n            }\n            return rules;\n        }, []);\n    };\n    _proto.makeStyleTag = function makeStyleTag(name, cssString, relativeToTag) {\n        if (cssString) {\n            invariant$1(isString(cssString), \"makeStyleTag accepts only strings as second parameter\");\n        }\n        var tag = document.createElement(\"style\");\n        if (this._nonce) tag.setAttribute(\"nonce\", this._nonce);\n        tag.type = \"text/css\";\n        tag.setAttribute(\"data-\" + name, \"\");\n        if (cssString) {\n            tag.appendChild(document.createTextNode(cssString));\n        }\n        var head = document.head || document.getElementsByTagName(\"head\")[0];\n        if (relativeToTag) {\n            head.insertBefore(tag, relativeToTag);\n        } else {\n            head.appendChild(tag);\n        }\n        return tag;\n    };\n    _createClass(StyleSheet, [\n        {\n            key: \"length\",\n            get: function get() {\n                return this._rulesCount;\n            }\n        }\n    ]);\n    return StyleSheet;\n}();\nfunction invariant$1(condition, message) {\n    if (!condition) {\n        throw new Error(\"StyleSheet: \" + message + \".\");\n    }\n}\nfunction hash(str) {\n    var _$hash = 5381, i = str.length;\n    while(i){\n        _$hash = _$hash * 33 ^ str.charCodeAt(--i);\n    }\n    /* JavaScript does bitwise operations (like XOR, above) on 32-bit signed\n   * integers. Since we want the results to be always positive, convert the\n   * signed int to an unsigned by doing an unsigned bitshift. */ return _$hash >>> 0;\n}\nvar stringHash = hash;\nvar sanitize = function(rule) {\n    return rule.replace(/\\/style/gi, \"\\\\/style\");\n};\nvar cache = {};\n/**\n * computeId\n *\n * Compute and memoize a jsx id from a basedId and optionally props.\n */ function computeId(baseId, props) {\n    if (!props) {\n        return \"jsx-\" + baseId;\n    }\n    var propsToString = String(props);\n    var key = baseId + propsToString;\n    if (!cache[key]) {\n        cache[key] = \"jsx-\" + stringHash(baseId + \"-\" + propsToString);\n    }\n    return cache[key];\n}\n/**\n * computeSelector\n *\n * Compute and memoize dynamic selectors.\n */ function computeSelector(id, css) {\n    var selectoPlaceholderRegexp = /__jsx-style-dynamic-selector/g;\n    // Sanitize SSR-ed CSS.\n    // Client side code doesn't need to be sanitized since we use\n    // document.createTextNode (dev) and the CSSOM api sheet.insertRule (prod).\n    if (typeof window === \"undefined\") {\n        css = sanitize(css);\n    }\n    var idcss = id + css;\n    if (!cache[idcss]) {\n        cache[idcss] = css.replace(selectoPlaceholderRegexp, id);\n    }\n    return cache[idcss];\n}\nfunction mapRulesToStyle(cssRules, options) {\n    if (options === void 0) options = {};\n    return cssRules.map(function(args) {\n        var id = args[0];\n        var css = args[1];\n        return /*#__PURE__*/ React__default[\"default\"].createElement(\"style\", {\n            id: \"__\" + id,\n            // Avoid warnings upon render with a key\n            key: \"__\" + id,\n            nonce: options.nonce ? options.nonce : undefined,\n            dangerouslySetInnerHTML: {\n                __html: css\n            }\n        });\n    });\n}\nvar StyleSheetRegistry = /*#__PURE__*/ function() {\n    function StyleSheetRegistry(param) {\n        var ref = param === void 0 ? {} : param, _styleSheet = ref.styleSheet, styleSheet = _styleSheet === void 0 ? null : _styleSheet, _optimizeForSpeed = ref.optimizeForSpeed, optimizeForSpeed = _optimizeForSpeed === void 0 ? false : _optimizeForSpeed;\n        this._sheet = styleSheet || new StyleSheet({\n            name: \"styled-jsx\",\n            optimizeForSpeed: optimizeForSpeed\n        });\n        this._sheet.inject();\n        if (styleSheet && typeof optimizeForSpeed === \"boolean\") {\n            this._sheet.setOptimizeForSpeed(optimizeForSpeed);\n            this._optimizeForSpeed = this._sheet.isOptimizeForSpeed();\n        }\n        this._fromServer = undefined;\n        this._indices = {};\n        this._instancesCounts = {};\n    }\n    var _proto = StyleSheetRegistry.prototype;\n    _proto.add = function add(props) {\n        var _this = this;\n        if (undefined === this._optimizeForSpeed) {\n            this._optimizeForSpeed = Array.isArray(props.children);\n            this._sheet.setOptimizeForSpeed(this._optimizeForSpeed);\n            this._optimizeForSpeed = this._sheet.isOptimizeForSpeed();\n        }\n        if (typeof window !== \"undefined\" && !this._fromServer) {\n            this._fromServer = this.selectFromServer();\n            this._instancesCounts = Object.keys(this._fromServer).reduce(function(acc, tagName) {\n                acc[tagName] = 0;\n                return acc;\n            }, {});\n        }\n        var ref = this.getIdAndRules(props), styleId = ref.styleId, rules = ref.rules;\n        // Deduping: just increase the instances count.\n        if (styleId in this._instancesCounts) {\n            this._instancesCounts[styleId] += 1;\n            return;\n        }\n        var indices = rules.map(function(rule) {\n            return _this._sheet.insertRule(rule);\n        }) // Filter out invalid rules\n        .filter(function(index) {\n            return index !== -1;\n        });\n        this._indices[styleId] = indices;\n        this._instancesCounts[styleId] = 1;\n    };\n    _proto.remove = function remove(props) {\n        var _this = this;\n        var styleId = this.getIdAndRules(props).styleId;\n        invariant(styleId in this._instancesCounts, \"styleId: `\" + styleId + \"` not found\");\n        this._instancesCounts[styleId] -= 1;\n        if (this._instancesCounts[styleId] < 1) {\n            var tagFromServer = this._fromServer && this._fromServer[styleId];\n            if (tagFromServer) {\n                tagFromServer.parentNode.removeChild(tagFromServer);\n                delete this._fromServer[styleId];\n            } else {\n                this._indices[styleId].forEach(function(index) {\n                    return _this._sheet.deleteRule(index);\n                });\n                delete this._indices[styleId];\n            }\n            delete this._instancesCounts[styleId];\n        }\n    };\n    _proto.update = function update(props, nextProps) {\n        this.add(nextProps);\n        this.remove(props);\n    };\n    _proto.flush = function flush() {\n        this._sheet.flush();\n        this._sheet.inject();\n        this._fromServer = undefined;\n        this._indices = {};\n        this._instancesCounts = {};\n    };\n    _proto.cssRules = function cssRules() {\n        var _this = this;\n        var fromServer = this._fromServer ? Object.keys(this._fromServer).map(function(styleId) {\n            return [\n                styleId,\n                _this._fromServer[styleId]\n            ];\n        }) : [];\n        var cssRules = this._sheet.cssRules();\n        return fromServer.concat(Object.keys(this._indices).map(function(styleId) {\n            return [\n                styleId,\n                _this._indices[styleId].map(function(index) {\n                    return cssRules[index].cssText;\n                }).join(_this._optimizeForSpeed ? \"\" : \"\\n\")\n            ];\n        }) // filter out empty rules\n        .filter(function(rule) {\n            return Boolean(rule[1]);\n        }));\n    };\n    _proto.styles = function styles(options) {\n        return mapRulesToStyle(this.cssRules(), options);\n    };\n    _proto.getIdAndRules = function getIdAndRules(props) {\n        var css = props.children, dynamic = props.dynamic, id = props.id;\n        if (dynamic) {\n            var styleId = computeId(id, dynamic);\n            return {\n                styleId: styleId,\n                rules: Array.isArray(css) ? css.map(function(rule) {\n                    return computeSelector(styleId, rule);\n                }) : [\n                    computeSelector(styleId, css)\n                ]\n            };\n        }\n        return {\n            styleId: computeId(id),\n            rules: Array.isArray(css) ? css : [\n                css\n            ]\n        };\n    };\n    /**\n   * selectFromServer\n   *\n   * Collects style tags from the document with id __jsx-XXX\n   */ _proto.selectFromServer = function selectFromServer() {\n        var elements = Array.prototype.slice.call(document.querySelectorAll('[id^=\"__jsx-\"]'));\n        return elements.reduce(function(acc, element) {\n            var id = element.id.slice(2);\n            acc[id] = element;\n            return acc;\n        }, {});\n    };\n    return StyleSheetRegistry;\n}();\nfunction invariant(condition, message) {\n    if (!condition) {\n        throw new Error(\"StyleSheetRegistry: \" + message + \".\");\n    }\n}\nvar StyleSheetContext = /*#__PURE__*/ React.createContext(null);\nStyleSheetContext.displayName = \"StyleSheetContext\";\nfunction createStyleRegistry() {\n    return new StyleSheetRegistry();\n}\nfunction StyleRegistry(param) {\n    _s();\n    var configuredRegistry = param.registry, children = param.children;\n    var rootRegistry = React.useContext(StyleSheetContext);\n    var ref = React.useState({\n        \"StyleRegistry.useState[ref]\": function() {\n            return rootRegistry || configuredRegistry || createStyleRegistry();\n        }\n    }[\"StyleRegistry.useState[ref]\"]), registry = ref[0];\n    return /*#__PURE__*/ React__default[\"default\"].createElement(StyleSheetContext.Provider, {\n        value: registry\n    }, children);\n}\n_s(StyleRegistry, \"F6PIZFsaWgcE6rBNmd+Zkq3zRoY=\");\n_c1 = StyleRegistry;\nfunction useStyleRegistry() {\n    _s1();\n    return React.useContext(StyleSheetContext);\n}\n_s1(useStyleRegistry, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\n// Opt-into the new `useInsertionEffect` API in React 18, fallback to `useLayoutEffect`.\n// https://github.com/reactwg/react-18/discussions/110\nvar useInsertionEffect = React__default[\"default\"].useInsertionEffect || React__default[\"default\"].useLayoutEffect;\nvar defaultRegistry = typeof window !== \"undefined\" ? createStyleRegistry() : undefined;\nfunction JSXStyle(props) {\n    _s2();\n    var registry = defaultRegistry ? defaultRegistry : useStyleRegistry();\n    // If `registry` does not exist, we do nothing here.\n    if (!registry) {\n        return null;\n    }\n    if (typeof window === \"undefined\") {\n        registry.add(props);\n        return null;\n    }\n    useInsertionEffect({\n        \"JSXStyle.useInsertionEffect\": function() {\n            registry.add(props);\n            return ({\n                \"JSXStyle.useInsertionEffect\": function() {\n                    registry.remove(props);\n                }\n            })[\"JSXStyle.useInsertionEffect\"];\n        // props.children can be string[], will be striped since id is identical\n        }\n    }[\"JSXStyle.useInsertionEffect\"], [\n        props.id,\n        String(props.dynamic)\n    ]);\n    return null;\n}\n_s2(JSXStyle, \"48Sqj1BUqkshsPdz6NEWXDn8pF4=\", false, function() {\n    return [\n        useStyleRegistry,\n        useInsertionEffect\n    ];\n});\n_c2 = JSXStyle;\nJSXStyle.dynamic = function(info) {\n    return info.map(function(tagInfo) {\n        var baseId = tagInfo[0];\n        var props = tagInfo[1];\n        return computeId(baseId, props);\n    }).join(\" \");\n};\nexports.StyleRegistry = StyleRegistry;\nexports.createStyleRegistry = createStyleRegistry;\nexports.style = JSXStyle;\nexports.useStyleRegistry = useStyleRegistry;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"React__default\");\n$RefreshReg$(_c1, \"StyleRegistry\");\n$RefreshReg$(_c2, \"JSXStyle\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/styled-jsx/dist/index/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/styled-jsx/style.js":
/*!******************************************!*\
  !*** ./node_modules/styled-jsx/style.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nmodule.exports = __webpack_require__(/*! ./dist/index */ \"(app-pages-browser)/./node_modules/styled-jsx/dist/index/index.js\").style;\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9zdHlsZWQtanN4L3N0eWxlLmpzIiwibWFwcGluZ3MiOiI7QUFBQUEsbUlBQThDIiwic291cmNlcyI6WyIvVXNlcnMvYWF5dXNobWlzaHJhL0Rlc2t0b3Avb2xkIGludmluY2libGUgd2l0aCBkZWVwcmVzZWFyY2gvbm9kZV9tb2R1bGVzL3N0eWxlZC1qc3gvc3R5bGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Rpc3QvaW5kZXgnKS5zdHlsZVxuIl0sIm5hbWVzIjpbIm1vZHVsZSIsImV4cG9ydHMiLCJyZXF1aXJlIiwic3R5bGUiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/styled-jsx/style.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/SocialMediaPreview.tsx":
/*!***********************************************!*\
  !*** ./src/components/SocialMediaPreview.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SocialMediaPreview)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n// Social Media Feed Animation\nfunction SocialMediaIcon() {\n    _s();\n    const [posts, setPosts] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [newPostAnimation, setNewPostAnimation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [totalReach, setTotalReach] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(15420);\n    const [engagementRate, setEngagementRate] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(4.8);\n    const [growthRate, setGrowthRate] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(12.5);\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [currentTypingText, setCurrentTypingText] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');\n    const [showLiveIndicator, setShowLiveIndicator] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const intervalRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const typingRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const postContents = [\n        \"🚀 Just launched our new AI-powered content creation tool! The future of marketing is here.\",\n        \"💡 5 game-changing AI strategies that transformed our content workflow. Thread below 👇\",\n        \"📊 Our latest campaign achieved 340% ROI using AI-generated content. Here's how we did it...\",\n        \"✨ Behind the scenes: How AI is revolutionizing creative processes in 2024\",\n        \"🎯 Pro tip: Combine human creativity with AI efficiency for unbeatable results\",\n        \"🔥 BREAKING: Our AI just went viral! 10M+ views in 24 hours 📈\",\n        \"💬 What's your biggest content creation challenge? Let's solve it together!\",\n        \"🎉 Celebrating 50K followers! Thank you for joining our AI revolution journey\"\n    ];\n    const platforms = [\n        'Twitter',\n        'Instagram',\n        'LinkedIn',\n        'TikTok'\n    ];\n    // Typing animation effect\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"SocialMediaIcon.useEffect\": ()=>{\n            const startTyping = {\n                \"SocialMediaIcon.useEffect.startTyping\": ()=>{\n                    setIsTyping(true);\n                    const randomPost = postContents[Math.floor(Math.random() * postContents.length)];\n                    let charIndex = 0;\n                    const typeChar = {\n                        \"SocialMediaIcon.useEffect.startTyping.typeChar\": ()=>{\n                            if (charIndex < randomPost.length) {\n                                setCurrentTypingText(randomPost.substring(0, charIndex + 1));\n                                charIndex++;\n                                typingRef.current = setTimeout(typeChar, 50 + Math.random() * 100);\n                            } else {\n                                setTimeout({\n                                    \"SocialMediaIcon.useEffect.startTyping.typeChar\": ()=>{\n                                        setIsTyping(false);\n                                        setCurrentTypingText('');\n                                    }\n                                }[\"SocialMediaIcon.useEffect.startTyping.typeChar\"], 2000);\n                            }\n                        }\n                    }[\"SocialMediaIcon.useEffect.startTyping.typeChar\"];\n                    typeChar();\n                }\n            }[\"SocialMediaIcon.useEffect.startTyping\"];\n            // Start typing animation randomly\n            const typingInterval = setInterval({\n                \"SocialMediaIcon.useEffect.typingInterval\": ()=>{\n                    if (Math.random() > 0.7) {\n                        startTyping();\n                    }\n                }\n            }[\"SocialMediaIcon.useEffect.typingInterval\"], 8000);\n            return ({\n                \"SocialMediaIcon.useEffect\": ()=>{\n                    clearInterval(typingInterval);\n                    if (typingRef.current) clearTimeout(typingRef.current);\n                }\n            })[\"SocialMediaIcon.useEffect\"];\n        }\n    }[\"SocialMediaIcon.useEffect\"], []);\n    // Live indicator animation\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"SocialMediaIcon.useEffect\": ()=>{\n            const liveInterval = setInterval({\n                \"SocialMediaIcon.useEffect.liveInterval\": ()=>{\n                    setShowLiveIndicator({\n                        \"SocialMediaIcon.useEffect.liveInterval\": (prev)=>!prev\n                    }[\"SocialMediaIcon.useEffect.liveInterval\"]);\n                }\n            }[\"SocialMediaIcon.useEffect.liveInterval\"], 3000);\n            return ({\n                \"SocialMediaIcon.useEffect\": ()=>clearInterval(liveInterval)\n            })[\"SocialMediaIcon.useEffect\"];\n        }\n    }[\"SocialMediaIcon.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"SocialMediaIcon.useEffect\": ()=>{\n            intervalRef.current = setInterval({\n                \"SocialMediaIcon.useEffect\": ()=>{\n                    setNewPostAnimation(true);\n                    setTimeout({\n                        \"SocialMediaIcon.useEffect\": ()=>{\n                            const newPost = {\n                                id: Date.now(),\n                                content: postContents[Math.floor(Math.random() * postContents.length)],\n                                likes: Math.floor(Math.random() * 1000) + 50,\n                                shares: Math.floor(Math.random() * 50) + 5,\n                                comments: Math.floor(Math.random() * 25) + 2,\n                                platform: platforms[Math.floor(Math.random() * platforms.length)],\n                                isLiking: false,\n                                isTrending: Math.random() > 0.6\n                            };\n                            setPosts({\n                                \"SocialMediaIcon.useEffect\": (prev)=>[\n                                        newPost,\n                                        ...prev.slice(0, 1)\n                                    ]\n                            }[\"SocialMediaIcon.useEffect\"]);\n                            setTotalReach({\n                                \"SocialMediaIcon.useEffect\": (prev)=>prev + Math.floor(Math.random() * 500) + 200\n                            }[\"SocialMediaIcon.useEffect\"]);\n                            setEngagementRate({\n                                \"SocialMediaIcon.useEffect\": (prev)=>Math.min(10, prev + Math.random() * 0.5)\n                            }[\"SocialMediaIcon.useEffect\"]);\n                            setGrowthRate({\n                                \"SocialMediaIcon.useEffect\": (prev)=>Math.min(50, prev + Math.random() * 2)\n                            }[\"SocialMediaIcon.useEffect\"]);\n                            setNotifications({\n                                \"SocialMediaIcon.useEffect\": (prev)=>prev + Math.floor(Math.random() * 3) + 1\n                            }[\"SocialMediaIcon.useEffect\"]);\n                            setNewPostAnimation(false);\n                        }\n                    }[\"SocialMediaIcon.useEffect\"], 2000);\n                    // Update existing posts with like animations\n                    setPosts({\n                        \"SocialMediaIcon.useEffect\": (prev)=>prev.map({\n                                \"SocialMediaIcon.useEffect\": (post)=>({\n                                        ...post,\n                                        likes: post.likes + Math.floor(Math.random() * 10) + 2,\n                                        shares: post.shares + Math.floor(Math.random() * 3) + 1,\n                                        comments: post.comments + Math.floor(Math.random() * 5) + 1,\n                                        isLiking: Math.random() > 0.7,\n                                        isTrending: post.isTrending || Math.random() > 0.8\n                                    })\n                            }[\"SocialMediaIcon.useEffect\"])\n                    }[\"SocialMediaIcon.useEffect\"]);\n                    // Reset like animations\n                    setTimeout({\n                        \"SocialMediaIcon.useEffect\": ()=>{\n                            setPosts({\n                                \"SocialMediaIcon.useEffect\": (prev)=>prev.map({\n                                        \"SocialMediaIcon.useEffect\": (post)=>({\n                                                ...post,\n                                                isLiking: false\n                                            })\n                                    }[\"SocialMediaIcon.useEffect\"])\n                            }[\"SocialMediaIcon.useEffect\"]);\n                        }\n                    }[\"SocialMediaIcon.useEffect\"], 1000);\n                }\n            }[\"SocialMediaIcon.useEffect\"], 5000);\n            return ({\n                \"SocialMediaIcon.useEffect\": ()=>{\n                    if (intervalRef.current) clearInterval(intervalRef.current);\n                }\n            })[\"SocialMediaIcon.useEffect\"];\n        }\n    }[\"SocialMediaIcon.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-7b375792afc2c2e5\" + \" \" + \"relative w-full h-full flex items-center justify-center p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-7b375792afc2c2e5\" + \" \" + \"absolute inset-0 bg-gradient-to-br from-gray-900/40 via-black/60 to-gray-900/40 backdrop-blur-2xl\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                lineNumber: 125,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-7b375792afc2c2e5\" + \" \" + \"relative z-10 w-full max-w-xs\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-7b375792afc2c2e5\" + \" \" + \"w-full h-[340px] bg-black/30 backdrop-blur-2xl rounded-2xl border border-white/10 shadow-2xl relative overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-7b375792afc2c2e5\" + \" \" + \"absolute inset-2 bg-black/90 backdrop-blur-2xl rounded-xl overflow-hidden border border-white/5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-7b375792afc2c2e5\" + \" \" + \"h-6 bg-black/60 backdrop-blur-2xl flex items-center justify-between px-3 text-white/80 text-xs border-b border-white/5\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-7b375792afc2c2e5\" + \" \" + \"font-medium\",\n                                                children: \"9:41 AM\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-7b375792afc2c2e5\" + \" \" + \"flex items-center space-x-1\",\n                                                children: [\n                                                    notifications > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-7b375792afc2c2e5\" + \" \" + \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-7b375792afc2c2e5\" + \" \" + \"w-4 h-2 bg-green-400 rounded-sm\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                                lineNumber: 140,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-7b375792afc2c2e5\" + \" \" + \"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-7b375792afc2c2e5\" + \" \" + \"text-white text-xs font-bold\",\n                                                                    children: notifications > 9 ? '9+' : notifications\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                                    lineNumber: 142,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                                lineNumber: 141,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                        lineNumber: 139,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-7b375792afc2c2e5\" + \" \" + \"font-medium\",\n                                                        children: \"100%\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                        lineNumber: 146,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-7b375792afc2c2e5\" + \" \" + \"h-12 bg-black/50 backdrop-blur-2xl flex items-center justify-between px-3 border-b border-white/5\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-7b375792afc2c2e5\" + \" \" + \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-7b375792afc2c2e5\" + \" \" + \"w-6 h-6 bg-blue-500/20 backdrop-blur-xl rounded-lg flex items-center justify-center border border-blue-500/30\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-7b375792afc2c2e5\" + \" \" + \"text-blue-400 font-bold text-sm\",\n                                                            children: \"\\uD83D\\uDCF1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                            lineNumber: 154,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                        lineNumber: 153,\n                                                        columnNumber: 35\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"jsx-7b375792afc2c2e5\" + \" \" + \"text-white/90 font-bold text-sm\",\n                                                        children: \"Social Hub\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                        lineNumber: 156,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 15\n                                            }, this),\n                                            showLiveIndicator && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-7b375792afc2c2e5\" + \" \" + \"flex items-center space-x-1 bg-red-500/20 backdrop-blur-2xl px-2 py-1 rounded-full border border-red-500/20\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-7b375792afc2c2e5\" + \" \" + \"w-2 h-2 bg-red-400/80 rounded-full animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-7b375792afc2c2e5\" + \" \" + \"text-red-400 text-xs font-bold\",\n                                                        children: \"LIVE\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 13\n                                    }, this),\n                                    newPostAnimation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-7b375792afc2c2e5\" + \" \" + \"absolute top-18 left-2 right-2 h-16 bg-gradient-to-r from-blue-500/90 to-cyan-500/90 backdrop-blur-xl rounded-lg flex items-center justify-center animate-bounce z-10 border border-white/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-7b375792afc2c2e5\" + \" \" + \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-7b375792afc2c2e5\" + \" \" + \"text-lg mb-1 animate-spin\",\n                                                    children: \"✨\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-7b375792afc2c2e5\" + \" \" + \"text-white font-semibold text-xs\",\n                                                    children: \"Creating Viral Content...\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 15\n                                    }, this),\n                                    isTyping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-7b375792afc2c2e5\" + \" \" + \"absolute top-20 left-2 right-2 bg-blue-500/90 backdrop-blur-xl rounded-lg p-2 z-10 border border-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-7b375792afc2c2e5\" + \" \" + \"flex items-center space-x-2 mb-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-7b375792afc2c2e5\" + \" \" + \"w-4 h-4 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-7b375792afc2c2e5\" + \" \" + \"text-white text-xs font-semibold\",\n                                                        children: \"AI is typing...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-7b375792afc2c2e5\" + \" \" + \"flex space-x-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-7b375792afc2c2e5\" + \" \" + \"w-1 h-1 bg-white rounded-full animate-bounce\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                                lineNumber: 185,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    animationDelay: '0.2s'\n                                                                },\n                                                                className: \"jsx-7b375792afc2c2e5\" + \" \" + \"w-1 h-1 bg-white rounded-full animate-bounce\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                                lineNumber: 186,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    animationDelay: '0.4s'\n                                                                },\n                                                                className: \"jsx-7b375792afc2c2e5\" + \" \" + \"w-1 h-1 bg-white rounded-full animate-bounce\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                                lineNumber: 187,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-7b375792afc2c2e5\" + \" \" + \"text-white text-xs bg-white/10 rounded p-1\",\n                                                children: [\n                                                    currentTypingText,\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-7b375792afc2c2e5\" + \" \" + \"animate-pulse\",\n                                                        children: \"|\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 38\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-7b375792afc2c2e5\" + \" \" + \"p-2 space-y-2 overflow-hidden h-full\",\n                                        children: posts.map((post, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-7b375792afc2c2e5\" + \" \" + \"bg-black/30 backdrop-blur-2xl rounded-lg p-3 border border-white/10 transition-all duration-500 relative \".concat(index === 0 ? 'animate-slideInFromTop' : '', \" \").concat(post.isTrending ? 'ring-2 ring-yellow-400/30' : ''),\n                                                children: [\n                                                    post.isTrending && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-7b375792afc2c2e5\" + \" \" + \"absolute -top-1 -right-1 bg-yellow-500/20 backdrop-blur-2xl text-yellow-400 px-2 py-1 rounded-full text-xs font-bold animate-pulse border border-yellow-500/20\",\n                                                        children: \"\\uD83D\\uDD25 TRENDING\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-7b375792afc2c2e5\" + \" \" + \"flex items-center space-x-2 mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-7b375792afc2c2e5\" + \" \" + \"w-6 h-6 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-7b375792afc2c2e5\" + \" \" + \"text-white text-xs font-bold\",\n                                                                        children: \"AI\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                                        lineNumber: 214,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-7b375792afc2c2e5\" + \" \" + \"absolute -bottom-0.5 -right-0.5 w-2 h-2 bg-green-400 rounded-full border border-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                                        lineNumber: 216,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                                lineNumber: 213,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-7b375792afc2c2e5\" + \" \" + \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-7b375792afc2c2e5\" + \" \" + \"flex items-center space-x-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"jsx-7b375792afc2c2e5\" + \" \" + \"text-white/90 text-xs font-semibold\",\n                                                                                children: \"AI Content Creator\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                                                lineNumber: 220,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-7b375792afc2c2e5\" + \" \" + \"w-3 h-3 bg-blue-500/60 rounded-full flex items-center justify-center\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-7b375792afc2c2e5\" + \" \" + \"text-white/80 text-xs\",\n                                                                                    children: \"✓\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                                                    lineNumber: 222,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                                                lineNumber: 221,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                                        lineNumber: 219,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-7b375792afc2c2e5\" + \" \" + \"text-white/50 text-xs\",\n                                                                        children: [\n                                                                            post.platform,\n                                                                            \" • 2m ago\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                                        lineNumber: 225,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                                lineNumber: 218,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"jsx-7b375792afc2c2e5\" + \" \" + \"text-white/70 text-xs mb-2 leading-relaxed line-clamp-2\",\n                                                        children: postContents[index % postContents.length]\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-7b375792afc2c2e5\" + \" \" + \"flex justify-between text-xs\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-7b375792afc2c2e5\" + \" \" + \"flex items-center space-x-1 text-red-400 transition-all \".concat(post.isLiking ? 'scale-125' : ''),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-7b375792afc2c2e5\" + \" \" + ((post.isLiking ? 'animate-bounce' : '') || \"\"),\n                                                                        children: \"❤️\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                                        lineNumber: 235,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-7b375792afc2c2e5\" + \" \" + \"animate-pulse font-medium\",\n                                                                        children: post.likes.toLocaleString()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                                        lineNumber: 236,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    post.isLiking && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-7b375792afc2c2e5\" + \" \" + \"absolute\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-7b375792afc2c2e5\" + \" \" + \"text-red-400 animate-ping\",\n                                                                            children: \"\\uD83D\\uDC96\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                                            lineNumber: 239,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                                        lineNumber: 238,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                                lineNumber: 234,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-7b375792afc2c2e5\" + \" \" + \"flex items-center space-x-1 text-green-400\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            animationDuration: '3s'\n                                                                        },\n                                                                        className: \"jsx-7b375792afc2c2e5\" + \" \" + \"animate-spin\",\n                                                                        children: \"\\uD83D\\uDD04\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                                        lineNumber: 244,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-7b375792afc2c2e5\" + \" \" + \"animate-pulse font-medium\",\n                                                                        children: post.shares\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                                        lineNumber: 245,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                                lineNumber: 243,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-7b375792afc2c2e5\" + \" \" + \"flex items-center space-x-1 text-blue-400\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-7b375792afc2c2e5\",\n                                                                        children: \"\\uD83D\\uDCAC\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                                        lineNumber: 248,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-7b375792afc2c2e5\" + \" \" + \"animate-pulse font-medium\",\n                                                                        children: post.comments\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                                        lineNumber: 249,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                                lineNumber: 247,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    post.isLiking && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-7b375792afc2c2e5\" + \" \" + \"absolute inset-0 pointer-events-none\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-7b375792afc2c2e5\" + \" \" + \"absolute top-2 right-2 text-red-400 animate-bounce\",\n                                                                children: \"\\uD83D\\uDC95\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                                lineNumber: 256,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    animationDelay: '0.2s'\n                                                                },\n                                                                className: \"jsx-7b375792afc2c2e5\" + \" \" + \"absolute bottom-2 left-2 text-yellow-400 animate-bounce\",\n                                                                children: \"⭐\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                                lineNumber: 257,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    animationDelay: '0.4s'\n                                                                },\n                                                                className: \"jsx-7b375792afc2c2e5\" + \" \" + \"absolute top-1/2 left-1/2 text-blue-400 animate-bounce\",\n                                                                children: \"✨\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                                lineNumber: 258,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                        lineNumber: 255,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, post.id, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-7b375792afc2c2e5\" + \" \" + \"absolute bottom-3 left-1/2 transform -translate-x-1/2 w-8 h-8 bg-white/20 backdrop-blur-xl rounded-full border border-white/30 animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-7b375792afc2c2e5\" + \" \" + \"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent pointer-events-none\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-7b375792afc2c2e5\" + \" \" + \"mt-3 flex space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-7b375792afc2c2e5\" + \" \" + \"bg-white/10 backdrop-blur-xl rounded-lg border border-white/20 p-2 flex-1 relative overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-7b375792afc2c2e5\" + \" \" + \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-7b375792afc2c2e5\" + \" \" + \"text-lg font-bold text-blue-400 mb-1 animate-pulse\",\n                                                children: totalReach.toLocaleString()\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-7b375792afc2c2e5\" + \" \" + \"text-white/70 text-xs\",\n                                                children: \"Reach\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-7b375792afc2c2e5\" + \" \" + \"absolute top-1 right-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-7b375792afc2c2e5\" + \" \" + \"w-2 h-2 bg-green-400 rounded-full animate-ping\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-7b375792afc2c2e5\" + \" \" + \"bg-white/10 backdrop-blur-xl rounded-lg border border-white/20 p-2 flex-1 relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-7b375792afc2c2e5\" + \" \" + \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-7b375792afc2c2e5\" + \" \" + \"text-lg font-bold text-cyan-400 mb-1\",\n                                                children: [\n                                                    engagementRate.toFixed(1),\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-7b375792afc2c2e5\" + \" \" + \"text-white/70 text-xs\",\n                                                children: \"Engagement\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-7b375792afc2c2e5\" + \" \" + \"absolute top-1 right-1 text-green-400 text-xs animate-bounce\",\n                                        children: \"↗️\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-7b375792afc2c2e5\" + \" \" + \"bg-white/10 backdrop-blur-xl rounded-lg border border-white/20 p-2 flex-1 relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-7b375792afc2c2e5\" + \" \" + \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-7b375792afc2c2e5\" + \" \" + \"text-lg font-bold text-blue-500 mb-1\",\n                                                children: [\n                                                    \"+\",\n                                                    growthRate.toFixed(1),\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-7b375792afc2c2e5\" + \" \" + \"text-white/70 text-xs\",\n                                                children: \"Growth\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 13\n                                    }, this),\n                                    growthRate > 20 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-7b375792afc2c2e5\" + \" \" + \"absolute top-1 right-1 text-orange-400 text-xs animate-pulse\",\n                                        children: \"\\uD83D\\uDD25\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-7b375792afc2c2e5\" + \" \" + \"absolute -top-2 -left-2 bg-blue-500/80 backdrop-blur-xl rounded-full w-10 h-10 flex items-center justify-center animate-float border border-white/20 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"jsx-7b375792afc2c2e5\" + \" \" + \"text-white text-sm\",\n                                children: \"\\uD83D\\uDCD8\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-7b375792afc2c2e5\" + \" \" + \"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-ping\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                        lineNumber: 306,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            animationDelay: '0.3s'\n                        },\n                        className: \"jsx-7b375792afc2c2e5\" + \" \" + \"absolute -top-1 -right-3 bg-cyan-500/80 backdrop-blur-xl rounded-full w-8 h-8 flex items-center justify-center animate-float border border-white/20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"jsx-7b375792afc2c2e5\" + \" \" + \"text-white text-xs\",\n                            children: \"\\uD83D\\uDCF7\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            animationDelay: '0.6s'\n                        },\n                        className: \"jsx-7b375792afc2c2e5\" + \" \" + \"absolute -bottom-2 -left-3 bg-blue-600/80 backdrop-blur-xl rounded-full w-8 h-8 flex items-center justify-center animate-float border border-white/20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            style: {\n                                animationDuration: '4s'\n                            },\n                            className: \"jsx-7b375792afc2c2e5\" + \" \" + \"text-white text-xs animate-spin\",\n                            children: \"\\uD83C\\uDFA5\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                        lineNumber: 313,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            animationDelay: '0.9s'\n                        },\n                        className: \"jsx-7b375792afc2c2e5\" + \" \" + \"absolute -bottom-1 -right-2 bg-cyan-600/80 backdrop-blur-xl rounded-full w-10 h-10 flex items-center justify-center animate-float border border-white/20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"jsx-7b375792afc2c2e5\" + \" \" + \"text-white text-sm\",\n                            children: \"\\uD83D\\uDCAC\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                            lineNumber: 317,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                        lineNumber: 316,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-7b375792afc2c2e5\" + \" \" + \"absolute top-4 -right-16 bg-white/10 backdrop-blur-xl rounded-lg border border-white/20 p-2 text-white text-xs\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-7b375792afc2c2e5\" + \" \" + \"space-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-7b375792afc2c2e5\" + \" \" + \"flex items-center space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"jsx-7b375792afc2c2e5\" + \" \" + \"animate-bounce\",\n                                            children: \"\\uD83D\\uDC65\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"jsx-7b375792afc2c2e5\" + \" \" + \"animate-pulse font-medium\",\n                                            children: \"2.8K\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-7b375792afc2c2e5\" + \" \" + \"flex items-center space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"jsx-7b375792afc2c2e5\" + \" \" + \"text-green-400\",\n                                            children: \"\\uD83D\\uDCC8\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"jsx-7b375792afc2c2e5\" + \" \" + \"animate-pulse font-medium text-green-400\",\n                                            children: \"+24%\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-7b375792afc2c2e5\" + \" \" + \"flex items-center space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"jsx-7b375792afc2c2e5\" + \" \" + \"animate-pulse\",\n                                            children: \"\\uD83D\\uDD25\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"jsx-7b375792afc2c2e5\" + \" \" + \"animate-pulse font-medium text-orange-400\",\n                                            children: \"Viral\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                            lineNumber: 322,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                        lineNumber: 321,\n                        columnNumber: 9\n                    }, this),\n                    notifications > 5 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-7b375792afc2c2e5\" + \" \" + \"absolute top-8 left-1/2 transform -translate-x-1/2 bg-blue-500/90 backdrop-blur-xl text-white px-3 py-1 rounded-full text-xs font-bold animate-bounce border border-white/20\",\n                        children: \"\\uD83C\\uDF89 Going Viral!\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n                lineNumber: 128,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"7b375792afc2c2e5\",\n                children: \"@-webkit-keyframes slideInFromTop{0%{-webkit-transform:translatey(-100%);transform:translatey(-100%);opacity:0}100%{-webkit-transform:translatey(0);transform:translatey(0);opacity:1}}@-moz-keyframes slideInFromTop{0%{-moz-transform:translatey(-100%);transform:translatey(-100%);opacity:0}100%{-moz-transform:translatey(0);transform:translatey(0);opacity:1}}@-o-keyframes slideInFromTop{0%{-o-transform:translatey(-100%);transform:translatey(-100%);opacity:0}100%{-o-transform:translatey(0);transform:translatey(0);opacity:1}}@keyframes slideInFromTop{0%{-webkit-transform:translatey(-100%);-moz-transform:translatey(-100%);-o-transform:translatey(-100%);transform:translatey(-100%);opacity:0}100%{-webkit-transform:translatey(0);-moz-transform:translatey(0);-o-transform:translatey(0);transform:translatey(0);opacity:1}}@-webkit-keyframes float{0%,100%{-webkit-transform:translatey(0px);transform:translatey(0px)}50%{-webkit-transform:translatey(-5px);transform:translatey(-5px)}}@-moz-keyframes float{0%,100%{-moz-transform:translatey(0px);transform:translatey(0px)}50%{-moz-transform:translatey(-5px);transform:translatey(-5px)}}@-o-keyframes float{0%,100%{-o-transform:translatey(0px);transform:translatey(0px)}50%{-o-transform:translatey(-5px);transform:translatey(-5px)}}@keyframes float{0%,100%{-webkit-transform:translatey(0px);-moz-transform:translatey(0px);-o-transform:translatey(0px);transform:translatey(0px)}50%{-webkit-transform:translatey(-5px);-moz-transform:translatey(-5px);-o-transform:translatey(-5px);transform:translatey(-5px)}}.animate-slideInFromTop.jsx-7b375792afc2c2e5{-webkit-animation:slideInFromTop.6s ease-out;-moz-animation:slideInFromTop.6s ease-out;-o-animation:slideInFromTop.6s ease-out;animation:slideInFromTop.6s ease-out}.animate-float.jsx-7b375792afc2c2e5{-webkit-animation:float 3s ease-in-out infinite;-moz-animation:float 3s ease-in-out infinite;-o-animation:float 3s ease-in-out infinite;animation:float 3s ease-in-out infinite}.line-clamp-2.jsx-7b375792afc2c2e5{display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;overflow:hidden}\"\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n        lineNumber: 123,\n        columnNumber: 5\n    }, this);\n}\n_s(SocialMediaIcon, \"GH8omOh7Spbrk0SVCb9fDGQ9FIs=\");\n_c = SocialMediaIcon;\nfunction SocialMediaPreview() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full h-full bg-gradient-to-br from-gray-900 via-blue-900 to-cyan-900\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SocialMediaIcon, {}, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n            lineNumber: 385,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SocialMediaPreview.tsx\",\n        lineNumber: 384,\n        columnNumber: 5\n    }, this);\n}\n_c1 = SocialMediaPreview;\nvar _c, _c1;\n$RefreshReg$(_c, \"SocialMediaIcon\");\n$RefreshReg$(_c1, \"SocialMediaPreview\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL1NvY2lhbE1lZGlhUHJldmlldy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBRW1EO0FBRW5ELDhCQUE4QjtBQUM5QixTQUFTRzs7SUFDUCxNQUFNLENBQUNDLE9BQU9DLFNBQVMsR0FBR0osK0NBQVFBLENBQW9KLEVBQUU7SUFDeEwsTUFBTSxDQUFDSyxrQkFBa0JDLG9CQUFvQixHQUFHTiwrQ0FBUUEsQ0FBQztJQUN6RCxNQUFNLENBQUNPLFlBQVlDLGNBQWMsR0FBR1IsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDUyxnQkFBZ0JDLGtCQUFrQixHQUFHViwrQ0FBUUEsQ0FBQztJQUNyRCxNQUFNLENBQUNXLFlBQVlDLGNBQWMsR0FBR1osK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDYSxlQUFlQyxpQkFBaUIsR0FBR2QsK0NBQVFBLENBQUM7SUFDbkQsTUFBTSxDQUFDZSxVQUFVQyxZQUFZLEdBQUdoQiwrQ0FBUUEsQ0FBQztJQUN6QyxNQUFNLENBQUNpQixtQkFBbUJDLHFCQUFxQixHQUFHbEIsK0NBQVFBLENBQUM7SUFDM0QsTUFBTSxDQUFDbUIsbUJBQW1CQyxxQkFBcUIsR0FBR3BCLCtDQUFRQSxDQUFDO0lBQzNELE1BQU1xQixjQUFjdEIsNkNBQU1BLENBQXdCO0lBQ2xELE1BQU11QixZQUFZdkIsNkNBQU1BLENBQXdCO0lBRWhELE1BQU13QixlQUFlO1FBQ25CO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7S0FDRDtJQUVELE1BQU1DLFlBQVk7UUFBQztRQUFXO1FBQWE7UUFBWTtLQUFTO0lBRWhFLDBCQUEwQjtJQUMxQnZCLGdEQUFTQTtxQ0FBQztZQUNSLE1BQU13Qjt5REFBYztvQkFDbEJULFlBQVk7b0JBQ1osTUFBTVUsYUFBYUgsWUFBWSxDQUFDSSxLQUFLQyxLQUFLLENBQUNELEtBQUtFLE1BQU0sS0FBS04sYUFBYU8sTUFBTSxFQUFFO29CQUNoRixJQUFJQyxZQUFZO29CQUVoQixNQUFNQzswRUFBVzs0QkFDZixJQUFJRCxZQUFZTCxXQUFXSSxNQUFNLEVBQUU7Z0NBQ2pDWixxQkFBcUJRLFdBQVdPLFNBQVMsQ0FBQyxHQUFHRixZQUFZO2dDQUN6REE7Z0NBQ0FULFVBQVVZLE9BQU8sR0FBR0MsV0FBV0gsVUFBVSxLQUFLTCxLQUFLRSxNQUFNLEtBQUs7NEJBQ2hFLE9BQU87Z0NBQ0xNO3NGQUFXO3dDQUNUbkIsWUFBWTt3Q0FDWkUscUJBQXFCO29DQUN2QjtxRkFBRzs0QkFDTDt3QkFDRjs7b0JBRUFjO2dCQUNGOztZQUVBLGtDQUFrQztZQUNsQyxNQUFNSSxpQkFBaUJDOzREQUFZO29CQUNqQyxJQUFJVixLQUFLRSxNQUFNLEtBQUssS0FBSzt3QkFDdkJKO29CQUNGO2dCQUNGOzJEQUFHO1lBRUg7NkNBQU87b0JBQ0xhLGNBQWNGO29CQUNkLElBQUlkLFVBQVVZLE9BQU8sRUFBRUssYUFBYWpCLFVBQVVZLE9BQU87Z0JBQ3ZEOztRQUNGO29DQUFHLEVBQUU7SUFFTCwyQkFBMkI7SUFDM0JqQyxnREFBU0E7cUNBQUM7WUFDUixNQUFNdUMsZUFBZUg7MERBQVk7b0JBQy9CakI7a0VBQXFCcUIsQ0FBQUEsT0FBUSxDQUFDQTs7Z0JBQ2hDO3lEQUFHO1lBRUg7NkNBQU8sSUFBTUgsY0FBY0U7O1FBQzdCO29DQUFHLEVBQUU7SUFFTHZDLGdEQUFTQTtxQ0FBQztZQUNSb0IsWUFBWWEsT0FBTyxHQUFHRzs2Q0FBWTtvQkFDaEMvQixvQkFBb0I7b0JBRXBCNkI7cURBQVc7NEJBQ1QsTUFBTU8sVUFBVTtnQ0FDZEMsSUFBSUMsS0FBS0MsR0FBRztnQ0FDWkMsU0FBU3ZCLFlBQVksQ0FBQ0ksS0FBS0MsS0FBSyxDQUFDRCxLQUFLRSxNQUFNLEtBQUtOLGFBQWFPLE1BQU0sRUFBRTtnQ0FDdEVpQixPQUFPcEIsS0FBS0MsS0FBSyxDQUFDRCxLQUFLRSxNQUFNLEtBQUssUUFBUTtnQ0FDMUNtQixRQUFRckIsS0FBS0MsS0FBSyxDQUFDRCxLQUFLRSxNQUFNLEtBQUssTUFBTTtnQ0FDekNvQixVQUFVdEIsS0FBS0MsS0FBSyxDQUFDRCxLQUFLRSxNQUFNLEtBQUssTUFBTTtnQ0FDM0NxQixVQUFVMUIsU0FBUyxDQUFDRyxLQUFLQyxLQUFLLENBQUNELEtBQUtFLE1BQU0sS0FBS0wsVUFBVU0sTUFBTSxFQUFFO2dDQUNqRXFCLFVBQVU7Z0NBQ1ZDLFlBQVl6QixLQUFLRSxNQUFNLEtBQUs7NEJBQzlCOzRCQUVBekI7NkRBQVNxQyxDQUFBQSxPQUFRO3dDQUFDQzsyQ0FBWUQsS0FBS1ksS0FBSyxDQUFDLEdBQUc7cUNBQUc7OzRCQUMvQzdDOzZEQUFjaUMsQ0FBQUEsT0FBUUEsT0FBT2QsS0FBS0MsS0FBSyxDQUFDRCxLQUFLRSxNQUFNLEtBQUssT0FBTzs7NEJBQy9EbkI7NkRBQWtCK0IsQ0FBQUEsT0FBUWQsS0FBSzJCLEdBQUcsQ0FBQyxJQUFJYixPQUFPZCxLQUFLRSxNQUFNLEtBQUs7OzRCQUM5RGpCOzZEQUFjNkIsQ0FBQUEsT0FBUWQsS0FBSzJCLEdBQUcsQ0FBQyxJQUFJYixPQUFPZCxLQUFLRSxNQUFNLEtBQUs7OzRCQUMxRGY7NkRBQWlCMkIsQ0FBQUEsT0FBUUEsT0FBT2QsS0FBS0MsS0FBSyxDQUFDRCxLQUFLRSxNQUFNLEtBQUssS0FBSzs7NEJBQ2hFdkIsb0JBQW9CO3dCQUN0QjtvREFBRztvQkFFSCw2Q0FBNkM7b0JBQzdDRjtxREFBU3FDLENBQUFBLE9BQVFBLEtBQUtjLEdBQUc7NkRBQUNDLENBQUFBLE9BQVM7d0NBQ2pDLEdBQUdBLElBQUk7d0NBQ1BULE9BQU9TLEtBQUtULEtBQUssR0FBR3BCLEtBQUtDLEtBQUssQ0FBQ0QsS0FBS0UsTUFBTSxLQUFLLE1BQU07d0NBQ3JEbUIsUUFBUVEsS0FBS1IsTUFBTSxHQUFHckIsS0FBS0MsS0FBSyxDQUFDRCxLQUFLRSxNQUFNLEtBQUssS0FBSzt3Q0FDdERvQixVQUFVTyxLQUFLUCxRQUFRLEdBQUd0QixLQUFLQyxLQUFLLENBQUNELEtBQUtFLE1BQU0sS0FBSyxLQUFLO3dDQUMxRHNCLFVBQVV4QixLQUFLRSxNQUFNLEtBQUs7d0NBQzFCdUIsWUFBWUksS0FBS0osVUFBVSxJQUFJekIsS0FBS0UsTUFBTSxLQUFLO29DQUNqRDs7O29CQUVBLHdCQUF3QjtvQkFDeEJNO3FEQUFXOzRCQUNUL0I7NkRBQVNxQyxDQUFBQSxPQUFRQSxLQUFLYyxHQUFHO3FFQUFDQyxDQUFBQSxPQUFTO2dEQUFFLEdBQUdBLElBQUk7Z0RBQUVMLFVBQVU7NENBQU07Ozt3QkFDaEU7b0RBQUc7Z0JBQ0w7NENBQUc7WUFFSDs2Q0FBTztvQkFDTCxJQUFJOUIsWUFBWWEsT0FBTyxFQUFFSSxjQUFjakIsWUFBWWEsT0FBTztnQkFDNUQ7O1FBQ0Y7b0NBQUcsRUFBRTtJQUVMLHFCQUNFLDhEQUFDdUI7a0RBQWM7OzBCQUViLDhEQUFDQTswREFBYzs7Ozs7OzBCQUdmLDhEQUFDQTswREFBYzs7a0NBRWIsOERBQUNBO2tFQUFjOzswQ0FFYiw4REFBQ0E7MEVBQWM7O2tEQUViLDhEQUFDQTtrRkFBYzs7MERBQ2IsOERBQUNDOzBGQUFlOzBEQUFjOzs7Ozs7MERBQzlCLDhEQUFDRDswRkFBYzs7b0RBRVo1QyxnQkFBZ0IsbUJBQ2YsOERBQUM0QztrR0FBYzs7MEVBQ2IsOERBQUNBOzBHQUFjOzs7Ozs7MEVBQ2YsOERBQUNBOzBHQUFjOzBFQUNiLDRFQUFDQzs4R0FBZTs4RUFBZ0M3QyxnQkFBZ0IsSUFBSSxPQUFPQTs7Ozs7Ozs7Ozs7Ozs7Ozs7a0VBSWpGLDhEQUFDNkM7a0dBQWU7a0VBQWM7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFLbEMsOERBQUNEO2tGQUFjOzswREFDYiw4REFBQ0E7MEZBQWM7O2tFQUNLLDhEQUFDQTtrR0FBYztrRUFDN0IsNEVBQUNDO3NHQUFlO3NFQUFrQzs7Ozs7Ozs7Ozs7a0VBRXBELDhEQUFDQztrR0FBYTtrRUFBa0M7Ozs7Ozs7Ozs7Ozs0Q0FJbkN4QyxtQ0FDYiw4REFBQ3NDOzBGQUFjOztrRUFDYiw4REFBQ0E7a0dBQWM7Ozs7OztrRUFDZiw4REFBQ0M7a0dBQWU7a0VBQWlDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7b0NBTXhEckQsa0NBQ0MsOERBQUNvRDtrRkFBYztrREFDYiw0RUFBQ0E7c0ZBQWM7OzhEQUNiLDhEQUFDQTs4RkFBYzs4REFBNEI7Ozs7Ozs4REFDM0MsOERBQUNDOzhGQUFlOzhEQUFtQzs7Ozs7Ozs7Ozs7Ozs7Ozs7b0NBTXhEM0MsMEJBQ0MsOERBQUMwQztrRkFBYzs7MERBQ2IsOERBQUNBOzBGQUFjOztrRUFDYiw4REFBQ0E7a0dBQWM7Ozs7OztrRUFDZiw4REFBQ0M7a0dBQWU7a0VBQW1DOzs7Ozs7a0VBQ25ELDhEQUFDRDtrR0FBYzs7MEVBQ2IsOERBQUNBOzBHQUFjOzs7Ozs7MEVBQ2YsOERBQUNBO2dFQUE2REcsT0FBTztvRUFBRUMsZ0JBQWdCO2dFQUFPOzBHQUEvRTs7Ozs7OzBFQUNmLDhEQUFDSjtnRUFBNkRHLE9BQU87b0VBQUVDLGdCQUFnQjtnRUFBTzswR0FBL0U7Ozs7Ozs7Ozs7Ozs7Ozs7OzswREFHbkIsOERBQUNKOzBGQUFjOztvREFDWnhDO2tFQUFrQiw4REFBQ3lDO2tHQUFlO2tFQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQU16RCw4REFBQ0Q7a0ZBQWM7a0RBQ1p0RCxNQUFNb0QsR0FBRyxDQUFDLENBQUNDLE1BQU1NLHNCQUNoQiw4REFBQ0w7MEZBRVksNEdBRVBELE9BREZNLFVBQVUsSUFBSSwyQkFBMkIsSUFDMUMsS0FBc0QsT0FBbkROLEtBQUtKLFVBQVUsR0FBRyw4QkFBOEI7O29EQUduREksS0FBS0osVUFBVSxrQkFDZCw4REFBQ0s7a0dBQWM7a0VBQWlLOzs7Ozs7a0VBS2xMLDhEQUFDQTtrR0FBYzs7MEVBQ2IsOERBQUNBOzBHQUFjOztrRkFDYiw4REFBQ0M7a0hBQWU7a0ZBQStCOzs7Ozs7a0ZBRS9DLDhEQUFDRDtrSEFBYzs7Ozs7Ozs7Ozs7OzBFQUVqQiw4REFBQ0E7MEdBQWM7O2tGQUNiLDhEQUFDQTtrSEFBYzs7MEZBQ2IsOERBQUNDOzBIQUFlOzBGQUFzQzs7Ozs7OzBGQUN0RCw4REFBQ0Q7MEhBQWM7MEZBQ2IsNEVBQUNDOzhIQUFlOzhGQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7a0ZBRzVDLDhEQUFDRDtrSEFBYzs7NEVBQXlCRCxLQUFLTixRQUFROzRFQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tFQUkxRCw4REFBQ2E7a0dBQVk7a0VBQ1Z4QyxZQUFZLENBQUN1QyxRQUFRdkMsYUFBYU8sTUFBTSxDQUFDOzs7Ozs7a0VBRzVDLDhEQUFDMkI7a0dBQWM7OzBFQUNiLDhEQUFDQTswR0FBZSwyREFBNEYsT0FBakNELEtBQUtMLFFBQVEsR0FBRyxjQUFjOztrRkFDdkcsOERBQUNPO21IQUFnQkYsQ0FBQUEsS0FBS0wsUUFBUSxHQUFHLG1CQUFtQixFQUFDO2tGQUFHOzs7Ozs7a0ZBQ3hELDhEQUFDTztrSEFBZTtrRkFBNkJGLEtBQUtULEtBQUssQ0FBQ2lCLGNBQWM7Ozs7OztvRUFDckVSLEtBQUtMLFFBQVEsa0JBQ1osOERBQUNNO2tIQUFjO2tGQUNiLDRFQUFDQztzSEFBZTtzRkFBNEI7Ozs7Ozs7Ozs7Ozs7Ozs7OzBFQUlsRCw4REFBQ0Q7MEdBQWM7O2tGQUNiLDhEQUFDQzt3RUFBOEJFLE9BQU87NEVBQUVLLG1CQUFtQjt3RUFBSztrSEFBaEQ7a0ZBQW1EOzs7Ozs7a0ZBQ25FLDhEQUFDUDtrSEFBZTtrRkFBNkJGLEtBQUtSLE1BQU07Ozs7Ozs7Ozs7OzswRUFFMUQsOERBQUNTOzBHQUFjOztrRkFDYiw4REFBQ0M7O2tGQUFLOzs7Ozs7a0ZBQ04sOERBQUNBO2tIQUFlO2tGQUE2QkYsS0FBS1AsUUFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7O29EQUs3RE8sS0FBS0wsUUFBUSxrQkFDWiw4REFBQ007a0dBQWM7OzBFQUNiLDhEQUFDQTswR0FBYzswRUFBcUQ7Ozs7OzswRUFDcEUsOERBQUNBO2dFQUF3RUcsT0FBTztvRUFBRUMsZ0JBQWdCO2dFQUFPOzBHQUExRjswRUFBNkY7Ozs7OzswRUFDNUcsOERBQUNKO2dFQUF1RUcsT0FBTztvRUFBRUMsZ0JBQWdCO2dFQUFPOzBHQUF6RjswRUFBNEY7Ozs7Ozs7Ozs7Ozs7K0NBMUQxR0wsS0FBS2IsRUFBRTs7Ozs7Ozs7Ozs7Ozs7OzswQ0FtRXBCLDhEQUFDYzswRUFBYzs7Ozs7OzBDQUdmLDhEQUFDQTswRUFBYzs7Ozs7Ozs7Ozs7O2tDQUlqQiw4REFBQ0E7a0VBQWM7OzBDQUNiLDhEQUFDQTswRUFBYzs7a0RBQ2IsOERBQUNBO2tGQUFjOzswREFDYiw4REFBQ0E7MEZBQWM7MERBQXNEbEQsV0FBV3lELGNBQWM7Ozs7OzswREFDOUYsOERBQUNQOzBGQUFjOzBEQUF3Qjs7Ozs7Ozs7Ozs7O2tEQUd6Qyw4REFBQ0E7a0ZBQWM7a0RBQ2IsNEVBQUNBO3NGQUFjOzs7Ozs7Ozs7Ozs7Ozs7OzswQ0FHbkIsOERBQUNBOzBFQUFjOztrREFDYiw4REFBQ0E7a0ZBQWM7OzBEQUNiLDhEQUFDQTswRkFBYzs7b0RBQXdDaEQsZUFBZXlELE9BQU8sQ0FBQztvREFBRzs7Ozs7OzswREFDakYsOERBQUNUOzBGQUFjOzBEQUF3Qjs7Ozs7Ozs7Ozs7O2tEQUd6Qyw4REFBQ0E7a0ZBQWM7a0RBQStEOzs7Ozs7Ozs7Ozs7MENBRWhGLDhEQUFDQTswRUFBYzs7a0RBQ2IsOERBQUNBO2tGQUFjOzswREFDYiw4REFBQ0E7MEZBQWM7O29EQUF1QztvREFBRTlDLFdBQVd1RCxPQUFPLENBQUM7b0RBQUc7Ozs7Ozs7MERBQzlFLDhEQUFDVDswRkFBYzswREFBd0I7Ozs7Ozs7Ozs7OztvQ0FHeEM5QyxhQUFhLG9CQUNaLDhEQUFDOEM7a0ZBQWM7a0RBQStEOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBTXBGLDhEQUFDQTtrRUFBYzs7MENBQ2IsOERBQUNDOzBFQUFlOzBDQUFxQjs7Ozs7OzBDQUNyQyw4REFBQ0Q7MEVBQWM7Ozs7Ozs7Ozs7OztrQ0FFakIsOERBQUNBO3dCQUFvS0csT0FBTzs0QkFBRUMsZ0JBQWdCO3dCQUFPO2tFQUF0TDtrQ0FDYiw0RUFBQ0g7c0VBQWU7c0NBQXFCOzs7Ozs7Ozs7OztrQ0FFdkMsOERBQUNEO3dCQUFzS0csT0FBTzs0QkFBRUMsZ0JBQWdCO3dCQUFPO2tFQUF4TDtrQ0FDYiw0RUFBQ0g7NEJBQWlERSxPQUFPO2dDQUFFSyxtQkFBbUI7NEJBQUs7c0VBQW5FO3NDQUFzRTs7Ozs7Ozs7Ozs7a0NBRXhGLDhEQUFDUjt3QkFBeUtHLE9BQU87NEJBQUVDLGdCQUFnQjt3QkFBTztrRUFBM0w7a0NBQ2IsNEVBQUNIO3NFQUFlO3NDQUFxQjs7Ozs7Ozs7Ozs7a0NBSXZDLDhEQUFDRDtrRUFBYztrQ0FDYiw0RUFBQ0E7c0VBQWM7OzhDQUNiLDhEQUFDQTs4RUFBYzs7c0RBQ2IsOERBQUNDO3NGQUFlO3NEQUFpQjs7Ozs7O3NEQUNqQyw4REFBQ0E7c0ZBQWU7c0RBQTRCOzs7Ozs7Ozs7Ozs7OENBRTlDLDhEQUFDRDs4RUFBYzs7c0RBQ2IsOERBQUNDO3NGQUFlO3NEQUFpQjs7Ozs7O3NEQUNqQyw4REFBQ0E7c0ZBQWU7c0RBQTJDOzs7Ozs7Ozs7Ozs7OENBRTdELDhEQUFDRDs4RUFBYzs7c0RBQ2IsOERBQUNDO3NGQUFlO3NEQUFnQjs7Ozs7O3NEQUNoQyw4REFBQ0E7c0ZBQWU7c0RBQTRDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztvQkFNakU3QyxnQkFBZ0IsbUJBQ2YsOERBQUM0QztrRUFBYztrQ0FBK0s7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUF3Q3hNO0dBdFhTdkQ7S0FBQUE7QUF3WE0sU0FBU2lFO0lBQ3RCLHFCQUNFLDhEQUFDVjtRQUFJVyxXQUFVO2tCQUNiLDRFQUFDbEU7Ozs7Ozs7Ozs7QUFHUDtNQU53QmlFIiwic291cmNlcyI6WyIvVXNlcnMvYWF5dXNobWlzaHJhL0Rlc2t0b3Avb2xkIGludmluY2libGUgd2l0aCBkZWVwcmVzZWFyY2gvc3JjL2NvbXBvbmVudHMvU29jaWFsTWVkaWFQcmV2aWV3LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgdXNlUmVmLCB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnXG5cbi8vIFNvY2lhbCBNZWRpYSBGZWVkIEFuaW1hdGlvblxuZnVuY3Rpb24gU29jaWFsTWVkaWFJY29uKCkge1xuICBjb25zdCBbcG9zdHMsIHNldFBvc3RzXSA9IHVzZVN0YXRlPEFycmF5PHsgaWQ6IG51bWJlcjsgY29udGVudDogc3RyaW5nOyBsaWtlczogbnVtYmVyOyBzaGFyZXM6IG51bWJlcjsgY29tbWVudHM6IG51bWJlcjsgcGxhdGZvcm06IHN0cmluZzsgaXNMaWtpbmc6IGJvb2xlYW47IGlzVHJlbmRpbmc6IGJvb2xlYW4gfT4+KFtdKVxuICBjb25zdCBbbmV3UG9zdEFuaW1hdGlvbiwgc2V0TmV3UG9zdEFuaW1hdGlvbl0gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW3RvdGFsUmVhY2gsIHNldFRvdGFsUmVhY2hdID0gdXNlU3RhdGUoMTU0MjApXG4gIGNvbnN0IFtlbmdhZ2VtZW50UmF0ZSwgc2V0RW5nYWdlbWVudFJhdGVdID0gdXNlU3RhdGUoNC44KVxuICBjb25zdCBbZ3Jvd3RoUmF0ZSwgc2V0R3Jvd3RoUmF0ZV0gPSB1c2VTdGF0ZSgxMi41KVxuICBjb25zdCBbbm90aWZpY2F0aW9ucywgc2V0Tm90aWZpY2F0aW9uc10gPSB1c2VTdGF0ZSgwKVxuICBjb25zdCBbaXNUeXBpbmcsIHNldElzVHlwaW5nXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbY3VycmVudFR5cGluZ1RleHQsIHNldEN1cnJlbnRUeXBpbmdUZXh0XSA9IHVzZVN0YXRlKCcnKVxuICBjb25zdCBbc2hvd0xpdmVJbmRpY2F0b3IsIHNldFNob3dMaXZlSW5kaWNhdG9yXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBpbnRlcnZhbFJlZiA9IHVzZVJlZjxOb2RlSlMuVGltZW91dCB8IG51bGw+KG51bGwpXG4gIGNvbnN0IHR5cGluZ1JlZiA9IHVzZVJlZjxOb2RlSlMuVGltZW91dCB8IG51bGw+KG51bGwpXG5cbiAgY29uc3QgcG9zdENvbnRlbnRzID0gW1xuICAgIFwi8J+agCBKdXN0IGxhdW5jaGVkIG91ciBuZXcgQUktcG93ZXJlZCBjb250ZW50IGNyZWF0aW9uIHRvb2whIFRoZSBmdXR1cmUgb2YgbWFya2V0aW5nIGlzIGhlcmUuXCIsXG4gICAgXCLwn5KhIDUgZ2FtZS1jaGFuZ2luZyBBSSBzdHJhdGVnaWVzIHRoYXQgdHJhbnNmb3JtZWQgb3VyIGNvbnRlbnQgd29ya2Zsb3cuIFRocmVhZCBiZWxvdyDwn5GHXCIsXG4gICAgXCLwn5OKIE91ciBsYXRlc3QgY2FtcGFpZ24gYWNoaWV2ZWQgMzQwJSBST0kgdXNpbmcgQUktZ2VuZXJhdGVkIGNvbnRlbnQuIEhlcmUncyBob3cgd2UgZGlkIGl0Li4uXCIsXG4gICAgXCLinKggQmVoaW5kIHRoZSBzY2VuZXM6IEhvdyBBSSBpcyByZXZvbHV0aW9uaXppbmcgY3JlYXRpdmUgcHJvY2Vzc2VzIGluIDIwMjRcIixcbiAgICBcIvCfjq8gUHJvIHRpcDogQ29tYmluZSBodW1hbiBjcmVhdGl2aXR5IHdpdGggQUkgZWZmaWNpZW5jeSBmb3IgdW5iZWF0YWJsZSByZXN1bHRzXCIsXG4gICAgXCLwn5SlIEJSRUFLSU5HOiBPdXIgQUkganVzdCB3ZW50IHZpcmFsISAxME0rIHZpZXdzIGluIDI0IGhvdXJzIPCfk4hcIixcbiAgICBcIvCfkqwgV2hhdCdzIHlvdXIgYmlnZ2VzdCBjb250ZW50IGNyZWF0aW9uIGNoYWxsZW5nZT8gTGV0J3Mgc29sdmUgaXQgdG9nZXRoZXIhXCIsXG4gICAgXCLwn46JIENlbGVicmF0aW5nIDUwSyBmb2xsb3dlcnMhIFRoYW5rIHlvdSBmb3Igam9pbmluZyBvdXIgQUkgcmV2b2x1dGlvbiBqb3VybmV5XCJcbiAgXVxuXG4gIGNvbnN0IHBsYXRmb3JtcyA9IFsnVHdpdHRlcicsICdJbnN0YWdyYW0nLCAnTGlua2VkSW4nLCAnVGlrVG9rJ11cblxuICAvLyBUeXBpbmcgYW5pbWF0aW9uIGVmZmVjdFxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IHN0YXJ0VHlwaW5nID0gKCkgPT4ge1xuICAgICAgc2V0SXNUeXBpbmcodHJ1ZSlcbiAgICAgIGNvbnN0IHJhbmRvbVBvc3QgPSBwb3N0Q29udGVudHNbTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogcG9zdENvbnRlbnRzLmxlbmd0aCldXG4gICAgICBsZXQgY2hhckluZGV4ID0gMFxuICAgICAgXG4gICAgICBjb25zdCB0eXBlQ2hhciA9ICgpID0+IHtcbiAgICAgICAgaWYgKGNoYXJJbmRleCA8IHJhbmRvbVBvc3QubGVuZ3RoKSB7XG4gICAgICAgICAgc2V0Q3VycmVudFR5cGluZ1RleHQocmFuZG9tUG9zdC5zdWJzdHJpbmcoMCwgY2hhckluZGV4ICsgMSkpXG4gICAgICAgICAgY2hhckluZGV4KytcbiAgICAgICAgICB0eXBpbmdSZWYuY3VycmVudCA9IHNldFRpbWVvdXQodHlwZUNoYXIsIDUwICsgTWF0aC5yYW5kb20oKSAqIDEwMClcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgICAgIHNldElzVHlwaW5nKGZhbHNlKVxuICAgICAgICAgICAgc2V0Q3VycmVudFR5cGluZ1RleHQoJycpXG4gICAgICAgICAgfSwgMjAwMClcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgXG4gICAgICB0eXBlQ2hhcigpXG4gICAgfVxuXG4gICAgLy8gU3RhcnQgdHlwaW5nIGFuaW1hdGlvbiByYW5kb21seVxuICAgIGNvbnN0IHR5cGluZ0ludGVydmFsID0gc2V0SW50ZXJ2YWwoKCkgPT4ge1xuICAgICAgaWYgKE1hdGgucmFuZG9tKCkgPiAwLjcpIHtcbiAgICAgICAgc3RhcnRUeXBpbmcoKVxuICAgICAgfVxuICAgIH0sIDgwMDApXG5cbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgY2xlYXJJbnRlcnZhbCh0eXBpbmdJbnRlcnZhbClcbiAgICAgIGlmICh0eXBpbmdSZWYuY3VycmVudCkgY2xlYXJUaW1lb3V0KHR5cGluZ1JlZi5jdXJyZW50KVxuICAgIH1cbiAgfSwgW10pXG5cbiAgLy8gTGl2ZSBpbmRpY2F0b3IgYW5pbWF0aW9uXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgbGl2ZUludGVydmFsID0gc2V0SW50ZXJ2YWwoKCkgPT4ge1xuICAgICAgc2V0U2hvd0xpdmVJbmRpY2F0b3IocHJldiA9PiAhcHJldilcbiAgICB9LCAzMDAwKVxuXG4gICAgcmV0dXJuICgpID0+IGNsZWFySW50ZXJ2YWwobGl2ZUludGVydmFsKVxuICB9LCBbXSlcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGludGVydmFsUmVmLmN1cnJlbnQgPSBzZXRJbnRlcnZhbCgoKSA9PiB7XG4gICAgICBzZXROZXdQb3N0QW5pbWF0aW9uKHRydWUpXG4gICAgICBcbiAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICBjb25zdCBuZXdQb3N0ID0ge1xuICAgICAgICAgIGlkOiBEYXRlLm5vdygpLFxuICAgICAgICAgIGNvbnRlbnQ6IHBvc3RDb250ZW50c1tNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiBwb3N0Q29udGVudHMubGVuZ3RoKV0sXG4gICAgICAgICAgbGlrZXM6IE1hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIDEwMDApICsgNTAsXG4gICAgICAgICAgc2hhcmVzOiBNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiA1MCkgKyA1LFxuICAgICAgICAgIGNvbW1lbnRzOiBNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiAyNSkgKyAyLFxuICAgICAgICAgIHBsYXRmb3JtOiBwbGF0Zm9ybXNbTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogcGxhdGZvcm1zLmxlbmd0aCldLFxuICAgICAgICAgIGlzTGlraW5nOiBmYWxzZSxcbiAgICAgICAgICBpc1RyZW5kaW5nOiBNYXRoLnJhbmRvbSgpID4gMC42XG4gICAgICAgIH1cblxuICAgICAgICBzZXRQb3N0cyhwcmV2ID0+IFtuZXdQb3N0LCAuLi5wcmV2LnNsaWNlKDAsIDEpXSlcbiAgICAgICAgc2V0VG90YWxSZWFjaChwcmV2ID0+IHByZXYgKyBNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiA1MDApICsgMjAwKVxuICAgICAgICBzZXRFbmdhZ2VtZW50UmF0ZShwcmV2ID0+IE1hdGgubWluKDEwLCBwcmV2ICsgTWF0aC5yYW5kb20oKSAqIDAuNSkpXG4gICAgICAgIHNldEdyb3d0aFJhdGUocHJldiA9PiBNYXRoLm1pbig1MCwgcHJldiArIE1hdGgucmFuZG9tKCkgKiAyKSlcbiAgICAgICAgc2V0Tm90aWZpY2F0aW9ucyhwcmV2ID0+IHByZXYgKyBNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiAzKSArIDEpXG4gICAgICAgIHNldE5ld1Bvc3RBbmltYXRpb24oZmFsc2UpXG4gICAgICB9LCAyMDAwKVxuXG4gICAgICAvLyBVcGRhdGUgZXhpc3RpbmcgcG9zdHMgd2l0aCBsaWtlIGFuaW1hdGlvbnNcbiAgICAgIHNldFBvc3RzKHByZXYgPT4gcHJldi5tYXAocG9zdCA9PiAoe1xuICAgICAgICAuLi5wb3N0LFxuICAgICAgICBsaWtlczogcG9zdC5saWtlcyArIE1hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIDEwKSArIDIsXG4gICAgICAgIHNoYXJlczogcG9zdC5zaGFyZXMgKyBNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiAzKSArIDEsXG4gICAgICAgIGNvbW1lbnRzOiBwb3N0LmNvbW1lbnRzICsgTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogNSkgKyAxLFxuICAgICAgICBpc0xpa2luZzogTWF0aC5yYW5kb20oKSA+IDAuNyxcbiAgICAgICAgaXNUcmVuZGluZzogcG9zdC5pc1RyZW5kaW5nIHx8IE1hdGgucmFuZG9tKCkgPiAwLjhcbiAgICAgIH0pKSlcblxuICAgICAgLy8gUmVzZXQgbGlrZSBhbmltYXRpb25zXG4gICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgc2V0UG9zdHMocHJldiA9PiBwcmV2Lm1hcChwb3N0ID0+ICh7IC4uLnBvc3QsIGlzTGlraW5nOiBmYWxzZSB9KSkpXG4gICAgICB9LCAxMDAwKVxuICAgIH0sIDUwMDApXG5cbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgaWYgKGludGVydmFsUmVmLmN1cnJlbnQpIGNsZWFySW50ZXJ2YWwoaW50ZXJ2YWxSZWYuY3VycmVudClcbiAgICB9XG4gIH0sIFtdKVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSB3LWZ1bGwgaC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHAtNFwiPlxuICAgICAgey8qIERhcmtlciBHbGFzcyBCYWNrZ3JvdW5kICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tZ3JheS05MDAvNDAgdmlhLWJsYWNrLzYwIHRvLWdyYXktOTAwLzQwIGJhY2tkcm9wLWJsdXItMnhsXCI+PC9kaXY+XG4gICAgICBcbiAgICAgIHsvKiBNYWluIENvbnRlbnQgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIHotMTAgdy1mdWxsIG1heC13LXhzXCI+XG4gICAgICAgIHsvKiBQaG9uZSBDb250YWluZXIgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIGgtWzM0MHB4XSBiZy1ibGFjay8zMCBiYWNrZHJvcC1ibHVyLTJ4bCByb3VuZGVkLTJ4bCBib3JkZXIgYm9yZGVyLXdoaXRlLzEwIHNoYWRvdy0yeGwgcmVsYXRpdmUgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgICAgey8qIFBob25lIFNjcmVlbiAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTIgYmctYmxhY2svOTAgYmFja2Ryb3AtYmx1ci0yeGwgcm91bmRlZC14bCBvdmVyZmxvdy1oaWRkZW4gYm9yZGVyIGJvcmRlci13aGl0ZS81XCI+XG4gICAgICAgICAgICB7LyogU3RhdHVzIEJhciAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC02IGJnLWJsYWNrLzYwIGJhY2tkcm9wLWJsdXItMnhsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBweC0zIHRleHQtd2hpdGUvODAgdGV4dC14cyBib3JkZXItYiBib3JkZXItd2hpdGUvNVwiPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPjk6NDEgQU08L3NwYW4+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0xXCI+XG4gICAgICAgICAgICAgICAgey8qIE5vdGlmaWNhdGlvbiBCYWRnZSAqL31cbiAgICAgICAgICAgICAgICB7bm90aWZpY2F0aW9ucyA+IDAgJiYgKFxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctNCBoLTIgYmctZ3JlZW4tNDAwIHJvdW5kZWQtc21cIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSAtdG9wLTEgLXJpZ2h0LTEgdy0zIGgtMyBiZy1yZWQtNTAwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgdGV4dC14cyBmb250LWJvbGRcIj57bm90aWZpY2F0aW9ucyA+IDkgPyAnOSsnIDogbm90aWZpY2F0aW9uc308L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPjEwMCU8L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBBcHAgSGVhZGVyICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTEyIGJnLWJsYWNrLzUwIGJhY2tkcm9wLWJsdXItMnhsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBweC0zIGJvcmRlci1iIGJvcmRlci13aGl0ZS81XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTYgaC02IGJnLWJsdWUtNTAwLzIwIGJhY2tkcm9wLWJsdXIteGwgcm91bmRlZC1sZyBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBib3JkZXIgYm9yZGVyLWJsdWUtNTAwLzMwXCI+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtYmx1ZS00MDAgZm9udC1ib2xkIHRleHQtc21cIj7wn5OxPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC13aGl0ZS85MCBmb250LWJvbGQgdGV4dC1zbVwiPlNvY2lhbCBIdWI8L2gzPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgIHsvKiBMaXZlIFN0cmVhbWluZyBJbmRpY2F0b3IgKi99XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7c2hvd0xpdmVJbmRpY2F0b3IgJiYgKFxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTEgYmctcmVkLTUwMC8yMCBiYWNrZHJvcC1ibHVyLTJ4bCBweC0yIHB5LTEgcm91bmRlZC1mdWxsIGJvcmRlciBib3JkZXItcmVkLTUwMC8yMFwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMiBoLTIgYmctcmVkLTQwMC84MCByb3VuZGVkLWZ1bGwgYW5pbWF0ZS1wdWxzZVwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXJlZC00MDAgdGV4dC14cyBmb250LWJvbGRcIj5MSVZFPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogTmV3IFBvc3QgQW5pbWF0aW9uICovfVxuICAgICAgICAgICAge25ld1Bvc3RBbmltYXRpb24gJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC0xOCBsZWZ0LTIgcmlnaHQtMiBoLTE2IGJnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTUwMC85MCB0by1jeWFuLTUwMC85MCBiYWNrZHJvcC1ibHVyLXhsIHJvdW5kZWQtbGcgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgYW5pbWF0ZS1ib3VuY2Ugei0xMCBib3JkZXIgYm9yZGVyLXdoaXRlLzIwXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWxnIG1iLTEgYW5pbWF0ZS1zcGluXCI+4pyoPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtc2VtaWJvbGQgdGV4dC14c1wiPkNyZWF0aW5nIFZpcmFsIENvbnRlbnQuLi48L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgey8qIFR5cGluZyBJbmRpY2F0b3IgKi99XG4gICAgICAgICAgICB7aXNUeXBpbmcgJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC0yMCBsZWZ0LTIgcmlnaHQtMiBiZy1ibHVlLTUwMC85MCBiYWNrZHJvcC1ibHVyLXhsIHJvdW5kZWQtbGcgcC0yIHotMTAgYm9yZGVyIGJvcmRlci13aGl0ZS8yMFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIG1iLTFcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy00IGgtNCBiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS01MDAgdG8tY3lhbi01MDAgcm91bmRlZC1mdWxsXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIHRleHQteHMgZm9udC1zZW1pYm9sZFwiPkFJIGlzIHR5cGluZy4uLjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTEgaC0xIGJnLXdoaXRlIHJvdW5kZWQtZnVsbCBhbmltYXRlLWJvdW5jZVwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMSBoLTEgYmctd2hpdGUgcm91bmRlZC1mdWxsIGFuaW1hdGUtYm91bmNlXCIgc3R5bGU9e3sgYW5pbWF0aW9uRGVsYXk6ICcwLjJzJyB9fT48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTEgaC0xIGJnLXdoaXRlIHJvdW5kZWQtZnVsbCBhbmltYXRlLWJvdW5jZVwiIHN0eWxlPXt7IGFuaW1hdGlvbkRlbGF5OiAnMC40cycgfX0+PC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtd2hpdGUgdGV4dC14cyBiZy13aGl0ZS8xMCByb3VuZGVkIHAtMVwiPlxuICAgICAgICAgICAgICAgICAge2N1cnJlbnRUeXBpbmdUZXh0fTxzcGFuIGNsYXNzTmFtZT1cImFuaW1hdGUtcHVsc2VcIj58PC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgIHsvKiBQb3N0cyBGZWVkICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTIgc3BhY2UteS0yIG92ZXJmbG93LWhpZGRlbiBoLWZ1bGxcIj5cbiAgICAgICAgICAgICAge3Bvc3RzLm1hcCgocG9zdCwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICBrZXk9e3Bvc3QuaWR9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BiZy1ibGFjay8zMCBiYWNrZHJvcC1ibHVyLTJ4bCByb3VuZGVkLWxnIHAtMyBib3JkZXIgYm9yZGVyLXdoaXRlLzEwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTUwMCByZWxhdGl2ZSAke1xuICAgICAgICAgICAgICAgICAgICBpbmRleCA9PT0gMCA/ICdhbmltYXRlLXNsaWRlSW5Gcm9tVG9wJyA6ICcnXG4gICAgICAgICAgICAgICAgICB9ICR7cG9zdC5pc1RyZW5kaW5nID8gJ3JpbmctMiByaW5nLXllbGxvdy00MDAvMzAnIDogJyd9YH1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICB7LyogVHJlbmRpbmcgQmFkZ2UgKi99XG4gICAgICAgICAgICAgICAgICB7cG9zdC5pc1RyZW5kaW5nICYmIChcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSAtdG9wLTEgLXJpZ2h0LTEgYmcteWVsbG93LTUwMC8yMCBiYWNrZHJvcC1ibHVyLTJ4bCB0ZXh0LXllbGxvdy00MDAgcHgtMiBweS0xIHJvdW5kZWQtZnVsbCB0ZXh0LXhzIGZvbnQtYm9sZCBhbmltYXRlLXB1bHNlIGJvcmRlciBib3JkZXIteWVsbG93LTUwMC8yMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIPCflKUgVFJFTkRJTkdcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy02IGgtNiBiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS01MDAgdG8tY3lhbi01MDAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHJlbGF0aXZlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC13aGl0ZSB0ZXh0LXhzIGZvbnQtYm9sZFwiPkFJPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIHsvKiBPbmxpbmUgaW5kaWNhdG9yICovfVxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgLWJvdHRvbS0wLjUgLXJpZ2h0LTAuNSB3LTIgaC0yIGJnLWdyZWVuLTQwMCByb3VuZGVkLWZ1bGwgYm9yZGVyIGJvcmRlci13aGl0ZVwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC13aGl0ZS85MCB0ZXh0LXhzIGZvbnQtc2VtaWJvbGRcIj5BSSBDb250ZW50IENyZWF0b3I8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMyBoLTMgYmctYmx1ZS01MDAvNjAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtd2hpdGUvODAgdGV4dC14c1wiPuKckzwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC13aGl0ZS81MCB0ZXh0LXhzXCI+e3Bvc3QucGxhdGZvcm19IOKAoiAybSBhZ288L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC13aGl0ZS83MCB0ZXh0LXhzIG1iLTIgbGVhZGluZy1yZWxheGVkIGxpbmUtY2xhbXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICB7cG9zdENvbnRlbnRzW2luZGV4ICUgcG9zdENvbnRlbnRzLmxlbmd0aF19XG4gICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gdGV4dC14c1wiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMSB0ZXh0LXJlZC00MDAgdHJhbnNpdGlvbi1hbGwgJHtwb3N0LmlzTGlraW5nID8gJ3NjYWxlLTEyNScgOiAnJ31gfT5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e3Bvc3QuaXNMaWtpbmcgPyAnYW5pbWF0ZS1ib3VuY2UnIDogJyd9PuKdpO+4jzwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJhbmltYXRlLXB1bHNlIGZvbnQtbWVkaXVtXCI+e3Bvc3QubGlrZXMudG9Mb2NhbGVTdHJpbmcoKX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAge3Bvc3QuaXNMaWtpbmcgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXJlZC00MDAgYW5pbWF0ZS1waW5nXCI+8J+Sljwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMSB0ZXh0LWdyZWVuLTQwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpblwiIHN0eWxlPXt7IGFuaW1hdGlvbkR1cmF0aW9uOiAnM3MnIH19PvCflIQ8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiYW5pbWF0ZS1wdWxzZSBmb250LW1lZGl1bVwiPntwb3N0LnNoYXJlc308L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMSB0ZXh0LWJsdWUtNDAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4+8J+SrDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJhbmltYXRlLXB1bHNlIGZvbnQtbWVkaXVtXCI+e3Bvc3QuY29tbWVudHN9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICB7LyogRW5nYWdlbWVudCBBbmltYXRpb24gKi99XG4gICAgICAgICAgICAgICAgICB7cG9zdC5pc0xpa2luZyAmJiAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBwb2ludGVyLWV2ZW50cy1ub25lXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtMiByaWdodC0yIHRleHQtcmVkLTQwMCBhbmltYXRlLWJvdW5jZVwiPvCfkpU8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGJvdHRvbS0yIGxlZnQtMiB0ZXh0LXllbGxvdy00MDAgYW5pbWF0ZS1ib3VuY2VcIiBzdHlsZT17eyBhbmltYXRpb25EZWxheTogJzAuMnMnIH19PuKtkDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTEvMiBsZWZ0LTEvMiB0ZXh0LWJsdWUtNDAwIGFuaW1hdGUtYm91bmNlXCIgc3R5bGU9e3sgYW5pbWF0aW9uRGVsYXk6ICcwLjRzJyB9fT7inKg8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIEhvbWUgQnV0dG9uIHdpdGggUHVsc2UgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBib3R0b20tMyBsZWZ0LTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS14LTEvMiB3LTggaC04IGJnLXdoaXRlLzIwIGJhY2tkcm9wLWJsdXIteGwgcm91bmRlZC1mdWxsIGJvcmRlciBib3JkZXItd2hpdGUvMzAgYW5pbWF0ZS1wdWxzZVwiPjwvZGl2PlxuICAgICAgICAgIFxuICAgICAgICAgIHsvKiBHbGFzcyBPdmVybGF5IEVmZmVjdCAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgYmctZ3JhZGllbnQtdG8tYnIgZnJvbS13aGl0ZS81IHRvLXRyYW5zcGFyZW50IHBvaW50ZXItZXZlbnRzLW5vbmVcIj48L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIEVuaGFuY2VkIFN0YXRzIFBhbmVsICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTMgZmxleCBzcGFjZS14LTJcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlLzEwIGJhY2tkcm9wLWJsdXIteGwgcm91bmRlZC1sZyBib3JkZXIgYm9yZGVyLXdoaXRlLzIwIHAtMiBmbGV4LTEgcmVsYXRpdmUgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LWJvbGQgdGV4dC1ibHVlLTQwMCBtYi0xIGFuaW1hdGUtcHVsc2VcIj57dG90YWxSZWFjaC50b0xvY2FsZVN0cmluZygpfTwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtd2hpdGUvNzAgdGV4dC14c1wiPlJlYWNoPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIHsvKiBHcm93dGggaW5kaWNhdG9yICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtMSByaWdodC0xXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yIGgtMiBiZy1ncmVlbi00MDAgcm91bmRlZC1mdWxsIGFuaW1hdGUtcGluZ1wiPjwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZS8xMCBiYWNrZHJvcC1ibHVyLXhsIHJvdW5kZWQtbGcgYm9yZGVyIGJvcmRlci13aGl0ZS8yMCBwLTIgZmxleC0xIHJlbGF0aXZlXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LWJvbGQgdGV4dC1jeWFuLTQwMCBtYi0xXCI+e2VuZ2FnZW1lbnRSYXRlLnRvRml4ZWQoMSl9JTwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtd2hpdGUvNzAgdGV4dC14c1wiPkVuZ2FnZW1lbnQ8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgey8qIFRyZW5kaW5nIGFycm93ICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtMSByaWdodC0xIHRleHQtZ3JlZW4tNDAwIHRleHQteHMgYW5pbWF0ZS1ib3VuY2VcIj7ihpfvuI88L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlLzEwIGJhY2tkcm9wLWJsdXIteGwgcm91bmRlZC1sZyBib3JkZXIgYm9yZGVyLXdoaXRlLzIwIHAtMiBmbGV4LTEgcmVsYXRpdmVcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtYm9sZCB0ZXh0LWJsdWUtNTAwIG1iLTFcIj4re2dyb3d0aFJhdGUudG9GaXhlZCgxKX0lPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC13aGl0ZS83MCB0ZXh0LXhzXCI+R3Jvd3RoPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIHsvKiBGaXJlIGluZGljYXRvciBmb3IgaG90IGdyb3d0aCAqL31cbiAgICAgICAgICAgIHtncm93dGhSYXRlID4gMjAgJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC0xIHJpZ2h0LTEgdGV4dC1vcmFuZ2UtNDAwIHRleHQteHMgYW5pbWF0ZS1wdWxzZVwiPvCflKU8L2Rpdj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBFbmhhbmNlZCBGbG9hdGluZyBTb2NpYWwgSWNvbnMgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgLXRvcC0yIC1sZWZ0LTIgYmctYmx1ZS01MDAvODAgYmFja2Ryb3AtYmx1ci14bCByb3VuZGVkLWZ1bGwgdy0xMCBoLTEwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGFuaW1hdGUtZmxvYXQgYm9yZGVyIGJvcmRlci13aGl0ZS8yMCByZWxhdGl2ZVwiPlxuICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgdGV4dC1zbVwiPvCfk5g8L3NwYW4+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSAtdG9wLTEgLXJpZ2h0LTEgdy0zIGgtMyBiZy1yZWQtNTAwIHJvdW5kZWQtZnVsbCBhbmltYXRlLXBpbmdcIj48L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgLXRvcC0xIC1yaWdodC0zIGJnLWN5YW4tNTAwLzgwIGJhY2tkcm9wLWJsdXIteGwgcm91bmRlZC1mdWxsIHctOCBoLTggZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgYW5pbWF0ZS1mbG9hdCBib3JkZXIgYm9yZGVyLXdoaXRlLzIwXCIgc3R5bGU9e3sgYW5pbWF0aW9uRGVsYXk6ICcwLjNzJyB9fT5cbiAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIHRleHQteHNcIj7wn5O3PC9zcGFuPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSAtYm90dG9tLTIgLWxlZnQtMyBiZy1ibHVlLTYwMC84MCBiYWNrZHJvcC1ibHVyLXhsIHJvdW5kZWQtZnVsbCB3LTggaC04IGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGFuaW1hdGUtZmxvYXQgYm9yZGVyIGJvcmRlci13aGl0ZS8yMFwiIHN0eWxlPXt7IGFuaW1hdGlvbkRlbGF5OiAnMC42cycgfX0+XG4gICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC13aGl0ZSB0ZXh0LXhzIGFuaW1hdGUtc3BpblwiIHN0eWxlPXt7IGFuaW1hdGlvbkR1cmF0aW9uOiAnNHMnIH19PvCfjqU8L3NwYW4+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIC1ib3R0b20tMSAtcmlnaHQtMiBiZy1jeWFuLTYwMC84MCBiYWNrZHJvcC1ibHVyLXhsIHJvdW5kZWQtZnVsbCB3LTEwIGgtMTAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgYW5pbWF0ZS1mbG9hdCBib3JkZXIgYm9yZGVyLXdoaXRlLzIwXCIgc3R5bGU9e3sgYW5pbWF0aW9uRGVsYXk6ICcwLjlzJyB9fT5cbiAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIHRleHQtc21cIj7wn5KsPC9zcGFuPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogRW5oYW5jZWQgRW5nYWdlbWVudCBNZXRyaWNzICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC00IC1yaWdodC0xNiBiZy13aGl0ZS8xMCBiYWNrZHJvcC1ibHVyLXhsIHJvdW5kZWQtbGcgYm9yZGVyIGJvcmRlci13aGl0ZS8yMCBwLTIgdGV4dC13aGl0ZSB0ZXh0LXhzXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTFcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0xXCI+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImFuaW1hdGUtYm91bmNlXCI+8J+RpTwvc3Bhbj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiYW5pbWF0ZS1wdWxzZSBmb250LW1lZGl1bVwiPjIuOEs8L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0xXCI+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tNDAwXCI+8J+TiDwvc3Bhbj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiYW5pbWF0ZS1wdWxzZSBmb250LW1lZGl1bSB0ZXh0LWdyZWVuLTQwMFwiPisyNCU8L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0xXCI+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImFuaW1hdGUtcHVsc2VcIj7wn5SlPC9zcGFuPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJhbmltYXRlLXB1bHNlIGZvbnQtbWVkaXVtIHRleHQtb3JhbmdlLTQwMFwiPlZpcmFsPC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBGbG9hdGluZyBOb3RpZmljYXRpb25zICovfVxuICAgICAgICB7bm90aWZpY2F0aW9ucyA+IDUgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTggbGVmdC0xLzIgdHJhbnNmb3JtIC10cmFuc2xhdGUteC0xLzIgYmctYmx1ZS01MDAvOTAgYmFja2Ryb3AtYmx1ci14bCB0ZXh0LXdoaXRlIHB4LTMgcHktMSByb3VuZGVkLWZ1bGwgdGV4dC14cyBmb250LWJvbGQgYW5pbWF0ZS1ib3VuY2UgYm9yZGVyIGJvcmRlci13aGl0ZS8yMFwiPlxuICAgICAgICAgICAg8J+OiSBHb2luZyBWaXJhbCFcbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cbiAgICAgIDwvZGl2PlxuXG4gICAgICA8c3R5bGUganN4PntgXG4gICAgICAgIEBrZXlmcmFtZXMgc2xpZGVJbkZyb21Ub3Age1xuICAgICAgICAgIDAlIHtcbiAgICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMTAwJSk7XG4gICAgICAgICAgICBvcGFjaXR5OiAwO1xuICAgICAgICAgIH1cbiAgICAgICAgICAxMDAlIHtcbiAgICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgwKTtcbiAgICAgICAgICAgIG9wYWNpdHk6IDE7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIEBrZXlmcmFtZXMgZmxvYXQge1xuICAgICAgICAgIDAlLCAxMDAlIHtcbiAgICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgwcHgpO1xuICAgICAgICAgIH1cbiAgICAgICAgICA1MCUge1xuICAgICAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC01cHgpO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICAuYW5pbWF0ZS1zbGlkZUluRnJvbVRvcCB7XG4gICAgICAgICAgYW5pbWF0aW9uOiBzbGlkZUluRnJvbVRvcCAwLjZzIGVhc2Utb3V0O1xuICAgICAgICB9XG4gICAgICAgIC5hbmltYXRlLWZsb2F0IHtcbiAgICAgICAgICBhbmltYXRpb246IGZsb2F0IDNzIGVhc2UtaW4tb3V0IGluZmluaXRlO1xuICAgICAgICB9XG4gICAgICAgIC5saW5lLWNsYW1wLTIge1xuICAgICAgICAgIGRpc3BsYXk6IC13ZWJraXQtYm94O1xuICAgICAgICAgIC13ZWJraXQtbGluZS1jbGFtcDogMjtcbiAgICAgICAgICAtd2Via2l0LWJveC1vcmllbnQ6IHZlcnRpY2FsO1xuICAgICAgICAgIG92ZXJmbG93OiBoaWRkZW47XG4gICAgICAgIH1cbiAgICAgIGB9PC9zdHlsZT5cbiAgICA8L2Rpdj5cbiAgKVxufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBTb2NpYWxNZWRpYVByZXZpZXcoKSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgaC1mdWxsIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tZ3JheS05MDAgdmlhLWJsdWUtOTAwIHRvLWN5YW4tOTAwXCI+XG4gICAgICA8U29jaWFsTWVkaWFJY29uIC8+XG4gICAgPC9kaXY+XG4gIClcbn0iXSwibmFtZXMiOlsidXNlUmVmIiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJTb2NpYWxNZWRpYUljb24iLCJwb3N0cyIsInNldFBvc3RzIiwibmV3UG9zdEFuaW1hdGlvbiIsInNldE5ld1Bvc3RBbmltYXRpb24iLCJ0b3RhbFJlYWNoIiwic2V0VG90YWxSZWFjaCIsImVuZ2FnZW1lbnRSYXRlIiwic2V0RW5nYWdlbWVudFJhdGUiLCJncm93dGhSYXRlIiwic2V0R3Jvd3RoUmF0ZSIsIm5vdGlmaWNhdGlvbnMiLCJzZXROb3RpZmljYXRpb25zIiwiaXNUeXBpbmciLCJzZXRJc1R5cGluZyIsImN1cnJlbnRUeXBpbmdUZXh0Iiwic2V0Q3VycmVudFR5cGluZ1RleHQiLCJzaG93TGl2ZUluZGljYXRvciIsInNldFNob3dMaXZlSW5kaWNhdG9yIiwiaW50ZXJ2YWxSZWYiLCJ0eXBpbmdSZWYiLCJwb3N0Q29udGVudHMiLCJwbGF0Zm9ybXMiLCJzdGFydFR5cGluZyIsInJhbmRvbVBvc3QiLCJNYXRoIiwiZmxvb3IiLCJyYW5kb20iLCJsZW5ndGgiLCJjaGFySW5kZXgiLCJ0eXBlQ2hhciIsInN1YnN0cmluZyIsImN1cnJlbnQiLCJzZXRUaW1lb3V0IiwidHlwaW5nSW50ZXJ2YWwiLCJzZXRJbnRlcnZhbCIsImNsZWFySW50ZXJ2YWwiLCJjbGVhclRpbWVvdXQiLCJsaXZlSW50ZXJ2YWwiLCJwcmV2IiwibmV3UG9zdCIsImlkIiwiRGF0ZSIsIm5vdyIsImNvbnRlbnQiLCJsaWtlcyIsInNoYXJlcyIsImNvbW1lbnRzIiwicGxhdGZvcm0iLCJpc0xpa2luZyIsImlzVHJlbmRpbmciLCJzbGljZSIsIm1pbiIsIm1hcCIsInBvc3QiLCJkaXYiLCJzcGFuIiwiaDMiLCJzdHlsZSIsImFuaW1hdGlvbkRlbGF5IiwiaW5kZXgiLCJwIiwidG9Mb2NhbGVTdHJpbmciLCJhbmltYXRpb25EdXJhdGlvbiIsInRvRml4ZWQiLCJTb2NpYWxNZWRpYVByZXZpZXciLCJjbGFzc05hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/SocialMediaPreview.tsx\n"));

/***/ })

}]);