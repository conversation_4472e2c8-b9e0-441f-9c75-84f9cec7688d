"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_components_VideoScriptPreview_tsx"],{

/***/ "(app-pages-browser)/./src/components/VideoScriptPreview.tsx":
/*!***********************************************!*\
  !*** ./src/components/VideoScriptPreview.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ VideoScriptPreview)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n// Compact YouTube Script Studio Preview for Dashboard Card\nfunction VideoScriptStudio() {\n    _s();\n    const [currentScript, setCurrentScript] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showCursor, setShowCursor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [sceneNumber, setSceneNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [videoMetrics, setVideoMetrics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        views: 125847,\n        likes: 8924,\n        retention: 87.5,\n        engagement: 12.3,\n        duration: '12:34'\n    });\n    const [isRecording, setIsRecording] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [scriptProgress, setScriptProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [activeSection, setActiveSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0) // 0: intro, 1: hook, 2: content, 3: cta\n    ;\n    const [particles, setParticles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [glowIntensity, setGlowIntensity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0.5);\n    const typewriterRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const cursorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Generate fewer particles for compact view\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"VideoScriptStudio.useEffect\": ()=>{\n            const generateParticles = {\n                \"VideoScriptStudio.useEffect.generateParticles\": ()=>{\n                    const newParticles = Array.from({\n                        length: 6\n                    }, {\n                        \"VideoScriptStudio.useEffect.generateParticles.newParticles\": (_, i)=>({\n                                id: i,\n                                x: Math.random() * 100,\n                                y: Math.random() * 100,\n                                delay: Math.random() * 2\n                            })\n                    }[\"VideoScriptStudio.useEffect.generateParticles.newParticles\"]);\n                    setParticles(newParticles);\n                }\n            }[\"VideoScriptStudio.useEffect.generateParticles\"];\n            generateParticles();\n            const interval = setInterval(generateParticles, 20000);\n            return ({\n                \"VideoScriptStudio.useEffect\": ()=>clearInterval(interval)\n            })[\"VideoScriptStudio.useEffect\"];\n        }\n    }[\"VideoScriptStudio.useEffect\"], []);\n    // Dynamic glow effect\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"VideoScriptStudio.useEffect\": ()=>{\n            const interval = setInterval({\n                \"VideoScriptStudio.useEffect.interval\": ()=>{\n                    setGlowIntensity({\n                        \"VideoScriptStudio.useEffect.interval\": (prev)=>0.3 + Math.sin(Date.now() * 0.002) * 0.3\n                    }[\"VideoScriptStudio.useEffect.interval\"]);\n                }\n            }[\"VideoScriptStudio.useEffect.interval\"], 150);\n            return ({\n                \"VideoScriptStudio.useEffect\": ()=>clearInterval(interval)\n            })[\"VideoScriptStudio.useEffect\"];\n        }\n    }[\"VideoScriptStudio.useEffect\"], []);\n    const scriptSections = [\n        {\n            title: \"INTRO\",\n            content: \"[INTRO MUSIC FADES IN]\\n\\nHey YouTube! Welcome back to my channel! 👋\\n\\nIf you're new here, I'm [YOUR NAME] and I help creators...\",\n            timestamp: \"00:00\",\n            color: \"#ff6b6b\",\n            emoji: \"🎬\",\n            bgGradient: \"from-red-500/20 to-orange-500/20\"\n        },\n        {\n            title: \"HOOK\",\n            content: \"What if I told you there's a 30-second technique that can DOUBLE your video retention?\\n\\n[PAUSE FOR DRAMATIC EFFECT]...\",\n            timestamp: \"00:15\",\n            color: \"#4ecdc4\",\n            emoji: \"⚡\",\n            bgGradient: \"from-cyan-500/20 to-teal-500/20\"\n        },\n        {\n            title: \"CONTENT\",\n            content: \"Alright, let's dive into the good stuff! 💪\\n\\n[TRANSITION TO SCREEN RECORDING]\\n\\nStep 1: The 3-Second Rule...\",\n            timestamp: \"00:45\",\n            color: \"#45b7d1\",\n            emoji: \"🎓\",\n            bgGradient: \"from-blue-500/20 to-indigo-500/20\"\n        },\n        {\n            title: \"CTA\",\n            content: \"If this video helped you out, SMASH that like button! 👍\\n\\nAnd if you want more content like this, hit subscribe!\",\n            timestamp: \"08:30\",\n            color: \"#f093fb\",\n            emoji: \"🎯\",\n            bgGradient: \"from-purple-500/20 to-pink-500/20\"\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"VideoScriptStudio.useEffect\": ()=>{\n            let charIndex = 0;\n            let isDeleting = false;\n            let sectionPause = false;\n            const typeScript = {\n                \"VideoScriptStudio.useEffect.typeScript\": ()=>{\n                    const currentSection = scriptSections[activeSection];\n                    if (sectionPause) {\n                        setTimeout({\n                            \"VideoScriptStudio.useEffect.typeScript\": ()=>{\n                                sectionPause = false;\n                                setActiveSection({\n                                    \"VideoScriptStudio.useEffect.typeScript\": (prev)=>(prev + 1) % scriptSections.length\n                                }[\"VideoScriptStudio.useEffect.typeScript\"]);\n                                charIndex = 0;\n                            }\n                        }[\"VideoScriptStudio.useEffect.typeScript\"], 2000);\n                        return;\n                    }\n                    if (!isDeleting) {\n                        setCurrentScript(currentSection.content.substring(0, charIndex + 1));\n                        charIndex++;\n                        setScriptProgress(charIndex / currentSection.content.length * 100);\n                        // Update metrics periodically\n                        if (charIndex % 15 === 0) {\n                            setVideoMetrics({\n                                \"VideoScriptStudio.useEffect.typeScript\": (prev)=>({\n                                        ...prev,\n                                        views: prev.views + Math.floor(Math.random() * 100) + 25,\n                                        likes: prev.likes + Math.floor(Math.random() * 8) + 1,\n                                        retention: Math.min(96, prev.retention + Math.random() * 0.5),\n                                        engagement: Math.min(20, prev.engagement + Math.random() * 0.3)\n                                    })\n                            }[\"VideoScriptStudio.useEffect.typeScript\"]);\n                        }\n                        // Update scene number\n                        if (charIndex % 80 === 0) {\n                            setSceneNumber({\n                                \"VideoScriptStudio.useEffect.typeScript\": (prev)=>Math.min(8, prev + 1)\n                            }[\"VideoScriptStudio.useEffect.typeScript\"]);\n                        }\n                        // Recording indicator\n                        if (charIndex % 30 === 0 && Math.random() > 0.7) {\n                            setIsRecording({\n                                \"VideoScriptStudio.useEffect.typeScript\": (prev)=>!prev\n                            }[\"VideoScriptStudio.useEffect.typeScript\"]);\n                        }\n                        if (charIndex === currentSection.content.length) {\n                            setTimeout({\n                                \"VideoScriptStudio.useEffect.typeScript\": ()=>{\n                                    isDeleting = true;\n                                }\n                            }[\"VideoScriptStudio.useEffect.typeScript\"], 2500);\n                        }\n                    } else {\n                        setCurrentScript(currentSection.content.substring(0, charIndex - 1));\n                        charIndex--;\n                        setScriptProgress(charIndex / currentSection.content.length * 100);\n                        if (charIndex === 0) {\n                            isDeleting = false;\n                            sectionPause = true;\n                        }\n                    }\n                }\n            }[\"VideoScriptStudio.useEffect.typeScript\"];\n            typewriterRef.current = setInterval(typeScript, isDeleting ? 20 : 80);\n            return ({\n                \"VideoScriptStudio.useEffect\": ()=>{\n                    if (typewriterRef.current) clearInterval(typewriterRef.current);\n                }\n            })[\"VideoScriptStudio.useEffect\"];\n        }\n    }[\"VideoScriptStudio.useEffect\"], [\n        activeSection\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"VideoScriptStudio.useEffect\": ()=>{\n            cursorRef.current = setInterval({\n                \"VideoScriptStudio.useEffect\": ()=>{\n                    setShowCursor({\n                        \"VideoScriptStudio.useEffect\": (prev)=>!prev\n                    }[\"VideoScriptStudio.useEffect\"]);\n                }\n            }[\"VideoScriptStudio.useEffect\"], 500);\n            return ({\n                \"VideoScriptStudio.useEffect\": ()=>{\n                    if (cursorRef.current) clearInterval(cursorRef.current);\n                }\n            })[\"VideoScriptStudio.useEffect\"];\n        }\n    }[\"VideoScriptStudio.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative w-full h-full flex items-center justify-center p-2 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-[#0f0f0f] rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        transition: {\n                            duration: 1\n                        },\n                        className: \"absolute inset-0 bg-gradient-to-br \".concat(scriptSections[activeSection].bgGradient, \" rounded-lg\")\n                    }, activeSection, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        animate: {\n                            scale: [\n                                1,\n                                1.2,\n                                1\n                            ],\n                            x: [\n                                0,\n                                30,\n                                0\n                            ],\n                            y: [\n                                0,\n                                -20,\n                                0\n                            ],\n                            opacity: [\n                                0.1,\n                                0.3,\n                                0.1\n                            ]\n                        },\n                        transition: {\n                            duration: 15,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                        },\n                        className: \"absolute top-1/4 left-1/4 w-32 h-32 bg-[#ff0000]/20 rounded-full blur-[40px]\",\n                        style: {\n                            filter: \"blur(40px) brightness(\".concat(glowIntensity + 0.3, \")\")\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                        children: particles.map((particle)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    scale: 0\n                                },\n                                animate: {\n                                    opacity: [\n                                        0,\n                                        0.4,\n                                        0\n                                    ],\n                                    scale: [\n                                        0,\n                                        1,\n                                        0\n                                    ],\n                                    x: [\n                                        particle.x + '%',\n                                        particle.x + 15 + '%'\n                                    ],\n                                    y: [\n                                        particle.y + '%',\n                                        particle.y - 20 + '%'\n                                    ]\n                                },\n                                transition: {\n                                    duration: 6 + Math.random() * 2,\n                                    repeat: Infinity,\n                                    delay: particle.delay,\n                                    ease: \"easeInOut\"\n                                },\n                                className: \"absolute w-1 h-1 rounded-full\",\n                                style: {\n                                    backgroundColor: scriptSections[activeSection].color\n                                }\n                            }, particle.id, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                lineNumber: 165,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 w-full h-full flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: -10\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        className: \"bg-[#1a1a1a]/90 backdrop-blur-sm border border-[#303030]/50 rounded-t-lg p-2 flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                            animate: {\n                                                rotate: 360,\n                                                boxShadow: [\n                                                    \"0 0 0px \".concat(scriptSections[activeSection].color),\n                                                    \"0 0 10px \".concat(scriptSections[activeSection].color, \"40\"),\n                                                    \"0 0 0px \".concat(scriptSections[activeSection].color)\n                                                ]\n                                            },\n                                            transition: {\n                                                rotate: {\n                                                    duration: 20,\n                                                    repeat: Infinity,\n                                                    ease: \"linear\"\n                                                },\n                                                boxShadow: {\n                                                    duration: 3,\n                                                    repeat: Infinity\n                                                }\n                                            },\n                                            className: \"w-6 h-6 bg-gradient-to-br from-[#ff0000] to-[#cc0000] rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold text-xs\",\n                                                children: \"YT\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.h3, {\n                                                    animate: {\n                                                        color: [\n                                                            scriptSections[activeSection].color,\n                                                            '#ffffff',\n                                                            scriptSections[activeSection].color\n                                                        ]\n                                                    },\n                                                    transition: {\n                                                        duration: 4,\n                                                        repeat: Infinity\n                                                    },\n                                                    className: \"font-bold text-xs\",\n                                                    children: \"Studio Pro\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-[#aaa] text-[10px]\",\n                                                    children: \"AI Script Generator\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                                        children: isRecording && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                            initial: {\n                                                scale: 0\n                                            },\n                                            animate: {\n                                                scale: [\n                                                    1,\n                                                    1.1,\n                                                    1\n                                                ]\n                                            },\n                                            exit: {\n                                                scale: 0\n                                            },\n                                            transition: {\n                                                scale: {\n                                                    duration: 1,\n                                                    repeat: Infinity\n                                                }\n                                            },\n                                            className: \"flex items-center space-x-1 bg-red-600/30 border border-red-500/50 rounded-full px-2 py-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1 h-1 bg-red-500 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-400 text-[8px] font-bold\",\n                                                    children: \"REC\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-[#181818]/95 backdrop-blur-sm border-x border-[#303030]/50 p-2 flex-1 flex flex-col min-h-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1 mb-2 bg-[#0f0f0f]/80 rounded-lg p-1\",\n                                children: scriptSections.map((section, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                                        whileHover: {\n                                            scale: 1.02\n                                        },\n                                        whileTap: {\n                                            scale: 0.98\n                                        },\n                                        onClick: ()=>setActiveSection(index),\n                                        className: \"flex-1 py-1 px-2 rounded-md text-[8px] font-semibold transition-all duration-300 relative \".concat(activeSection === index ? 'text-white' : 'text-[#aaa] hover:text-white'),\n                                        style: {\n                                            background: activeSection === index ? \"linear-gradient(135deg, \".concat(section.color, \"60, \").concat(section.color, \"30)\") : 'transparent'\n                                        },\n                                        children: [\n                                            activeSection === index && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                                layoutId: \"activeTab\",\n                                                className: \"absolute inset-0 rounded-md\",\n                                                style: {\n                                                    background: \"linear-gradient(135deg, \".concat(section.color, \"40, \").concat(section.color, \"20)\")\n                                                },\n                                                transition: {\n                                                    type: \"spring\",\n                                                    damping: 25,\n                                                    stiffness: 300\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.span, {\n                                                animate: {\n                                                    scale: activeSection === index ? [\n                                                        1,\n                                                        1.1,\n                                                        1\n                                                    ] : 1\n                                                },\n                                                transition: {\n                                                    duration: 2,\n                                                    repeat: Infinity\n                                                },\n                                                className: \"text-[10px] mr-1 relative z-10\",\n                                                children: section.emoji\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"relative z-10\",\n                                                children: section.title\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                                animate: {\n                                                    scale: [\n                                                        1,\n                                                        1.2,\n                                                        1\n                                                    ],\n                                                    boxShadow: [\n                                                        \"0 0 0px \".concat(scriptSections[activeSection].color),\n                                                        \"0 0 8px \".concat(scriptSections[activeSection].color, \"40\"),\n                                                        \"0 0 0px \".concat(scriptSections[activeSection].color)\n                                                    ]\n                                                },\n                                                transition: {\n                                                    duration: 3,\n                                                    repeat: Infinity\n                                                },\n                                                className: \"w-2 h-2 rounded-full\",\n                                                style: {\n                                                    backgroundColor: scriptSections[activeSection].color\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-semibold text-[10px]\",\n                                                children: scriptSections[activeSection].title\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-[#aaa] text-[8px]\",\n                                                children: scriptSections[activeSection].timestamp\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 text-[8px]\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-[#aaa]\",\n                                                children: [\n                                                    \"Scene \",\n                                                    sceneNumber\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 bg-[#303030]/50 rounded-full h-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                                    className: \"h-1 rounded-full\",\n                                                    style: {\n                                                        width: \"\".concat(scriptProgress, \"%\"),\n                                                        backgroundColor: scriptSections[activeSection].color\n                                                    },\n                                                    transition: {\n                                                        duration: 0.3\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-[#aaa]\",\n                                                children: [\n                                                    Math.round(scriptProgress),\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                                                lineNumber: 346,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: 10\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                className: \"bg-[#0f0f0f]/80 border rounded-lg p-2 flex-1 relative overflow-hidden\",\n                                style: {\n                                    borderColor: scriptSections[activeSection].color + '40'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-mono text-[8px] text-white leading-relaxed whitespace-pre-wrap overflow-hidden\",\n                                        children: [\n                                            currentScript.substring(0, 120),\n                                            \"...\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.span, {\n                                                animate: {\n                                                    opacity: showCursor ? 1 : 0\n                                                },\n                                                style: {\n                                                    color: scriptSections[activeSection].color\n                                                },\n                                                className: \"font-bold\",\n                                                children: \"|\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-1 right-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                            animate: {\n                                                scale: [\n                                                    1,\n                                                    1.05,\n                                                    1\n                                                ]\n                                            },\n                                            transition: {\n                                                duration: 2,\n                                                repeat: Infinity\n                                            },\n                                            className: \"border rounded-full px-2 py-1 backdrop-blur-sm\",\n                                            style: {\n                                                backgroundColor: scriptSections[activeSection].color + '20',\n                                                borderColor: scriptSections[activeSection].color + '40'\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-[8px] font-bold\",\n                                                style: {\n                                                    color: scriptSections[activeSection].color\n                                                },\n                                                children: \"\\uD83E\\uDDE0 AI\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, activeSection, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                                lineNumber: 351,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        className: \"bg-[#1a1a1a]/90 backdrop-blur-sm border border-[#303030]/50 rounded-b-lg p-2 flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-4 gap-2 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    className: \"cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                            animate: {\n                                                color: [\n                                                    '#ffffff',\n                                                    scriptSections[activeSection].color,\n                                                    '#ffffff'\n                                                ]\n                                            },\n                                            transition: {\n                                                duration: 4,\n                                                repeat: Infinity\n                                            },\n                                            className: \"text-xs font-bold\",\n                                            children: videoMetrics.views > 999999 ? \"\".concat((videoMetrics.views / 1000000).toFixed(1), \"M\") : \"\".concat(Math.floor(videoMetrics.views / 1000), \"K\")\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                                            lineNumber: 401,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-[#aaa] text-[8px]\",\n                                            children: \"\\uD83D\\uDC41️ Views\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                                            lineNumber: 408,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    className: \"cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                            animate: {\n                                                color: [\n                                                    \"#fff\",\n                                                    \"#ff0000\",\n                                                    \"#fff\"\n                                                ]\n                                            },\n                                            transition: {\n                                                duration: 3,\n                                                repeat: Infinity\n                                            },\n                                            className: \"text-xs font-bold\",\n                                            children: [\n                                                Math.floor(videoMetrics.likes / 1000),\n                                                \"K\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-[#aaa] text-[8px]\",\n                                            children: \"\\uD83D\\uDC4D Likes\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                                            lineNumber: 419,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                                    lineNumber: 411,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    className: \"cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                            animate: {\n                                                color: [\n                                                    '#10b981',\n                                                    '#ffffff',\n                                                    '#10b981'\n                                                ]\n                                            },\n                                            transition: {\n                                                duration: 5,\n                                                repeat: Infinity\n                                            },\n                                            className: \"text-xs font-bold\",\n                                            children: [\n                                                videoMetrics.retention.toFixed(1),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-[#aaa] text-[8px]\",\n                                            children: \"\\uD83D\\uDCCA Retention\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                                            lineNumber: 430,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                                    lineNumber: 422,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    className: \"cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                            animate: {\n                                                color: [\n                                                    \"#fff\",\n                                                    \"#ffd700\",\n                                                    \"#fff\"\n                                                ]\n                                            },\n                                            transition: {\n                                                duration: 3.5,\n                                                repeat: Infinity\n                                            },\n                                            className: \"text-xs font-bold\",\n                                            children: [\n                                                videoMetrics.engagement.toFixed(1),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                                            lineNumber: 434,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-[#aaa] text-[8px]\",\n                                            children: \"\\uD83D\\uDCAC Engagement\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                                            lineNumber: 441,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                                    lineNumber: 433,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                            lineNumber: 399,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                        lineNumber: 394,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        animate: {\n                            scale: [\n                                1,\n                                1.05,\n                                1\n                            ]\n                        },\n                        transition: {\n                            duration: 4,\n                            repeat: Infinity\n                        },\n                        className: \"absolute -top-1 -right-1 bg-gradient-to-r from-[#ff0000] to-[#ff6b6b] text-white px-2 py-1 rounded-full text-[8px] font-bold shadow-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.span, {\n                                animate: {\n                                    rotate: [\n                                        0,\n                                        360\n                                    ]\n                                },\n                                transition: {\n                                    duration: 4,\n                                    repeat: Infinity,\n                                    ease: \"linear\"\n                                },\n                                className: \"inline-block mr-1\",\n                                children: \"\\uD83D\\uDD25\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                                lineNumber: 454,\n                                columnNumber: 11\n                            }, this),\n                            \"VIRAL\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                        lineNumber: 447,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n                lineNumber: 218,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n        lineNumber: 163,\n        columnNumber: 5\n    }, this);\n}\n_s(VideoScriptStudio, \"vABc+VbFKUBW3oPvwJ4U1Mm2ZBA=\");\n_c = VideoScriptStudio;\nfunction VideoScriptPreview() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full h-full bg-[#0f0f0f] rounded-lg overflow-hidden\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VideoScriptStudio, {}, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n            lineNumber: 471,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoScriptPreview.tsx\",\n        lineNumber: 470,\n        columnNumber: 5\n    }, this);\n}\n_c1 = VideoScriptPreview;\nvar _c, _c1;\n$RefreshReg$(_c, \"VideoScriptStudio\");\n$RefreshReg$(_c1, \"VideoScriptPreview\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/VideoScriptPreview.tsx\n"));

/***/ })

}]);