"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_components_dashboard_RecentContent_tsx"],{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/copy.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Copy)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.356.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Copy = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Copy\", [\n    [\n        \"rect\",\n        {\n            width: \"14\",\n            height: \"14\",\n            x: \"8\",\n            y: \"8\",\n            rx: \"2\",\n            ry: \"2\",\n            key: \"17jyea\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2\",\n            key: \"zix9uf\"\n        }\n    ]\n]);\n //# sourceMappingURL=copy.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY29weS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQWFNLGFBQU8sZ0VBQWdCLENBQUMsTUFBUTtJQUNwQztRQUFDLE1BQVE7UUFBQTtZQUFFLEtBQU8sT0FBTTtZQUFBLE9BQVEsS0FBTTtZQUFBLEdBQUcsQ0FBSztZQUFBLEdBQUc7WUFBSyxDQUFJLE9BQUs7WUFBQSxHQUFJLElBQUs7WUFBQSxJQUFLO1FBQUEsQ0FBVTtLQUFBO0lBQ3ZGO1FBQUMsTUFBUTtRQUFBO1lBQUUsR0FBRyxDQUEyRDtZQUFBLEtBQUs7UUFBQSxDQUFVO0tBQUE7Q0FDekYiLCJzb3VyY2VzIjpbIi9Vc2Vycy9zcmMvaWNvbnMvY29weS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uJztcblxuLyoqXG4gKiBAY29tcG9uZW50IEBuYW1lIENvcHlcbiAqIEBkZXNjcmlwdGlvbiBMdWNpZGUgU1ZHIGljb24gY29tcG9uZW50LCByZW5kZXJzIFNWRyBFbGVtZW50IHdpdGggY2hpbGRyZW4uXG4gKlxuICogQHByZXZpZXcgIVtpbWddKGRhdGE6aW1hZ2Uvc3ZnK3htbDtiYXNlNjQsUEhOMlp5QWdlRzFzYm5NOUltaDBkSEE2THk5M2QzY3Vkek11YjNKbkx6SXdNREF2YzNabklnb2dJSGRwWkhSb1BTSXlOQ0lLSUNCb1pXbG5hSFE5SWpJMElnb2dJSFpwWlhkQ2IzZzlJakFnTUNBeU5DQXlOQ0lLSUNCbWFXeHNQU0p1YjI1bElnb2dJSE4wY205clpUMGlJekF3TUNJZ2MzUjViR1U5SW1KaFkydG5jbTkxYm1RdFkyOXNiM0k2SUNObVptWTdJR0p2Y21SbGNpMXlZV1JwZFhNNklESndlQ0lLSUNCemRISnZhMlV0ZDJsa2RHZzlJaklpQ2lBZ2MzUnliMnRsTFd4cGJtVmpZWEE5SW5KdmRXNWtJZ29nSUhOMGNtOXJaUzFzYVc1bGFtOXBiajBpY205MWJtUWlDajRLSUNBOGNtVmpkQ0IzYVdSMGFEMGlNVFFpSUdobGFXZG9kRDBpTVRRaUlIZzlJamdpSUhrOUlqZ2lJSEo0UFNJeUlpQnllVDBpTWlJZ0x6NEtJQ0E4Y0dGMGFDQmtQU0pOTkNBeE5tTXRNUzR4SURBdE1pMHVPUzB5TFRKV05HTXdMVEV1TVM0NUxUSWdNaTB5YURFd1l6RXVNU0F3SURJZ0xqa2dNaUF5SWlBdlBnbzhMM04yWno0SykgLSBodHRwczovL2x1Y2lkZS5kZXYvaWNvbnMvY29weVxuICogQHNlZSBodHRwczovL2x1Y2lkZS5kZXYvZ3VpZGUvcGFja2FnZXMvbHVjaWRlLXJlYWN0IC0gRG9jdW1lbnRhdGlvblxuICpcbiAqIEBwYXJhbSB7T2JqZWN0fSBwcm9wcyAtIEx1Y2lkZSBpY29ucyBwcm9wcyBhbmQgYW55IHZhbGlkIFNWRyBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtKU1guRWxlbWVudH0gSlNYIEVsZW1lbnRcbiAqXG4gKi9cbmNvbnN0IENvcHkgPSBjcmVhdGVMdWNpZGVJY29uKCdDb3B5JywgW1xuICBbJ3JlY3QnLCB7IHdpZHRoOiAnMTQnLCBoZWlnaHQ6ICcxNCcsIHg6ICc4JywgeTogJzgnLCByeDogJzInLCByeTogJzInLCBrZXk6ICcxN2p5ZWEnIH1dLFxuICBbJ3BhdGgnLCB7IGQ6ICdNNCAxNmMtMS4xIDAtMi0uOS0yLTJWNGMwLTEuMS45LTIgMi0yaDEwYzEuMSAwIDIgLjkgMiAyJywga2V5OiAneml4OXVmJyB9XSxcbl0pO1xuXG5leHBvcnQgZGVmYXVsdCBDb3B5O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js":
/*!****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/refresh-cw.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RefreshCw)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.356.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst RefreshCw = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"RefreshCw\", [\n    [\n        \"path\",\n        {\n            d: \"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8\",\n            key: \"v9h5vc\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M21 3v5h-5\",\n            key: \"1q7to0\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16\",\n            key: \"3uifl3\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M8 16H3v5\",\n            key: \"1cv678\"\n        }\n    ]\n]);\n //# sourceMappingURL=refresh-cw.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js":
/*!*************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/trash-2.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Trash2)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.356.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Trash2 = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Trash2\", [\n    [\n        \"path\",\n        {\n            d: \"M3 6h18\",\n            key: \"d0wm0j\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6\",\n            key: \"4alrt4\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2\",\n            key: \"v07s0e\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"10\",\n            x2: \"10\",\n            y1: \"11\",\n            y2: \"17\",\n            key: \"1uufr5\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"14\",\n            x2: \"14\",\n            y1: \"11\",\n            y2: \"17\",\n            key: \"xtxkd\"\n        }\n    ]\n]);\n //# sourceMappingURL=trash-2.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/dashboard/RecentContent.tsx":
/*!****************************************************!*\
  !*** ./src/components/dashboard/RecentContent.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RecentContent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Crown_Eye_FileText_Mail_RefreshCw_Trash2_Video_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Crown,Eye,FileText,Mail,RefreshCw,Trash2,Video!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Crown_Eye_FileText_Mail_RefreshCw_Trash2_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Crown,Eye,FileText,Mail,RefreshCw,Trash2,Video!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Crown_Eye_FileText_Mail_RefreshCw_Trash2_Video_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Crown,Eye,FileText,Mail,RefreshCw,Trash2,Video!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/video.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Crown_Eye_FileText_Mail_RefreshCw_Trash2_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Crown,Eye,FileText,Mail,RefreshCw,Trash2,Video!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Crown_Eye_FileText_Mail_RefreshCw_Trash2_Video_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Crown,Eye,FileText,Mail,RefreshCw,Trash2,Video!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Crown_Eye_FileText_Mail_RefreshCw_Trash2_Video_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Crown,Eye,FileText,Mail,RefreshCw,Trash2,Video!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Crown_Eye_FileText_Mail_RefreshCw_Trash2_Video_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Crown,Eye,FileText,Mail,RefreshCw,Trash2,Video!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Crown_Eye_FileText_Mail_RefreshCw_Trash2_Video_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Crown,Eye,FileText,Mail,RefreshCw,Trash2,Video!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Crown_Eye_FileText_Mail_RefreshCw_Trash2_Video_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Crown,Eye,FileText,Mail,RefreshCw,Trash2,Video!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst contentTypeConfig = {\n    blog: {\n        icon: _barrel_optimize_names_Clock_Copy_Crown_Eye_FileText_Mail_RefreshCw_Trash2_Video_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        label: 'Blog Post',\n        color: 'from-pink-500 to-rose-500',\n        bgColor: 'bg-pink-500/10',\n        borderColor: 'border-pink-500/20'\n    },\n    email: {\n        icon: _barrel_optimize_names_Clock_Copy_Crown_Eye_FileText_Mail_RefreshCw_Trash2_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        label: 'Email',\n        color: 'from-emerald-500 to-teal-500',\n        bgColor: 'bg-emerald-500/10',\n        borderColor: 'border-emerald-500/20'\n    },\n    youtube_script: {\n        icon: _barrel_optimize_names_Clock_Copy_Crown_Eye_FileText_Mail_RefreshCw_Trash2_Video_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        label: 'YouTube Script',\n        color: 'from-red-500 to-orange-500',\n        bgColor: 'bg-red-500/10',\n        borderColor: 'border-red-500/20'\n    },\n    invincible_research: {\n        icon: _barrel_optimize_names_Clock_Copy_Crown_Eye_FileText_Mail_RefreshCw_Trash2_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        label: 'Invincible V.1',\n        color: 'from-violet-700 to-indigo-700',\n        bgColor: 'bg-violet-700/10',\n        borderColor: 'border-violet-700/20'\n    }\n};\nfunction RecentContent(param) {\n    let { limit = 6, showFilters = true } = param;\n    _s();\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [isRefreshing, setIsRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const fetchContent = async function() {\n        let type = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'all';\n        try {\n            setIsRefreshing(true);\n            const params = new URLSearchParams({\n                limit: limit.toString(),\n                offset: '0'\n            });\n            if (type !== 'all') {\n                params.append('type', type);\n            }\n            const response = await fetch(\"/api/content?\".concat(params));\n            if (response.ok) {\n                const data = await response.json();\n                setContent(data.content || []);\n            }\n        } catch (error) {\n            console.error('Error fetching content:', error);\n        } finally{\n            setIsLoading(false);\n            setIsRefreshing(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RecentContent.useEffect\": ()=>{\n            fetchContent(selectedType);\n        }\n    }[\"RecentContent.useEffect\"], [\n        selectedType,\n        limit\n    ]);\n    const handleTypeFilter = (type)=>{\n        setSelectedType(type);\n    };\n    const handleDelete = async (contentId)=>{\n        try {\n            const response = await fetch(\"/api/content?id=\".concat(contentId), {\n                method: 'DELETE'\n            });\n            if (response.ok) {\n                setContent((prev)=>prev.filter((item)=>item.id !== contentId));\n            }\n        } catch (error) {\n            console.error('Error deleting content:', error);\n        }\n    };\n    const handleCopy = async (content)=>{\n        try {\n            await navigator.clipboard.writeText(content);\n        } catch (error) {\n            console.error('Error copying content:', error);\n        }\n    };\n    // Helper function to ensure articles are stored and get proper URLs\n    const getArticleViewUrl = async (item)=>{\n        try {\n            // First check if this article already has a stored URL by using its ID\n            if (item.id) {\n                // Check if the article exists in the clean URL system\n                const checkResponse = await fetch(\"/api/articles/\".concat(item.id));\n                if (checkResponse.ok) {\n                    return \"/article-view/\".concat(item.id);\n                }\n            }\n            // If not found, store the article using the proper API\n            const response = await fetch('/api/articles/store', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    title: item.title,\n                    content: item.content,\n                    type: item.type,\n                    metadata: item.metadata\n                })\n            });\n            const data = await response.json();\n            if (response.ok && data.success && data.url) {\n                return data.url;\n            }\n            // Fallback to legacy URL if storing fails\n            console.warn('Failed to store article, using fallback URL');\n            return \"/article-view?article=\".concat(encodeURIComponent(item.content), \"&title=\").concat(encodeURIComponent(item.title));\n        } catch (error) {\n            console.error('Error generating article URL:', error);\n            // Fallback to legacy URL on error\n            return \"/article-view?article=\".concat(encodeURIComponent(item.content), \"&title=\").concat(encodeURIComponent(item.title));\n        }\n    };\n    const handleViewClick = async (item, event)=>{\n        event.preventDefault();\n        try {\n            const url = await getArticleViewUrl(item);\n            window.location.href = url;\n        } catch (error) {\n            console.error('Error handling view click:', error);\n            // Fallback to direct navigation with legacy URL\n            window.location.href = \"/article-view?article=\".concat(encodeURIComponent(item.content), \"&title=\").concat(encodeURIComponent(item.title));\n        }\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString('en-US', {\n            month: 'short',\n            day: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n    const getContentConfig = (type)=>{\n        return contentTypeConfig[type] || contentTypeConfig.blog;\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-bold text-white\",\n                            children: \"Recent Content\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/RecentContent.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin w-5 h-5 border-2 border-violet-400 border-t-transparent rounded-full\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/RecentContent.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/RecentContent.tsx\",\n                    lineNumber: 202,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-4\",\n                    children: [\n                        1,\n                        2,\n                        3\n                    ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-pulse\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-24 bg-white/5 rounded-xl\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/RecentContent.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 15\n                            }, this)\n                        }, i, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/RecentContent.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/RecentContent.tsx\",\n                    lineNumber: 206,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/RecentContent.tsx\",\n            lineNumber: 201,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-bold text-white\",\n                        children: \"Recent Content\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/RecentContent.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            whileTap: {\n                                scale: 0.95\n                            },\n                            onClick: ()=>fetchContent(selectedType),\n                            disabled: isRefreshing,\n                            className: \"p-2 bg-white/5 hover:bg-white/10 border border-white/10 rounded-lg transition-colors disabled:opacity-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Crown_Eye_FileText_Mail_RefreshCw_Trash2_Video_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"w-4 h-4 text-gray-400 \".concat(isRefreshing ? 'animate-spin' : '')\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/RecentContent.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/RecentContent.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/RecentContent.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/RecentContent.tsx\",\n                lineNumber: 220,\n                columnNumber: 7\n            }, this),\n            showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-wrap gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>handleTypeFilter('all'),\n                        className: \"px-3 py-1.5 text-sm rounded-lg transition-colors \".concat(selectedType === 'all' ? 'bg-violet-600 text-white' : 'bg-white/5 text-gray-400 hover:bg-white/10 hover:text-white'),\n                        children: \"All\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/RecentContent.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 11\n                    }, this),\n                    Object.entries(contentTypeConfig).map((param)=>{\n                        let [type, config] = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>handleTypeFilter(type),\n                            className: \"px-3 py-1.5 text-sm rounded-lg transition-colors flex items-center space-x-1 \".concat(selectedType === type ? 'bg-violet-600 text-white' : 'bg-white/5 text-gray-400 hover:bg-white/10 hover:text-white'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(config.icon, {\n                                    className: \"w-3 h-3\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/RecentContent.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: config.label\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/RecentContent.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, type, true, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/RecentContent.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 13\n                        }, this);\n                    })\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/RecentContent.tsx\",\n                lineNumber: 237,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.AnimatePresence, {\n                    children: content.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Crown_Eye_FileText_Mail_RefreshCw_Trash2_Video_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"w-12 h-12 text-gray-600 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/RecentContent.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"No content found\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/RecentContent.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-500 mt-1\",\n                                children: \"Start creating content to see it here\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/RecentContent.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/RecentContent.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 13\n                    }, this) : content.map((item, index)=>{\n                        const config = getContentConfig(item.type);\n                        const IconComponent = config.icon;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            exit: {\n                                opacity: 0,\n                                y: -20\n                            },\n                            transition: {\n                                delay: index * 0.1\n                            },\n                            className: \"group relative backdrop-blur-xl border rounded-xl p-4 transition-all duration-300 \".concat(item.type === 'invincible_research' ? 'bg-white/10 border-white/20 hover:bg-white/15 hover:border-white/30 shadow-xl' : \"bg-white/5 \".concat(config.borderColor, \" hover:bg-white/10\")),\n                            children: [\n                                item.type === 'invincible_research' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-transparent rounded-xl\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/RecentContent.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 21\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative flex items-start justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-3 flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 rounded-lg border \".concat(item.type === 'invincible_research' ? 'bg-violet-700/20 border-violet-700/30 backdrop-blur-sm' : \"\".concat(config.bgColor, \" \").concat(config.borderColor)),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                        className: \"w-4 h-4 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/RecentContent.tsx\",\n                                                        lineNumber: 311,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/RecentContent.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-white font-medium truncate\",\n                                                            children: item.title\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/RecentContent.tsx\",\n                                                            lineNumber: 316,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-400 text-sm mt-1 line-clamp-2\",\n                                                            children: item.preview\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/RecentContent.tsx\",\n                                                            lineNumber: 317,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-4 mt-2 text-xs text-gray-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Crown_Eye_FileText_Mail_RefreshCw_Trash2_Video_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/RecentContent.tsx\",\n                                                                            lineNumber: 321,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: formatDate(item.createdAt)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/RecentContent.tsx\",\n                                                                            lineNumber: 322,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/RecentContent.tsx\",\n                                                                    lineNumber: 320,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                item.wordCount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        item.wordCount.toLocaleString(),\n                                                                        \" words\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/RecentContent.tsx\",\n                                                                    lineNumber: 325,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                item.tone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"capitalize\",\n                                                                    children: item.tone\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/RecentContent.tsx\",\n                                                                    lineNumber: 328,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/RecentContent.tsx\",\n                                                            lineNumber: 319,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/RecentContent.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/RecentContent.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                                    whileHover: {\n                                                        scale: 1.1\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.9\n                                                    },\n                                                    onClick: ()=>handleCopy(item.content),\n                                                    className: \"p-1.5 text-gray-400 hover:text-white rounded-lg transition-colors \".concat(item.type === 'invincible_research' ? 'hover:bg-white/15 backdrop-blur-sm' : 'hover:bg-white/10'),\n                                                    title: \"Copy content\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Crown_Eye_FileText_Mail_RefreshCw_Trash2_Video_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/RecentContent.tsx\",\n                                                        lineNumber: 347,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/RecentContent.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                                    whileHover: {\n                                                        scale: 1.1\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.9\n                                                    },\n                                                    onClick: (e)=>handleViewClick(item, e),\n                                                    className: \"p-1.5 text-gray-400 hover:text-white rounded-lg transition-colors \".concat(item.type === 'invincible_research' ? 'hover:bg-white/15 backdrop-blur-sm' : 'hover:bg-white/10'),\n                                                    title: \"View content\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Crown_Eye_FileText_Mail_RefreshCw_Trash2_Video_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/RecentContent.tsx\",\n                                                        lineNumber: 361,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/RecentContent.tsx\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                                    whileHover: {\n                                                        scale: 1.1\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.9\n                                                    },\n                                                    onClick: ()=>handleDelete(item.id),\n                                                    className: \"p-1.5 text-gray-400 hover:text-red-400 rounded-lg transition-colors \".concat(item.type === 'invincible_research' ? 'hover:bg-red-500/20 backdrop-blur-sm' : 'hover:bg-red-500/10'),\n                                                    title: \"Delete content\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Crown_Eye_FileText_Mail_RefreshCw_Trash2_Video_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/RecentContent.tsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/RecentContent.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/RecentContent.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/RecentContent.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, item.id, true, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/RecentContent.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 17\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/RecentContent.tsx\",\n                    lineNumber: 267,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/RecentContent.tsx\",\n                lineNumber: 266,\n                columnNumber: 7\n            }, this),\n            content.length >= limit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: \"/content\",\n                    className: \"text-violet-400 hover:text-violet-300 text-sm font-medium\",\n                    children: \"View all content →\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/RecentContent.tsx\",\n                    lineNumber: 389,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/RecentContent.tsx\",\n                lineNumber: 388,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/RecentContent.tsx\",\n        lineNumber: 218,\n        columnNumber: 5\n    }, this);\n}\n_s(RecentContent, \"4wYB0RWsOCC6SWUsjU2Ij3+S3lk=\");\n_c = RecentContent;\nvar _c;\n$RefreshReg$(_c, \"RecentContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/RecentContent.tsx\n"));

/***/ })

}]);