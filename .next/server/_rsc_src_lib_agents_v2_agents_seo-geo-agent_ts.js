"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_agents_v2_agents_seo-geo-agent_ts";
exports.ids = ["_rsc_src_lib_agents_v2_agents_seo-geo-agent_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/agents/v2/agents/seo-geo-agent.ts":
/*!***************************************************!*\
  !*** ./src/lib/agents/v2/agents/seo-geo-agent.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SEOGEOAgent: () => (/* binding */ SEOGEOAgent),\n/* harmony export */   createSEOGEOAgent: () => (/* binding */ createSEOGEOAgent)\n/* harmony export */ });\n/* harmony import */ var _core_state_schema__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../core/state-schema */ \"(rsc)/./src/lib/agents/v2/core/state-schema.ts\");\n/* harmony import */ var _tools_kimi_k2_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../tools/kimi-k2-client */ \"(rsc)/./src/lib/agents/v2/tools/kimi-k2-client.ts\");\n/* harmony import */ var _utils_json_parser__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/json-parser */ \"(rsc)/./src/lib/agents/v2/utils/json-parser.ts\");\n/**\n * Invincible V.2 - SEO & GEO Optimization Agent\n * Advanced optimization for traditional search engines and AI search systems\n */ \n\n\nclass SEOGEOAgent {\n    constructor(config){\n        this.config = {\n            enableGEO: true,\n            enableAdvancedSEO: true,\n            ...config\n        };\n        this.kimiClient = (0,_tools_kimi_k2_client__WEBPACK_IMPORTED_MODULE_1__.createKimiK2Client)(this.config.kimiApiKey);\n    }\n    /**\n   * Main SEO/GEO optimization execution\n   */ async execute(state) {\n        try {\n            let updatedState = _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.updateWorkflow(state, {\n                currentAgent: 'seo_geo_agent',\n                progress: 60\n            });\n            updatedState = _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.addMessage(updatedState, {\n                agent: 'seo_geo_agent',\n                action: 'seo_geo_optimization_started',\n                data: {\n                    enableGEO: this.config.enableGEO\n                },\n                status: 'in_progress'\n            });\n            // Phase 1: SEO Optimization\n            if (this.config.enableAdvancedSEO) {\n                const seoOptimization = await this.optimizeForSEO(updatedState);\n                updatedState = _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.updateGeneration(updatedState, {\n                    content: seoOptimization.optimizedContent\n                });\n                updatedState = _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.updateAnalysis(updatedState, {\n                    seo: {\n                        ...updatedState.analysis.seo,\n                        primaryKeywords: seoOptimization.keywords.primary,\n                        secondaryKeywords: seoOptimization.keywords.secondary\n                    }\n                });\n                updatedState = _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.addMessage(updatedState, {\n                    agent: 'seo_geo_agent',\n                    action: 'seo_optimization_completed',\n                    data: {\n                        seoScore: seoOptimization.seoScore,\n                        keywordsOptimized: seoOptimization.keywords.primary.length\n                    },\n                    status: 'completed'\n                });\n            }\n            // Phase 2: GEO Optimization\n            if (this.config.enableGEO) {\n                const geoOptimization = await this.optimizeForGEO(updatedState);\n                updatedState = _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.updateAnalysis(updatedState, {\n                    geo: geoOptimization.geoData\n                });\n                updatedState = _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.addMessage(updatedState, {\n                    agent: 'seo_geo_agent',\n                    action: 'geo_optimization_completed',\n                    data: {\n                        geoScore: geoOptimization.geoScore,\n                        aiSearchOptimized: geoOptimization.geoData.aiSearchVisibility\n                    },\n                    status: 'completed'\n                });\n            }\n            // Mark analysis as completed\n            updatedState = {\n                ...updatedState,\n                analysis: {\n                    ...updatedState.analysis,\n                    completed: true\n                }\n            };\n            updatedState = _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.updateWorkflow(updatedState, {\n                progress: 100,\n                nextAgent: 'quality_agent',\n                completedAgents: [\n                    ...updatedState.workflow.completedAgents,\n                    'seo_geo_agent'\n                ]\n            });\n            return updatedState;\n        } catch (error) {\n            console.error('SEO/GEO Agent error:', error);\n            const errorState = _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.updateWorkflow(state, {\n                errors: [\n                    ...state.workflow.errors,\n                    `SEO/GEO optimization failed: ${error}`\n                ]\n            });\n            return _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.addMessage(errorState, {\n                agent: 'seo_geo_agent',\n                action: 'seo_geo_optimization_failed',\n                data: {\n                    error: error.toString()\n                },\n                status: 'error'\n            });\n        }\n    }\n    /**\n   * Optimize content for traditional SEO\n   */ async optimizeForSEO(state) {\n        const content = state.generation.content.content;\n        const topic = state.requirements.topic;\n        const prompt = `Optimize this content for traditional search engine SEO while maintaining quality and readability.\n\n**Content to Optimize:**\n${content.substring(0, 2000)}...\n\n**Optimization Requirements:**\n1. Natural keyword integration (avoid keyword stuffing)\n2. Proper heading hierarchy (H1, H2, H3)\n3. Meta descriptions and title optimization\n4. Internal linking opportunities\n5. Semantic keyword variations\n6. Featured snippet optimization\n7. Voice search optimization\n\n**Target Keywords:**\nPrimary: \"${topic}\"\nSecondary: ${state.analysis.seo.secondaryKeywords.join(', ')}\n\nReturn the optimized content with improvements while maintaining natural flow and readability.`;\n        try {\n            const response = await this.kimiClient.generateContent([\n                {\n                    role: 'user',\n                    content: prompt\n                }\n            ]);\n            return {\n                optimizedContent: response.content,\n                seoScore: 85,\n                keywords: {\n                    primary: [\n                        topic,\n                        ...state.analysis.seo.primaryKeywords.slice(0, 3)\n                    ],\n                    secondary: state.analysis.seo.secondaryKeywords.slice(0, 10)\n                }\n            };\n        } catch (error) {\n            console.error('SEO optimization failed:', error);\n            throw error;\n        }\n    }\n    /**\n   * Optimize content for Generative Engine Optimization (GEO)\n   */ async optimizeForGEO(state) {\n        const content = state.generation.content.content;\n        const topic = state.requirements.topic;\n        const prompt = `Optimize this content for Generative Engine Optimization (GEO) - making it more likely to be referenced by AI search engines like ChatGPT, Perplexity, Gemini, and Claude.\n\n**Content:**\n${content.substring(0, 1500)}...\n\n**GEO Optimization Requirements:**\n1. **Citation-Friendly Structure**: Create clear, quotable sections\n2. **Fact-Based Content**: Include verifiable statistics and data\n3. **Authority Signals**: Add credibility markers and expert insights\n4. **Reference Optimization**: Structure for easy AI citation\n5. **Voice Search Ready**: Optimize for natural language queries\n6. **Multimodal Optimization**: Structure for voice and visual search\n7. **AI-Friendly Formatting**: Use clear headers and bullet points\n\n**Topic Focus:** \"${topic}\"\n\nProvide optimization recommendations and score the content's GEO readiness (1-100).\n\nReturn JSON:\n{\n  \"geoOptimizations\": [\"optimization1\", \"optimization2\", ...],\n  \"aiSearchVisibility\": {\n    \"perplexity\": true/false,\n    \"chatgpt\": true/false, \n    \"gemini\": true/false,\n    \"claude\": true/false\n  },\n  \"referenceOptimization\": {\n    \"citations\": [\"citation1\", \"citation2\", ...],\n    \"sourceCredibility\": score_1_100,\n    \"factualAccuracy\": score_1_100\n  },\n  \"multimodalOptimization\": {\n    \"voiceSearchReady\": true/false,\n    \"visualSearchReady\": true/false,\n    \"structuredData\": {}\n  },\n  \"geoScore\": score_1_100\n}`;\n        try {\n            const response = await this.kimiClient.generateContent([\n                {\n                    role: 'user',\n                    content: prompt\n                }\n            ]);\n            const geoData = (0,_utils_json_parser__WEBPACK_IMPORTED_MODULE_2__.safeJsonParse)(response.content, {\n                aiSearchVisibility: {\n                    perplexity: true,\n                    chatgpt: true,\n                    gemini: true,\n                    claude: true\n                },\n                referenceOptimization: {\n                    citations: [],\n                    sourceCredibility: 75,\n                    factualAccuracy: 80\n                },\n                multimodalOptimization: {\n                    voiceSearchReady: true,\n                    visualSearchReady: true,\n                    structuredData: {}\n                },\n                geoScore: 75\n            });\n            return {\n                geoData: {\n                    aiSearchVisibility: geoData?.aiSearchVisibility || {\n                        perplexity: true,\n                        chatgpt: true,\n                        gemini: true,\n                        claude: true\n                    },\n                    referenceOptimization: geoData?.referenceOptimization || {\n                        citations: [],\n                        sourceCredibility: 80,\n                        factualAccuracy: 85\n                    },\n                    multimodalOptimization: geoData?.multimodalOptimization || {\n                        voiceSearchReady: true,\n                        visualSearchReady: true,\n                        structuredData: {}\n                    }\n                },\n                geoScore: geoData?.geoScore || 80\n            };\n        } catch (error) {\n            console.error('GEO optimization failed:', error);\n            // Fallback GEO data\n            return {\n                geoData: {\n                    aiSearchVisibility: {\n                        perplexity: true,\n                        chatgpt: true,\n                        gemini: true,\n                        claude: true\n                    },\n                    referenceOptimization: {\n                        citations: [],\n                        sourceCredibility: 75,\n                        factualAccuracy: 80\n                    },\n                    multimodalOptimization: {\n                        voiceSearchReady: true,\n                        visualSearchReady: true,\n                        structuredData: {}\n                    }\n                },\n                geoScore: 75\n            };\n        }\n    }\n}\nfunction createSEOGEOAgent(config) {\n    return new SEOGEOAgent(config);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/agents/v2/agents/seo-geo-agent.ts\n");

/***/ })

};
;