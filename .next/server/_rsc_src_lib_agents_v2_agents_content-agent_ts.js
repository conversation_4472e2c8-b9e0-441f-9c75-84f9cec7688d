"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_agents_v2_agents_content-agent_ts";
exports.ids = ["_rsc_src_lib_agents_v2_agents_content-agent_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/agents/v2/agents/content-agent.ts":
/*!***************************************************!*\
  !*** ./src/lib/agents/v2/agents/content-agent.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ContentAgent: () => (/* binding */ ContentAgent),\n/* harmony export */   createContentAgent: () => (/* binding */ createContentAgent)\n/* harmony export */ });\n/* harmony import */ var _core_state_schema__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../core/state-schema */ \"(rsc)/./src/lib/agents/v2/core/state-schema.ts\");\n/* harmony import */ var _tools_kimi_k2_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../tools/kimi-k2-client */ \"(rsc)/./src/lib/agents/v2/tools/kimi-k2-client.ts\");\n/* harmony import */ var _utils_json_parser__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/json-parser */ \"(rsc)/./src/lib/agents/v2/utils/json-parser.ts\");\n/**\n * Invincible V.2 - Advanced Content Generation Agent\n * Human-like writing with competitive intelligence and superior quality\n */ \n\n\nclass ContentAgent {\n    constructor(config){\n        this.config = {\n            temperature: 0.8,\n            maxRetries: 3,\n            enableStreaming: false,\n            ...config\n        };\n        this.kimiClient = (0,_tools_kimi_k2_client__WEBPACK_IMPORTED_MODULE_1__.createKimiK2Client)(this.config.kimiApiKey);\n    }\n    /**\n   * Main content generation execution\n   */ async execute(state) {\n        try {\n            // Update workflow status\n            let updatedState = _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.updateWorkflow(state, {\n                currentAgent: 'content_agent',\n                progress: 30\n            });\n            updatedState = _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.addMessage(updatedState, {\n                agent: 'content_agent',\n                action: 'content_generation_started',\n                data: {\n                    topic: state.requirements.topic,\n                    targetLength: state.requirements.contentLength\n                },\n                status: 'in_progress'\n            });\n            // Phase 1: Analyze research and competitive data\n            const analysis = this.analyzeResearchData(state);\n            updatedState = _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.addMessage(updatedState, {\n                agent: 'content_agent',\n                action: 'research_analysis_completed',\n                data: {\n                    researchQuality: analysis.qualityScore,\n                    competitiveAdvantage: analysis.competitiveOpportunities.length\n                },\n                status: 'completed'\n            });\n            // Phase 2: Determine content strategy\n            const strategy = await this.determineContentStrategy(state, analysis);\n            updatedState = _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.addMessage(updatedState, {\n                agent: 'content_agent',\n                action: 'content_strategy_determined',\n                data: {\n                    approach: strategy.approach,\n                    writingStyle: strategy.writingStyle,\n                    structureType: strategy.structureType\n                },\n                status: 'completed'\n            });\n            // Phase 3: Generate content outline\n            const outline = await this.generateAdvancedOutline(state, analysis, strategy);\n            updatedState = _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.updateGeneration(updatedState, {\n                outline: outline.sections\n            });\n            updatedState = _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.updateWorkflow(updatedState, {\n                progress: 50\n            });\n            updatedState = _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.addMessage(updatedState, {\n                agent: 'content_agent',\n                action: 'outline_generated',\n                data: {\n                    sections: outline.sections.length,\n                    estimatedWords: outline.estimatedWordCount\n                },\n                status: 'completed'\n            });\n            // Phase 4: Generate main content\n            const content = await this.generateSuperiorContent(state, analysis, strategy, outline);\n            updatedState = _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.updateGeneration(updatedState, {\n                content: content.content,\n                title: content.title,\n                wordCount: content.wordCount\n            });\n            updatedState = _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.updateWorkflow(updatedState, {\n                progress: 75\n            });\n            updatedState = _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.addMessage(updatedState, {\n                agent: 'content_agent',\n                action: 'content_generated',\n                data: {\n                    wordCount: content.wordCount,\n                    title: content.title\n                },\n                status: 'completed'\n            });\n            // Phase 5: Generate meta content\n            const metaContent = await this.generateMetaContent(content, state);\n            updatedState = _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.updateGeneration(updatedState, {\n                metaDescription: metaContent.metaDescription\n            });\n            // Phase 6: Apply initial humanization\n            const humanizedContent = await this.applyInitialHumanization(content.content);\n            updatedState = _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.updateGeneration(updatedState, {\n                content: humanizedContent.content,\n                humanizationApplied: humanizedContent.techniques\n            });\n            // Mark content generation as completed\n            updatedState = {\n                ...updatedState,\n                generation: {\n                    ...updatedState.generation,\n                    completed: true,\n                    iterations: 1\n                }\n            };\n            updatedState = _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.updateWorkflow(updatedState, {\n                progress: 100,\n                nextAgent: 'seo_geo_agent',\n                completedAgents: [\n                    ...updatedState.workflow.completedAgents,\n                    'content_agent'\n                ]\n            });\n            updatedState = _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.addMessage(updatedState, {\n                agent: 'content_agent',\n                action: 'content_generation_completed',\n                data: {\n                    finalWordCount: updatedState.generation.content.wordCount,\n                    humanizationTechniques: humanizedContent.techniques.length\n                },\n                status: 'completed'\n            });\n            return updatedState;\n        } catch (error) {\n            console.error('Content Agent error:', error);\n            const errorState = _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.updateWorkflow(state, {\n                errors: [\n                    ...state.workflow.errors,\n                    `Content generation failed: ${error}`\n                ]\n            });\n            return _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.addMessage(errorState, {\n                agent: 'content_agent',\n                action: 'content_generation_failed',\n                data: {\n                    error: error.toString()\n                },\n                status: 'error'\n            });\n        }\n    }\n    /**\n   * Analyze research data for content opportunities\n   */ analyzeResearchData(state) {\n        const researchData = state.research.data;\n        const competitiveAnalysis = state.analysis.competitive;\n        // Calculate research quality score\n        const totalResults = researchData.reduce((sum, r)=>sum + r.results.length, 0);\n        const qualityScore = Math.min(100, totalResults / 50 * 100);\n        // Extract key insights from research\n        const keyInsights = this.extractKeyInsights(researchData);\n        // Identify competitive opportunities\n        const competitiveOpportunities = [\n            ...competitiveAnalysis.contentGaps,\n            ...competitiveAnalysis.opportunities\n        ];\n        // Identify authority signals\n        const authoritySignals = this.extractAuthoritySignals(researchData);\n        return {\n            qualityScore,\n            keyInsights,\n            competitiveOpportunities,\n            contentGaps: competitiveAnalysis.contentGaps,\n            authoritySignals\n        };\n    }\n    /**\n   * Determine optimal content generation strategy\n   */ async determineContentStrategy(state, analysis) {\n        const prompt = `Based on the content requirements and research analysis, determine the optimal content generation strategy.\n\n**Content Requirements:**\n- Topic: \"${state.requirements.topic}\"\n- Length: ${state.requirements.contentLength} words\n- Tone: ${state.requirements.tone}\n- Audience: ${state.requirements.targetAudience}\n- Custom Instructions: ${state.requirements.customInstructions || 'None'}\n\n**Research Analysis:**\n- Quality Score: ${analysis.qualityScore}/100\n- Key Insights: ${analysis.keyInsights.slice(0, 5).join(', ')}\n- Competitive Opportunities: ${analysis.competitiveOpportunities.slice(0, 3).join(', ')}\n- Content Gaps: ${analysis.contentGaps.slice(0, 3).join(', ')}\n\n**Strategy Options:**\n1. **Approach**: comprehensive (detailed coverage), focused (specific angle), narrative (story-driven), analytical (data-driven)\n2. **Writing Style**: conversational (friendly), professional (formal), authoritative (expert), engaging (dynamic)\n3. **Structure Type**: traditional (intro-body-conclusion), modern (problem-solution), narrative (story), comparison (vs analysis)\n4. **Humanization Level**: light (minimal), moderate (balanced), aggressive (maximum human-like qualities)\n\nReturn JSON with optimal strategy:\n{\n  \"approach\": \"approach_type\",\n  \"writingStyle\": \"style_type\", \n  \"structureType\": \"structure_type\",\n  \"humanizationLevel\": \"level\",\n  \"reasoning\": \"Explanation of strategy choices\"\n}`;\n        try {\n            const response = await this.kimiClient.generateContent([\n                {\n                    role: 'user',\n                    content: prompt\n                }\n            ]);\n            const strategy = (0,_utils_json_parser__WEBPACK_IMPORTED_MODULE_2__.safeJsonParse)(response.content, {\n                approach: state.requirements.contentLength > 2500 ? 'comprehensive' : 'focused',\n                writingStyle: state.requirements.tone === 'casual' ? 'conversational' : 'professional',\n                structureType: 'traditional',\n                humanizationLevel: 'moderate'\n            });\n            return {\n                approach: strategy?.approach || 'comprehensive',\n                writingStyle: strategy?.writingStyle || 'professional',\n                structureType: strategy?.structureType || 'traditional',\n                humanizationLevel: strategy?.humanizationLevel || 'moderate'\n            };\n        } catch (error) {\n            console.error('Strategy determination failed:', error);\n            // Fallback strategy based on requirements\n            return {\n                approach: state.requirements.contentLength > 2500 ? 'comprehensive' : 'focused',\n                writingStyle: state.requirements.tone === 'casual' ? 'conversational' : 'professional',\n                structureType: 'traditional',\n                humanizationLevel: 'moderate'\n            };\n        }\n    }\n    /**\n   * Generate advanced content outline\n   */ async generateAdvancedOutline(state, analysis, strategy) {\n        const prompt = `Create an advanced content outline that will outperform all competitor content on \"${state.requirements.topic}\".\n\n**Content Strategy:**\n- Approach: ${strategy.approach}\n- Writing Style: ${strategy.writingStyle}  \n- Structure Type: ${strategy.structureType}\n- Target Length: ${state.requirements.contentLength} words\n\n**Competitive Intelligence:**\n- Content Gaps to Fill: ${analysis.contentGaps.join(', ')}\n- Opportunities: ${analysis.competitiveOpportunities.join(', ')}\n- Authority Signals to Include: ${analysis.authoritySignals.slice(0, 5).join(', ')}\n\n**Requirements:**\n1. Create comprehensive sections that cover gaps competitors miss\n2. Include unique angles and insights not found elsewhere\n3. Structure for optimal SEO and user engagement\n4. Plan for voice search and AI search optimization\n5. Include practical examples and actionable insights\n\nReturn JSON with detailed outline:\n{\n  \"sections\": [\n    \"Section 1: Title\",\n    \"Section 2: Title\", \n    ...\n  ],\n  \"sectionDetails\": [\n    {\n      \"title\": \"Section Title\",\n      \"wordCount\": estimated_words,\n      \"keyPoints\": [\"point1\", \"point2\", ...],\n      \"purpose\": \"why this section outperforms competitors\"\n    },\n    ...\n  ],\n  \"uniqueAngles\": [\"angle1\", \"angle2\", ...],\n  \"competitiveAdvantages\": [\"advantage1\", \"advantage2\", ...]\n}`;\n        try {\n            const response = await this.kimiClient.generateContent([\n                {\n                    role: 'user',\n                    content: prompt\n                }\n            ]);\n            const outline = (0,_utils_json_parser__WEBPACK_IMPORTED_MODULE_2__.safeJsonParse)(response.content, {\n                sections: [],\n                sectionDetails: []\n            });\n            const estimatedWordCount = outline?.sectionDetails?.reduce((sum, section)=>sum + (section.wordCount || 200), 0) || state.requirements.contentLength;\n            return {\n                sections: outline?.sections || [],\n                estimatedWordCount,\n                sectionDetails: outline?.sectionDetails || []\n            };\n        } catch (error) {\n            console.error('Outline generation failed:', error);\n            // Fallback outline\n            const sections = [\n                `Introduction to ${state.requirements.topic}`,\n                `What is ${state.requirements.topic}? - Complete Guide`,\n                `Benefits and Advantages of ${state.requirements.topic}`,\n                `How to Get Started with ${state.requirements.topic}`,\n                `Best Practices and Pro Tips`,\n                `Common Challenges and Solutions`,\n                `Future Trends and Predictions`,\n                `Conclusion and Next Steps`\n            ];\n            return {\n                sections,\n                estimatedWordCount: state.requirements.contentLength,\n                sectionDetails: sections.map((title)=>({\n                        title,\n                        wordCount: Math.floor(state.requirements.contentLength / sections.length),\n                        keyPoints: [],\n                        purpose: 'Comprehensive coverage'\n                    }))\n            };\n        }\n    }\n    /**\n   * Generate superior content that outperforms competitors\n   */ async generateSuperiorContent(state, analysis, strategy, outline) {\n        // Research data for context\n        const researchContext = state.research.data.map((r)=>`Query: ${r.query}\\nTop insights: ${r.results.slice(0, 3).map((res)=>res.content.substring(0, 200)).join(' | ')}`).join('\\n\\n');\n        const prompt = `Create superior content on \"${state.requirements.topic}\" that outperforms all competitor content.\n\n**Content Strategy:**\n- Approach: ${strategy.approach}\n- Writing Style: ${strategy.writingStyle}\n- Structure: ${strategy.structureType}\n- Target Length: ${state.requirements.contentLength} words\n- Tone: ${state.requirements.tone}\n- Audience: ${state.requirements.targetAudience}\n\n**Detailed Outline:**\n${outline.sections.map((section, index)=>`${index + 1}. ${section}`).join('\\n')}\n\n**Research Context:**\n${researchContext.substring(0, 3000)}\n\n**Competitive Advantages to Include:**\n${analysis.competitiveOpportunities.slice(0, 5).join('\\n')}\n\n**Authority Signals to Incorporate:**\n${analysis.authoritySignals.slice(0, 5).join('\\n')}\n\n**Writing Instructions:**\n1. **Human-Like Quality**: Write naturally with varied sentence structures, conversational transitions, and personal insights\n2. **Competitive Superiority**: Include unique information, deeper insights, and better examples than competitors\n3. **SEO Optimization**: Naturally integrate keywords while maintaining readability\n4. **GEO Optimization**: Structure content for AI systems to easily reference and cite\n5. **Engagement**: Use compelling hooks, real examples, and actionable advice\n6. **Authority**: Include data, expert insights, and credible information\n7. **Completeness**: Cover the topic comprehensively without gaps\n\n**Content Requirements:**\n- Use semantic HTML structure (h1, h2, h3, p, ul, ol)\n- Include natural keyword variations\n- Add transition sentences between sections\n- Use active voice and varied sentence lengths\n- Include specific examples and practical applications\n- End with clear next steps or conclusions\n\nCreate the complete article content in HTML format.`;\n        try {\n            const response = await this.kimiClient.generateArticleContent({\n                topic: state.requirements.topic,\n                outline: outline.sections,\n                requirements: state.requirements,\n                research: state.research.data,\n                competitorAnalysis: analysis\n            });\n            // Extract title from content or generate one\n            const content = response.content;\n            const titleMatch = content.match(/<h1[^>]*>(.*?)<\\/h1>/i);\n            const extractedTitle = titleMatch ? titleMatch[1].replace(/<[^>]*>/g, '') : '';\n            const title = extractedTitle || await this.generateTitle(state.requirements.topic, content);\n            // Calculate word count (approximate)\n            const wordCount = content.replace(/<[^>]*>/g, '').split(/\\s+/).length;\n            // Calculate readability score (simplified)\n            const readabilityScore = this.calculateReadabilityScore(content);\n            return {\n                content,\n                title,\n                wordCount,\n                readabilityScore\n            };\n        } catch (error) {\n            console.error('Content generation failed:', error);\n            throw new Error(`Content generation failed: ${error}`);\n        }\n    }\n    /**\n   * Generate meta content (title tag, meta description)\n   */ async generateMetaContent(content, state) {\n        const keywords = state.analysis.seo.primaryKeywords.slice(0, 5);\n        try {\n            const response = await this.kimiClient.generateMetaContent({\n                title: content.title,\n                content: content.content.substring(0, 1000),\n                keywords,\n                targetLength: 160\n            });\n            const metaData = (0,_utils_json_parser__WEBPACK_IMPORTED_MODULE_2__.safeJsonParse)(response.content, {\n                metaDescription: '',\n                titleTag: content.title,\n                focusKeyword: keywords[0] || state.requirements.topic\n            });\n            return {\n                metaDescription: metaData?.metaDescription || '',\n                titleTag: metaData?.titleTag || content.title,\n                focusKeyword: metaData?.focusKeyword || keywords[0] || state.requirements.topic\n            };\n        } catch (error) {\n            console.error('Meta content generation failed:', error);\n            // Fallback meta content\n            return {\n                metaDescription: `Comprehensive guide to ${state.requirements.topic}. Learn everything you need to know with expert insights and practical examples.`,\n                titleTag: content.title,\n                focusKeyword: state.requirements.topic\n            };\n        }\n    }\n    /**\n   * Apply initial humanization techniques\n   */ async applyInitialHumanization(content) {\n        try {\n            const response = await this.kimiClient.humanizeContent(content);\n            const techniques = [\n                'Sentence variation applied',\n                'Natural transitions added',\n                'Conversational elements integrated',\n                'Personal observations included',\n                'Rhythm patterns optimized'\n            ];\n            return {\n                content: response.content,\n                techniques\n            };\n        } catch (error) {\n            console.error('Initial humanization failed:', error);\n            return {\n                content,\n                techniques: [\n                    'Basic humanization applied'\n                ]\n            };\n        }\n    }\n    /**\n   * Generate compelling title\n   */ async generateTitle(topic, content) {\n        const prompt = `Generate a compelling, SEO-optimized title for this content about \"${topic}\".\n\nContent preview: ${content.substring(0, 500)}\n\nRequirements:\n- 50-60 characters for optimal SEO\n- Include primary keyword naturally\n- Be compelling and click-worthy\n- Avoid clickbait\n- Professional tone\n\nReturn just the title, no additional text.`;\n        try {\n            const response = await this.kimiClient.generateContent([\n                {\n                    role: 'user',\n                    content: prompt\n                }\n            ]);\n            return response.content.trim().replace(/^[\"']|[\"']$/g, '');\n        } catch (error) {\n            return `Complete Guide to ${topic}`;\n        }\n    }\n    /**\n   * Extract key insights from research data\n   */ extractKeyInsights(researchData) {\n        const insights = [];\n        for (const research of researchData){\n            for (const result of research.results.slice(0, 3)){\n                const content = result.content.toLowerCase();\n                // Extract insights based on common patterns\n                if (content.includes('study shows') || content.includes('research indicates')) {\n                    insights.push('Research-backed insights available');\n                }\n                if (content.includes('expert') || content.includes('specialist')) {\n                    insights.push('Expert perspectives identified');\n                }\n                if (content.includes('trend') || content.includes('growing')) {\n                    insights.push('Current trends and growth patterns');\n                }\n                if (content.includes('benefit') || content.includes('advantage')) {\n                    insights.push('Clear benefits and advantages');\n                }\n            }\n        }\n        return [\n            ...new Set(insights)\n        ].slice(0, 10);\n    }\n    /**\n   * Extract authority signals from research\n   */ extractAuthoritySignals(researchData) {\n        const signals = [];\n        for (const research of researchData){\n            for (const result of research.results.slice(0, 2)){\n                const content = result.content;\n                // Look for statistics\n                const stats = content.match(/\\d+%|\\d+\\s*(million|billion|thousand)/gi);\n                if (stats) {\n                    signals.push(`Statistics: ${stats.slice(0, 2).join(', ')}`);\n                }\n                // Look for authoritative sources\n                if (result.domain.includes('.edu') || result.domain.includes('.gov')) {\n                    signals.push(`Authoritative source: ${result.domain}`);\n                }\n                // Look for research citations\n                if (content.includes('study') || content.includes('research')) {\n                    signals.push('Research citations available');\n                }\n            }\n        }\n        return [\n            ...new Set(signals)\n        ].slice(0, 8);\n    }\n    /**\n   * Calculate readability score (simplified Flesch Reading Ease)\n   */ calculateReadabilityScore(content) {\n        const text = content.replace(/<[^>]*>/g, '');\n        const sentences = text.split(/[.!?]+/).length;\n        const words = text.split(/\\s+/).length;\n        const syllables = this.countSyllables(text);\n        if (sentences === 0 || words === 0) return 0;\n        const avgSentenceLength = words / sentences;\n        const avgSyllablesPerWord = syllables / words;\n        // Flesch Reading Ease formula\n        const score = 206.835 - 1.015 * avgSentenceLength - 84.6 * avgSyllablesPerWord;\n        return Math.max(0, Math.min(100, Math.round(score)));\n    }\n    /**\n   * Count syllables in text (simplified)\n   */ countSyllables(text) {\n        const words = text.toLowerCase().split(/\\s+/);\n        let totalSyllables = 0;\n        for (const word of words){\n            const syllableCount = word.match(/[aeiouy]+/g)?.length || 1;\n            totalSyllables += syllableCount;\n        }\n        return totalSyllables;\n    }\n}\n/**\n * Factory function to create content agent\n */ function createContentAgent(config) {\n    return new ContentAgent(config);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/agents/v2/agents/content-agent.ts\n");

/***/ })

};
;