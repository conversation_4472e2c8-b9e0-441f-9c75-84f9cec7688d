/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/invincible/page";
exports.ids = ["app/invincible/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Finvincible%2Fpage&page=%2Finvincible%2Fpage&appPaths=%2Finvincible%2Fpage&pagePath=private-next-app-dir%2Finvincible%2Fpage.tsx&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Finvincible%2Fpage&page=%2Finvincible%2Fpage&appPaths=%2Finvincible%2Fpage&pagePath=private-next-app-dir%2Finvincible%2Fpage.tsx&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/invincible/page.tsx */ \"(rsc)/./src/app/invincible/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'invincible',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/invincible/page\",\n        pathname: \"/invincible\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Finvincible%2Fpage&page=%2Finvincible%2Fpage&appPaths=%2Finvincible%2Fpage&pagePath=private-next-app-dir%2Finvincible%2Fpage.tsx&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcomponents%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcomponents%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/SessionProvider.tsx */ \"(rsc)/./src/components/SessionProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWF5dXNobWlzaHJhJTJGRGVza3RvcCUyRm9sZCUyMGludmluY2libGUlMjB3aXRoJTIwZGVlcHJlc2VhcmNoJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmZvbnQlMkZnb29nbGUlMkZ0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMnNyYyUyRmFwcCUyRmxheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWF5dXNobWlzaHJhJTJGRGVza3RvcCUyRm9sZCUyMGludmluY2libGUlMjB3aXRoJTIwZGVlcHJlc2VhcmNoJTJGc3JjJTJGYXBwJTJGZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZhYXl1c2htaXNocmElMkZEZXNrdG9wJTJGb2xkJTIwaW52aW5jaWJsZSUyMHdpdGglMjBkZWVwcmVzZWFyY2glMkZzcmMlMkZjb21wb25lbnRzJTJGU2Vzc2lvblByb3ZpZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9MQUFpSyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIi9Vc2Vycy9hYXl1c2htaXNocmEvRGVza3RvcC9vbGQgaW52aW5jaWJsZSB3aXRoIGRlZXByZXNlYXJjaC9zcmMvY29tcG9uZW50cy9TZXNzaW9uUHJvdmlkZXIudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcomponents%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Finvincible%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Finvincible%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/invincible/page.tsx */ \"(rsc)/./src/app/invincible/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWF5dXNobWlzaHJhJTJGRGVza3RvcCUyRm9sZCUyMGludmluY2libGUlMjB3aXRoJTIwZGVlcHJlc2VhcmNoJTJGc3JjJTJGYXBwJTJGaW52aW5jaWJsZSUyRnBhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzS0FBNkgiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9hYXl1c2htaXNocmEvRGVza3RvcC9vbGQgaW52aW5jaWJsZSB3aXRoIGRlZXByZXNlYXJjaC9zcmMvYXBwL2ludmluY2libGUvcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Finvincible%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"333781e81268\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvVXNlcnMvYWF5dXNobWlzaHJhL0Rlc2t0b3Avb2xkIGludmluY2libGUgd2l0aCBkZWVwcmVzZWFyY2gvc3JjL2FwcC9nbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjMzMzc4MWU4MTI2OFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/invincible/page.tsx":
/*!*************************************!*\
  !*** ./src/app/invincible/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_SessionProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/SessionProvider */ \"(rsc)/./src/components/SessionProvider.tsx\");\n\n\n\n\nconst metadata = {\n    title: 'Invincible - AI Content Generation Platform',\n    description: 'The ultimate content writing SaaS platform powered by advanced AI technology'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"dark\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className)} bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 min-h-screen`,\n            suppressHydrationWarning: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SessionProvider__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/SessionProvider.tsx":
/*!********************************************!*\
  !*** ./src/components/SessionProvider.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SessionProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SessionProvider.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcomponents%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcomponents%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/SessionProvider.tsx */ \"(ssr)/./src/components/SessionProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWF5dXNobWlzaHJhJTJGRGVza3RvcCUyRm9sZCUyMGludmluY2libGUlMjB3aXRoJTIwZGVlcHJlc2VhcmNoJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmZvbnQlMkZnb29nbGUlMkZ0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMnNyYyUyRmFwcCUyRmxheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWF5dXNobWlzaHJhJTJGRGVza3RvcCUyRm9sZCUyMGludmluY2libGUlMjB3aXRoJTIwZGVlcHJlc2VhcmNoJTJGc3JjJTJGYXBwJTJGZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZhYXl1c2htaXNocmElMkZEZXNrdG9wJTJGb2xkJTIwaW52aW5jaWJsZSUyMHdpdGglMjBkZWVwcmVzZWFyY2glMkZzcmMlMkZjb21wb25lbnRzJTJGU2Vzc2lvblByb3ZpZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9MQUFpSyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIi9Vc2Vycy9hYXl1c2htaXNocmEvRGVza3RvcC9vbGQgaW52aW5jaWJsZSB3aXRoIGRlZXByZXNlYXJjaC9zcmMvY29tcG9uZW50cy9TZXNzaW9uUHJvdmlkZXIudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcomponents%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Finvincible%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Finvincible%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/invincible/page.tsx */ \"(ssr)/./src/app/invincible/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWF5dXNobWlzaHJhJTJGRGVza3RvcCUyRm9sZCUyMGludmluY2libGUlMjB3aXRoJTIwZGVlcHJlc2VhcmNoJTJGc3JjJTJGYXBwJTJGaW52aW5jaWJsZSUyRnBhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzS0FBNkgiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9hYXl1c2htaXNocmEvRGVza3RvcC9vbGQgaW52aW5jaWJsZSB3aXRoIGRlZXByZXNlYXJjaC9zcmMvYXBwL2ludmluY2libGUvcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Finvincible%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/invincible/page.tsx":
/*!*************************************!*\
  !*** ./src/app/invincible/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ InvinciblePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart,Brain,Crown,Monitor,Rocket,Search,Settings,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart,Brain,Crown,Monitor,Rocket,Search,Settings,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart,Brain,Crown,Monitor,Rocket,Search,Settings,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart,Brain,Crown,Monitor,Rocket,Search,Settings,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart,Brain,Crown,Monitor,Rocket,Search,Settings,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart,Brain,Crown,Monitor,Rocket,Search,Settings,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart,Brain,Crown,Monitor,Rocket,Search,Settings,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart,Brain,Crown,Monitor,Rocket,Search,Settings,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart,Brain,Crown,Monitor,Rocket,Search,Settings,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart,Brain,Crown,Monitor,Rocket,Search,Settings,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bar-chart.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart,Brain,Crown,Monitor,Rocket,Search,Settings,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart,Brain,Crown,Monitor,Rocket,Search,Settings,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart,Brain,Crown,Monitor,Rocket,Search,Settings,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _components_InvincibleStreamingUI__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/InvincibleStreamingUI */ \"(ssr)/./src/components/InvincibleStreamingUI.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nfunction InvinciblePage() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_4__.useSession)();\n    const [config, setConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        topic: '',\n        customInstructions: '',\n        targetAudience: '',\n        contentLength: 2000,\n        tone: 'professional',\n        keywords: [],\n        searchDepth: 7,\n        competitorCount: 5,\n        deepSearchQueriesPerTopic: 7,\n        uniquenessLevel: 'high',\n        version: 'v1'\n    });\n    const [keywordInput, setKeywordInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showStreamingUI, setShowStreamingUI] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [streamingResult, setStreamingResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Handle URL parameters from Megatron\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"InvinciblePage.useEffect\": ()=>{\n            const topic = searchParams.get('topic');\n            const customInstructions = searchParams.get('customInstructions');\n            const source = searchParams.get('source');\n            const videoUrl = searchParams.get('videoUrl');\n            const videoTitle = searchParams.get('videoTitle');\n            if (source === 'megatron' && topic) {\n                setConfig({\n                    \"InvinciblePage.useEffect\": (prevConfig)=>({\n                            ...prevConfig,\n                            topic: topic,\n                            customInstructions: customInstructions || prevConfig.customInstructions,\n                            // Add metadata about the source\n                            ...videoUrl && {\n                                sourceVideoUrl: videoUrl\n                            },\n                            ...videoTitle && {\n                                sourceVideoTitle: videoTitle\n                            }\n                        })\n                }[\"InvinciblePage.useEffect\"]);\n            }\n        }\n    }[\"InvinciblePage.useEffect\"], [\n        searchParams\n    ]);\n    // Redirect to login if not authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"InvinciblePage.useEffect\": ()=>{\n            if (status === 'unauthenticated') {\n                window.location.href = '/login';\n            }\n        }\n    }[\"InvinciblePage.useEffect\"], [\n        status\n    ]);\n    // Loading state\n    if (status === 'loading') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-black flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin w-12 h-12 border-2 border-violet-400 border-t-transparent rounded-full mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                lineNumber: 106,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n            lineNumber: 105,\n            columnNumber: 7\n        }, this);\n    }\n    // Don't render if not authenticated\n    if (status === 'unauthenticated') {\n        return null;\n    }\n    const handleStartStreaming = async ()=>{\n        if (!config.topic.trim()) {\n            setError('Please enter a topic');\n            return;\n        }\n        setError('');\n        setIsStreaming(true);\n        // For all modes including autonomous, use the streaming UI\n        setShowStreamingUI(true);\n    };\n    const handleStreamingComplete = async (result)=>{\n        setStreamingResult(result);\n        setIsStreaming(false);\n        setIsSaving(true);\n        try {\n            console.log('🔍 Raw result structure:', {\n                hasResult: !!result.result,\n                hasArticle: !!result.article,\n                hasContent: !!result.content,\n                resultKeys: Object.keys(result || {}),\n                resultType: typeof result.result,\n                resultContent: result.result ? Object.keys(result.result) : 'N/A'\n            });\n            // Extract article data with comprehensive fallback logic\n            let articleData = null;\n            let articleTitle = '';\n            let articleContent = '';\n            // Handle different result structures\n            if (result.result) {\n                // result.result path\n                articleData = result.result;\n                articleTitle = result.result.title || '';\n                articleContent = result.result.content || '';\n                console.log('📄 Using result.result path');\n            } else if (result.article) {\n                // Alternative: result.article path\n                articleData = result.article;\n                articleTitle = result.article.title || '';\n                articleContent = result.article.content || '';\n                console.log('📄 Using result.article path');\n            } else if (result.content) {\n                // Direct content path\n                articleContent = result.content;\n                articleTitle = result.title || `Article about ${config.topic}`;\n                console.log('📄 Using result.content path');\n            } else {\n                // Last resort: use result directly\n                articleData = result;\n                articleTitle = result.title || `Article about ${config.topic}`;\n                articleContent = result.content || '';\n                console.log('📄 Using result direct path');\n            }\n            console.log('📊 Extracted article data:', {\n                hasArticleData: !!articleData,\n                title: articleTitle,\n                titleLength: articleTitle?.length || 0,\n                contentLength: articleContent?.length || 0,\n                contentPreview: articleContent?.substring(0, 200) || 'EMPTY',\n                contentType: typeof articleContent\n            });\n            // Validate title\n            if (!articleTitle || articleTitle.trim().length === 0) {\n                articleTitle = `Complete Guide to ${config.topic}`;\n                console.log('⚠️ Using fallback title:', articleTitle);\n            }\n            // Validate content with detailed error reporting\n            if (!articleContent || articleContent.trim().length === 0) {\n                console.error('❌ Content validation failed:', {\n                    contentExists: !!articleContent,\n                    contentType: typeof articleContent,\n                    contentLength: articleContent?.length || 0,\n                    rawContent: articleContent,\n                    resultStructure: JSON.stringify(result, null, 2).substring(0, 1000),\n                    configVersion: config.version,\n                    extractionPath: 'fallback paths'\n                });\n                // Error message for content generation failure\n                const errorMessage = `Content generation failed - no content was generated. This might be due to API issues or content filtering. Please try again or contact support if the issue persists.`;\n                throw new Error(errorMessage);\n            }\n            // Final validation\n            if (articleContent.trim().length < 50) {\n                throw new Error(`Generated content is too short (${articleContent.length} characters). Please try again.`);\n            }\n            // Save the article to the database\n            const response = await fetch('/api/articles/store', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    title: articleTitle,\n                    content: articleContent,\n                    type: 'invincible',\n                    metadata: {\n                        topic: config.topic,\n                        tone: config.tone,\n                        targetAudience: config.targetAudience,\n                        contentLength: config.contentLength,\n                        customInstructions: config.customInstructions,\n                        keywords: config.keywords,\n                        executionTime: result.executionTime || result.stats?.executionTime,\n                        totalSources: result.insights?.totalSources || result.stats?.totalSources,\n                        uniquenessScore: result.stats?.uniquenessScore,\n                        seoScore: articleData?.seoScore || result.article?.seoScore,\n                        readabilityScore: articleData?.readabilityScore || result.article?.readabilityScore,\n                        qualityScore: result.qualityScore || result.insights?.qualityScore,\n                        competitorsAnalyzed: result.insights?.competitorsAnalyzed,\n                        iterationsCompleted: result.insights?.iterationsCompleted,\n                        factCheckReport: result.factCheckReport,\n                        generatedAt: new Date().toISOString()\n                    },\n                    tone: config.tone,\n                    language: 'en'\n                })\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                throw new Error(`Failed to save article: ${errorText}`);\n            }\n            const saveResult = await response.json();\n            if (saveResult.success) {\n                // Small delay to show saving completion\n                await new Promise((resolve)=>setTimeout(resolve, 1000));\n                // Redirect to the article view page with the saved article ID\n                router.push(`/article-view/${saveResult.article.id}`);\n            } else {\n                throw new Error(saveResult.error || 'Failed to save article');\n            }\n        } catch (error) {\n            console.error('Error saving article:', error);\n            setIsSaving(false);\n            setError(`Article generated successfully but failed to save: ${error instanceof Error ? error.message : 'Unknown error'}. Please try again.`);\n        }\n    };\n    const handleStreamingError = (error)=>{\n        setError(error);\n        setIsStreaming(false);\n    };\n    const addKeyword = ()=>{\n        if (keywordInput.trim() && !config.keywords?.includes(keywordInput.trim())) {\n            setConfig({\n                ...config,\n                keywords: [\n                    ...config.keywords || [],\n                    keywordInput.trim()\n                ]\n            });\n            setKeywordInput('');\n        }\n    };\n    const removeKeyword = (keyword)=>{\n        setConfig({\n            ...config,\n            keywords: config.keywords?.filter((k)=>k !== keyword) || []\n        });\n    };\n    const resetToConfiguration = ()=>{\n        setShowStreamingUI(false);\n        setIsStreaming(false);\n        setStreamingResult(null);\n        setError('');\n        setIsSaving(false);\n    };\n    const viewGeneratedArticle = ()=>{\n        router.push('/article-view');\n    };\n    // If showing streaming UI, render the streaming component\n    if (showStreamingUI) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_InvincibleStreamingUI__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            topic: config.topic,\n            contentLength: config.contentLength,\n            tone: config.tone,\n            targetAudience: config.targetAudience,\n            customInstructions: config.customInstructions,\n            onComplete: handleStreamingComplete,\n            onError: handleStreamingError,\n            isSaving: isSaving\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n            lineNumber: 309,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-black\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-violet-900/10 via-black to-indigo-900/10\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                        lineNumber: 326,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        animate: {\n                            x: [\n                                0,\n                                100,\n                                0\n                            ],\n                            y: [\n                                0,\n                                -100,\n                                0\n                            ]\n                        },\n                        transition: {\n                            duration: 20,\n                            repeat: Infinity,\n                            ease: \"linear\"\n                        },\n                        className: \"absolute top-1/4 left-1/4 w-[500px] h-[500px] bg-violet-500/10 rounded-full blur-[100px]\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                        lineNumber: 329,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        animate: {\n                            x: [\n                                0,\n                                -100,\n                                0\n                            ],\n                            y: [\n                                0,\n                                100,\n                                0\n                            ]\n                        },\n                        transition: {\n                            duration: 15,\n                            repeat: Infinity,\n                            ease: \"linear\"\n                        },\n                        className: \"absolute bottom-1/4 right-1/4 w-[600px] h-[600px] bg-indigo-500/10 rounded-full blur-[120px]\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                        lineNumber: 341,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                lineNumber: 325,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.header, {\n                initial: {\n                    opacity: 0,\n                    y: -20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                className: \"relative z-10 border-b border-white/10 backdrop-blur-xl bg-black/40\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-6 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/dashboard\",\n                                        className: \"flex items-center space-x-3 text-gray-400 hover:text-white transition-colors group\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-5 h-5 group-hover:-translate-x-1 transition-transform\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"Back to Dashboard\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 369,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-6 w-px bg-white/20\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 rounded-xl blur-lg opacity-70 bg-gradient-to-r from-violet-600 to-indigo-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative bg-black rounded-xl p-2.5 border border-white/20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-6 h-6 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                            lineNumber: 378,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-xl font-bold text-white\",\n                                                        children: \"Invincible\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-400\",\n                                                        children: \"Advanced AI Content Generation\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                lineNumber: 363,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden md:flex items-center space-x-6 text-sm text-gray-400\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-4 h-4 text-emerald-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 395,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"AI Detection Bypass\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 396,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-4 h-4 text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Live Streaming\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 400,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-4 h-4 text-yellow-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Real-time Analysis\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                lineNumber: 392,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                        lineNumber: 362,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                    lineNumber: 361,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                lineNumber: 356,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"relative z-10 max-w-6xl mx-auto px-6 py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-flex items-center space-x-2 px-4 py-2 rounded-full bg-white/10 backdrop-blur-md border border-white/20 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"w-4 h-4 text-green-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                        lineNumber: 421,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-200\",\n                                        children: \"Live Streaming Generation\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"w-4 h-4 text-violet-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-200\",\n                                        children: \"AI Detection Bypass\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                        lineNumber: 424,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-4 h-4 text-yellow-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-5xl md:text-6xl font-bold text-white mb-6 leading-tight\",\n                                children: [\n                                    \"Watch Your Content\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"block bg-gradient-to-r from-violet-400 via-purple-400 to-indigo-400 bg-clip-text text-transparent\",\n                                        children: \"Come to Life\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                lineNumber: 428,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-300 max-w-3xl mx-auto mb-8 leading-relaxed\",\n                                children: \"Experience the most advanced content generation with real-time streaming. Watch as our AI analyzes competitors, processes research, applies humanization techniques, and creates superior articles that bypass AI detection.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                lineNumber: 435,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto mt-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center mb-4 mx-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-6 h-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 443,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-white mb-2\",\n                                                children: \"Live Streaming\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 445,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"Watch queries, analysis, and generation in real-time\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 448,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                        lineNumber: 441,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center mb-4 mx-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-6 h-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-white mb-2\",\n                                                children: \"AI Detection Bypass\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"Advanced humanization with date variation and jargon removal\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center mb-4 mx-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-6 h-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 466,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-white mb-2\",\n                                                children: \"Superior Quality\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"Content that surpasses all competitors with comprehensive research\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                        lineNumber: 465,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                lineNumber: 440,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                        lineNumber: 415,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: -20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    delay: 0.1\n                                },\n                                className: \"lg:col-span-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 mb-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 bg-gradient-to-r from-violet-600 to-indigo-600 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"w-5 h-5 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 490,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-2xl font-bold text-white\",\n                                                    children: \"Configuration\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 493,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                            lineNumber: 489,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-white/90\",\n                                                            children: \"Invincible Version\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                            lineNumber: 499,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-2 gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: ()=>setConfig({\n                                                                            ...config,\n                                                                            version: 'v1'\n                                                                        }),\n                                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"px-6 py-4 rounded-xl border transition-all flex items-center space-x-3\", config.version === 'v1' ? \"bg-green-600/20 border-green-500/50 text-green-300\" : \"bg-white/5 border-white/10 text-gray-300 hover:bg-white/10 hover:border-white/20\"),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"w-5 h-5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                            lineNumber: 513,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-left\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"font-semibold\",\n                                                                                    children: \"V.1 Classic\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                                    lineNumber: 515,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-xs opacity-80\",\n                                                                                    children: \"Original streaming system\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                                    lineNumber: 516,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                            lineNumber: 514,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                    lineNumber: 503,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: ()=>setConfig({\n                                                                            ...config,\n                                                                            version: 'v2'\n                                                                        }),\n                                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"px-6 py-4 rounded-xl border transition-all flex items-center space-x-3 relative\", config.version === 'v2' ? \"bg-violet-600/20 border-violet-500/50 text-violet-300\" : \"bg-white/5 border-white/10 text-gray-300 hover:bg-white/10 hover:border-white/20\"),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                            className: \"w-5 h-5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                            lineNumber: 529,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-left\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"font-semibold flex items-center space-x-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: \"V.2 Autonomous\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                                            lineNumber: 532,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"px-2 py-1 bg-violet-600/30 text-xs rounded-full\",\n                                                                                            children: \"BETA\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                                            lineNumber: 533,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                                    lineNumber: 531,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-xs opacity-80\",\n                                                                                    children: \"Kimi K2 + LangGraph + Tavily\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                                    lineNumber: 535,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                            lineNumber: 530,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                    lineNumber: 519,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                            lineNumber: 502,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 498,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-white/90\",\n                                                            children: [\n                                                                \"Topic \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-violet-400\",\n                                                                    children: \"*\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                    lineNumber: 544,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                            lineNumber: 543,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: config.topic,\n                                                            onChange: (e)=>setConfig({\n                                                                    ...config,\n                                                                    topic: e.target.value\n                                                                }),\n                                                            className: \"w-full px-4 py-4 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:bg-white/10 focus:border-violet-500/50 transition-all\",\n                                                            placeholder: \"e.g., The 5 Best CLI Agents of 2025\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                            lineNumber: 546,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 542,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-white/90\",\n                                                                    children: \"Target Audience\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                    lineNumber: 559,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: config.targetAudience,\n                                                                    onChange: (e)=>setConfig({\n                                                                            ...config,\n                                                                            targetAudience: e.target.value\n                                                                        }),\n                                                                    className: \"w-full px-4 py-4 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:bg-white/10 focus:border-violet-500/50 transition-all\",\n                                                                    placeholder: \"e.g., Developers and tech enthusiasts\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                    lineNumber: 562,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                            lineNumber: 558,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-white/90\",\n                                                                    children: \"Content Length (words)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                    lineNumber: 572,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: config.contentLength,\n                                                                    onChange: (e)=>setConfig({\n                                                                            ...config,\n                                                                            contentLength: parseInt(e.target.value) || 2000\n                                                                        }),\n                                                                    className: \"w-full px-4 py-4 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:bg-white/10 focus:border-violet-500/50 transition-all\",\n                                                                    min: \"500\",\n                                                                    max: \"5000\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                    lineNumber: 575,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                            lineNumber: 571,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 557,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-white/90\",\n                                                            children: \"Custom Instructions\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                            lineNumber: 588,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            value: config.customInstructions,\n                                                            onChange: (e)=>setConfig({\n                                                                    ...config,\n                                                                    customInstructions: e.target.value\n                                                                }),\n                                                            className: \"w-full px-4 py-4 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:bg-white/10 focus:border-violet-500/50 transition-all resize-none\",\n                                                            rows: 4,\n                                                            placeholder: \"e.g., Focus on practical value and real-world usage. Include specific examples and avoid generic advice.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                            lineNumber: 591,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 587,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-white/90\",\n                                                            children: \"Writing Tone\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                            lineNumber: 602,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-2 md:grid-cols-3 gap-3\",\n                                                            children: [\n                                                                'professional',\n                                                                'conversational',\n                                                                'casual',\n                                                                'authoritative',\n                                                                'friendly',\n                                                                'technical'\n                                                            ].map((tone)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: ()=>setConfig({\n                                                                            ...config,\n                                                                            tone\n                                                                        }),\n                                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"px-4 py-3 rounded-xl border transition-all capitalize\", config.tone === tone ? \"bg-violet-600/20 border-violet-500/50 text-violet-300\" : \"bg-white/5 border-white/10 text-gray-300 hover:bg-white/10 hover:border-white/20\"),\n                                                                    children: tone\n                                                                }, tone, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                    lineNumber: 607,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                            lineNumber: 605,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 601,\n                                                    columnNumber: 17\n                                                }, this),\n                                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                                    initial: {\n                                                        opacity: 0,\n                                                        scale: 0.9\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        scale: 1\n                                                    },\n                                                    className: \"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-red-700 dark:text-red-300 text-sm\",\n                                                        children: error\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 633,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 628,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                                    onClick: handleStartStreaming,\n                                                    whileHover: {\n                                                        scale: 1.02\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.98\n                                                    },\n                                                    className: \"w-full py-4 bg-gradient-to-r from-violet-600 via-purple-600 to-indigo-600 text-white rounded-xl font-semibold text-lg hover:shadow-2xl transition-all duration-300 flex items-center justify-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"w-6 h-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                            lineNumber: 644,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Start Live Generation\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                            lineNumber: 645,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-6 h-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                            lineNumber: 646,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                    lineNumber: 638,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                            lineNumber: 496,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                    lineNumber: 488,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                lineNumber: 482,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    delay: 0.2\n                                },\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-2 bg-gradient-to-r from-green-600 to-emerald-600 rounded-lg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"w-5 h-5 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                            lineNumber: 663,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 662,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-bold text-white\",\n                                                        children: \"What You'll See\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 665,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 661,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 670,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-white font-medium\",\n                                                                        children: \"Live Search Queries\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                        lineNumber: 672,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-400 text-sm\",\n                                                                        children: \"Watch as AI generates and executes targeted research queries\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                        lineNumber: 673,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 671,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 669,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 678,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-white font-medium\",\n                                                                        children: \"Real-time Analysis\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                        lineNumber: 680,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-400 text-sm\",\n                                                                        children: \"See competitor analysis and content understanding in action\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                        lineNumber: 681,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 679,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 677,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-purple-400 rounded-full mt-2 flex-shrink-0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 686,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-white font-medium\",\n                                                                        children: \"AI Humanization\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                        lineNumber: 688,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-400 text-sm\",\n                                                                        children: \"Watch date variation, jargon removal, and bypass techniques\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                        lineNumber: 689,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 687,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 685,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-orange-400 rounded-full mt-2 flex-shrink-0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 694,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-white font-medium\",\n                                                                        children: \"Content Generation\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                        lineNumber: 696,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-400 text-sm\",\n                                                                        children: \"See superior content creation with full context\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                        lineNumber: 697,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 695,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 693,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 668,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                        lineNumber: 660,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-2 bg-gradient-to-r from-blue-600 to-cyan-600 rounded-lg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"w-5 h-5 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                            lineNumber: 707,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 706,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-bold text-white\",\n                                                        children: \"Generation Stats\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 709,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 705,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-green-400\",\n                                                                children: \"10+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 714,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: \"Search Queries\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 715,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 713,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-blue-400\",\n                                                                children: \"15+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 718,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: \"Sources Analyzed\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 719,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 717,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-purple-400\",\n                                                                children: \"4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 722,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: \"Analysis Phases\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 723,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 721,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-orange-400\",\n                                                                children: \"8+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 726,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: \"AI Bypass Techniques\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 727,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 725,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 712,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                        lineNumber: 704,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-violet-900/20 to-indigo-900/20 border border-violet-500/20 rounded-2xl p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-6 h-6 text-yellow-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 735,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-bold text-white\",\n                                                        children: \"Enhanced Features\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 736,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 734,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"w-4 h-4 text-emerald-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 741,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-300\",\n                                                                children: \"Advanced AI Detection Bypass\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 742,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 740,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"w-4 h-4 text-purple-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 745,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-300\",\n                                                                children: \"Human Writing Pattern Analysis\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 746,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 744,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"w-4 h-4 text-blue-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 749,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-300\",\n                                                                children: \"Comprehensive Research\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 750,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 748,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart_Brain_Crown_Monitor_Rocket_Search_Settings_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                className: \"w-4 h-4 text-red-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 753,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-300\",\n                                                                children: \"Superior Competition Analysis\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                                lineNumber: 754,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                        lineNumber: 752,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                                lineNumber: 739,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                        lineNumber: 733,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                                lineNumber: 653,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                        lineNumber: 480,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n                lineNumber: 413,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible/page.tsx\",\n        lineNumber: 323,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/invincible/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/InvincibleStreamingUI.tsx":
/*!**************************************************!*\
  !*** ./src/components/InvincibleStreamingUI.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowRight_Brain_CheckCircle_Clock_Cpu_Database_FileText_Globe_Search_Shield_Target_Terminal_TrendingUp_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowRight,Brain,CheckCircle,Clock,Cpu,Database,FileText,Globe,Search,Shield,Target,Terminal,TrendingUp,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/cpu.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowRight_Brain_CheckCircle_Clock_Cpu_Database_FileText_Globe_Search_Shield_Target_Terminal_TrendingUp_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowRight,Brain,CheckCircle,Clock,Cpu,Database,FileText,Globe,Search,Shield,Target,Terminal,TrendingUp,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowRight_Brain_CheckCircle_Clock_Cpu_Database_FileText_Globe_Search_Shield_Target_Terminal_TrendingUp_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowRight,Brain,CheckCircle,Clock,Cpu,Database,FileText,Globe,Search,Shield,Target,Terminal,TrendingUp,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowRight_Brain_CheckCircle_Clock_Cpu_Database_FileText_Globe_Search_Shield_Target_Terminal_TrendingUp_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowRight,Brain,CheckCircle,Clock,Cpu,Database,FileText,Globe,Search,Shield,Target,Terminal,TrendingUp,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowRight_Brain_CheckCircle_Clock_Cpu_Database_FileText_Globe_Search_Shield_Target_Terminal_TrendingUp_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowRight,Brain,CheckCircle,Clock,Cpu,Database,FileText,Globe,Search,Shield,Target,Terminal,TrendingUp,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/terminal.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowRight_Brain_CheckCircle_Clock_Cpu_Database_FileText_Globe_Search_Shield_Target_Terminal_TrendingUp_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowRight,Brain,CheckCircle,Clock,Cpu,Database,FileText,Globe,Search,Shield,Target,Terminal,TrendingUp,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowRight_Brain_CheckCircle_Clock_Cpu_Database_FileText_Globe_Search_Shield_Target_Terminal_TrendingUp_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowRight,Brain,CheckCircle,Clock,Cpu,Database,FileText,Globe,Search,Shield,Target,Terminal,TrendingUp,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowRight_Brain_CheckCircle_Clock_Cpu_Database_FileText_Globe_Search_Shield_Target_Terminal_TrendingUp_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowRight,Brain,CheckCircle,Clock,Cpu,Database,FileText,Globe,Search,Shield,Target,Terminal,TrendingUp,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowRight_Brain_CheckCircle_Clock_Cpu_Database_FileText_Globe_Search_Shield_Target_Terminal_TrendingUp_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowRight,Brain,CheckCircle,Clock,Cpu,Database,FileText,Globe,Search,Shield,Target,Terminal,TrendingUp,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowRight_Brain_CheckCircle_Clock_Cpu_Database_FileText_Globe_Search_Shield_Target_Terminal_TrendingUp_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowRight,Brain,CheckCircle,Clock,Cpu,Database,FileText,Globe,Search,Shield,Target,Terminal,TrendingUp,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowRight_Brain_CheckCircle_Clock_Cpu_Database_FileText_Globe_Search_Shield_Target_Terminal_TrendingUp_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowRight,Brain,CheckCircle,Clock,Cpu,Database,FileText,Globe,Search,Shield,Target,Terminal,TrendingUp,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowRight_Brain_CheckCircle_Clock_Cpu_Database_FileText_Globe_Search_Shield_Target_Terminal_TrendingUp_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowRight,Brain,CheckCircle,Clock,Cpu,Database,FileText,Globe,Search,Shield,Target,Terminal,TrendingUp,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowRight_Brain_CheckCircle_Clock_Cpu_Database_FileText_Globe_Search_Shield_Target_Terminal_TrendingUp_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowRight,Brain,CheckCircle,Clock,Cpu,Database,FileText,Globe,Search,Shield,Target,Terminal,TrendingUp,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowRight_Brain_CheckCircle_Clock_Cpu_Database_FileText_Globe_Search_Shield_Target_Terminal_TrendingUp_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowRight,Brain,CheckCircle,Clock,Cpu,Database,FileText,Globe,Search,Shield,Target,Terminal,TrendingUp,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowRight_Brain_CheckCircle_Clock_Cpu_Database_FileText_Globe_Search_Shield_Target_Terminal_TrendingUp_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowRight,Brain,CheckCircle,Clock,Cpu,Database,FileText,Globe,Search,Shield,Target,Terminal,TrendingUp,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowRight_Brain_CheckCircle_Clock_Cpu_Database_FileText_Globe_Search_Shield_Target_Terminal_TrendingUp_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowRight,Brain,CheckCircle,Clock,Cpu,Database,FileText,Globe,Search,Shield,Target,Terminal,TrendingUp,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst InvincibleStreamingUI = ({ topic, contentLength, tone, targetAudience, customInstructions, onComplete, onError, isSaving })=>{\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentPhase, setCurrentPhase] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [events, setEvents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchQueries, setSearchQueries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [scrapedDomains, setScrapedDomains] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [analysisComplete, setAnalysisComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [humanizationStats, setHumanizationStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [finalResult, setFinalResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [connectionStatus, setConnectionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('connecting');\n    const eventsContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const eventSourceRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Auto-start streaming when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"InvincibleStreamingUI.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"InvincibleStreamingUI.useEffect.timer\": ()=>{\n                    startStreaming();\n                }\n            }[\"InvincibleStreamingUI.useEffect.timer\"], 1000); // Small delay for dramatic effect\n            return ({\n                \"InvincibleStreamingUI.useEffect\": ()=>{\n                    clearTimeout(timer);\n                    if (eventSourceRef.current) {\n                        eventSourceRef.current.close();\n                    }\n                }\n            })[\"InvincibleStreamingUI.useEffect\"];\n        }\n    }[\"InvincibleStreamingUI.useEffect\"], []);\n    const startStreaming = ()=>{\n        if (isStreaming) return;\n        console.log('🔍 InvincibleStreamingUI: Starting streaming');\n        setIsStreaming(true);\n        setProgress(0);\n        setEvents([]);\n        setSearchQueries([]);\n        setScrapedDomains([]);\n        setAnalysisComplete([]);\n        setHumanizationStats({});\n        setFinalResult(null);\n        setError('');\n        setConnectionStatus('connecting');\n        // Safe JSON parsing helper\n        const safeParseJSON = (rawData)=>{\n            try {\n                if (!rawData || rawData === 'undefined' || rawData === 'null') {\n                    console.warn('Received invalid SSE data:', rawData);\n                    return null;\n                }\n                return JSON.parse(rawData);\n            } catch (error) {\n                console.error('Failed to parse SSE data:', rawData, error);\n                return null;\n            }\n        };\n        console.log('🎯 Routing to streaming endpoint');\n        // Uses GET with query parameters for EventSource\n        const params = new URLSearchParams({\n            topic: topic,\n            contentLength: (contentLength || 2000).toString(),\n            tone: tone || 'professional',\n            targetAudience: targetAudience || 'general audience',\n            customInstructions: customInstructions || ''\n        });\n        const eventSource = new EventSource(`/api/invincible/stream?${params.toString()}`);\n        eventSourceRef.current = eventSource;\n        // Connection established\n        eventSource.onopen = ()=>{\n            setConnectionStatus('connected');\n            addEvent('connection', {\n                message: 'Connection established',\n                timestamp: Date.now()\n            });\n        };\n        // Handle different event types\n        eventSource.addEventListener('start', (e)=>{\n            const data = safeParseJSON(e.data);\n            if (data) {\n                setCurrentPhase('initialization');\n                addEvent('start', data);\n            }\n        });\n        eventSource.addEventListener('phase', (e)=>{\n            const data = safeParseJSON(e.data);\n            if (data) {\n                setCurrentPhase(data.phase);\n                setProgress(data.step / data.total * 100);\n                addEvent('phase', data);\n            }\n        });\n        eventSource.addEventListener('search_query', (e)=>{\n            const data = safeParseJSON(e.data);\n            if (data && data.query) {\n                setSearchQueries((prev)=>[\n                        ...prev,\n                        data.query\n                    ]);\n                addEvent('search_query', data);\n            }\n        });\n        eventSource.addEventListener('search_results', (e)=>{\n            const data = safeParseJSON(e.data);\n            if (data) {\n                addEvent('search_results', data);\n            }\n        });\n        eventSource.addEventListener('scraping', (e)=>{\n            const data = safeParseJSON(e.data);\n            if (data && data.domain) {\n                setScrapedDomains((prev)=>[\n                        ...prev,\n                        data.domain\n                    ]);\n                addEvent('scraping', data);\n            }\n        });\n        eventSource.addEventListener('analysis', (e)=>{\n            const data = safeParseJSON(e.data);\n            if (data && data.type) {\n                setAnalysisComplete((prev)=>[\n                        ...prev,\n                        data.type\n                    ]);\n                addEvent('analysis', data);\n            }\n        });\n        eventSource.addEventListener('humanization', (e)=>{\n            const data = safeParseJSON(e.data);\n            if (data && data.type) {\n                setHumanizationStats((prev)=>({\n                        ...prev,\n                        [data.type]: data\n                    }));\n                addEvent('humanization', data);\n            }\n        });\n        eventSource.addEventListener('progress', (e)=>{\n            const data = safeParseJSON(e.data);\n            if (data) {\n                addEvent('progress', data);\n            }\n        });\n        eventSource.addEventListener('success', (e)=>{\n            const data = safeParseJSON(e.data);\n            if (data) {\n                setFinalResult(data);\n                setProgress(100);\n                setConnectionStatus('complete');\n                addEvent('success', data);\n                onComplete?.(data);\n            }\n        });\n        eventSource.addEventListener('error', (e)=>{\n            const data = safeParseJSON(e.data);\n            if (data) {\n                setError(data.error || 'An error occurred');\n                addEvent('error', data);\n                onError?.(data.error || 'An error occurred');\n            }\n        });\n        eventSource.addEventListener('complete', (e)=>{\n            setIsStreaming(false);\n            setConnectionStatus('complete');\n            eventSource.close();\n        });\n        eventSource.onerror = (error)=>{\n            console.error('SSE connection error occurred');\n            console.error('Event details:', {\n                readyState: eventSource.readyState,\n                url: eventSource.url,\n                timestamp: new Date().toISOString()\n            });\n            let errorMessage = 'Connection error';\n            // Determine error cause based on readyState\n            switch(eventSource.readyState){\n                case EventSource.CONNECTING:\n                    errorMessage = 'Unable to establish connection to server';\n                    break;\n                case EventSource.CLOSED:\n                    errorMessage = 'Connection to server was closed unexpectedly';\n                    break;\n                default:\n                    errorMessage = 'Connection error occurred';\n            }\n            setError(`${errorMessage} - attempting to reconnect...`);\n            setConnectionStatus('error');\n            setIsStreaming(false);\n            eventSource.close();\n            // Attempt to reconnect after 3 seconds\n            setTimeout(()=>{\n                if (!finalResult) {\n                    console.log('Attempting to reconnect...');\n                    startStreaming();\n                }\n            }, 3000);\n        };\n    };\n    const addEvent = (type, data)=>{\n        const event = {\n            type,\n            data,\n            timestamp: Date.now()\n        };\n        setEvents((prev)=>[\n                ...prev,\n                event\n            ].slice(-50)); // Keep only last 50 events\n    };\n    // Auto-scroll events container\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"InvincibleStreamingUI.useEffect\": ()=>{\n            if (eventsContainerRef.current) {\n                eventsContainerRef.current.scrollTop = eventsContainerRef.current.scrollHeight;\n            }\n        }\n    }[\"InvincibleStreamingUI.useEffect\"], [\n        events\n    ]);\n    const getPhaseInfo = (phase)=>{\n        switch(phase){\n            case 'initialization':\n                return {\n                    icon: _barrel_optimize_names_Activity_AlertCircle_ArrowRight_Brain_CheckCircle_Clock_Cpu_Database_FileText_Globe_Search_Shield_Target_Terminal_TrendingUp_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n                    name: 'System Initialization',\n                    color: 'from-blue-400 to-cyan-400',\n                    bgColor: 'bg-blue-500/10',\n                    description: 'Booting up Invincible AI systems...'\n                };\n            case 'primary_search':\n                return {\n                    icon: _barrel_optimize_names_Activity_AlertCircle_ArrowRight_Brain_CheckCircle_Clock_Cpu_Database_FileText_Globe_Search_Shield_Target_Terminal_TrendingUp_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                    name: 'Primary Search Protocol',\n                    color: 'from-green-400 to-emerald-400',\n                    bgColor: 'bg-green-500/10',\n                    description: 'Executing competitive intelligence gathering...'\n                };\n            case 'content_analysis':\n                return {\n                    icon: _barrel_optimize_names_Activity_AlertCircle_ArrowRight_Brain_CheckCircle_Clock_Cpu_Database_FileText_Globe_Search_Shield_Target_Terminal_TrendingUp_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                    name: 'Neural Content Analysis',\n                    color: 'from-purple-400 to-pink-400',\n                    bgColor: 'bg-purple-500/10',\n                    description: 'Deep learning analysis in progress...'\n                };\n            case 'comprehensive_research':\n                return {\n                    icon: _barrel_optimize_names_Activity_AlertCircle_ArrowRight_Brain_CheckCircle_Clock_Cpu_Database_FileText_Globe_Search_Shield_Target_Terminal_TrendingUp_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                    name: 'Data Mining Operations',\n                    color: 'from-orange-400 to-red-400',\n                    bgColor: 'bg-orange-500/10',\n                    description: 'Extracting knowledge from global sources...'\n                };\n            case 'content_generation':\n                return {\n                    icon: _barrel_optimize_names_Activity_AlertCircle_ArrowRight_Brain_CheckCircle_Clock_Cpu_Database_FileText_Globe_Search_Shield_Target_Terminal_TrendingUp_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                    name: 'Content Synthesis',\n                    color: 'from-violet-400 to-indigo-400',\n                    bgColor: 'bg-violet-500/10',\n                    description: 'Generating superior content architecture...'\n                };\n            default:\n                return {\n                    icon: _barrel_optimize_names_Activity_AlertCircle_ArrowRight_Brain_CheckCircle_Clock_Cpu_Database_FileText_Globe_Search_Shield_Target_Terminal_TrendingUp_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                    name: 'Processing',\n                    color: 'from-gray-400 to-gray-500',\n                    bgColor: 'bg-gray-500/10',\n                    description: 'System operations active...'\n                };\n        }\n    };\n    const getConnectionStatusColor = ()=>{\n        switch(connectionStatus){\n            case 'connecting':\n                return 'text-yellow-400';\n            case 'connected':\n                return 'text-green-400';\n            case 'error':\n                return 'text-red-400';\n            case 'complete':\n                return 'text-blue-400';\n            default:\n                return 'text-gray-400';\n        }\n    };\n    const phaseInfo = getPhaseInfo(currentPhase);\n    const PhaseIcon = phaseInfo.icon;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-black relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                        lineNumber: 345,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-10\",\n                        children: [\n                            ...Array(20)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                className: \"absolute w-px bg-gradient-to-b from-transparent via-green-400 to-transparent\",\n                                style: {\n                                    left: `${i * 5}%`,\n                                    height: '100%'\n                                },\n                                animate: {\n                                    opacity: [\n                                        0.1,\n                                        0.3,\n                                        0.1\n                                    ],\n                                    scaleY: [\n                                        0.5,\n                                        1,\n                                        0.5\n                                    ]\n                                },\n                                transition: {\n                                    duration: 2 + Math.random() * 3,\n                                    repeat: Infinity,\n                                    delay: Math.random() * 2\n                                }\n                            }, i, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                lineNumber: 350,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                        lineNumber: 348,\n                        columnNumber: 9\n                    }, undefined),\n                    [\n                        ...Array(30)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                            className: \"absolute w-1 h-1 bg-violet-400 rounded-full\",\n                            style: {\n                                left: `${Math.random() * 100}%`,\n                                top: `${Math.random() * 100}%`\n                            },\n                            animate: {\n                                y: [\n                                    0,\n                                    -20,\n                                    0\n                                ],\n                                opacity: [\n                                    0.2,\n                                    0.8,\n                                    0.2\n                                ],\n                                scale: [\n                                    0.5,\n                                    1,\n                                    0.5\n                                ]\n                            },\n                            transition: {\n                                duration: 3 + Math.random() * 4,\n                                repeat: Infinity,\n                                delay: Math.random() * 3\n                            }\n                        }, i, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                            lineNumber: 372,\n                            columnNumber: 11\n                        }, undefined))\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                lineNumber: 344,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 p-6 max-w-7xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: -20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        className: \"text-center mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-flex items-center space-x-3 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-r from-violet-600 to-indigo-600 rounded-xl blur-lg opacity-70\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                lineNumber: 404,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative bg-black rounded-xl p-3 border border-violet-500/50\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowRight_Brain_CheckCircle_Clock_Cpu_Database_FileText_Globe_Search_Shield_Target_Terminal_TrendingUp_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-8 h-8 text-violet-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                    lineNumber: 406,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl font-bold bg-gradient-to-r from-white via-violet-200 to-indigo-200 bg-clip-text text-transparent\",\n                                        children: \"INVINCIBLE LIVE GENERATION\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center space-x-2 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowRight_Brain_CheckCircle_Clock_Cpu_Database_FileText_Globe_Search_Shield_Target_Terminal_TrendingUp_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: `w-4 h-4 ${getConnectionStatusColor()}`\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: `${getConnectionStatusColor()} font-medium`,\n                                        children: connectionStatus.toUpperCase()\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                        lineNumber: 416,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-500\",\n                                        children: \"•\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Real-time AI Processing\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                lineNumber: 414,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                        lineNumber: 397,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            scale: 0.95\n                        },\n                        animate: {\n                            opacity: 1,\n                            scale: 1\n                        },\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-violet-900/20 to-indigo-900/20 backdrop-blur-xl border border-violet-500/30 rounded-2xl p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-gradient-to-r from-violet-500 to-indigo-500 rounded-xl\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowRight_Brain_CheckCircle_Clock_Cpu_Database_FileText_Globe_Search_Shield_Target_Terminal_TrendingUp_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-6 h-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-bold text-white mb-2\",\n                                                children: \"Mission Target\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold bg-gradient-to-r from-violet-400 to-indigo-400 bg-clip-text text-transparent mb-3\",\n                                                children: [\n                                                    '\"',\n                                                    topic,\n                                                    '\"'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                lineNumber: 439,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-400\",\n                                                                children: \"Length:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                                lineNumber: 444,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white ml-2 font-medium\",\n                                                                children: [\n                                                                    contentLength || 2000,\n                                                                    \" words\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                                lineNumber: 445,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                        lineNumber: 443,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-400\",\n                                                                children: \"Tone:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                                lineNumber: 448,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white ml-2 font-medium\",\n                                                                children: tone || 'professional'\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                                lineNumber: 449,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                        lineNumber: 447,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-400\",\n                                                                children: \"Audience:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                                lineNumber: 452,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white ml-2 font-medium\",\n                                                                children: targetAudience || 'general'\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                                lineNumber: 453,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                        lineNumber: 451,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-400\",\n                                                                children: \"Mode:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                                lineNumber: 456,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-green-400 ml-2 font-medium\",\n                                                                children: \"INVINCIBLE\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                                lineNumber: 457,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                        lineNumber: 455,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                        lineNumber: 437,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                lineNumber: 433,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                            lineNumber: 432,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                        lineNumber: 427,\n                        columnNumber: 9\n                    }, undefined),\n                    isStreaming && currentPhase && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `${phaseInfo.bgColor} backdrop-blur-xl border border-white/10 rounded-2xl p-6`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: `p-3 bg-gradient-to-r ${phaseInfo.color} rounded-xl`,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PhaseIcon, {\n                                                        className: \"w-6 h-6 text-black\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                        lineNumber: 476,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                    lineNumber: 475,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-bold text-white\",\n                                                            children: phaseInfo.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                            lineNumber: 479,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-300\",\n                                                            children: phaseInfo.description\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                            lineNumber: 480,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                    lineNumber: 478,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                            lineNumber: 474,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-3xl font-bold text-white mb-1\",\n                                                    children: [\n                                                        Math.round(progress),\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                    lineNumber: 484,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: \"Complete\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                    lineNumber: 485,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                            lineNumber: 483,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                    lineNumber: 473,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full bg-black/50 rounded-full h-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                            className: `h-3 rounded-full bg-gradient-to-r ${phaseInfo.color} relative overflow-hidden`,\n                                            initial: {\n                                                width: 0\n                                            },\n                                            animate: {\n                                                width: `${progress}%`\n                                            },\n                                            transition: {\n                                                duration: 0.8,\n                                                ease: \"easeOut\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                lineNumber: 497,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                            lineNumber: 491,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                        lineNumber: 490,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                    lineNumber: 489,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                            lineNumber: 472,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                        lineNumber: 467,\n                        columnNumber: 11\n                    }, undefined),\n                    isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 lg:grid-cols-4 gap-4 mb-8\",\n                        children: [\n                            {\n                                icon: _barrel_optimize_names_Activity_AlertCircle_ArrowRight_Brain_CheckCircle_Clock_Cpu_Database_FileText_Globe_Search_Shield_Target_Terminal_TrendingUp_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                                label: 'Search Queries',\n                                value: searchQueries.length,\n                                color: 'from-green-400 to-emerald-400',\n                                bgColor: 'bg-green-500/10',\n                                unit: 'executed'\n                            },\n                            {\n                                icon: _barrel_optimize_names_Activity_AlertCircle_ArrowRight_Brain_CheckCircle_Clock_Cpu_Database_FileText_Globe_Search_Shield_Target_Terminal_TrendingUp_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                                label: 'Sources Analyzed',\n                                value: scrapedDomains.length,\n                                color: 'from-blue-400 to-cyan-400',\n                                bgColor: 'bg-blue-500/10',\n                                unit: 'websites'\n                            },\n                            {\n                                icon: _barrel_optimize_names_Activity_AlertCircle_ArrowRight_Brain_CheckCircle_Clock_Cpu_Database_FileText_Globe_Search_Shield_Target_Terminal_TrendingUp_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                                label: 'Analysis Steps',\n                                value: analysisComplete.length,\n                                color: 'from-purple-400 to-pink-400',\n                                bgColor: 'bg-purple-500/10',\n                                unit: 'complete'\n                            },\n                            {\n                                icon: _barrel_optimize_names_Activity_AlertCircle_ArrowRight_Brain_CheckCircle_Clock_Cpu_Database_FileText_Globe_Search_Shield_Target_Terminal_TrendingUp_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                                label: 'AI Bypass',\n                                value: Object.keys(humanizationStats).length,\n                                color: 'from-orange-400 to-red-400',\n                                bgColor: 'bg-orange-500/10',\n                                unit: 'applied'\n                            }\n                        ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: index * 0.1\n                                },\n                                className: `${stat.bgColor} backdrop-blur-xl border border-white/10 rounded-xl p-4`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `p-2 bg-gradient-to-r ${stat.color} rounded-lg`,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                                    className: \"w-5 h-5 text-black\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                    lineNumber: 551,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                lineNumber: 550,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-medium text-sm\",\n                                                children: stat.label\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                lineNumber: 553,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                        lineNumber: 549,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                className: \"text-3xl font-bold text-white mb-1\",\n                                                initial: {\n                                                    scale: 0.8,\n                                                    opacity: 0\n                                                },\n                                                animate: {\n                                                    scale: 1,\n                                                    opacity: 1\n                                                },\n                                                transition: {\n                                                    type: \"spring\",\n                                                    stiffness: 500,\n                                                    damping: 25\n                                                },\n                                                children: stat.value\n                                            }, stat.value, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                lineNumber: 556,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-400\",\n                                                children: stat.unit\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                lineNumber: 565,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                        lineNumber: 555,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, index, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                lineNumber: 542,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                        lineNumber: 507,\n                        columnNumber: 11\n                    }, undefined),\n                    isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-black/60 backdrop-blur-xl border border-green-500/30 rounded-2xl overflow-hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-r from-green-900/50 to-emerald-900/50 px-6 py-4 border-b border-green-500/30\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowRight_Brain_CheckCircle_Clock_Cpu_Database_FileText_Globe_Search_Shield_Target_Terminal_TrendingUp_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-5 h-5 text-green-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                lineNumber: 582,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-green-400 font-mono font-bold\",\n                                                children: \"INVINCIBLE_TERMINAL\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                lineNumber: 583,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2 ml-auto\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 rounded-full bg-red-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                        lineNumber: 585,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 rounded-full bg-yellow-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                        lineNumber: 586,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 rounded-full bg-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                        lineNumber: 587,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                lineNumber: 584,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                        lineNumber: 581,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                    lineNumber: 580,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    ref: eventsContainerRef,\n                                    className: \"h-80 overflow-y-auto p-4 space-y-2 font-mono text-sm\",\n                                    style: {\n                                        background: 'linear-gradient(180deg, rgba(0,0,0,0.8) 0%, rgba(0,20,0,0.3) 100%)'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.AnimatePresence, {\n                                            children: events.map((event, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                    initial: {\n                                                        opacity: 0,\n                                                        x: -20\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        x: 0\n                                                    },\n                                                    exit: {\n                                                        opacity: 0,\n                                                        x: 20\n                                                    },\n                                                    className: \"flex items-start space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-500 text-xs mt-1 flex-shrink-0\",\n                                                            children: [\n                                                                \"[\",\n                                                                new Date(event.timestamp).toLocaleTimeString(),\n                                                                \"]\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                            lineNumber: 608,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: `${event.type === 'error' ? 'text-red-400' : event.type === 'success' ? 'text-green-400' : event.type === 'phase' ? 'text-blue-400' : event.type === 'search_query' ? 'text-purple-400' : event.type === 'humanization' ? 'text-orange-400' : 'text-green-300'}`,\n                                                                    children: [\n                                                                        event.type === 'phase' ? '>>> ' : '',\n                                                                        event.data.message\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                                    lineNumber: 612,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                event.data.query && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-gray-400 text-xs mt-1 pl-4\",\n                                                                    children: [\n                                                                        'QUERY: \"',\n                                                                        event.data.query,\n                                                                        '\"'\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                                    lineNumber: 624,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                event.data.improvements && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-gray-400 text-xs mt-1 pl-4\",\n                                                                    children: [\n                                                                        \"APPLIED: \",\n                                                                        event.data.improvements.join(', ')\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                                    lineNumber: 629,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                            lineNumber: 611,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                    lineNumber: 601,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                            lineNumber: 599,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                            animate: {\n                                                opacity: [\n                                                    1,\n                                                    0\n                                                ]\n                                            },\n                                            transition: {\n                                                duration: 1,\n                                                repeat: Infinity\n                                            },\n                                            className: \"text-green-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs\",\n                                                    children: [\n                                                        \"[\",\n                                                        new Date().toLocaleTimeString(),\n                                                        \"]\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                    lineNumber: 645,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-3\",\n                                                    children: \"█\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                    lineNumber: 646,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                            lineNumber: 640,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                    lineNumber: 592,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                            lineNumber: 579,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                        lineNumber: 574,\n                        columnNumber: 11\n                    }, undefined),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            scale: 0.9\n                        },\n                        animate: {\n                            opacity: 1,\n                            scale: 1\n                        },\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-900/20 backdrop-blur-xl border border-red-500/50 rounded-2xl p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowRight_Brain_CheckCircle_Clock_Cpu_Database_FileText_Globe_Search_Shield_Target_Terminal_TrendingUp_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"w-6 h-6 text-red-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                        lineNumber: 663,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-bold text-red-400 mb-1\",\n                                                children: \"System Alert\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                lineNumber: 665,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-300\",\n                                                children: error\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                lineNumber: 666,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                        lineNumber: 664,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                lineNumber: 662,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                            lineNumber: 661,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                        lineNumber: 656,\n                        columnNumber: 11\n                    }, undefined),\n                    finalResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 0.5\n                        },\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-green-900/30 to-emerald-900/30 backdrop-blur-xl border border-green-500/50 rounded-2xl p-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3 mb-6\",\n                                    children: isSaving ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin w-8 h-8 border-2 border-blue-400 border-t-transparent rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                lineNumber: 685,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-3xl font-bold text-blue-400\",\n                                                children: \"SAVING ARTICLE...\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                lineNumber: 686,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowRight_Brain_CheckCircle_Clock_Cpu_Database_FileText_Globe_Search_Shield_Target_Terminal_TrendingUp_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-8 h-8 text-green-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                lineNumber: 690,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-3xl font-bold text-green-400\",\n                                                children: \"MISSION ACCOMPLISHED\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                lineNumber: 691,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                    lineNumber: 682,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\",\n                                    children: [\n                                        {\n                                            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowRight_Brain_CheckCircle_Clock_Cpu_Database_FileText_Globe_Search_Shield_Target_Terminal_TrendingUp_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                                            label: 'Words Generated',\n                                            value: finalResult.article?.wordCount || 0,\n                                            color: 'text-blue-400'\n                                        },\n                                        {\n                                            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowRight_Brain_CheckCircle_Clock_Cpu_Database_FileText_Globe_Search_Shield_Target_Terminal_TrendingUp_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                                            label: 'SEO Score',\n                                            value: `${finalResult.article?.seoScore || 0}/100`,\n                                            color: 'text-green-400'\n                                        },\n                                        {\n                                            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowRight_Brain_CheckCircle_Clock_Cpu_Database_FileText_Globe_Search_Shield_Target_Terminal_TrendingUp_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                                            label: 'Generation Time',\n                                            value: `${Math.round((finalResult.stats?.executionTime || 0) / 1000)}s`,\n                                            color: 'text-purple-400'\n                                        }\n                                    ].map((metric, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-center mb-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(metric.icon, {\n                                                        className: `w-8 h-8 ${metric.color}`\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                        lineNumber: 719,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                    lineNumber: 718,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: `text-3xl font-bold ${metric.color} mb-1`,\n                                                    children: metric.value\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                    lineNumber: 721,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-400 text-sm\",\n                                                    children: metric.label\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                    lineNumber: 724,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                            lineNumber: 717,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                    lineNumber: 696,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-black/40 rounded-xl p-6 border border-green-500/30\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-bold text-green-400 mb-3 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowRight_Brain_CheckCircle_Clock_Cpu_Database_FileText_Globe_Search_Shield_Target_Terminal_TrendingUp_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"w-5 h-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                    lineNumber: 731,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Generated Article Preview\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                            lineNumber: 730,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400 text-sm\",\n                                                            children: \"TITLE:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                            lineNumber: 737,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xl font-bold text-white mt-1\",\n                                                            children: finalResult.article?.title\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                            lineNumber: 738,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                    lineNumber: 736,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400 text-sm\",\n                                                            children: \"META DESCRIPTION:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                            lineNumber: 744,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-300 mt-1 leading-relaxed\",\n                                                            children: finalResult.article?.metaDescription\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                            lineNumber: 745,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                    lineNumber: 743,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                            lineNumber: 735,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        isSaving && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-6 p-4 bg-blue-900/20 border border-blue-500/30 rounded-xl\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"animate-spin w-5 h-5 border-2 border-blue-400 border-t-transparent rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                        lineNumber: 754,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-400 font-medium\",\n                                                        children: \"Saving article and redirecting to article view...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                        lineNumber: 755,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                                lineNumber: 753,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                            lineNumber: 752,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                                    lineNumber: 729,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                            lineNumber: 681,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                        lineNumber: 675,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n                lineNumber: 394,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleStreamingUI.tsx\",\n        lineNumber: 342,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (InvincibleStreamingUI);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/InvincibleStreamingUI.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/SessionProvider.tsx":
/*!********************************************!*\
  !*** ./src/components/SessionProvider.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SessionProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction SessionProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SessionProvider.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9TZXNzaW9uUHJvdmlkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUU0RTtBQU83RCxTQUFTQSxnQkFBZ0IsRUFBRUUsUUFBUSxFQUF3QjtJQUN4RSxxQkFDRSw4REFBQ0QsNERBQXVCQTtrQkFDckJDOzs7Ozs7QUFHUCIsInNvdXJjZXMiOlsiL1VzZXJzL2FheXVzaG1pc2hyYS9EZXNrdG9wL29sZCBpbnZpbmNpYmxlIHdpdGggZGVlcHJlc2VhcmNoL3NyYy9jb21wb25lbnRzL1Nlc3Npb25Qcm92aWRlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IFNlc3Npb25Qcm92aWRlciBhcyBOZXh0QXV0aFNlc3Npb25Qcm92aWRlciB9IGZyb20gJ25leHQtYXV0aC9yZWFjdCdcbmltcG9ydCB7IFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0J1xuXG5pbnRlcmZhY2UgU2Vzc2lvblByb3ZpZGVyUHJvcHMge1xuICBjaGlsZHJlbjogUmVhY3ROb2RlXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFNlc3Npb25Qcm92aWRlcih7IGNoaWxkcmVuIH06IFNlc3Npb25Qcm92aWRlclByb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPE5leHRBdXRoU2Vzc2lvblByb3ZpZGVyPlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvTmV4dEF1dGhTZXNzaW9uUHJvdmlkZXI+XG4gIClcbn0gIl0sIm5hbWVzIjpbIlNlc3Npb25Qcm92aWRlciIsIk5leHRBdXRoU2Vzc2lvblByb3ZpZGVyIiwiY2hpbGRyZW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/SessionProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   safeDecodeURIComponent: () => (/* binding */ safeDecodeURIComponent),\n/* harmony export */   safeEncodeURIComponent: () => (/* binding */ safeEncodeURIComponent)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\n * Safely decode URI component with fallback handling\n * @param encodedString - The URI encoded string to decode\n * @param fallback - Optional fallback value if decoding fails\n * @returns Decoded string or fallback/original string if decoding fails\n */ function safeDecodeURIComponent(encodedString, fallback) {\n    if (!encodedString) {\n        return fallback || '';\n    }\n    try {\n        return decodeURIComponent(encodedString);\n    } catch (error) {\n        console.warn('URI decode failed, using fallback:', error);\n        return fallback || encodedString;\n    }\n}\n/**\n * Safely encode URI component with error handling\n * @param str - The string to encode\n * @returns Encoded string or empty string if encoding fails\n */ function safeEncodeURIComponent(str) {\n    if (!str) return '';\n    try {\n        return encodeURIComponent(str);\n    } catch (error) {\n        console.warn('URI encode failed:', error);\n        return '';\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/tailwind-merge","vendor-chunks/next-auth","vendor-chunks/motion-dom","vendor-chunks/lucide-react","vendor-chunks/@babel","vendor-chunks/motion-utils","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Finvincible%2Fpage&page=%2Finvincible%2Fpage&appPaths=%2Finvincible%2Fpage&pagePath=private-next-app-dir%2Finvincible%2Fpage.tsx&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();