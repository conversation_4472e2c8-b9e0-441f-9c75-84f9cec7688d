/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/article-view/[id]/page";
exports.ids = ["app/article-view/[id]/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Farticle-view%2F%5Bid%5D%2Fpage&page=%2Farticle-view%2F%5Bid%5D%2Fpage&appPaths=%2Farticle-view%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Farticle-view%2F%5Bid%5D%2Fpage.tsx&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Farticle-view%2F%5Bid%5D%2Fpage&page=%2Farticle-view%2F%5Bid%5D%2Fpage&appPaths=%2Farticle-view%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Farticle-view%2F%5Bid%5D%2Fpage.tsx&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/article-view/[id]/page.tsx */ \"(rsc)/./src/app/article-view/[id]/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'article-view',\n        {\n        children: [\n        '[id]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/article-view/[id]/page\",\n        pathname: \"/article-view/[id]\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcnRpY2xlLXZpZXclMkYlNUJpZCU1RCUyRnBhZ2UmcGFnZT0lMkZhcnRpY2xlLXZpZXclMkYlNUJpZCU1RCUyRnBhZ2UmYXBwUGF0aHM9JTJGYXJ0aWNsZS12aWV3JTJGJTVCaWQlNUQlMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGYXJ0aWNsZS12aWV3JTJGJTVCaWQlNUQlMkZwYWdlLnRzeCZhcHBEaXI9JTJGVXNlcnMlMkZhYXl1c2htaXNocmElMkZEZXNrdG9wJTJGb2xkJTIwaW52aW5jaWJsZSUyMHdpdGglMjBkZWVwcmVzZWFyY2glMkZzcmMlMkZhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPSUyRlVzZXJzJTJGYWF5dXNobWlzaHJhJTJGRGVza3RvcCUyRm9sZCUyMGludmluY2libGUlMjB3aXRoJTIwZGVlcHJlc2VhcmNoJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxzQkFBc0Isb0pBQW9IO0FBQzFJLHNCQUFzQiwwTkFBZ0Y7QUFDdEcsc0JBQXNCLDBOQUFnRjtBQUN0RyxzQkFBc0IsZ09BQW1GO0FBQ3pHLG9CQUFvQixvTEFBb0k7QUFHdEo7QUFHQTtBQUNGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQztBQUNqQztBQUNBO0FBQ0EsU0FBUztBQUNULE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUN1QjtBQUdyQjtBQUNGLDZCQUE2QixtQkFBbUI7QUFDaEQ7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUdFO0FBQ0Y7QUFDTyx3QkFBd0IsdUdBQWtCO0FBQ2pEO0FBQ0EsY0FBYyxrRUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsQ0FBQzs7QUFFRCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IG1vZHVsZTAgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9hYXl1c2htaXNocmEvRGVza3RvcC9vbGQgaW52aW5jaWJsZSB3aXRoIGRlZXByZXNlYXJjaC9zcmMvYXBwL2xheW91dC50c3hcIik7XG5jb25zdCBtb2R1bGUxID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yXCIpO1xuY29uc3QgbW9kdWxlMiA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2ZvcmJpZGRlbi1lcnJvclwiKTtcbmNvbnN0IG1vZHVsZTMgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy91bmF1dGhvcml6ZWQtZXJyb3JcIik7XG5jb25zdCBwYWdlNCA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2FheXVzaG1pc2hyYS9EZXNrdG9wL29sZCBpbnZpbmNpYmxlIHdpdGggZGVlcHJlc2VhcmNoL3NyYy9hcHAvYXJ0aWNsZS12aWV3L1tpZF0vcGFnZS50c3hcIik7XG5pbXBvcnQgeyBBcHBQYWdlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1tb2R1bGVzL2FwcC1wYWdlL21vZHVsZS5jb21waWxlZFwiIHdpdGgge1xuICAgICd0dXJib3BhY2stdHJhbnNpdGlvbic6ICduZXh0LXNzcidcbn07XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1raW5kXCIgd2l0aCB7XG4gICAgJ3R1cmJvcGFjay10cmFuc2l0aW9uJzogJ25leHQtc2VydmVyLXV0aWxpdHknXG59O1xuLy8gV2UgaW5qZWN0IHRoZSB0cmVlIGFuZCBwYWdlcyBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgdHJlZSA9IHtcbiAgICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgJycsXG4gICAgICAgIHtcbiAgICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgJ2FydGljbGUtdmlldycsXG4gICAgICAgIHtcbiAgICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgJ1tpZF0nLFxuICAgICAgICB7XG4gICAgICAgIGNoaWxkcmVuOiBbJ19fUEFHRV9fJywge30sIHtcbiAgICAgICAgICBwYWdlOiBbcGFnZTQsIFwiL1VzZXJzL2FheXVzaG1pc2hyYS9EZXNrdG9wL29sZCBpbnZpbmNpYmxlIHdpdGggZGVlcHJlc2VhcmNoL3NyYy9hcHAvYXJ0aWNsZS12aWV3L1tpZF0vcGFnZS50c3hcIl0sXG4gICAgICAgICAgXG4gICAgICAgIH1dXG4gICAgICB9LFxuICAgICAgICB7XG4gICAgICAgIFxuICAgICAgICBcbiAgICAgIH1cbiAgICAgIF1cbiAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgXG4gICAgICAgIFxuICAgICAgfVxuICAgICAgXVxuICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAnbGF5b3V0JzogW21vZHVsZTAsIFwiL1VzZXJzL2FheXVzaG1pc2hyYS9EZXNrdG9wL29sZCBpbnZpbmNpYmxlIHdpdGggZGVlcHJlc2VhcmNoL3NyYy9hcHAvbGF5b3V0LnRzeFwiXSxcbidub3QtZm91bmQnOiBbbW9kdWxlMSwgXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yXCJdLFxuJ2ZvcmJpZGRlbic6IFttb2R1bGUyLCBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9mb3JiaWRkZW4tZXJyb3JcIl0sXG4ndW5hdXRob3JpemVkJzogW21vZHVsZTMsIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3VuYXV0aG9yaXplZC1lcnJvclwiXSxcbiAgICAgICAgXG4gICAgICB9XG4gICAgICBdXG4gICAgICB9LmNoaWxkcmVuO1xuY29uc3QgcGFnZXMgPSBbXCIvVXNlcnMvYWF5dXNobWlzaHJhL0Rlc2t0b3Avb2xkIGludmluY2libGUgd2l0aCBkZWVwcmVzZWFyY2gvc3JjL2FwcC9hcnRpY2xlLXZpZXcvW2lkXS9wYWdlLnRzeFwiXTtcbmV4cG9ydCB7IHRyZWUsIHBhZ2VzIH07XG5leHBvcnQgeyBkZWZhdWx0IGFzIEdsb2JhbEVycm9yIH0gZnJvbSBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9lcnJvci1ib3VuZGFyeVwiIHdpdGgge1xuICAgICd0dXJib3BhY2stdHJhbnNpdGlvbic6ICduZXh0LXNlcnZlci11dGlsaXR5J1xufTtcbmNvbnN0IF9fbmV4dF9hcHBfcmVxdWlyZV9fID0gX193ZWJwYWNrX3JlcXVpcmVfX1xuY29uc3QgX19uZXh0X2FwcF9sb2FkX2NodW5rX18gPSAoKSA9PiBQcm9taXNlLnJlc29sdmUoKVxuZXhwb3J0IGNvbnN0IF9fbmV4dF9hcHBfXyA9IHtcbiAgICByZXF1aXJlOiBfX25leHRfYXBwX3JlcXVpcmVfXyxcbiAgICBsb2FkQ2h1bms6IF9fbmV4dF9hcHBfbG9hZF9jaHVua19fXG59O1xuZXhwb3J0ICogZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvYXBwLXJlbmRlci9lbnRyeS1iYXNlXCIgd2l0aCB7XG4gICAgJ3R1cmJvcGFjay10cmFuc2l0aW9uJzogJ25leHQtc2VydmVyLXV0aWxpdHknXG59O1xuLy8gQ3JlYXRlIGFuZCBleHBvcnQgdGhlIHJvdXRlIG1vZHVsZSB0aGF0IHdpbGwgYmUgY29uc3VtZWQuXG5leHBvcnQgY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUGFnZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUEFHRSxcbiAgICAgICAgcGFnZTogXCIvYXJ0aWNsZS12aWV3L1tpZF0vcGFnZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXJ0aWNsZS12aWV3L1tpZF1cIixcbiAgICAgICAgLy8gVGhlIGZvbGxvd2luZyBhcmVuJ3QgdXNlZCBpbiBwcm9kdWN0aW9uLlxuICAgICAgICBidW5kbGVQYXRoOiAnJyxcbiAgICAgICAgZmlsZW5hbWU6ICcnLFxuICAgICAgICBhcHBQYXRoczogW11cbiAgICB9LFxuICAgIHVzZXJsYW5kOiB7XG4gICAgICAgIGxvYWRlclRyZWU6IHRyZWVcbiAgICB9XG59KTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXBhZ2UuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Farticle-view%2F%5Bid%5D%2Fpage&page=%2Farticle-view%2F%5Bid%5D%2Fpage&appPaths=%2Farticle-view%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Farticle-view%2F%5Bid%5D%2Fpage.tsx&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcomponents%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcomponents%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/SessionProvider.tsx */ \"(rsc)/./src/components/SessionProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWF5dXNobWlzaHJhJTJGRGVza3RvcCUyRm9sZCUyMGludmluY2libGUlMjB3aXRoJTIwZGVlcHJlc2VhcmNoJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmZvbnQlMkZnb29nbGUlMkZ0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMnNyYyUyRmFwcCUyRmxheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWF5dXNobWlzaHJhJTJGRGVza3RvcCUyRm9sZCUyMGludmluY2libGUlMjB3aXRoJTIwZGVlcHJlc2VhcmNoJTJGc3JjJTJGYXBwJTJGZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZhYXl1c2htaXNocmElMkZEZXNrdG9wJTJGb2xkJTIwaW52aW5jaWJsZSUyMHdpdGglMjBkZWVwcmVzZWFyY2glMkZzcmMlMkZjb21wb25lbnRzJTJGU2Vzc2lvblByb3ZpZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9MQUFpSyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIi9Vc2Vycy9hYXl1c2htaXNocmEvRGVza3RvcC9vbGQgaW52aW5jaWJsZSB3aXRoIGRlZXByZXNlYXJjaC9zcmMvY29tcG9uZW50cy9TZXNzaW9uUHJvdmlkZXIudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcomponents%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Farticle-view%2F%5Bid%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Farticle-view%2F%5Bid%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/article-view/[id]/page.tsx */ \"(rsc)/./src/app/article-view/[id]/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWF5dXNobWlzaHJhJTJGRGVza3RvcCUyRm9sZCUyMGludmluY2libGUlMjB3aXRoJTIwZGVlcHJlc2VhcmNoJTJGc3JjJTJGYXBwJTJGYXJ0aWNsZS12aWV3JTJGJTVCaWQlNUQlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0xBQW9JIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvYWF5dXNobWlzaHJhL0Rlc2t0b3Avb2xkIGludmluY2libGUgd2l0aCBkZWVwcmVzZWFyY2gvc3JjL2FwcC9hcnRpY2xlLXZpZXcvW2lkXS9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Farticle-view%2F%5Bid%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/article-view/[id]/page.tsx":
/*!********************************************!*\
  !*** ./src/app/article-view/[id]/page.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"333781e81268\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvVXNlcnMvYWF5dXNobWlzaHJhL0Rlc2t0b3Avb2xkIGludmluY2libGUgd2l0aCBkZWVwcmVzZWFyY2gvc3JjL2FwcC9nbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjMzMzc4MWU4MTI2OFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_SessionProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/SessionProvider */ \"(rsc)/./src/components/SessionProvider.tsx\");\n\n\n\n\nconst metadata = {\n    title: 'Invincible - AI Content Generation Platform',\n    description: 'The ultimate content writing SaaS platform powered by advanced AI technology'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"dark\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className)} bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 min-h-screen`,\n            suppressHydrationWarning: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SessionProvider__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/SessionProvider.tsx":
/*!********************************************!*\
  !*** ./src/components/SessionProvider.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SessionProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SessionProvider.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcomponents%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcomponents%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/SessionProvider.tsx */ \"(ssr)/./src/components/SessionProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWF5dXNobWlzaHJhJTJGRGVza3RvcCUyRm9sZCUyMGludmluY2libGUlMjB3aXRoJTIwZGVlcHJlc2VhcmNoJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmZvbnQlMkZnb29nbGUlMkZ0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMnNyYyUyRmFwcCUyRmxheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWF5dXNobWlzaHJhJTJGRGVza3RvcCUyRm9sZCUyMGludmluY2libGUlMjB3aXRoJTIwZGVlcHJlc2VhcmNoJTJGc3JjJTJGYXBwJTJGZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZhYXl1c2htaXNocmElMkZEZXNrdG9wJTJGb2xkJTIwaW52aW5jaWJsZSUyMHdpdGglMjBkZWVwcmVzZWFyY2glMkZzcmMlMkZjb21wb25lbnRzJTJGU2Vzc2lvblByb3ZpZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9MQUFpSyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIi9Vc2Vycy9hYXl1c2htaXNocmEvRGVza3RvcC9vbGQgaW52aW5jaWJsZSB3aXRoIGRlZXByZXNlYXJjaC9zcmMvY29tcG9uZW50cy9TZXNzaW9uUHJvdmlkZXIudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcomponents%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Farticle-view%2F%5Bid%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Farticle-view%2F%5Bid%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/article-view/[id]/page.tsx */ \"(ssr)/./src/app/article-view/[id]/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWF5dXNobWlzaHJhJTJGRGVza3RvcCUyRm9sZCUyMGludmluY2libGUlMjB3aXRoJTIwZGVlcHJlc2VhcmNoJTJGc3JjJTJGYXBwJTJGYXJ0aWNsZS12aWV3JTJGJTVCaWQlNUQlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0xBQW9JIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvYWF5dXNobWlzaHJhL0Rlc2t0b3Avb2xkIGludmluY2libGUgd2l0aCBkZWVwcmVzZWFyY2gvc3JjL2FwcC9hcnRpY2xlLXZpZXcvW2lkXS9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Farticle-view%2F%5Bid%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/article-view/[id]/page.tsx":
/*!********************************************!*\
  !*** ./src/app/article-view/[id]/page.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ArticleViewPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! react-markdown */ \"(ssr)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! remark-gfm */ \"(ssr)/./node_modules/remark-gfm/lib/index.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart,Bookmark,Brain,Calendar,Check,Clock,Copy,Crown,Download,ExternalLink,Eye,FileText,Globe,Heart,MessageCircle,Search,Shield,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart,Bookmark,Brain,Calendar,Check,Clock,Copy,Crown,Download,ExternalLink,Eye,FileText,Globe,Heart,MessageCircle,Search,Shield,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart,Bookmark,Brain,Calendar,Check,Clock,Copy,Crown,Download,ExternalLink,Eye,FileText,Globe,Heart,MessageCircle,Search,Shield,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart,Bookmark,Brain,Calendar,Check,Clock,Copy,Crown,Download,ExternalLink,Eye,FileText,Globe,Heart,MessageCircle,Search,Shield,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart,Bookmark,Brain,Calendar,Check,Clock,Copy,Crown,Download,ExternalLink,Eye,FileText,Globe,Heart,MessageCircle,Search,Shield,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart,Bookmark,Brain,Calendar,Check,Clock,Copy,Crown,Download,ExternalLink,Eye,FileText,Globe,Heart,MessageCircle,Search,Shield,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bookmark.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart,Bookmark,Brain,Calendar,Check,Clock,Copy,Crown,Download,ExternalLink,Eye,FileText,Globe,Heart,MessageCircle,Search,Shield,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart,Bookmark,Brain,Calendar,Check,Clock,Copy,Crown,Download,ExternalLink,Eye,FileText,Globe,Heart,MessageCircle,Search,Shield,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart,Bookmark,Brain,Calendar,Check,Clock,Copy,Crown,Download,ExternalLink,Eye,FileText,Globe,Heart,MessageCircle,Search,Shield,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart,Bookmark,Brain,Calendar,Check,Clock,Copy,Crown,Download,ExternalLink,Eye,FileText,Globe,Heart,MessageCircle,Search,Shield,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart,Bookmark,Brain,Calendar,Check,Clock,Copy,Crown,Download,ExternalLink,Eye,FileText,Globe,Heart,MessageCircle,Search,Shield,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart,Bookmark,Brain,Calendar,Check,Clock,Copy,Crown,Download,ExternalLink,Eye,FileText,Globe,Heart,MessageCircle,Search,Shield,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart,Bookmark,Brain,Calendar,Check,Clock,Copy,Crown,Download,ExternalLink,Eye,FileText,Globe,Heart,MessageCircle,Search,Shield,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart,Bookmark,Brain,Calendar,Check,Clock,Copy,Crown,Download,ExternalLink,Eye,FileText,Globe,Heart,MessageCircle,Search,Shield,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart,Bookmark,Brain,Calendar,Check,Clock,Copy,Crown,Download,ExternalLink,Eye,FileText,Globe,Heart,MessageCircle,Search,Shield,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bar-chart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart,Bookmark,Brain,Calendar,Check,Clock,Copy,Crown,Download,ExternalLink,Eye,FileText,Globe,Heart,MessageCircle,Search,Shield,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart,Bookmark,Brain,Calendar,Check,Clock,Copy,Crown,Download,ExternalLink,Eye,FileText,Globe,Heart,MessageCircle,Search,Shield,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart,Bookmark,Brain,Calendar,Check,Clock,Copy,Crown,Download,ExternalLink,Eye,FileText,Globe,Heart,MessageCircle,Search,Shield,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart,Bookmark,Brain,Calendar,Check,Clock,Copy,Crown,Download,ExternalLink,Eye,FileText,Globe,Heart,MessageCircle,Search,Shield,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart,Bookmark,Brain,Calendar,Check,Clock,Copy,Crown,Download,ExternalLink,Eye,FileText,Globe,Heart,MessageCircle,Search,Shield,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\nfunction ArticleViewPage() {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useParams)();\n    const [article, setArticle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [scores, setScores] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [readingProgress, setReadingProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isBookmarked, setIsBookmarked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLiked, setIsLiked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Handle scroll progress\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ArticleViewPage.useEffect\": ()=>{\n            const handleScroll = {\n                \"ArticleViewPage.useEffect.handleScroll\": ()=>{\n                    const totalHeight = document.documentElement.scrollHeight - window.innerHeight;\n                    const progress = window.scrollY / totalHeight * 100;\n                    setReadingProgress(Math.min(progress, 100));\n                }\n            }[\"ArticleViewPage.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"ArticleViewPage.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"ArticleViewPage.useEffect\"];\n        }\n    }[\"ArticleViewPage.useEffect\"], []);\n    // Handle authentication redirect\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ArticleViewPage.useEffect\": ()=>{\n            if (status === 'unauthenticated') {\n                router.push('/login');\n            }\n        }\n    }[\"ArticleViewPage.useEffect\"], [\n        status,\n        router\n    ]);\n    // Fetch article by ID\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ArticleViewPage.useEffect\": ()=>{\n            async function fetchArticle() {\n                if (status === 'loading' || !params.id) return;\n                if (status === 'unauthenticated') {\n                    router.push('/login');\n                    return;\n                }\n                try {\n                    setLoading(true);\n                    setError(null);\n                    const response = await fetch(`/api/articles/${params.id}`);\n                    const data = await response.json();\n                    if (!response.ok) {\n                        throw new Error(data.error || 'Failed to fetch article');\n                    }\n                    if (data.success && data.article) {\n                        // Check if this is a YouTube script and redirect if so\n                        if (data.article.type === 'youtube_script') {\n                            // Redirect to YouTube script viewer\n                            const youtubeUrl = `/youtube-script-view?script=${encodeURIComponent(data.article.content)}&title=${encodeURIComponent(data.article.title)}`;\n                            router.push(youtubeUrl);\n                            return;\n                        }\n                        setArticle(data.article);\n                        // Generate mock scores if metadata contains scoring info\n                        if (data.article.metadata?.scores) {\n                            setScores(data.article.metadata.scores);\n                        } else {\n                            // Generate realistic mock scores based on article type\n                            const mockScores = generateMockScores(data.article.type, data.article.wordCount || 0);\n                            setScores(mockScores);\n                        }\n                    } else {\n                        throw new Error('Article not found');\n                    }\n                } catch (error) {\n                    console.error('Error fetching article:', error);\n                    setError(error instanceof Error ? error.message : 'Failed to load article');\n                    // Redirect to dashboard after showing error\n                    setTimeout({\n                        \"ArticleViewPage.useEffect.fetchArticle\": ()=>{\n                            router.push('/dashboard');\n                        }\n                    }[\"ArticleViewPage.useEffect.fetchArticle\"], 3000);\n                } finally{\n                    setLoading(false);\n                }\n            }\n            fetchArticle();\n        }\n    }[\"ArticleViewPage.useEffect\"], [\n        params.id,\n        status,\n        router\n    ]);\n    // Generate mock scores based on article type and quality\n    const generateMockScores = (type, wordCount)=>{\n        // Base scores with some variation\n        const baseScore = 75 + Math.random() * 20;\n        return {\n            seoScore: Math.min(95, baseScore + (type === 'blog' ? 10 : 5) + Math.random() * 10),\n            aeoScore: Math.min(95, baseScore + Math.random() * 15),\n            geoScore: Math.min(95, baseScore + Math.random() * 20),\n            readabilityScore: Math.min(95, baseScore + (wordCount > 500 ? 5 : -5) + Math.random() * 10),\n            uniquenessScore: Math.min(95, baseScore + Math.random() * 15),\n            externalLinkingScore: Math.min(95, baseScore + Math.random() * 25),\n            overallScore: Math.min(95, baseScore + Math.random() * 10),\n            recommendations: [\n                'Content shows excellent depth and research quality',\n                'Strong competitive advantage over existing articles',\n                'Human-like writing style detected',\n                'Good use of statistics and insights'\n            ]\n        };\n    };\n    const copyToClipboard = ()=>{\n        if (article) {\n            navigator.clipboard.writeText(article.content);\n            setCopied(true);\n            setTimeout(()=>setCopied(false), 2000);\n        }\n    };\n    const downloadArticle = ()=>{\n        if (article) {\n            const blob = new Blob([\n                article.content\n            ], {\n                type: 'text/markdown'\n            });\n            const url = URL.createObjectURL(blob);\n            const a = document.createElement('a');\n            a.href = url;\n            a.download = `${article.title.replace(/\\s+/g, '-').toLowerCase()}.md`;\n            a.click();\n            URL.revokeObjectURL(url);\n        }\n    };\n    const getScoreColor = (score)=>{\n        if (score >= 85) return 'from-emerald-500 to-green-500';\n        if (score >= 70) return 'from-blue-500 to-cyan-500';\n        if (score >= 50) return 'from-amber-500 to-orange-500';\n        return 'from-red-500 to-rose-500';\n    };\n    const getScoreGrade = (score)=>{\n        if (score >= 90) return 'A+';\n        if (score >= 85) return 'A';\n        if (score >= 80) return 'A-';\n        if (score >= 75) return 'B+';\n        if (score >= 70) return 'B';\n        if (score >= 65) return 'B-';\n        if (score >= 60) return 'C+';\n        if (score >= 55) return 'C';\n        return 'D';\n    };\n    // Loading state\n    if (status === 'loading' || loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-black flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin w-12 h-12 border-2 border-violet-400 border-t-transparent rounded-full mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: \"Loading article...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                lineNumber: 215,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n            lineNumber: 214,\n            columnNumber: 7\n        }, this);\n    }\n    // Error state\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-black flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 bg-red-600/20 rounded-2xl mb-6 w-fit mx-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-8 h-8 text-red-400\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-white mb-2\",\n                        children: \"Article Not Found\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400 mb-6\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                        href: \"/dashboard\",\n                        className: \"inline-flex items-center px-6 py-3 bg-gradient-to-r from-violet-600 to-indigo-600 text-white rounded-xl hover:scale-105 transition-transform\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"w-5 h-5 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 13\n                            }, this),\n                            \"Go to Dashboard\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                lineNumber: 227,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n            lineNumber: 226,\n            columnNumber: 7\n        }, this);\n    }\n    // Don't render if not authenticated\n    if (status === 'unauthenticated' || !article) {\n        return null;\n    }\n    const wordCount = article.wordCount || article.content.split(/\\s+/).filter((word)=>word.length > 0).length;\n    const readingTime = Math.ceil(wordCount / 200);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-black\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-violet-900/10 via-black to-indigo-900/10\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        animate: {\n                            x: [\n                                0,\n                                100,\n                                0\n                            ],\n                            y: [\n                                0,\n                                -100,\n                                0\n                            ]\n                        },\n                        transition: {\n                            duration: 20,\n                            repeat: Infinity,\n                            ease: \"linear\"\n                        },\n                        className: \"absolute top-1/4 left-1/4 w-[500px] h-[500px] bg-violet-500/10 rounded-full blur-[100px]\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        animate: {\n                            x: [\n                                0,\n                                -100,\n                                0\n                            ],\n                            y: [\n                                0,\n                                100,\n                                0\n                            ]\n                        },\n                        transition: {\n                            duration: 15,\n                            repeat: Infinity,\n                            ease: \"linear\"\n                        },\n                        className: \"absolute bottom-1/4 right-1/4 w-[600px] h-[600px] bg-indigo-500/10 rounded-full blur-[120px]\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                lineNumber: 256,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                className: \"fixed top-0 left-0 h-1 bg-gradient-to-r from-violet-600 to-indigo-600 z-50\",\n                style: {\n                    width: `${readingProgress}%`\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                lineNumber: 287,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.header, {\n                initial: {\n                    opacity: 0,\n                    y: -20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                className: \"relative z-10 border-b border-white/10 backdrop-blur-xl bg-black/40\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-6 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                        href: \"/dashboard\",\n                                        className: \"flex items-center space-x-3 text-gray-400 hover:text-white transition-colors group\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-5 h-5 group-hover:-translate-x-1 transition-transform\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"Back to Dashboard\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-6 w-px bg-white/20\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-gradient-to-r from-violet-600 to-indigo-600 rounded-xl blur-lg opacity-70\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative bg-black rounded-xl p-2.5 border border-white/20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-6 h-6 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                            lineNumber: 316,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-xl font-bold text-white\",\n                                                        children: \"Superior Article\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-400\",\n                                                        children: [\n                                                            article.type.charAt(0).toUpperCase() + article.type.slice(1),\n                                                            \" Content\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden md:flex items-center space-x-6 text-sm text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: new Date(article.createdAt).toLocaleDateString('en-US', {\n                                                            month: 'short',\n                                                            day: 'numeric',\n                                                            year: 'numeric'\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 333,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            readingTime,\n                                                            \" min read\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            wordCount.toLocaleString(),\n                                                            \" words\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-6 w-px bg-white/20\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setIsBookmarked(!isBookmarked),\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"p-2.5 rounded-lg transition-colors\", isBookmarked ? 'bg-violet-600/20 text-violet-400' : 'text-gray-400 hover:text-white hover:bg-white/10'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: `w-5 h-5 ${isBookmarked ? 'fill-current' : ''}`\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setIsLiked(!isLiked),\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"p-2.5 rounded-lg transition-colors\", isLiked ? 'bg-red-600/20 text-red-400' : 'text-gray-400 hover:text-white hover:bg-white/10'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: `w-5 h-5 ${isLiked ? 'fill-current' : ''}`\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: copyToClipboard,\n                                                className: \"flex items-center space-x-2 px-4 py-2.5 text-sm font-medium text-white bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg hover:bg-white/20 transition-all\",\n                                                children: [\n                                                    copied ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 29\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 61\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: copied ? 'Copied' : 'Copy'\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.button, {\n                                                onClick: downloadArticle,\n                                                whileHover: {\n                                                    scale: 1.05\n                                                },\n                                                whileTap: {\n                                                    scale: 0.95\n                                                },\n                                                className: \"flex items-center space-x-2 px-4 py-2.5 text-sm font-medium text-white bg-gradient-to-r from-violet-600 to-indigo-600 rounded-lg hover:shadow-lg transition-all\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Download\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                lineNumber: 329,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                        lineNumber: 299,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                    lineNumber: 298,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                lineNumber: 293,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"relative z-10 max-w-6xl mx-auto px-6 py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 0.1\n                        },\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-flex items-center space-x-2 px-4 py-2 rounded-full bg-white/10 backdrop-blur-md border border-white/20 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"w-4 h-4 text-violet-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-200\",\n                                        children: \"AI Generated\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                        lineNumber: 402,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-4 h-4 text-yellow-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                lineNumber: 400,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap justify-center gap-3 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-violet-600/20 text-violet-300 border border-violet-500/30\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"AI Generated\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-emerald-600/20 text-emerald-300 border border-emerald-500/30\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                lineNumber: 412,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"SEO Optimized\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-amber-600/20 text-amber-300 border border-amber-500/30\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Superior Quality\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl md:text-6xl font-bold text-white leading-tight mb-6\",\n                                children: article.title\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                lineNumber: 421,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center space-x-8 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-gradient-to-r from-violet-600 to-indigo-600 rounded-full blur-md opacity-70\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 428,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative w-12 h-12 bg-gradient-to-r from-violet-600 to-indigo-600 rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            className: \"w-6 h-6 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                            lineNumber: 430,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                lineNumber: 427,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-white\",\n                                                        children: \"Invincible AI\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-400\",\n                                                        children: \"Content Agent\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 435,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-8 w-px bg-white/20\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-6 text-sm text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 443,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            readingTime,\n                                                            \" min read\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 447,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            wordCount.toLocaleString(),\n                                                            \" words\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 448,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                lineNumber: 446,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                        lineNumber: 441,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                        lineNumber: 394,\n                        columnNumber: 9\n                    }, this),\n                    scores && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 0.2\n                        },\n                        className: \"bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-8 mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-gradient-to-r from-violet-600 to-indigo-600 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"w-5 h-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                lineNumber: 464,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: \"Content Quality Analysis\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                lineNumber: 467,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                        lineNumber: 463,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"Overall Grade:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                lineNumber: 470,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: `text-3xl font-bold bg-gradient-to-r ${getScoreColor(scores.overallScore)} bg-clip-text text-transparent`,\n                                                children: getScoreGrade(scores.overallScore)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                lineNumber: 471,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                        lineNumber: 469,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                lineNumber: 462,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8\",\n                                children: [\n                                    {\n                                        label: 'SEO Score',\n                                        score: scores.seoScore,\n                                        icon: _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n                                        color: 'violet'\n                                    },\n                                    {\n                                        label: 'AEO Score',\n                                        score: scores.aeoScore,\n                                        icon: _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n                                        color: 'blue'\n                                    },\n                                    {\n                                        label: 'GEO Score',\n                                        score: scores.geoScore,\n                                        icon: _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n                                        color: 'emerald'\n                                    },\n                                    {\n                                        label: 'Readability',\n                                        score: scores.readabilityScore,\n                                        icon: _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"],\n                                        color: 'amber'\n                                    },\n                                    {\n                                        label: 'Uniqueness',\n                                        score: scores.uniquenessScore,\n                                        icon: _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                                        color: 'pink'\n                                    },\n                                    {\n                                        label: 'External Links',\n                                        score: scores.externalLinkingScore,\n                                        icon: _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"],\n                                        color: 'cyan'\n                                    }\n                                ].map((metric, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                        initial: {\n                                            scale: 0,\n                                            opacity: 0\n                                        },\n                                        animate: {\n                                            scale: 1,\n                                            opacity: 1\n                                        },\n                                        transition: {\n                                            delay: 0.05 * idx\n                                        },\n                                        className: \"text-center p-4 rounded-xl bg-white/5 border border-white/10 hover:bg-white/10 transition-all\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-24 h-24 mx-auto transform -rotate-90\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                cx: \"48\",\n                                                                cy: \"48\",\n                                                                r: \"40\",\n                                                                stroke: \"currentColor\",\n                                                                strokeWidth: \"6\",\n                                                                fill: \"none\",\n                                                                className: \"text-white/20\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                                lineNumber: 495,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                cx: \"48\",\n                                                                cy: \"48\",\n                                                                r: \"40\",\n                                                                stroke: `url(#gradient-${idx})`,\n                                                                strokeWidth: \"6\",\n                                                                fill: \"none\",\n                                                                strokeDasharray: `${metric.score / 100 * 251.33} 251.33`,\n                                                                strokeLinecap: \"round\",\n                                                                className: \"transition-all duration-1000\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                                lineNumber: 504,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                                                    id: `gradient-${idx}`,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                            offset: \"0%\",\n                                                                            stopColor: `var(--${metric.color}-500)`\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                                            lineNumber: 517,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                            offset: \"100%\",\n                                                                            stopColor: `var(--${metric.color}-400)`\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                                            lineNumber: 518,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                                    lineNumber: 516,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                                lineNumber: 515,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 494,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-2xl font-bold text-white\",\n                                                                    children: Math.round(metric.score)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                                    lineNumber: 524,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-400\",\n                                                                    children: \"/ 100\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                                    lineNumber: 525,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                            lineNumber: 523,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 522,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"inline-flex p-2 rounded-lg\", metric.color === 'violet' && \"bg-violet-600/20\", metric.color === 'blue' && \"bg-blue-600/20\", metric.color === 'emerald' && \"bg-emerald-600/20\", metric.color === 'amber' && \"bg-amber-600/20\", metric.color === 'pink' && \"bg-pink-600/20\", metric.color === 'cyan' && \"bg-cyan-600/20\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(metric.icon, {\n                                                            className: \"w-5 h-5 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                            lineNumber: 539,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 530,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-semibold text-white\",\n                                                        children: metric.label\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 541,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-400\",\n                                                        children: metric.score >= 85 ? 'Excellent' : metric.score >= 70 ? 'Good' : metric.score >= 50 ? 'Fair' : 'Needs Work'\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 542,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                lineNumber: 529,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, metric.label, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                        lineNumber: 486,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                lineNumber: 477,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                        lineNumber: 456,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 0.3\n                        },\n                        className: \"bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"prose prose-invert prose-lg max-w-none\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_27__.Markdown, {\n                                remarkPlugins: [\n                                    remark_gfm__WEBPACK_IMPORTED_MODULE_28__[\"default\"]\n                                ],\n                                components: {\n                                    h1: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-4xl font-bold text-white mb-6\",\n                                            children: children\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                            lineNumber: 563,\n                                            columnNumber: 39\n                                        }, void 0),\n                                    h2: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-3xl font-bold text-white mb-4 mt-8\",\n                                            children: children\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                            lineNumber: 564,\n                                            columnNumber: 39\n                                        }, void 0),\n                                    h3: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-white mb-3 mt-6\",\n                                            children: children\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                            lineNumber: 565,\n                                            columnNumber: 39\n                                        }, void 0),\n                                    p: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300 mb-4 leading-relaxed\",\n                                            children: children\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                            lineNumber: 566,\n                                            columnNumber: 38\n                                        }, void 0),\n                                    a: ({ href, children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: href,\n                                            className: \"text-violet-400 hover:text-violet-300 underline\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            children: children\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                            lineNumber: 568,\n                                            columnNumber: 19\n                                        }, void 0),\n                                    ul: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"text-gray-300 mb-6 ml-6 space-y-3 list-disc list-outside\",\n                                            children: children\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                            lineNumber: 573,\n                                            columnNumber: 19\n                                        }, void 0),\n                                    ol: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                            className: \"text-gray-300 mb-6 ml-6 space-y-3 list-decimal list-outside\",\n                                            children: children\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                            lineNumber: 578,\n                                            columnNumber: 19\n                                        }, void 0),\n                                    li: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"text-gray-300 leading-relaxed\",\n                                            children: children\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                            lineNumber: 583,\n                                            columnNumber: 19\n                                        }, void 0),\n                                    blockquote: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                                            className: \"border-l-4 border-violet-500 pl-4 italic text-gray-400 my-6\",\n                                            children: children\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                            lineNumber: 588,\n                                            columnNumber: 19\n                                        }, void 0),\n                                    code: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                            className: \"bg-white/10 px-2 py-1 rounded text-violet-300 font-mono text-sm\",\n                                            children: children\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                            lineNumber: 593,\n                                            columnNumber: 19\n                                        }, void 0),\n                                    table: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"overflow-x-auto my-8 rounded-2xl shadow-2xl border border-white/20 bg-gradient-to-br from-slate-800/50 to-slate-900/50 backdrop-blur-sm\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                className: \"min-w-full border-collapse\",\n                                                children: children\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                lineNumber: 599,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                            lineNumber: 598,\n                                            columnNumber: 19\n                                        }, void 0),\n                                    thead: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            className: \"bg-gradient-to-r from-blue-600/20 to-purple-600/20 border-b border-white/20\",\n                                            children: children\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                            lineNumber: 605,\n                                            columnNumber: 19\n                                        }, void 0),\n                                    tbody: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            className: \"divide-y divide-white/10\",\n                                            children: children\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                            lineNumber: 610,\n                                            columnNumber: 19\n                                        }, void 0),\n                                    tr: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"hover:bg-white/5 transition-all duration-200\",\n                                            children: children\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                            lineNumber: 615,\n                                            columnNumber: 19\n                                        }, void 0),\n                                    th: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-4 text-left font-semibold text-white border-r border-white/10 last:border-r-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-0 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-500 to-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                    lineNumber: 621,\n                                                    columnNumber: 21\n                                                }, void 0),\n                                                children\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                            lineNumber: 620,\n                                            columnNumber: 19\n                                        }, void 0),\n                                    td: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 text-gray-200 border-r border-white/10 last:border-r-0 font-medium\",\n                                            children: children\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                            lineNumber: 626,\n                                            columnNumber: 19\n                                        }, void 0)\n                                },\n                                children: article.content\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                lineNumber: 560,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                            lineNumber: 559,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                        lineNumber: 553,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                lineNumber: 392,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n        lineNumber: 254,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/article-view/[id]/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/SessionProvider.tsx":
/*!********************************************!*\
  !*** ./src/components/SessionProvider.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SessionProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction SessionProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SessionProvider.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9TZXNzaW9uUHJvdmlkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUU0RTtBQU83RCxTQUFTQSxnQkFBZ0IsRUFBRUUsUUFBUSxFQUF3QjtJQUN4RSxxQkFDRSw4REFBQ0QsNERBQXVCQTtrQkFDckJDOzs7Ozs7QUFHUCIsInNvdXJjZXMiOlsiL1VzZXJzL2FheXVzaG1pc2hyYS9EZXNrdG9wL29sZCBpbnZpbmNpYmxlIHdpdGggZGVlcHJlc2VhcmNoL3NyYy9jb21wb25lbnRzL1Nlc3Npb25Qcm92aWRlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IFNlc3Npb25Qcm92aWRlciBhcyBOZXh0QXV0aFNlc3Npb25Qcm92aWRlciB9IGZyb20gJ25leHQtYXV0aC9yZWFjdCdcbmltcG9ydCB7IFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0J1xuXG5pbnRlcmZhY2UgU2Vzc2lvblByb3ZpZGVyUHJvcHMge1xuICBjaGlsZHJlbjogUmVhY3ROb2RlXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFNlc3Npb25Qcm92aWRlcih7IGNoaWxkcmVuIH06IFNlc3Npb25Qcm92aWRlclByb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPE5leHRBdXRoU2Vzc2lvblByb3ZpZGVyPlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvTmV4dEF1dGhTZXNzaW9uUHJvdmlkZXI+XG4gIClcbn0gIl0sIm5hbWVzIjpbIlNlc3Npb25Qcm92aWRlciIsIk5leHRBdXRoU2Vzc2lvblByb3ZpZGVyIiwiY2hpbGRyZW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/SessionProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   safeDecodeURIComponent: () => (/* binding */ safeDecodeURIComponent),\n/* harmony export */   safeEncodeURIComponent: () => (/* binding */ safeEncodeURIComponent)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\n * Safely decode URI component with fallback handling\n * @param encodedString - The URI encoded string to decode\n * @param fallback - Optional fallback value if decoding fails\n * @returns Decoded string or fallback/original string if decoding fails\n */ function safeDecodeURIComponent(encodedString, fallback) {\n    if (!encodedString) {\n        return fallback || '';\n    }\n    try {\n        return decodeURIComponent(encodedString);\n    } catch (error) {\n        console.warn('URI decode failed, using fallback:', error);\n        return fallback || encodedString;\n    }\n}\n/**\n * Safely encode URI component with error handling\n * @param str - The string to encode\n * @returns Encoded string or empty string if encoding fails\n */ function safeEncodeURIComponent(str) {\n    if (!str) return '';\n    try {\n        return encodeURIComponent(str);\n    } catch (error) {\n        console.warn('URI encode failed:', error);\n        return '';\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/framer-motion","vendor-chunks/tailwind-merge","vendor-chunks/motion-dom","vendor-chunks/lucide-react","vendor-chunks/motion-utils","vendor-chunks/clsx","vendor-chunks/mdast-util-to-markdown","vendor-chunks/mdast-util-to-hast","vendor-chunks/micromark-core-commonmark","vendor-chunks/property-information","vendor-chunks/micromark","vendor-chunks/micromark-util-symbol","vendor-chunks/micromark-extension-gfm-table","vendor-chunks/@ungap","vendor-chunks/debug","vendor-chunks/vfile","vendor-chunks/unist-util-visit-parents","vendor-chunks/unified","vendor-chunks/micromark-util-subtokenize","vendor-chunks/micromark-extension-gfm-task-list-item","vendor-chunks/micromark-extension-gfm-strikethrough","vendor-chunks/micromark-extension-gfm-footnote","vendor-chunks/micromark-extension-gfm-autolink-literal","vendor-chunks/mdast-util-find-and-replace","vendor-chunks/style-to-js","vendor-chunks/vfile-message","vendor-chunks/unist-util-visit","vendor-chunks/unist-util-stringify-position","vendor-chunks/unist-util-position","vendor-chunks/unist-util-is","vendor-chunks/trough","vendor-chunks/trim-lines","vendor-chunks/space-separated-tokens","vendor-chunks/remark-rehype","vendor-chunks/remark-parse","vendor-chunks/remark-gfm","vendor-chunks/react-markdown","vendor-chunks/micromark-util-sanitize-uri","vendor-chunks/micromark-util-resolve-all","vendor-chunks/micromark-util-normalize-identifier","vendor-chunks/micromark-util-html-tag-name","vendor-chunks/micromark-util-encode","vendor-chunks/micromark-util-decode-string","vendor-chunks/micromark-util-decode-numeric-character-reference","vendor-chunks/micromark-util-combine-extensions","vendor-chunks/micromark-util-classify-character","vendor-chunks/micromark-util-chunked","vendor-chunks/micromark-util-character","vendor-chunks/micromark-factory-whitespace","vendor-chunks/micromark-factory-title","vendor-chunks/micromark-factory-space","vendor-chunks/micromark-factory-label","vendor-chunks/micromark-factory-destination","vendor-chunks/micromark-extension-gfm","vendor-chunks/micromark-extension-gfm-tagfilter","vendor-chunks/mdast-util-to-string","vendor-chunks/mdast-util-phrasing","vendor-chunks/mdast-util-gfm","vendor-chunks/mdast-util-gfm-task-list-item","vendor-chunks/mdast-util-gfm-table","vendor-chunks/mdast-util-gfm-strikethrough","vendor-chunks/mdast-util-gfm-footnote","vendor-chunks/mdast-util-gfm-autolink-literal","vendor-chunks/mdast-util-from-markdown","vendor-chunks/markdown-table","vendor-chunks/longest-streak","vendor-chunks/is-plain-obj","vendor-chunks/html-url-attributes","vendor-chunks/hast-util-whitespace","vendor-chunks/hast-util-to-jsx-runtime","vendor-chunks/estree-util-is-identifier-name","vendor-chunks/devlop","vendor-chunks/dequal","vendor-chunks/decode-named-character-reference","vendor-chunks/comma-separated-tokens","vendor-chunks/character-entities","vendor-chunks/ccount","vendor-chunks/bail","vendor-chunks/supports-color","vendor-chunks/style-to-object","vendor-chunks/ms","vendor-chunks/inline-style-parser","vendor-chunks/has-flag","vendor-chunks/extend"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Farticle-view%2F%5Bid%5D%2Fpage&page=%2Farticle-view%2F%5Bid%5D%2Fpage&appPaths=%2Farticle-view%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Farticle-view%2F%5Bid%5D%2Fpage.tsx&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();