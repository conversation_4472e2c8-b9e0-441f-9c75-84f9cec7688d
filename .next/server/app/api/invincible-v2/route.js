/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/invincible-v2/route";
exports.ids = ["app/api/invincible-v2/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Finvincible-v2%2Froute&page=%2Fapi%2Finvincible-v2%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finvincible-v2%2Froute.ts&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Finvincible-v2%2Froute&page=%2Fapi%2Finvincible-v2%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finvincible-v2%2Froute.ts&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_aayushmishra_Desktop_old_invincible_with_deepresearch_src_app_api_invincible_v2_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/invincible-v2/route.ts */ \"(rsc)/./src/app/api/invincible-v2/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/invincible-v2/route\",\n        pathname: \"/api/invincible-v2\",\n        filename: \"route\",\n        bundlePath: \"app/api/invincible-v2/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/api/invincible-v2/route.ts\",\n    nextConfigOutput,\n    userland: _Users_aayushmishra_Desktop_old_invincible_with_deepresearch_src_app_api_invincible_v2_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Finvincible-v2%2Froute&page=%2Fapi%2Finvincible-v2%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finvincible-v2%2Froute.ts&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/invincible-v2/route.ts":
/*!********************************************!*\
  !*** ./src/app/api/invincible-v2/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_agents_v2_core_supervisor__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/agents/v2/core/supervisor */ \"(rsc)/./src/lib/agents/v2/core/supervisor.ts\");\n/* harmony import */ var _lib_agents_v2_utils_logger__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/agents/v2/utils/logger */ \"(rsc)/./src/lib/agents/v2/utils/logger.ts\");\n/**\n * Invincible V.2 - Main API Route\n * Non-streaming endpoint for V2 autonomous agent system\n */ \n\n\nasync function POST(request) {\n    const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;\n    try {\n        _lib_agents_v2_utils_logger__WEBPACK_IMPORTED_MODULE_2__.logger.info('V2 API request received', {\n            requestId,\n            endpoint: '/api/invincible-v2',\n            method: 'POST'\n        });\n        const body = await request.json();\n        const { topic, contentLength = 2000, tone = 'professional', targetAudience = 'general audience', customInstructions = '', contentType = 'article' } = body;\n        _lib_agents_v2_utils_logger__WEBPACK_IMPORTED_MODULE_2__.logger.info('V2 API request parsed', {\n            requestId,\n            topic,\n            contentLength,\n            tone,\n            targetAudience,\n            contentType\n        });\n        // Validate required fields\n        if (!topic) {\n            _lib_agents_v2_utils_logger__WEBPACK_IMPORTED_MODULE_2__.logger.warn('V2 API validation failed: missing topic', {\n                requestId\n            });\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Topic is required'\n            }, {\n                status: 400\n            });\n        }\n        // Validate API keys\n        const openRouterApiKey = \"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\";\n        const tavilyApiKey = \"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\";\n        if (!openRouterApiKey) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'OpenRouter API key not configured'\n            }, {\n                status: 500\n            });\n        }\n        if (!tavilyApiKey) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Tavily API key not configured'\n            }, {\n                status: 500\n            });\n        }\n        console.log('✅ API keys validated, creating supervisor agent');\n        // Create requirements object\n        const requirements = {\n            topic,\n            contentLength: parseInt(contentLength.toString()),\n            tone,\n            targetAudience,\n            customInstructions,\n            contentType: contentType\n        };\n        // Create supervisor agent\n        const supervisor = (0,_lib_agents_v2_core_supervisor__WEBPACK_IMPORTED_MODULE_1__.createSupervisorAgent)({\n            openRouterApiKey,\n            tavilyApiKey,\n            streaming: false,\n            qualityThreshold: 80\n        });\n        console.log('🤖 Starting V.2 workflow execution...');\n        // Execute the complete workflow\n        const startTime = Date.now();\n        const finalState = await supervisor.executeWorkflow(requirements);\n        const executionTime = Date.now() - startTime;\n        console.log('✅ V.2 workflow completed in', executionTime, 'ms');\n        // Check for errors\n        if (finalState.workflow.errors.length > 0) {\n            console.error('❌ V.2 workflow errors:', finalState.workflow.errors);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Workflow execution failed',\n                details: finalState.workflow.errors\n            }, {\n                status: 500\n            });\n        }\n        // Check if result is available\n        if (!finalState.result) {\n            console.error('❌ No result generated');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'No result generated - workflow may have failed'\n            }, {\n                status: 500\n            });\n        }\n        console.log('📊 V.2 Results:', {\n            wordCount: finalState.result.article?.wordCount,\n            qualityScore: finalState.result.performance?.qualityScore,\n            executionTime: finalState.result.performance?.executionTime\n        });\n        // Return successful result\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            result: finalState.result,\n            metadata: {\n                sessionId: finalState.sessionId,\n                version: 'v2',\n                executionTime,\n                agentsExecuted: finalState.workflow.completedAgents.length,\n                messagesCount: finalState.messages.length,\n                researchQueries: finalState.research.data.length,\n                totalSources: finalState.research.data.reduce((sum, r)=>sum + r.results.length, 0)\n            }\n        });\n    } catch (error) {\n        console.error('❌ Invincible V.2 API Error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Internal server error',\n            details:  true ? error.toString() : 0\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/invincible-v2/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/agents/v2/core/state-schema.ts":
/*!************************************************!*\
  !*** ./src/lib/agents/v2/core/state-schema.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StateManager: () => (/* binding */ StateManager),\n/* harmony export */   createInitialState: () => (/* binding */ createInitialState),\n/* harmony export */   validateState: () => (/* binding */ validateState)\n/* harmony export */ });\n/**\n * Invincible V.2 - Advanced State Schema for LangGraph Multi-Agent System\n * Built for state-of-the-art content generation with competitive intelligence\n */ /**\n * State Update Functions\n * These functions help agents update specific parts of the state\n */ class StateManager {\n    static updateResearch(state, researchData) {\n        return {\n            ...state,\n            research: {\n                ...state.research,\n                data: [\n                    ...state.research.data,\n                    researchData\n                ]\n            }\n        };\n    }\n    static updateAnalysis(state, analysis) {\n        return {\n            ...state,\n            analysis: {\n                ...state.analysis,\n                ...analysis\n            }\n        };\n    }\n    static updateGeneration(state, generation) {\n        return {\n            ...state,\n            generation: {\n                ...state.generation,\n                content: {\n                    ...state.generation.content,\n                    ...generation\n                }\n            }\n        };\n    }\n    static updateQuality(state, quality) {\n        return {\n            ...state,\n            quality: {\n                ...state.quality,\n                metrics: {\n                    ...state.quality.metrics,\n                    ...quality\n                }\n            }\n        };\n    }\n    static updateWorkflow(state, workflow) {\n        return {\n            ...state,\n            workflow: {\n                ...state.workflow,\n                ...workflow\n            }\n        };\n    }\n    static addMessage(state, message) {\n        return {\n            ...state,\n            messages: [\n                ...state.messages,\n                {\n                    ...message,\n                    timestamp: Date.now()\n                }\n            ]\n        };\n    }\n}\n/**\n * Initial State Factory\n */ function createInitialState(requirements) {\n    return {\n        requirements,\n        research: {\n            data: [],\n            currentQuery: '',\n            completed: false\n        },\n        analysis: {\n            seo: {\n                primaryKeywords: [],\n                secondaryKeywords: [],\n                searchIntent: 'informational',\n                competitorKeywords: [],\n                geoOptimization: {\n                    voiceSearchOptimized: false,\n                    aiSearchOptimized: false,\n                    citationStructure: []\n                }\n            },\n            geo: {\n                aiSearchVisibility: {\n                    perplexity: false,\n                    chatgpt: false,\n                    gemini: false,\n                    claude: false\n                },\n                referenceOptimization: {\n                    citations: [],\n                    sourceCredibility: 0,\n                    factualAccuracy: 0\n                },\n                multimodalOptimization: {\n                    voiceSearchReady: false,\n                    visualSearchReady: false,\n                    structuredData: {}\n                }\n            },\n            competitive: {\n                topContent: [],\n                contentGaps: [],\n                opportunities: []\n            },\n            completed: false\n        },\n        generation: {\n            content: {\n                outline: [],\n                content: '',\n                title: '',\n                metaDescription: '',\n                wordCount: 0,\n                readabilityScore: 0,\n                humanizationApplied: []\n            },\n            iterations: 0,\n            completed: false\n        },\n        quality: {\n            metrics: {\n                aiDetectionScore: 0,\n                originalityScore: 0,\n                competitiveAdvantage: 0,\n                seoScore: 0,\n                geoScore: 0,\n                humanLikenessScore: 0\n            },\n            validationResults: [],\n            humanizationApplied: [],\n            completed: false\n        },\n        workflow: {\n            currentAgent: 'supervisor',\n            nextAgent: 'research_agent',\n            completedAgents: [],\n            errors: [],\n            progress: 0\n        },\n        messages: [],\n        result: null,\n        sessionId: `v2_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n        startTime: Date.now(),\n        version: 'v2'\n    };\n}\n/**\n * State Validation\n */ function validateState(state) {\n    return !!(state.requirements && state.sessionId && state.version === 'v2' && state.workflow && Array.isArray(state.messages));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/agents/v2/core/state-schema.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/agents/v2/core/supervisor.ts":
/*!**********************************************!*\
  !*** ./src/lib/agents/v2/core/supervisor.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SupervisorAgent: () => (/* binding */ SupervisorAgent),\n/* harmony export */   createSupervisorAgent: () => (/* binding */ createSupervisorAgent)\n/* harmony export */ });\n/* harmony import */ var _state_schema__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./state-schema */ \"(rsc)/./src/lib/agents/v2/core/state-schema.ts\");\n/* harmony import */ var _tools_kimi_k2_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../tools/kimi-k2-client */ \"(rsc)/./src/lib/agents/v2/tools/kimi-k2-client.ts\");\n/* harmony import */ var _utils_logger__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/logger */ \"(rsc)/./src/lib/agents/v2/utils/logger.ts\");\n/* harmony import */ var _utils_json_parser__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/json-parser */ \"(rsc)/./src/lib/agents/v2/utils/json-parser.ts\");\n/**\n * Invincible V.2 - Intelligent Supervisor Agent\n * Dynamic workflow coordination with decision-making capabilities\n */ \n\n\n\nclass SupervisorAgent {\n    constructor(config){\n        this.currentWorkflow = [];\n        this.config = {\n            streaming: true,\n            maxRetries: 3,\n            qualityThreshold: 85,\n            ...config\n        };\n        this.kimiClient = (0,_tools_kimi_k2_client__WEBPACK_IMPORTED_MODULE_1__.createKimiK2Client)(this.config.openRouterApiKey);\n        this.supervisorLogger = (0,_utils_logger__WEBPACK_IMPORTED_MODULE_2__.createLogger)({\n            agentName: 'SupervisorAgent',\n            workflowId: `workflow_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`\n        });\n        this.supervisorLogger.info('Supervisor initialized');\n        this.initializeAgentCapabilities();\n    }\n    /**\n   * Initialize agent capabilities and dependencies\n   */ initializeAgentCapabilities() {\n        this.agentCapabilities = new Map([\n            [\n                'research_agent',\n                {\n                    name: 'Research Agent',\n                    description: 'Conducts comprehensive research with competitive analysis and SEO intelligence',\n                    inputs: [\n                        'topic',\n                        'contentType',\n                        'targetAudience'\n                    ],\n                    outputs: [\n                        'researchData',\n                        'competitiveAnalysis',\n                        'seoIntelligence'\n                    ],\n                    estimatedTime: 45,\n                    dependencies: []\n                }\n            ],\n            [\n                'content_agent',\n                {\n                    name: 'Content Generation Agent',\n                    description: 'Creates high-quality, human-like content using advanced AI techniques',\n                    inputs: [\n                        'researchData',\n                        'outline',\n                        'requirements',\n                        'competitiveAnalysis'\n                    ],\n                    outputs: [\n                        'content',\n                        'title',\n                        'metaDescription'\n                    ],\n                    estimatedTime: 60,\n                    dependencies: [\n                        'research_agent'\n                    ]\n                }\n            ],\n            [\n                'seo_geo_agent',\n                {\n                    name: 'SEO & GEO Optimization Agent',\n                    description: 'Optimizes content for traditional SEO and generative engine optimization',\n                    inputs: [\n                        'content',\n                        'keywords',\n                        'competitiveAnalysis'\n                    ],\n                    outputs: [\n                        'optimizedContent',\n                        'seoScore',\n                        'geoOptimization'\n                    ],\n                    estimatedTime: 30,\n                    dependencies: [\n                        'content_agent'\n                    ]\n                }\n            ],\n            [\n                'quality_agent',\n                {\n                    name: 'Quality Assurance Agent',\n                    description: 'Validates content quality, applies humanization, and ensures AI detection bypass',\n                    inputs: [\n                        'content',\n                        'requirements',\n                        'qualityMetrics'\n                    ],\n                    outputs: [\n                        'validatedContent',\n                        'qualityScore',\n                        'humanizationReport'\n                    ],\n                    estimatedTime: 25,\n                    dependencies: [\n                        'seo_geo_agent'\n                    ]\n                }\n            ]\n        ]);\n    }\n    /**\n   * Main workflow execution method\n   */ async executeWorkflow(requirements) {\n        const workflowStartTime = Date.now();\n        let state = (0,_state_schema__WEBPACK_IMPORTED_MODULE_0__.createInitialState)(requirements);\n        this.supervisorLogger.info(`Workflow started: \"${requirements.topic}\"`);\n        try {\n            // Update supervisor status\n            state = _state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.updateWorkflow(state, {\n                currentAgent: 'supervisor',\n                progress: 5\n            });\n            const estimatedTime = this.calculateEstimatedTime();\n            this.supervisorLogger.info(`Workflow planned: ${this.currentWorkflow.join(' → ')} (~${estimatedTime}s)`);\n            state = _state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.addMessage(state, {\n                agent: 'supervisor',\n                action: 'workflow_started',\n                data: {\n                    topic: requirements.topic,\n                    estimatedTime\n                },\n                status: 'in_progress'\n            });\n            // Plan the optimal workflow\n            const workflowPlan = await this.planWorkflow(state);\n            this.currentWorkflow = workflowPlan.agents;\n            state = _state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.addMessage(state, {\n                agent: 'supervisor',\n                action: 'workflow_planned',\n                data: {\n                    plannedAgents: workflowPlan.agents,\n                    reasoning: workflowPlan.reasoning\n                },\n                status: 'completed'\n            });\n            // Execute each agent in the planned sequence\n            for (const agentName of this.currentWorkflow){\n                state = await this.executeAgent(agentName, state);\n                // Check if we should continue or modify the workflow\n                const shouldContinue = await this.evaluateProgress(state);\n                if (!shouldContinue.continue) {\n                    break;\n                }\n                if (shouldContinue.modifyWorkflow) {\n                    const newPlan = await this.replanWorkflow(state, shouldContinue.reason);\n                    this.currentWorkflow = newPlan.agents;\n                }\n            }\n            // Final workflow validation\n            const finalValidation = await this.validateFinalResult(state);\n            if (finalValidation.isValid) {\n                state = _state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.updateWorkflow(state, {\n                    progress: 100,\n                    currentAgent: 'supervisor',\n                    nextAgent: null\n                });\n                state = _state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.addMessage(state, {\n                    agent: 'supervisor',\n                    action: 'workflow_completed',\n                    data: {\n                        qualityScore: finalValidation.qualityScore,\n                        completionTime: Date.now() - state.startTime\n                    },\n                    status: 'completed'\n                });\n                state.endTime = Date.now();\n            } else {\n                throw new Error(`Final validation failed: ${finalValidation.issues.join(', ')}`);\n            }\n            return state;\n        } catch (error) {\n            console.error('Supervisor workflow error:', error);\n            return _state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.addMessage(state, {\n                agent: 'supervisor',\n                action: 'workflow_failed',\n                data: {\n                    error: error.toString()\n                },\n                status: 'error'\n            });\n        }\n    }\n    /**\n   * Plan the optimal workflow based on requirements\n   */ async planWorkflow(state) {\n        const systemPrompt = `You are an intelligent workflow supervisor for an advanced content generation system. Your role is to analyze content requirements and plan the optimal sequence of AI agents to execute.\n\nAvailable Agents:\n${Array.from(this.agentCapabilities.entries()).map(([name, cap])=>`- ${cap.name}: ${cap.description} (${cap.estimatedTime}s)`).join('\\n')}\n\nConsider:\n1. Dependencies between agents\n2. Content complexity and requirements\n3. Quality objectives\n4. Time constraints\n5. Potential optimization opportunities`;\n        const userPrompt = `Plan the optimal workflow for this content generation task:\n\n**Requirements:**\n- Topic: \"${state.requirements.topic}\"\n- Content Type: ${state.requirements.contentType}\n- Length: ${state.requirements.contentLength} words\n- Tone: ${state.requirements.tone}\n- Target Audience: ${state.requirements.targetAudience}\n- Custom Instructions: ${state.requirements.customInstructions || 'None'}\n\n**Quality Objectives:**\n- SEO optimized for search engines\n- GEO optimized for AI search (ChatGPT, Perplexity, etc.)\n- Human-like writing that bypasses AI detection\n- Competitive advantage over existing content\n- Factual accuracy and credibility\n\nReturn a JSON response with:\n{\n  \"agents\": [\"agent1\", \"agent2\", ...],\n  \"reasoning\": \"Explanation of why this sequence is optimal\",\n  \"skipConditions\": [\"Optional conditions where agents might be skipped\"],\n  \"optimizations\": [\"Potential workflow optimizations\"]\n}`;\n        try {\n            const response = await this.kimiClient.generateContent([\n                {\n                    role: 'system',\n                    content: systemPrompt\n                },\n                {\n                    role: 'user',\n                    content: userPrompt\n                }\n            ]);\n            const plan = (0,_utils_json_parser__WEBPACK_IMPORTED_MODULE_3__.safeJsonParse)(response.content, {\n                agents: [\n                    'research_agent',\n                    'content_agent',\n                    'seo_geo_agent',\n                    'quality_agent'\n                ],\n                reasoning: 'Fallback to standard workflow due to parsing error'\n            });\n            // Map agent names to proper identifiers\n            const agentNameMap = {\n                'Research Agent': 'research_agent',\n                'Content Generation Agent': 'content_agent',\n                'SEO & GEO Optimization Agent': 'seo_geo_agent',\n                'Quality Assurance Agent': 'quality_agent',\n                'research_agent': 'research_agent',\n                'content_agent': 'content_agent',\n                'seo_geo_agent': 'seo_geo_agent',\n                'quality_agent': 'quality_agent'\n            };\n            const mappedAgents = (plan?.agents || [\n                'research_agent',\n                'content_agent',\n                'seo_geo_agent',\n                'quality_agent'\n            ]).map((agent)=>agentNameMap[agent] || agent);\n            return {\n                agents: mappedAgents,\n                reasoning: plan?.reasoning || 'Standard workflow sequence',\n                estimatedTime: this.calculateEstimatedTime(mappedAgents)\n            };\n        } catch (error) {\n            console.error('Workflow planning failed:', error);\n            // Fallback to standard workflow\n            return {\n                agents: [\n                    'research_agent',\n                    'content_agent',\n                    'seo_geo_agent',\n                    'quality_agent'\n                ],\n                reasoning: 'Fallback to standard workflow due to planning error',\n                estimatedTime: this.calculateEstimatedTime()\n            };\n        }\n    }\n    /**\n   * Execute a specific agent\n   */ async executeAgent(agentName, state) {\n        const capability = this.agentCapabilities.get(agentName);\n        if (!capability) {\n            throw new Error(`Unknown agent: ${agentName}`);\n        }\n        try {\n            // Dynamic agent loading and execution\n            switch(agentName){\n                case 'research_agent':\n                    const { ResearchAgent } = await __webpack_require__.e(/*! import() */ \"_rsc_src_lib_agents_v2_agents_research-agent_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ../agents/research-agent */ \"(rsc)/./src/lib/agents/v2/agents/research-agent.ts\"));\n                    const researchAgent = new ResearchAgent({\n                        tavilyApiKey: this.config.tavilyApiKey\n                    });\n                    return await researchAgent.execute(state);\n                case 'content_agent':\n                    const { ContentAgent } = await __webpack_require__.e(/*! import() */ \"_rsc_src_lib_agents_v2_agents_content-agent_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ../agents/content-agent */ \"(rsc)/./src/lib/agents/v2/agents/content-agent.ts\"));\n                    const contentAgent = new ContentAgent({\n                        kimiApiKey: this.config.openRouterApiKey\n                    });\n                    return await contentAgent.execute(state);\n                case 'seo_geo_agent':\n                    const { SEOGEOAgent } = await __webpack_require__.e(/*! import() */ \"_rsc_src_lib_agents_v2_agents_seo-geo-agent_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ../agents/seo-geo-agent */ \"(rsc)/./src/lib/agents/v2/agents/seo-geo-agent.ts\"));\n                    const seoAgent = new SEOGEOAgent({\n                        kimiApiKey: this.config.openRouterApiKey\n                    });\n                    return await seoAgent.execute(state);\n                case 'quality_agent':\n                    const { QualityAgent } = await __webpack_require__.e(/*! import() */ \"_rsc_src_lib_agents_v2_agents_quality-agent_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ../agents/quality-agent */ \"(rsc)/./src/lib/agents/v2/agents/quality-agent.ts\"));\n                    const qualityAgent = new QualityAgent({\n                        kimiApiKey: this.config.openRouterApiKey\n                    });\n                    return await qualityAgent.execute(state);\n                default:\n                    throw new Error(`Agent execution not implemented: ${agentName}`);\n            }\n        } catch (error) {\n            console.error(`Agent ${agentName} execution failed:`, error);\n            return _state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.addMessage(state, {\n                agent: 'supervisor',\n                action: 'agent_execution_failed',\n                data: {\n                    agentName,\n                    error: error.toString()\n                },\n                status: 'error'\n            });\n        }\n    }\n    /**\n   * Evaluate progress and decide whether to continue\n   */ async evaluateProgress(state) {\n        // Check for errors\n        if (state.workflow.errors.length > 0) {\n            return {\n                continue: false,\n                reason: `Errors detected: ${state.workflow.errors.join(', ')}`\n            };\n        }\n        // Check completion status of current phase\n        const currentAgent = state.workflow.currentAgent;\n        switch(currentAgent){\n            case 'research_agent':\n                if (!state.research.completed) {\n                    return {\n                        continue: false,\n                        reason: 'Research phase incomplete'\n                    };\n                }\n                break;\n            case 'content_agent':\n                if (!state.generation.completed) {\n                    return {\n                        continue: false,\n                        reason: 'Content generation phase incomplete'\n                    };\n                }\n                break;\n            case 'seo_geo_agent':\n                if (!state.analysis.completed) {\n                    return {\n                        continue: false,\n                        reason: 'SEO/GEO optimization phase incomplete'\n                    };\n                }\n                break;\n            case 'quality_agent':\n                if (!state.quality.completed) {\n                    return {\n                        continue: false,\n                        reason: 'Quality assurance phase incomplete'\n                    };\n                }\n                break;\n        }\n        return {\n            continue: true\n        };\n    }\n    /**\n   * Replan workflow based on current state\n   */ async replanWorkflow(state, reason) {\n        const prompt = `Based on the current workflow state, determine if the remaining workflow should be modified.\n\n**Current State:**\n- Completed Agents: ${state.workflow.completedAgents.join(', ')}\n- Current Agent: ${state.workflow.currentAgent}\n- Progress: ${state.workflow.progress}%\n- Modification Reason: ${reason}\n\n**Available Remaining Agents:**\n${Array.from(this.agentCapabilities.keys()).filter((agent)=>!state.workflow.completedAgents.includes(agent)).join(', ')}\n\nShould the workflow be modified? Return JSON with:\n{\n  \"modify\": true/false,\n  \"agents\": [\"remaining_agent1\", \"remaining_agent2\", ...],\n  \"reasoning\": \"Explanation for any modifications\"\n}`;\n        try {\n            const response = await this.kimiClient.generateContent([\n                {\n                    role: 'user',\n                    content: prompt\n                }\n            ]);\n            const decision = (0,_utils_json_parser__WEBPACK_IMPORTED_MODULE_3__.safeJsonParse)(response.content, {\n                modify: false,\n                agents: this.currentWorkflow,\n                reasoning: 'No workflow modification needed'\n            });\n            return {\n                agents: decision?.agents || this.currentWorkflow,\n                reasoning: decision?.reasoning || 'No workflow modification needed'\n            };\n        } catch (error) {\n            console.error('Workflow replanning failed:', error);\n            return {\n                agents: this.currentWorkflow,\n                reasoning: 'Replanning failed, continuing with original workflow'\n            };\n        }\n    }\n    /**\n   * Validate the final result\n   */ async validateFinalResult(state) {\n        const issues = [];\n        let qualityScore = 0;\n        // Check if all required phases are completed\n        if (!state.research.completed) {\n            issues.push('Research phase not completed');\n        } else {\n            qualityScore += 25;\n        }\n        if (!state.generation.completed) {\n            issues.push('Content generation not completed');\n        } else {\n            qualityScore += 25;\n        }\n        if (!state.analysis.completed) {\n            issues.push('SEO/GEO analysis not completed');\n        } else {\n            qualityScore += 25;\n        }\n        if (!state.quality.completed) {\n            issues.push('Quality assurance not completed');\n        } else {\n            qualityScore += 25;\n        }\n        // Check content quality metrics\n        if (state.quality.metrics.aiDetectionScore > 50) {\n            issues.push('Content may be detected as AI-generated');\n            qualityScore -= 10;\n        }\n        if (state.quality.metrics.seoScore < 70) {\n            issues.push('SEO score below acceptable threshold');\n            qualityScore -= 10;\n        }\n        if (state.generation.content.wordCount < state.requirements.contentLength * 0.8) {\n            issues.push('Content length significantly below target');\n            qualityScore -= 15;\n        }\n        const isValid = issues.length === 0 && qualityScore >= this.config.qualityThreshold;\n        return {\n            isValid,\n            qualityScore: Math.max(0, qualityScore),\n            issues\n        };\n    }\n    /**\n   * Calculate estimated execution time\n   */ calculateEstimatedTime(agents) {\n        const agentsToExecute = agents || Array.from(this.agentCapabilities.keys());\n        return agentsToExecute.reduce((total, agentName)=>{\n            const capability = this.agentCapabilities.get(agentName);\n            return total + (capability?.estimatedTime || 30);\n        }, 0);\n    }\n    /**\n   * Generate workflow progress stream\n   */ async *generateProgressStream(requirements) {\n        let state = (0,_state_schema__WEBPACK_IMPORTED_MODULE_0__.createInitialState)(requirements);\n        yield {\n            type: 'workflow_started',\n            data: {\n                sessionId: state.sessionId,\n                estimatedTime: this.calculateEstimatedTime()\n            },\n            timestamp: Date.now()\n        };\n        try {\n            // Plan workflow\n            const plan = await this.planWorkflow(state);\n            yield {\n                type: 'workflow_planned',\n                data: {\n                    agents: plan.agents,\n                    reasoning: plan.reasoning\n                },\n                timestamp: Date.now()\n            };\n            // Execute agents with progress updates\n            for(let i = 0; i < plan.agents.length; i++){\n                const agentName = plan.agents[i];\n                yield {\n                    type: 'agent_started',\n                    data: {\n                        agent: agentName,\n                        progress: i / plan.agents.length * 100\n                    },\n                    timestamp: Date.now()\n                };\n                state = await this.executeAgent(agentName, state);\n                yield {\n                    type: 'agent_completed',\n                    data: {\n                        agent: agentName,\n                        progress: (i + 1) / plan.agents.length * 100,\n                        messages: state.messages.slice(-5)\n                    },\n                    timestamp: Date.now()\n                };\n            }\n            // Final validation\n            const validation = await this.validateFinalResult(state);\n            yield {\n                type: 'workflow_completed',\n                data: {\n                    success: validation.isValid,\n                    qualityScore: validation.qualityScore,\n                    result: state.result,\n                    totalTime: Date.now() - state.startTime\n                },\n                timestamp: Date.now()\n            };\n        } catch (error) {\n            yield {\n                type: 'workflow_error',\n                data: {\n                    error: error.toString()\n                },\n                timestamp: Date.now()\n            };\n        }\n    }\n    /**\n   * Get workflow status\n   */ getWorkflowStatus(state) {\n        const completedAgents = state.workflow.completedAgents.length;\n        const totalAgents = this.currentWorkflow.length;\n        const progress = totalAgents > 0 ? completedAgents / totalAgents * 100 : 0;\n        const remainingAgents = this.currentWorkflow.slice(completedAgents);\n        const estimatedTimeRemaining = remainingAgents.reduce((total, agentName)=>{\n            const capability = this.agentCapabilities.get(agentName);\n            return total + (capability?.estimatedTime || 30);\n        }, 0);\n        return {\n            phase: this.getWorkflowPhase(state),\n            progress,\n            estimatedTimeRemaining,\n            currentAgent: state.workflow.currentAgent,\n            nextAgent: state.workflow.nextAgent\n        };\n    }\n    /**\n   * Get current workflow phase name\n   */ getWorkflowPhase(state) {\n        if (!state.research.completed) return 'Research & Analysis';\n        if (!state.generation.completed) return 'Content Generation';\n        if (!state.analysis.completed) return 'SEO & GEO Optimization';\n        if (!state.quality.completed) return 'Quality Assurance';\n        return 'Completion';\n    }\n}\n/**\n * Factory function to create supervisor agent\n */ function createSupervisorAgent(config) {\n    return new SupervisorAgent(config);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/agents/v2/core/supervisor.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/agents/v2/tools/kimi-k2-client.ts":
/*!***************************************************!*\
  !*** ./src/lib/agents/v2/tools/kimi-k2-client.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   KimiK2Client: () => (/* binding */ KimiK2Client),\n/* harmony export */   KimiK2Utils: () => (/* binding */ KimiK2Utils),\n/* harmony export */   createKimiK2Client: () => (/* binding */ createKimiK2Client)\n/* harmony export */ });\n/* harmony import */ var openai__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! openai */ \"(rsc)/./node_modules/openai/index.mjs\");\n/* harmony import */ var _utils_logger__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/logger */ \"(rsc)/./src/lib/agents/v2/utils/logger.ts\");\n/**\n * Invincible V.2 - Kimi K2 OpenRouter Client Integration\n * Advanced AI client with agentic capabilities and tool calling\n */ \n\nclass KimiK2Client {\n    constructor(config){\n        this.config = {\n            baseURL: 'https://openrouter.ai/api/v1',\n            temperature: 0.7,\n            maxTokens: 4000,\n            streaming: false,\n            ...config\n        };\n        this.client = new openai__WEBPACK_IMPORTED_MODULE_1__[\"default\"]({\n            baseURL: this.config.baseURL,\n            apiKey: this.config.apiKey,\n            defaultHeaders: {\n                'HTTP-Referer': 'http://localhost:3000',\n                'X-Title': 'Invincible V2 Agent System'\n            }\n        });\n        this.aiLogger = (0,_utils_logger__WEBPACK_IMPORTED_MODULE_0__.createLogger)({\n            agentName: 'KimiK2Client',\n            model: this.config.model\n        });\n        this.aiLogger.info('🤖 OpenRouter Kimi K2 Client initialized', {\n            model: this.config.model,\n            baseURL: this.config.baseURL,\n            temperature: this.config.temperature,\n            maxTokens: this.config.maxTokens,\n            apiKeyPrefix: this.config.apiKey.substring(0, 15) + '...'\n        });\n    }\n    /**\n   * Generate content using Kimi K2 with advanced prompting\n   */ async generateContent(messages, tools, options) {\n        const requestConfig = {\n            ...this.config,\n            ...options\n        };\n        const requestId = `openrouter_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;\n        this.aiLogger.info('🚀 OpenRouter API Request Starting', {\n            requestId,\n            model: requestConfig.model,\n            temperature: requestConfig.temperature,\n            maxTokens: requestConfig.maxTokens,\n            messagesCount: messages.length,\n            hasTools: !!tools,\n            toolsCount: tools?.length || 0,\n            firstMessagePreview: messages[0]?.content?.substring(0, 100) + '...'\n        });\n        const startTime = Date.now();\n        try {\n            this.aiLogger.debug('📡 Making OpenRouter API call', {\n                requestId,\n                endpoint: `${this.config.baseURL}/chat/completions`,\n                payload: {\n                    model: requestConfig.model,\n                    temperature: requestConfig.temperature,\n                    max_tokens: requestConfig.maxTokens,\n                    stream: requestConfig.streaming\n                }\n            });\n            const response = await this.client.chat.completions.create({\n                model: requestConfig.model,\n                messages: messages,\n                temperature: requestConfig.temperature,\n                max_tokens: requestConfig.maxTokens,\n                tools: tools,\n                tool_choice: tools ? 'auto' : undefined,\n                stream: requestConfig.streaming\n            });\n            const responseTime = Date.now() - startTime;\n            this.aiLogger.info('✅ OpenRouter API Response Received', {\n                requestId,\n                responseTime: `${responseTime}ms`,\n                model: requestConfig.model,\n                hasResponse: !!response\n            });\n            const choice = response.choices[0];\n            const usage = response.usage;\n            // Calculate cost (Kimi K2 pricing: $0.15 input, $2.50 output per 1M tokens)\n            const cost = (usage?.prompt_tokens || 0) * 0.15 / 1000000 + (usage?.completion_tokens || 0) * 2.50 / 1000000;\n            const result = {\n                content: choice.message.content || '',\n                usage: {\n                    prompt_tokens: usage?.prompt_tokens || 0,\n                    completion_tokens: usage?.completion_tokens || 0,\n                    total_tokens: usage?.total_tokens || 0,\n                    cost: cost || 0\n                },\n                tool_calls: choice.message.tool_calls,\n                finish_reason: choice.finish_reason || 'stop'\n            };\n            this.aiLogger.info('📊 OpenRouter API Call Completed', {\n                requestId,\n                responseTime: `${responseTime}ms`,\n                promptTokens: result.usage.prompt_tokens,\n                completionTokens: result.usage.completion_tokens,\n                totalTokens: result.usage.total_tokens,\n                cost: `$${(result.usage.cost || 0).toFixed(6)}`,\n                finishReason: result.finish_reason,\n                contentLength: result.content.length,\n                contentPreview: result.content.substring(0, 150) + '...'\n            });\n            return result;\n        } catch (error) {\n            const responseTime = Date.now() - startTime;\n            this.aiLogger.error('❌ OpenRouter API Error', {\n                requestId,\n                responseTime: `${responseTime}ms`,\n                model: requestConfig.model,\n                error: error.message,\n                errorStack: error.stack,\n                errorName: error.name\n            });\n            console.error('Kimi K2 API Error:', error);\n            throw new Error(`Kimi K2 generation failed: ${error}`);\n        }\n    }\n    /**\n   * Generate content with streaming for real-time updates\n   */ async *generateStream(messages, tools, options) {\n        const requestConfig = {\n            ...this.config,\n            ...options,\n            streaming: true\n        };\n        try {\n            const stream = await this.client.chat.completions.create({\n                model: requestConfig.model,\n                messages: messages,\n                temperature: requestConfig.temperature,\n                max_tokens: requestConfig.maxTokens,\n                tools: tools,\n                tool_choice: tools ? 'auto' : undefined,\n                stream: true\n            });\n            let content = '';\n            let usage = null;\n            for await (const chunk of stream){\n                const delta = chunk.choices[0]?.delta;\n                if (delta?.content) {\n                    content += delta.content;\n                    yield {\n                        content: delta.content,\n                        done: false\n                    };\n                }\n                if (chunk.usage) {\n                    usage = chunk.usage;\n                }\n                if (chunk.choices[0]?.finish_reason) {\n                    yield {\n                        content: '',\n                        done: true,\n                        usage: usage ? {\n                            ...usage,\n                            cost: usage.prompt_tokens * 0.15 / 1000000 + usage.completion_tokens * 2.50 / 1000000\n                        } : undefined\n                    };\n                    break;\n                }\n            }\n        } catch (error) {\n            console.error('Kimi K2 Streaming Error:', error);\n            throw new Error(`Kimi K2 streaming failed: ${error}`);\n        }\n    }\n    /**\n   * Advanced content generation with specific prompting strategies\n   */ async generateArticleContent(params) {\n        const systemPrompt = `You are Kimi, an advanced AI content writer specialized in creating superior, human-like articles that outperform competitors. Your expertise includes:\n\n1. **Human-Like Writing**: Create content that flows naturally, uses varied sentence structures, and incorporates conversational elements\n2. **Competitive Intelligence**: Leverage competitor analysis to identify gaps and create superior content\n3. **SEO & GEO Optimization**: Optimize for both traditional search engines and AI search systems (ChatGPT, Perplexity, Gemini)\n4. **Factual Accuracy**: Ensure all information is accurate and well-researched\n5. **Engagement**: Write compelling content that captures and maintains reader attention\n\nWrite in a style that:\n- Bypasses AI detection through natural language patterns\n- Incorporates industry expertise and insider knowledge\n- Uses strategic keyword placement without keyword stuffing\n- Creates content that AI systems will want to reference and cite\n- Maintains perfect grammar while using conversational tone`;\n        const userPrompt = `Create a comprehensive article on \"${params.topic}\" that surpasses all competitor content.\n\n**Content Requirements:**\n- Length: ${params.requirements.contentLength || 2000} words\n- Tone: ${params.requirements.tone || 'professional'}\n- Audience: ${params.requirements.targetAudience || 'general'}\n- Custom Instructions: ${params.requirements.customInstructions || 'None'}\n\n**Article Outline:**\n${params.outline.map((section, index)=>`${index + 1}. ${section}`).join('\\n')}\n\n**Research Data:**\n${params.research.map((r)=>`- ${r.query}: ${r.results.length} sources`).join('\\n')}\n\n**Competitor Analysis:**\n- Top Competitors: ${params.competitorAnalysis.topCompetitors?.join(', ') || 'None'}\n- Content Gaps: ${params.competitorAnalysis.contentGaps?.join(', ') || 'None'}\n- Opportunities: ${params.competitorAnalysis.strengthsWeaknesses?.join(', ') || 'None'}\n\nCreate an article that:\n1. Covers all outlined sections comprehensively\n2. Incorporates unique insights not found in competitor content\n3. Uses natural, human-like language patterns\n4. Includes strategic internal linking opportunities\n5. Is optimized for AI search engines (GEO)\n6. Maintains perfect factual accuracy\n\nReturn the content in HTML format with proper semantic structure.`;\n        return await this.generateContent([\n            {\n                role: 'system',\n                content: systemPrompt\n            },\n            {\n                role: 'user',\n                content: userPrompt\n            }\n        ]);\n    }\n    /**\n   * Generate SEO-optimized meta content\n   */ async generateMetaContent(params) {\n        const prompt = `Based on the article content and title \"${params.title}\", create SEO-optimized meta content:\n\n**Keywords to include:** ${params.keywords.join(', ')}\n**Target meta description length:** ${params.targetLength || 160} characters\n\n**Article Content Preview:**\n${params.content.substring(0, 500)}...\n\nGenerate:\n1. **Title Tag** (50-60 characters, include primary keyword)\n2. **Meta Description** (${params.targetLength || 160} characters max, compelling and keyword-rich)\n3. **H1 Tag** (Different from title tag, keyword optimized)\n4. **Focus Keyword** (Primary keyword for this article)\n5. **Secondary Keywords** (3-5 related keywords)\n\nFormat as JSON with keys: titleTag, metaDescription, h1Tag, focusKeyword, secondaryKeywords`;\n        return await this.generateContent([\n            {\n                role: 'user',\n                content: prompt\n            }\n        ]);\n    }\n    /**\n   * Human-like content refinement and AI detection bypass\n   */ async humanizeContent(content) {\n        const prompt = `Refine this content to make it more human-like and bypass AI detection while maintaining quality and meaning:\n\n**Original Content:**\n${content}\n\nApply these humanization techniques:\n1. **Sentence Variation**: Mix short, medium, and long sentences\n2. **Natural Transitions**: Use conversational connectors\n3. **Personal Touch**: Add subtle personal observations where appropriate\n4. **Rhythm Variation**: Vary paragraph lengths and structure\n5. **Colloquial Elements**: Include natural speech patterns\n6. **Unique Insights**: Add distinctive perspectives or analogies\n7. **Date Variations**: Update any date references to be current and relevant\n\nMaintain:\n- All factual accuracy\n- SEO optimization\n- Professional tone\n- Comprehensive coverage\n- Logical flow\n\nReturn the humanized content in the same format as the original.`;\n        return await this.generateContent([\n            {\n                role: 'user',\n                content: prompt\n            }\n        ], undefined, {\n            temperature: 0.9\n        });\n    }\n    /**\n   * Get estimated cost for a request\n   */ estimateCost(inputTokens, outputTokens) {\n        return inputTokens * 0.15 / 1000000 + outputTokens * 2.50 / 1000000;\n    }\n    /**\n   * Validate API connection\n   */ async validateConnection() {\n        try {\n            const response = await this.generateContent([\n                {\n                    role: 'user',\n                    content: 'Hello, respond with \"OK\" if you are working correctly.'\n                }\n            ], undefined, {\n                maxTokens: 10\n            });\n            return response.content.toLowerCase().includes('ok');\n        } catch (error) {\n            console.error('Kimi K2 connection validation failed:', error);\n            return false;\n        }\n    }\n}\n/**\n * Factory function to create Kimi K2 client\n */ function createKimiK2Client(apiKey, useFreeModel = false) {\n    return new KimiK2Client({\n        apiKey,\n        model: useFreeModel ? 'moonshotai/kimi-k2:free' : 'moonshotai/kimi-k2'\n    });\n}\n/**\n * Utility functions for token estimation\n */ class KimiK2Utils {\n    /**\n   * Estimate token count (rough approximation: 1 token ≈ 4 characters)\n   */ static estimateTokens(text) {\n        return Math.ceil(text.length / 4);\n    }\n    /**\n   * Prepare messages for API call\n   */ static prepareMessages(systemPrompt, userPrompt, conversation = []) {\n        const messages = [\n            {\n                role: 'system',\n                content: systemPrompt\n            }\n        ];\n        // Add conversation history\n        conversation.forEach((msg)=>{\n            messages.push({\n                role: msg.role,\n                content: msg.content\n            });\n        });\n        // Add current user prompt\n        messages.push({\n            role: 'user',\n            content: userPrompt\n        });\n        return messages;\n    }\n    /**\n   * Create tools definition for function calling\n   */ static createTools(toolDefinitions) {\n        return toolDefinitions.map((tool)=>({\n                type: 'function',\n                function: {\n                    name: tool.name,\n                    description: tool.description,\n                    parameters: tool.parameters\n                }\n            }));\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/agents/v2/tools/kimi-k2-client.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/agents/v2/utils/json-parser.ts":
/*!************************************************!*\
  !*** ./src/lib/agents/v2/utils/json-parser.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseJsonWithFallback: () => (/* binding */ parseJsonWithFallback),\n/* harmony export */   safeJsonParse: () => (/* binding */ safeJsonParse)\n/* harmony export */ });\n/**\n * Utility to safely parse JSON responses from AI models\n * Handles markdown-wrapped JSON and other common response formats\n */ function safeJsonParse(content, fallback) {\n    if (!content || typeof content !== 'string') {\n        return fallback || null;\n    }\n    // Remove markdown code blocks if present\n    let cleanedContent = content.trim();\n    // Remove ```json and ``` wrappers\n    if (cleanedContent.startsWith('```json')) {\n        cleanedContent = cleanedContent.replace(/^```json\\s*/, '').replace(/\\s*```$/, '');\n    } else if (cleanedContent.startsWith('```')) {\n        cleanedContent = cleanedContent.replace(/^```\\s*/, '').replace(/\\s*```$/, '');\n    }\n    // Remove any leading/trailing whitespace\n    cleanedContent = cleanedContent.trim();\n    try {\n        return JSON.parse(cleanedContent);\n    } catch (error) {\n        console.warn('JSON parsing failed:', error);\n        console.warn('Original content:', content);\n        console.warn('Cleaned content:', cleanedContent);\n        // Try to extract JSON from the content using regex\n        const jsonMatch = cleanedContent.match(/\\{[\\s\\S]*\\}/);\n        if (jsonMatch) {\n            try {\n                return JSON.parse(jsonMatch[0]);\n            } catch (regexError) {\n                console.warn('Regex JSON extraction also failed:', regexError);\n            }\n        }\n        return fallback || null;\n    }\n}\n/**\n * Parse JSON response with specific fallback structure\n */ function parseJsonWithFallback(content, fallback) {\n    const result = safeJsonParse(content);\n    return result || fallback;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/agents/v2/utils/json-parser.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/agents/v2/utils/logger.ts":
/*!*******************************************!*\
  !*** ./src/lib/agents/v2/utils/logger.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LogLevel: () => (/* binding */ LogLevel),\n/* harmony export */   V2Logger: () => (/* binding */ V2Logger),\n/* harmony export */   createLogger: () => (/* binding */ createLogger),\n/* harmony export */   logPerformance: () => (/* binding */ logPerformance),\n/* harmony export */   logger: () => (/* binding */ logger)\n/* harmony export */ });\n/**\n * Invincible V.2 - Advanced Logging System\n * Structured logging with levels, context, and performance tracking\n */ var LogLevel = /*#__PURE__*/ function(LogLevel) {\n    LogLevel[LogLevel[\"DEBUG\"] = 0] = \"DEBUG\";\n    LogLevel[LogLevel[\"INFO\"] = 1] = \"INFO\";\n    LogLevel[LogLevel[\"WARN\"] = 2] = \"WARN\";\n    LogLevel[LogLevel[\"ERROR\"] = 3] = \"ERROR\";\n    LogLevel[LogLevel[\"CRITICAL\"] = 4] = \"CRITICAL\";\n    return LogLevel;\n}({});\nclass V2Logger {\n    constructor(){\n        this.logs = [];\n        this.maxLogs = 1000;\n        this.logLevel =  true ? 0 : 0;\n        this.sessionId = `v2_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n    }\n    static getInstance() {\n        if (!V2Logger.instance) {\n            V2Logger.instance = new V2Logger();\n        }\n        return V2Logger.instance;\n    }\n    /**\n   * Set the minimum log level\n   */ setLogLevel(level) {\n        this.logLevel = level;\n    }\n    /**\n   * Log debug information\n   */ debug(message, context = {}) {\n        this.log(0, message, context);\n    }\n    /**\n   * Log informational messages\n   */ info(message, context = {}) {\n        this.log(1, message, context);\n    }\n    /**\n   * Log warnings\n   */ warn(message, context = {}) {\n        this.log(2, message, context);\n    }\n    /**\n   * Log errors\n   */ error(message, context = {}, error) {\n        this.log(3, message, context, error);\n    }\n    /**\n   * Log critical errors\n   */ critical(message, context = {}, error) {\n        this.log(4, message, context, error);\n    }\n    /**\n   * Log agent actions with structured context\n   */ agentAction(agentName, action, status, context = {}) {\n        const level = status === 'failed' ? 3 : 1;\n        const message = `${agentName.toUpperCase()}: ${action} ${status}`;\n        this.log(level, message, {\n            ...context,\n            agentName,\n            action,\n            status,\n            sessionId: this.sessionId\n        });\n    }\n    /**\n   * Log workflow progress\n   */ workflowProgress(phase, progress, context = {}) {\n        this.log(1, `Workflow progress: ${phase} (${progress}%)`, {\n            ...context,\n            phase,\n            progress,\n            sessionId: this.sessionId\n        });\n    }\n    /**\n   * Log performance metrics\n   */ performance(operation, duration, context = {}) {\n        const level = duration > 5000 ? 2 : 1;\n        const message = `Performance: ${operation} completed in ${duration}ms`;\n        this.log(level, message, {\n            ...context,\n            operation,\n            duration,\n            sessionId: this.sessionId\n        });\n    }\n    /**\n   * Log API calls with details\n   */ apiCall(service, endpoint, method, status, duration, context = {}) {\n        const level = status >= 400 ? 3 : 1;\n        const message = `API Call: ${method} ${service}/${endpoint} - ${status} (${duration}ms)`;\n        this.log(level, message, {\n            ...context,\n            service,\n            endpoint,\n            method,\n            status,\n            duration,\n            sessionId: this.sessionId\n        });\n    }\n    /**\n   * Log search operations\n   */ searchOperation(query, resultsCount, duration, context = {}) {\n        this.log(1, `Search: \"${query}\" returned ${resultsCount} results in ${duration}ms`, {\n            ...context,\n            query,\n            resultsCount,\n            duration,\n            operation: 'search',\n            sessionId: this.sessionId\n        });\n    }\n    /**\n   * Log content generation\n   */ contentGeneration(type, wordCount, qualityScore, duration, context = {}) {\n        this.log(1, `Content Generated: ${type} (${wordCount} words, quality: ${qualityScore}) in ${duration}ms`, {\n            ...context,\n            contentType: type,\n            wordCount,\n            qualityScore,\n            duration,\n            operation: 'content_generation',\n            sessionId: this.sessionId\n        });\n    }\n    /**\n   * Core logging method\n   */ log(level, message, context = {}, error) {\n        if (level < this.logLevel) {\n            return;\n        }\n        const logEntry = {\n            level,\n            message,\n            context: {\n                ...context,\n                sessionId: context.sessionId || this.sessionId,\n                timestamp: Date.now()\n            },\n            timestamp: Date.now(),\n            error,\n            stack: error?.stack\n        };\n        // Add to internal log buffer\n        this.logs.push(logEntry);\n        if (this.logs.length > this.maxLogs) {\n            this.logs.shift();\n        }\n        // Console output with formatting\n        this.outputToConsole(logEntry);\n        // In production, you could also send to external logging service\n        if (false) {}\n    }\n    /**\n   * Format and output to console (minimalistic)\n   */ outputToConsole(entry) {\n        const time = new Date(entry.timestamp).toLocaleTimeString();\n        const level = [\n            '🔍',\n            '✅',\n            '⚠️',\n            '❌',\n            '🚨'\n        ][entry.level];\n        // Build minimal context\n        const parts = [];\n        if (entry.context.agentName) parts.push(entry.context.agentName);\n        if (entry.context.action) parts.push(entry.context.action);\n        if (entry.context.duration) parts.push(`${entry.context.duration}ms`);\n        const context = parts.length > 0 ? `[${parts.join('|')}]` : '';\n        // Single line output\n        console.log(`${level} ${time} ${context} ${entry.message}`);\n        // Only show metadata for errors or if it contains important info\n        if (entry.level >= 3 && entry.error) {\n            console.log(`   ↳ ${entry.error.message}`);\n        }\n    }\n    /**\n   * Send to external logging service (placeholder)\n   */ sendToExternalLogger(entry) {\n    // In production, implement sending to services like:\n    // - DataDog\n    // - New Relic\n    // - CloudWatch\n    // - Sentry\n    // - Custom logging endpoint\n    // Example structure for external logging:\n    // await fetch('/api/logs', {\n    //   method: 'POST',\n    //   headers: { 'Content-Type': 'application/json' },\n    //   body: JSON.stringify(entry)\n    // });\n    }\n    /**\n   * Get recent logs\n   */ getRecentLogs(count = 50) {\n        return this.logs.slice(-count);\n    }\n    /**\n   * Get logs by level\n   */ getLogsByLevel(level) {\n        return this.logs.filter((log)=>log.level === level);\n    }\n    /**\n   * Get logs by agent\n   */ getLogsByAgent(agentName) {\n        return this.logs.filter((log)=>log.context.agentName === agentName);\n    }\n    /**\n   * Clear logs\n   */ clearLogs() {\n        this.logs = [];\n    }\n    /**\n   * Get session statistics\n   */ getSessionStats() {\n        const logsByLevel = this.logs.reduce((acc, log)=>{\n            const levelName = LogLevel[log.level];\n            acc[levelName] = (acc[levelName] || 0) + 1;\n            return acc;\n        }, {});\n        const durationsMs = this.logs.filter((log)=>log.context.duration).map((log)=>log.context.duration);\n        const avgDuration = durationsMs.length > 0 ? durationsMs.reduce((sum, d)=>sum + d, 0) / durationsMs.length : 0;\n        return {\n            totalLogs: this.logs.length,\n            logsByLevel,\n            avgDuration: Math.round(avgDuration),\n            errors: this.logs.filter((log)=>log.level >= 3).length,\n            warnings: this.logs.filter((log)=>log.level === 2).length\n        };\n    }\n}\n/**\n * Singleton logger instance\n */ const logger = V2Logger.getInstance();\n/**\n * Performance tracking decorator\n */ function logPerformance(operation) {\n    return function(target, propertyName, descriptor) {\n        const method = descriptor.value;\n        descriptor.value = async function(...args) {\n            const startTime = Date.now();\n            const context = {\n                agentName: target.constructor.name,\n                operation,\n                method: propertyName\n            };\n            try {\n                logger.debug(`Starting ${operation}`, context);\n                const result = await method.apply(this, args);\n                const duration = Date.now() - startTime;\n                logger.performance(operation, duration, context);\n                return result;\n            } catch (error) {\n                const duration = Date.now() - startTime;\n                logger.error(`${operation} failed after ${duration}ms`, context, error);\n                throw error;\n            }\n        };\n        return descriptor;\n    };\n}\n/**\n * Helper function to create a child logger with context\n */ function createLogger(context) {\n    return {\n        debug: (message, additionalContext = {})=>logger.debug(message, {\n                ...context,\n                ...additionalContext\n            }),\n        info: (message, additionalContext = {})=>logger.info(message, {\n                ...context,\n                ...additionalContext\n            }),\n        warn: (message, additionalContext = {})=>logger.warn(message, {\n                ...context,\n                ...additionalContext\n            }),\n        error: (message, additionalContext = {}, error)=>logger.error(message, {\n                ...context,\n                ...additionalContext\n            }, error)\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/agents/v2/utils/logger.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:fs":
/*!**************************!*\
  !*** external "node:fs" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:fs");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "node:stream/web":
/*!**********************************!*\
  !*** external "node:stream/web" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream/web");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "worker_threads":
/*!*********************************!*\
  !*** external "worker_threads" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("worker_threads");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/formdata-node","vendor-chunks/openai","vendor-chunks/tr46","vendor-chunks/web-streams-polyfill","vendor-chunks/node-fetch","vendor-chunks/whatwg-url","vendor-chunks/event-target-shim","vendor-chunks/agentkeepalive","vendor-chunks/form-data-encoder","vendor-chunks/webidl-conversions","vendor-chunks/abort-controller","vendor-chunks/ms","vendor-chunks/humanize-ms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Finvincible-v2%2Froute&page=%2Fapi%2Finvincible-v2%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finvincible-v2%2Froute.ts&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();