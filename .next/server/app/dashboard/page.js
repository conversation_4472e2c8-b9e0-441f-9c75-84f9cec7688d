/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/page";
exports.ids = ["app/dashboard/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(rsc)/./src/app/dashboard/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcomponents%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcomponents%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/SessionProvider.tsx */ \"(rsc)/./src/components/SessionProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWF5dXNobWlzaHJhJTJGRGVza3RvcCUyRm9sZCUyMGludmluY2libGUlMjB3aXRoJTIwZGVlcHJlc2VhcmNoJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmZvbnQlMkZnb29nbGUlMkZ0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMnNyYyUyRmFwcCUyRmxheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWF5dXNobWlzaHJhJTJGRGVza3RvcCUyRm9sZCUyMGludmluY2libGUlMjB3aXRoJTIwZGVlcHJlc2VhcmNoJTJGc3JjJTJGYXBwJTJGZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZhYXl1c2htaXNocmElMkZEZXNrdG9wJTJGb2xkJTIwaW52aW5jaWJsZSUyMHdpdGglMjBkZWVwcmVzZWFyY2glMkZzcmMlMkZjb21wb25lbnRzJTJGU2Vzc2lvblByb3ZpZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9MQUFpSyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIi9Vc2Vycy9hYXl1c2htaXNocmEvRGVza3RvcC9vbGQgaW52aW5jaWJsZSB3aXRoIGRlZXByZXNlYXJjaC9zcmMvY29tcG9uZW50cy9TZXNzaW9uUHJvdmlkZXIudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcomponents%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fdashboard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fdashboard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(rsc)/./src/app/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWF5dXNobWlzaHJhJTJGRGVza3RvcCUyRm9sZCUyMGludmluY2libGUlMjB3aXRoJTIwZGVlcHJlc2VhcmNoJTJGc3JjJTJGYXBwJTJGZGFzaGJvYXJkJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9LQUE0SCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2FheXVzaG1pc2hyYS9EZXNrdG9wL29sZCBpbnZpbmNpYmxlIHdpdGggZGVlcHJlc2VhcmNoL3NyYy9hcHAvZGFzaGJvYXJkL3BhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fdashboard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"333781e81268\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvVXNlcnMvYWF5dXNobWlzaHJhL0Rlc2t0b3Avb2xkIGludmluY2libGUgd2l0aCBkZWVwcmVzZWFyY2gvc3JjL2FwcC9nbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjMzMzc4MWU4MTI2OFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_SessionProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/SessionProvider */ \"(rsc)/./src/components/SessionProvider.tsx\");\n\n\n\n\nconst metadata = {\n    title: 'Invincible - AI Content Generation Platform',\n    description: 'The ultimate content writing SaaS platform powered by advanced AI technology'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"dark\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className)} bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 min-h-screen`,\n            suppressHydrationWarning: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SessionProvider__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/SessionProvider.tsx":
/*!********************************************!*\
  !*** ./src/components/SessionProvider.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SessionProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SessionProvider.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcomponents%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcomponents%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/SessionProvider.tsx */ \"(ssr)/./src/components/SessionProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWF5dXNobWlzaHJhJTJGRGVza3RvcCUyRm9sZCUyMGludmluY2libGUlMjB3aXRoJTIwZGVlcHJlc2VhcmNoJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmZvbnQlMkZnb29nbGUlMkZ0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMnNyYyUyRmFwcCUyRmxheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWF5dXNobWlzaHJhJTJGRGVza3RvcCUyRm9sZCUyMGludmluY2libGUlMjB3aXRoJTIwZGVlcHJlc2VhcmNoJTJGc3JjJTJGYXBwJTJGZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZhYXl1c2htaXNocmElMkZEZXNrdG9wJTJGb2xkJTIwaW52aW5jaWJsZSUyMHdpdGglMjBkZWVwcmVzZWFyY2glMkZzcmMlMkZjb21wb25lbnRzJTJGU2Vzc2lvblByb3ZpZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9MQUFpSyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIi9Vc2Vycy9hYXl1c2htaXNocmEvRGVza3RvcC9vbGQgaW52aW5jaWJsZSB3aXRoIGRlZXByZXNlYXJjaC9zcmMvY29tcG9uZW50cy9TZXNzaW9uUHJvdmlkZXIudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcomponents%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fdashboard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fdashboard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(ssr)/./src/app/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWF5dXNobWlzaHJhJTJGRGVza3RvcCUyRm9sZCUyMGludmluY2libGUlMjB3aXRoJTIwZGVlcHJlc2VhcmNoJTJGc3JjJTJGYXBwJTJGZGFzaGJvYXJkJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9LQUE0SCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2FheXVzaG1pc2hyYS9EZXNrdG9wL29sZCBpbnZpbmNpYmxlIHdpdGggZGVlcHJlc2VhcmNoL3NyYy9hcHAvZGFzaGJvYXJkL3BhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fdashboard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dynamic */ \"(ssr)/./node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Cpu,Crown,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Cpu,Crown,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Cpu,Crown,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/layers.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Cpu,Crown,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Cpu,Crown,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-help.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Cpu,Crown,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Cpu,Crown,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Cpu,Crown,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Cpu,Crown,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Cpu,Crown,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/video.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Cpu,Crown,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Cpu,Crown,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/cpu.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Cpu,Crown,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Cpu,Crown,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Cpu,Crown,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Cpu,Crown,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Cpu,Crown,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Cpu,Crown,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Cpu,Crown,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Cpu,Crown,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Cpu,Crown,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Cpu,Crown,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Cpu,Crown,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ProfileButton__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ProfileButton */ \"(ssr)/./src/components/ProfileButton.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n// Lazy load RecentContent to reduce initial bundle size\nconst OptimizedRecentContent = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(async ()=>{\n     true && /*require.resolve*/(null /* weak dependency, without id */);\n}, {\n    loadableGenerated: {\n        modules: [\n            \"app/dashboard/page.tsx -> \" + \"@/components/dashboard/RecentContent\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: Array.from({\n                length: 3\n            }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white/10 rounded-lg p-4 animate-pulse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 bg-white/10 rounded mb-2 w-3/4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-3 bg-white/10 rounded w-1/2\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, i, true, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n            lineNumber: 63,\n            columnNumber: 5\n        }, undefined)\n});\n// Optimized lazy loading with React.memo and reduced bundle size\nconst OptimizedInvincibleOrb = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(async ()=>{\n     true && /*require.resolve*/(null /* weak dependency, without id */);\n}, {\n    loadableGenerated: {\n        modules: [\n            \"app/dashboard/page.tsx -> \" + \"@/components/InvincibleOrb\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-16 h-16 border-2 border-violet-500/30 border-t-violet-500 rounded-full animate-spin mx-auto\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n            lineNumber: 77,\n            columnNumber: 18\n        }, undefined)\n});\nconst OptimizedBlogPreview = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(async ()=>{\n     true && /*require.resolve*/(null /* weak dependency, without id */);\n}, {\n    loadableGenerated: {\n        modules: [\n            \"app/dashboard/page.tsx -> \" + \"@/components/BlogPreview\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-16 h-16 border-2 border-pink-500/30 border-t-pink-500 rounded-full animate-spin mx-auto\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n            lineNumber: 82,\n            columnNumber: 18\n        }, undefined)\n});\nconst OptimizedEmailPreview = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(async ()=>{\n     true && /*require.resolve*/(null /* weak dependency, without id */);\n}, {\n    loadableGenerated: {\n        modules: [\n            \"app/dashboard/page.tsx -> \" + \"@/components/EmailPreview\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-16 h-16 border-2 border-emerald-500/30 border-t-emerald-500 rounded-full animate-spin mx-auto\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n            lineNumber: 87,\n            columnNumber: 18\n        }, undefined)\n});\nconst OptimizedSocialMediaPreview = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(async ()=>{\n     true && /*require.resolve*/(null /* weak dependency, without id */);\n}, {\n    loadableGenerated: {\n        modules: [\n            \"app/dashboard/page.tsx -> \" + \"@/components/SocialMediaPreview\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-16 h-16 border-2 border-blue-500/30 border-t-blue-500 rounded-full animate-spin mx-auto\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n            lineNumber: 92,\n            columnNumber: 18\n        }, undefined)\n});\nconst OptimizedVideoScriptPreview = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(async ()=>{\n     true && /*require.resolve*/(null /* weak dependency, without id */);\n}, {\n    loadableGenerated: {\n        modules: [\n            \"app/dashboard/page.tsx -> \" + \"@/components/VideoScriptPreview\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-16 h-16 border-2 border-red-500/30 border-t-red-500 rounded-full animate-spin mx-auto\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n            lineNumber: 97,\n            columnNumber: 18\n        }, undefined)\n});\nconst OptimizedVideoAlchemyPreview = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(async ()=>{\n     true && /*require.resolve*/(null /* weak dependency, without id */);\n}, {\n    loadableGenerated: {\n        modules: [\n            \"app/dashboard/page.tsx -> \" + \"@/components/VideoAlchemyPreview\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-16 h-16 border-2 border-purple-500/30 border-t-purple-500 rounded-full animate-spin mx-auto\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n            lineNumber: 102,\n            columnNumber: 18\n        }, undefined)\n});\nconst OptimizedMegatronPreview = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(async ()=>{\n     true && /*require.resolve*/(null /* weak dependency, without id */);\n}, {\n    loadableGenerated: {\n        modules: [\n            \"app/dashboard/page.tsx -> \" + \"@/components/MegatronPreview\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-16 h-16 border-2 border-red-500/30 border-t-red-500 rounded-full animate-spin mx-auto\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n            lineNumber: 107,\n            columnNumber: 18\n        }, undefined)\n});\n// Optimized Sidebar Component with Memoization\nconst EnhancedSidebar = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(({ isOpen, onClose, tools, selectedTool, setSelectedTool, hoveredTool, setHoveredTool })=>{\n    // Memoized animation variants for better performance\n    const sidebarVariants = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"EnhancedSidebar.useMemo[sidebarVariants]\": ()=>({\n                open: {\n                    x: 0,\n                    transition: {\n                        type: \"spring\",\n                        stiffness: 300,\n                        damping: 30\n                    }\n                },\n                closed: {\n                    x: -320,\n                    transition: {\n                        type: \"spring\",\n                        stiffness: 300,\n                        damping: 30\n                    }\n                }\n            })\n    }[\"EnhancedSidebar.useMemo[sidebarVariants]\"], []);\n    // Memoized handlers to prevent re-renders\n    const handleToolSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedSidebar.useCallback[handleToolSelect]\": (toolId)=>{\n            setSelectedTool(toolId);\n        }\n    }[\"EnhancedSidebar.useCallback[handleToolSelect]\"], [\n        setSelectedTool\n    ]);\n    const handleToolHover = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedSidebar.useCallback[handleToolHover]\": (toolId)=>{\n            setHoveredTool(toolId);\n        }\n    }[\"EnhancedSidebar.useCallback[handleToolHover]\"], [\n        setHoveredTool\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    transition: {\n                        duration: 0.2\n                    },\n                    onClick: onClose,\n                    className: \"fixed inset-0 bg-black/60 backdrop-blur-sm z-40 lg:hidden\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                lineNumber: 154,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.aside, {\n                initial: \"closed\",\n                animate: isOpen ? \"open\" : \"closed\",\n                variants: sidebarVariants,\n                className: \"fixed left-0 top-0 h-screen w-[320px] z-50 lg:z-10\",\n                style: {\n                    willChange: 'transform'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black/85 backdrop-blur-xl border-r border-white/10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-br from-violet-950/15 via-black/60 to-indigo-950/15\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10 h-full flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 border-b border-white/10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"flex items-center space-x-4 group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-r from-violet-500 to-indigo-500 rounded-xl blur-lg opacity-50\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                                    whileHover: {\n                                                        scale: 1.05\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.95\n                                                    },\n                                                    transition: {\n                                                        duration: 0.2\n                                                    },\n                                                    className: \"relative bg-gradient-to-r from-violet-800 to-indigo-800 rounded-xl p-3 border border-white/20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-7 h-7 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-2xl font-bold text-white\",\n                                                    children: \"Invincible\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: \"Creative AI Suite\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"flex-1 p-4 space-y-2 overflow-y-auto custom-scrollbar\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                        onClick: ()=>handleToolSelect(''),\n                                        whileHover: {\n                                            x: 2\n                                        },\n                                        whileTap: {\n                                            scale: 0.98\n                                        },\n                                        transition: {\n                                            duration: 0.1\n                                        },\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"w-full flex items-center space-x-4 px-4 py-4 rounded-xl transition-all duration-200 relative\", !selectedTool ? \"bg-white/15 text-white shadow-lg border border-white/20\" : \"text-gray-400 hover:text-white hover:bg-white/8\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-2 rounded-lg bg-white/10\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 221,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-left\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-semibold\",\n                                                                children: \"Dashboard\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 224,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs opacity-70\",\n                                                                children: \"Overview & Stats\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 225,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            !selectedTool && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute right-3 w-1 h-8 bg-white rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"pt-6 pb-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 px-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-px flex-1 bg-white/20\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs font-semibold text-gray-400 uppercase tracking-wider\",\n                                                    children: \"AI Tools\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-px flex-1 bg-white/20\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/content\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                            whileHover: {\n                                                x: 2\n                                            },\n                                            whileTap: {\n                                                scale: 0.98\n                                            },\n                                            transition: {\n                                                duration: 0.2\n                                            },\n                                            className: \"w-full flex items-center space-x-4 px-4 py-4 text-gray-400 hover:text-white hover:bg-white/8 rounded-xl transition-all duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 rounded-lg bg-white/5\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 254,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-left\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-semibold\",\n                                                            children: \"Content Library\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 257,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs opacity-70\",\n                                                            children: \"View Past Content\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 258,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    tools.map((tool)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                            onClick: ()=>handleToolSelect(tool.id),\n                                            onMouseEnter: ()=>handleToolHover(tool.id),\n                                            onMouseLeave: ()=>handleToolHover(null),\n                                            whileHover: {\n                                                x: 2\n                                            },\n                                            whileTap: {\n                                                scale: 0.98\n                                            },\n                                            transition: {\n                                                duration: 0.1\n                                            },\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"w-full flex items-center space-x-4 px-4 py-4 rounded-xl transition-all duration-200 relative\", selectedTool === tool.id ? \"bg-gradient-to-r text-white shadow-lg border border-white/20\" : \"text-gray-400 hover:text-white hover:bg-white/8\", selectedTool === tool.id && tool.color),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"p-2 rounded-lg transition-colors duration-200\", selectedTool === tool.id ? \"bg-white/20\" : \"bg-white/10\"),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tool.icon, {\n                                                                className: \"w-5 h-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 288,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-left\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-semibold\",\n                                                                            children: tool.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                            lineNumber: 292,\n                                                                            columnNumber: 23\n                                                                        }, undefined),\n                                                                        tool.beta && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"px-1.5 py-0.5 text-xs bg-blue-500/20 text-blue-400 rounded border border-blue-500/30\",\n                                                                            children: \"BETA\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                            lineNumber: 294,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        tool.comingSoon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"px-1.5 py-0.5 text-xs bg-gray-500/20 text-gray-400 rounded border border-gray-500/30\",\n                                                                            children: \"SOON\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                            lineNumber: 299,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 291,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs opacity-70\",\n                                                                    children: tool.subtitle\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 304,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 290,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                selectedTool === tool.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute right-3 w-1 h-8 bg-white rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                hoveredTool === tool.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute right-3 top-3 bg-black/60 rounded-full px-2 py-1 text-xs text-white\",\n                                                    children: tool.stats.generated\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, tool.id, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 15\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border-t border-white/10 space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/settings\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                            whileHover: {\n                                                x: 2\n                                            },\n                                            whileTap: {\n                                                scale: 0.98\n                                            },\n                                            transition: {\n                                                duration: 0.2\n                                            },\n                                            className: \"w-full flex items-center space-x-4 px-4 py-3 text-gray-400 hover:text-white hover:bg-white/8 rounded-xl transition-all duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 rounded-lg bg-white/5\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: \"Settings\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 334,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                        whileHover: {\n                                            x: 2\n                                        },\n                                        whileTap: {\n                                            scale: 0.98\n                                        },\n                                        transition: {\n                                            duration: 0.2\n                                        },\n                                        className: \"w-full flex items-center space-x-4 px-4 py-3 text-gray-400 hover:text-white hover:bg-white/8 rounded-xl transition-all duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 rounded-lg bg-white/5\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 345,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"Help & Support\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n});\n// Optimized Dashboard with Performance Enhancements\nfunction DashboardPage() {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_4__.useSession)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    const [selectedTool, setSelectedTool] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('invincible-agent');\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showNotifications, setShowNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hoveredTool, setHoveredTool] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userProfile, setUserProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoadingProfile, setIsLoadingProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [userStats, setUserStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoadingStats, setIsLoadingStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // ALL useCallback hooks must be at the top level and in consistent order\n    const toggleSidebar = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DashboardPage.useCallback[toggleSidebar]\": ()=>setSidebarOpen({\n                \"DashboardPage.useCallback[toggleSidebar]\": (prev)=>!prev\n            }[\"DashboardPage.useCallback[toggleSidebar]\"])\n    }[\"DashboardPage.useCallback[toggleSidebar]\"], []);\n    const toggleNotifications = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DashboardPage.useCallback[toggleNotifications]\": ()=>setShowNotifications({\n                \"DashboardPage.useCallback[toggleNotifications]\": (prev)=>!prev\n            }[\"DashboardPage.useCallback[toggleNotifications]\"])\n    }[\"DashboardPage.useCallback[toggleNotifications]\"], []);\n    const handleSearchChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DashboardPage.useCallback[handleSearchChange]\": (e)=>{\n            setSearchQuery(e.target.value);\n        }\n    }[\"DashboardPage.useCallback[handleSearchChange]\"], []);\n    // Memoized stats calculation for tools to prevent recalculation on every render\n    const getToolStats = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DashboardPage.useCallback[getToolStats]\": (toolType)=>{\n            if (!userStats?.contentBreakdown) {\n                return {\n                    generated: 0,\n                    quality: 9.0,\n                    avgTime: '0 min'\n                };\n            }\n            const generated = userStats.contentBreakdown[toolType] || 0;\n            const quality = Math.min(9.8, 8.5 + generated * 0.05);\n            const avgTimes = {\n                'invincible_research': '4 min',\n                'blog': '3 min',\n                'email': '1 min',\n                'social_media': '30 sec',\n                'youtube_script': '4 min',\n                'video_alchemy': '3 min'\n            };\n            return {\n                generated,\n                quality: Math.round(quality * 10) / 10,\n                avgTime: avgTimes[toolType] || '2 min'\n            };\n        }\n    }[\"DashboardPage.useCallback[getToolStats]\"], [\n        userStats?.contentBreakdown\n    ]);\n    // Memoized tools configuration to prevent recreation - MUST be before any conditional returns\n    const tools = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"DashboardPage.useMemo[tools]\": ()=>[\n                {\n                    id: 'invincible-agent',\n                    title: 'Invincible V.1',\n                    subtitle: 'Superior Content',\n                    description: 'RAG-based content generation that analyzes competition, understands human writing patterns, and creates superior articles that dominate search results.',\n                    icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                    color: 'from-violet-800/80 to-indigo-800/80',\n                    bgGradient: 'from-violet-950/30 to-indigo-950/30',\n                    accentColor: 'violet',\n                    stats: getToolStats('invincible_research'),\n                    features: [\n                        'RAG Research',\n                        'Competitive Analysis',\n                        'Human Writing Style',\n                        'SEO Optimization',\n                        'Superior Quality',\n                        'Knowledge Base'\n                    ],\n                    href: '/invincible',\n                    preview: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(OptimizedInvincibleOrb, {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 457,\n                        columnNumber: 16\n                    }, this)\n                },\n                {\n                    id: 'email-generator',\n                    title: 'Email Generator',\n                    subtitle: 'Professional Emails',\n                    description: 'Generate compelling email campaigns, newsletters, and professional communications with AI-powered personalization.',\n                    icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                    color: 'from-emerald-500 to-teal-600',\n                    bgGradient: 'from-emerald-950/30 to-teal-950/30',\n                    accentColor: 'emerald',\n                    stats: getToolStats('email'),\n                    features: [\n                        'Personalization',\n                        'A/B Testing',\n                        'Professional Tone',\n                        'Quick Generation'\n                    ],\n                    href: '/email-generator'\n                },\n                {\n                    id: 'social-media-generator',\n                    title: 'Social Media',\n                    subtitle: 'Viral Content',\n                    description: 'Create engaging social media posts, captions, and content strategies that resonate with your audience across all platforms.',\n                    icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                    color: 'from-pink-500 to-rose-600',\n                    bgGradient: 'from-pink-950/30 to-rose-950/30',\n                    accentColor: 'pink',\n                    stats: getToolStats('social_media'),\n                    features: [\n                        'Multi-Platform',\n                        'Trending Hashtags',\n                        'Engagement Optimization',\n                        'Quick Generation'\n                    ],\n                    href: '/social-media-generator'\n                },\n                {\n                    id: 'blog-generator',\n                    title: 'Blog Generator',\n                    subtitle: 'SEO Optimized',\n                    description: 'Generate comprehensive, SEO-optimized blog posts with research, proper structure, and engaging content that ranks well.',\n                    icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                    color: 'from-blue-500 to-indigo-600',\n                    bgGradient: 'from-blue-950/30 to-indigo-950/30',\n                    accentColor: 'blue',\n                    stats: getToolStats('blog'),\n                    features: [\n                        'SEO Optimization',\n                        'Research Integration',\n                        'Long-form Content',\n                        'Professional Structure'\n                    ],\n                    href: '/blog-generator'\n                },\n                {\n                    id: 'youtube-script',\n                    title: 'YouTube Scripts',\n                    subtitle: 'Video Content',\n                    description: 'Create compelling YouTube video scripts with hooks, engagement techniques, and structured content for maximum viewer retention.',\n                    icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n                    color: 'from-red-500 to-orange-600',\n                    bgGradient: 'from-red-950/30 to-orange-950/30',\n                    accentColor: 'red',\n                    stats: getToolStats('youtube_script'),\n                    features: [\n                        'Hook Generation',\n                        'Retention Optimization',\n                        'CTA Integration',\n                        'Script Structure'\n                    ],\n                    href: '/youtube-script'\n                },\n                {\n                    id: 'video-alchemy',\n                    title: 'Video Alchemy',\n                    subtitle: 'Coming Soon',\n                    description: 'Transform your ideas into stunning video content with AI-powered video generation and editing capabilities.',\n                    icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                    color: 'from-purple-500 to-violet-600',\n                    bgGradient: 'from-purple-950/30 to-violet-950/30',\n                    accentColor: 'purple',\n                    stats: getToolStats('video_alchemy'),\n                    features: [\n                        'AI Video Generation',\n                        'Auto Editing',\n                        'Style Transfer',\n                        'Quick Export'\n                    ],\n                    href: '#',\n                    comingSoon: true\n                },\n                {\n                    id: 'megatron',\n                    title: 'Megatron',\n                    subtitle: 'Ultimate AI',\n                    description: 'The most powerful AI assistant for complex tasks, research, analysis, and creative projects with unlimited potential.',\n                    icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                    color: 'from-gray-600 to-slate-700',\n                    bgGradient: 'from-gray-950/30 to-slate-950/30',\n                    accentColor: 'gray',\n                    stats: getToolStats('megatron'),\n                    features: [\n                        'Advanced Reasoning',\n                        'Multi-task Handling',\n                        'Research Capabilities',\n                        'Creative Solutions'\n                    ],\n                    href: '#',\n                    comingSoon: true\n                },\n                {\n                    id: 'invincible-v3',\n                    title: 'Invincible V3',\n                    subtitle: 'Next-Gen System',\n                    description: 'Revolutionary LangGraph-powered workflow with enhanced research, SEO/GEO optimization, and AI detection bypass.',\n                    icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n                    color: 'from-blue-600 to-purple-700',\n                    bgGradient: 'from-blue-950/30 to-purple-950/30',\n                    accentColor: 'blue',\n                    stats: {\n                        generated: 0,\n                        quality: 9.5,\n                        avgTime: '3 min'\n                    },\n                    features: [\n                        'LangGraph Workflow',\n                        'Real-time Progress',\n                        'Enhanced Research',\n                        'GEO Optimization',\n                        'AI Bypass',\n                        'Quality Assurance'\n                    ],\n                    href: '/invincible-v3',\n                    beta: true\n                }\n            ]\n    }[\"DashboardPage.useMemo[tools]\"], [\n        getToolStats\n    ]);\n    // Redirect to login if not authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            if (status === 'unauthenticated') {\n                router.push('/login');\n            }\n        }\n    }[\"DashboardPage.useEffect\"], [\n        status,\n        router\n    ]);\n    // Fetch user profile data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            if (session?.user) {\n                fetchUserProfile();\n                fetchUserStats();\n            }\n        }\n    }[\"DashboardPage.useEffect\"], [\n        session\n    ]);\n    const fetchUserProfile = async ()=>{\n        try {\n            const response = await fetch('/api/user/profile');\n            if (response.ok) {\n                const data = await response.json();\n                setUserProfile(data);\n            }\n        } catch (error) {\n            console.error('Error fetching user profile:', error);\n        } finally{\n            setIsLoadingProfile(false);\n        }\n    };\n    const fetchUserStats = async ()=>{\n        try {\n            const response = await fetch('/api/stats');\n            if (response.ok) {\n                const data = await response.json();\n                setUserStats(data.stats);\n            }\n        } catch (error) {\n            console.error('Error fetching user stats:', error);\n        } finally{\n            setIsLoadingStats(false);\n        }\n    };\n    // Generate user initials for avatar\n    const getUserInitials = ()=>{\n        if (userProfile?.firstName && userProfile?.lastName) {\n            return `${userProfile.firstName[0]}${userProfile.lastName[0]}`;\n        } else if (userProfile?.name) {\n            const names = userProfile.name.split(' ');\n            return names.length > 1 ? `${names[0][0]}${names[names.length - 1][0]}` : names[0][0];\n        } else if (userProfile?.email) {\n            return userProfile.email[0].toUpperCase();\n        }\n        return 'U';\n    };\n    // Get display name\n    const getDisplayName = ()=>{\n        if (userProfile?.firstName && userProfile?.lastName) {\n            return `${userProfile.firstName} ${userProfile.lastName}`;\n        } else if (userProfile?.name) {\n            return userProfile.name;\n        } else if (userProfile?.email) {\n            return userProfile.email.split('@')[0];\n        }\n        return 'User';\n    };\n    // Loading state\n    if (status === 'loading' || isLoadingProfile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin w-12 h-12 border-2 border-violet-400 border-t-transparent rounded-full mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 628,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: \"Loading your dashboard...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 629,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                lineNumber: 627,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n            lineNumber: 626,\n            columnNumber: 7\n        }, this);\n    }\n    // Don't render if not authenticated\n    if (status === 'unauthenticated') {\n        return null;\n    }\n    const activeTool = tools.find((t)=>t.id === selectedTool) || tools[0];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-black\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-violet-950/10 via-black to-indigo-950/10\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 646,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        transition: {\n                            duration: 0.5,\n                            ease: [\n                                0.4,\n                                0,\n                                0.2,\n                                1\n                            ]\n                        },\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"absolute inset-0 bg-gradient-to-br opacity-20\", activeTool.bgGradient)\n                    }, activeTool.id, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 649,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-1/4 left-1/4 w-[500px] h-[500px] bg-violet-700/10 rounded-full blur-[100px] animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 661,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-1/4 right-1/4 w-[600px] h-[600px] bg-indigo-700/10 rounded-full blur-[120px] animate-pulse\",\n                        style: {\n                            animationDelay: '1s'\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 662,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                lineNumber: 645,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 flex min-h-screen\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EnhancedSidebar, {\n                        isOpen: sidebarOpen,\n                        onClose: ()=>setSidebarOpen(false),\n                        tools: tools,\n                        selectedTool: selectedTool,\n                        setSelectedTool: setSelectedTool,\n                        hoveredTool: hoveredTool,\n                        setHoveredTool: setHoveredTool\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 668,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                        onClick: toggleSidebar,\n                        animate: {\n                            left: sidebarOpen ? 308 : 20\n                        },\n                        transition: {\n                            type: \"spring\",\n                            stiffness: 300,\n                            damping: 30\n                        },\n                        whileHover: {\n                            scale: 1.02\n                        },\n                        whileTap: {\n                            scale: 0.98\n                        },\n                        className: \"fixed top-6 z-50 p-3 bg-black/80 backdrop-blur-sm border border-white/20 rounded-xl hover:bg-black/90 transition-colors duration-200\",\n                        style: {\n                            willChange: 'transform'\n                        },\n                        children: sidebarOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                            className: \"w-5 h-5 text-white\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                            lineNumber: 694,\n                            columnNumber: 26\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                            className: \"w-5 h-5 text-white\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                            lineNumber: 694,\n                            columnNumber: 75\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 679,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.main, {\n                        animate: {\n                            marginLeft: sidebarOpen ? 320 : 0\n                        },\n                        transition: {\n                            type: \"tween\",\n                            duration: 0.3,\n                            ease: [\n                                0.4,\n                                0,\n                                0.2,\n                                1\n                            ]\n                        },\n                        className: \"flex-1 min-h-screen\",\n                        style: {\n                            willChange: 'margin-left'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                                className: \"sticky top-0 z-40 backdrop-blur-xl bg-black/60 border-b border-white/10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                    animate: {\n                                        paddingLeft: sidebarOpen ? 32 : 80\n                                    },\n                                    transition: {\n                                        type: \"tween\",\n                                        duration: 0.3,\n                                        ease: [\n                                            0.4,\n                                            0,\n                                            0.2,\n                                            1\n                                        ]\n                                    },\n                                    className: \"px-8 py-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-white\",\n                                                        children: selectedTool ? activeTool.title : 'Dashboard Overview'\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 723,\n                                                        columnNumber: 39\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                className: \"absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 729,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                placeholder: \"Search tools, features...\",\n                                                                value: searchQuery,\n                                                                onChange: handleSearchChange,\n                                                                className: \"w-96 pl-12 pr-4 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/15 focus:border-violet-500/50 focus:outline-none transition-colors duration-200\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 730,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 728,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 722,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"relative p-3 text-gray-400 hover:text-white transition-colors duration-200 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl hover:bg-white/15\",\n                                                        onClick: toggleNotifications,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                className: \"w-5 h-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 746,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"absolute top-2 right-2 w-2 h-2 bg-violet-500 rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 747,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 742,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-8 w-px bg-white/20\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 750,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProfileButton__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        userProfile: userProfile,\n                                                        className: \"pl-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 752,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 741,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 721,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                    lineNumber: 712,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 711,\n                                columnNumber: 23\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                animate: {\n                                    paddingLeft: sidebarOpen ? 32 : 80\n                                },\n                                transition: {\n                                    type: \"tween\",\n                                    duration: 0.3,\n                                    ease: [\n                                        0.4,\n                                        0,\n                                        0.2,\n                                        1\n                                    ]\n                                },\n                                className: \"p-8 pb-16 pr-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                                    mode: \"wait\",\n                                    children: selectedTool ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToolDetails, {\n                                        tool: activeTool\n                                    }, selectedTool, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 775,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DashboardOverview, {\n                                        tools: tools,\n                                        userProfile: userProfile,\n                                        getDisplayName: getDisplayName,\n                                        userStats: userStats,\n                                        isLoadingStats: isLoadingStats\n                                    }, \"overview\", false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 777,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                    lineNumber: 773,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 762,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 698,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                lineNumber: 666,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n        lineNumber: 643,\n        columnNumber: 5\n    }, this);\n}\n// Optimized Tool Details Component with Memoization\nconst ToolDetails = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(({ tool })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n        initial: {\n            opacity: 0,\n            scale: 0.95\n        },\n        animate: {\n            opacity: 1,\n            scale: 1\n        },\n        exit: {\n            opacity: 0,\n            scale: 0.95\n        },\n        transition: {\n            duration: 0.2,\n            ease: \"easeOut\"\n        },\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.3,\n                    delay: 0.1\n                },\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"relative overflow-hidden rounded-3xl backdrop-blur-xl border p-8\", tool.id === 'invincible-agent' ? \"bg-gradient-to-br from-white/10 to-white/5 border-white/20 shadow-2xl\" : \"bg-gradient-to-br from-white/5 to-white/0 border-white/10\"),\n                children: [\n                    tool.id === 'invincible-agent' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-white/20 via-transparent to-transparent rounded-3xl\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 812,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"absolute inset-0 bg-gradient-to-br opacity-20\", tool.color)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 815,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10 grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"p-4 rounded-2xl text-white shadow-2xl\", tool.id === 'invincible-agent' ? \"bg-gradient-to-br from-violet-800/70 to-indigo-800/70 backdrop-blur-sm border border-white/30\" : (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"bg-gradient-to-br\", tool.color)),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tool.icon, {\n                                                    className: \"w-8 h-8\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 829,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 823,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-3xl font-bold text-white\",\n                                                        children: tool.title\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 832,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg text-gray-300\",\n                                                        children: tool.subtitle\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 833,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 831,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 822,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300 leading-relaxed\",\n                                        children: tool.description\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 837,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-3\",\n                                        children: tool.features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"px-4 py-2 rounded-full text-sm text-white border\", tool.id === 'invincible-agent' ? \"bg-white/15 backdrop-blur-sm border-white/30\" : \"bg-white/10 backdrop-blur-sm border-white/20\"),\n                                                children: feature\n                                            }, index, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 843,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 841,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: tool.href,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                                whileHover: {\n                                                    scale: 1.05\n                                                },\n                                                whileTap: {\n                                                    scale: 0.95\n                                                },\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"px-12 py-6 rounded-2xl font-bold text-lg text-white shadow-2xl transition-all flex items-center space-x-3\", tool.id === 'invincible-agent' ? \"bg-gradient-to-r from-violet-800/80 to-indigo-800/80 backdrop-blur-sm border border-white/30 hover:from-violet-700/90 hover:to-indigo-700/90\" : (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"bg-gradient-to-r\", tool.color)),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                        className: \"w-6 h-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 869,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Launch \",\n                                                            tool.title\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 870,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 859,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 858,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 857,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 821,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"relative h-[400px] rounded-2xl overflow-hidden border\", tool.id === 'invincible-agent' ? \"bg-black/30 backdrop-blur-sm border-white/20\" : \"bg-black/40 border-white/10\"),\n                                children: tool.preview || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tool.icon, {\n                                                className: \"w-24 h-24 text-white/20 mx-auto\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 886,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400\",\n                                                children: \"Interactive preview coming soon\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 887,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 885,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                    lineNumber: 884,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 877,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 820,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                lineNumber: 800,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.3,\n                    delay: 0.2\n                },\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: `/content?type=${tool.id === 'invincible-agent' ? 'invincible_research' : tool.id === 'blog-generator' ? 'blog' : tool.id === 'email-generator' ? 'email' : tool.id === 'youtube-script' ? 'youtube_script' : tool.id.replace('-', '_')}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                delay: 0.25\n                            },\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"backdrop-blur-xl border rounded-2xl p-6 cursor-pointer hover:bg-white/10 transition-all duration-200 group\", tool.id === 'invincible-agent' ? \"bg-white/10 border-white/20 shadow-xl hover:border-white/30\" : \"bg-white/5 border-white/10 hover:border-white/20\"),\n                            title: `View all ${tool.title} content in your library`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"w-8 h-8 group-hover:scale-110 transition-transform duration-200\", `text-${tool.accentColor}-400`)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 916,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                    className: \"w-5 h-5 text-emerald-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 918,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                    className: \"w-4 h-4 text-gray-400 group-hover:text-white transition-colors\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 919,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 917,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                    lineNumber: 915,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-3xl font-bold text-white mb-1 group-hover:text-violet-200 transition-colors\",\n                                    children: tool.stats.generated\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                    lineNumber: 922,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-400 group-hover:text-gray-300 transition-colors\",\n                                    children: \"Content Generated - Click to view\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                    lineNumber: 923,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center text-xs text-violet-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                className: \"w-3 h-3 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 928,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"View in Content Library\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 929,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 927,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                    lineNumber: 926,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                            lineNumber: 903,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 902,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 0.35\n                        },\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"backdrop-blur-xl border rounded-2xl p-6\", tool.id === 'invincible-agent' ? \"bg-white/10 border-white/20 shadow-xl\" : \"bg-white/5 border-white/10\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"w-8 h-8\", `text-${tool.accentColor}-400`)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 947,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            1,\n                                            2,\n                                            3,\n                                            4,\n                                            5\n                                        ].map((star)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"w-3 h-3 rounded-full\", star <= Math.round(tool.stats.quality / 2) ? \"bg-yellow-400\" : \"bg-gray-600\")\n                                            }, star, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 950,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 948,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 946,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-3xl font-bold text-white mb-1\",\n                                children: [\n                                    tool.stats.quality,\n                                    \"/10\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 962,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-400\",\n                                children: \"Quality Score\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 963,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 935,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 0.45\n                        },\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"backdrop-blur-xl border rounded-2xl p-6\", tool.id === 'invincible-agent' ? \"bg-white/10 border-white/20 shadow-xl\" : \"bg-white/5 border-white/10\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"w-8 h-8\", `text-${tool.accentColor}-400`)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 978,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                        className: \"w-5 h-5 text-blue-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 979,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 977,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-3xl font-bold text-white mb-1\",\n                                children: tool.stats.avgTime\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 981,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-400\",\n                                children: \"Average Time\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 982,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 966,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                lineNumber: 896,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n        lineNumber: 792,\n        columnNumber: 5\n    }, undefined);\n});\n// Optimized Dashboard Overview Component with Memoization\nconst DashboardOverview = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(({ tools, userProfile, getDisplayName, userStats, isLoadingStats })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n        initial: {\n            opacity: 0,\n            scale: 0.95\n        },\n        animate: {\n            opacity: 1,\n            scale: 1\n        },\n        exit: {\n            opacity: 0,\n            scale: 0.95\n        },\n        transition: {\n            duration: 0.2,\n            ease: \"easeOut\"\n        },\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-violet-800/20 to-indigo-800/20 backdrop-blur-xl border border-white/10 rounded-3xl p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl font-bold text-white mb-4\",\n                        children: [\n                            \"Welcome back, \",\n                            getDisplayName(),\n                            \"! ✨\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 1007,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-300 mb-6\",\n                        children: \"Your creative AI toolkit is ready. What will you create today?\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 1008,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                        children: isLoadingStats ? // Loading skeleton\n                        Array.from({\n                            length: 4\n                        }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: index * 0.1\n                                },\n                                className: \"bg-black/40 backdrop-blur-sm border border-white/10 rounded-xl p-4 animate-pulse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-5 bg-white/10 rounded mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 1021,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-8 bg-white/10 rounded mb-1\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 1022,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-white/10 rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 1023,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, index, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 1014,\n                                columnNumber: 15\n                            }, undefined)) : [\n                            {\n                                label: 'Total Created',\n                                value: userStats?.totalContent?.toString() || '0',\n                                icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                                change: userStats?.trends?.contentGrowth || '+0%'\n                            },\n                            {\n                                label: 'Time Saved',\n                                value: `${userStats?.timeSavedHours || 0} hrs`,\n                                icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"],\n                                change: userStats?.trends?.timeEfficiency || '+0%'\n                            },\n                            {\n                                label: 'Quality Score',\n                                value: `${userStats?.qualityScore || 9.0}/10`,\n                                icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"],\n                                change: userStats?.trends?.qualityImprovement || '+0.0'\n                            },\n                            {\n                                label: 'Active Tools',\n                                value: userStats?.trends?.toolsActive?.toString() || '0',\n                                icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                                change: '+' + (userStats?.trends?.toolsActive || 0)\n                            }\n                        ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: index * 0.1\n                                },\n                                className: \"bg-black/40 backdrop-blur-sm border border-white/10 rounded-xl p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                                className: \"w-5 h-5 text-violet-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 1061,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-emerald-400\",\n                                                children: stat.change\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 1062,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 1060,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-white\",\n                                        children: stat.value\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 1064,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-400\",\n                                        children: stat.label\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 1065,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, stat.label, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 1053,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 1010,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                lineNumber: 1006,\n                columnNumber: 15\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-white mb-6\",\n                        children: \"Your AI Tools\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 1074,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                        children: tools.map((tool, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: tool.href,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        scale: 0.9\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        scale: 1\n                                    },\n                                    transition: {\n                                        delay: index * 0.1\n                                    },\n                                    whileHover: {\n                                        y: -8\n                                    },\n                                    className: \"group relative cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"absolute inset-0 bg-gradient-to-br opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl blur-xl\", tool.color)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 1085,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"relative backdrop-blur-xl border transition-all\", tool.id === 'invincible-agent' ? \"bg-black/40 border-white/20 hover:border-white/30 rounded-2xl shadow-2xl\" : \"bg-black/60 border-white/10 hover:border-white/20 rounded-2xl\"),\n                                            children: [\n                                                tool.id === 'invincible-agent' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-transparent rounded-2xl\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 1098,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start justify-between mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"p-3 rounded-xl text-white\", tool.id === 'invincible-agent' ? \"bg-gradient-to-br from-violet-800/60 to-indigo-800/60 backdrop-blur-sm border border-white/20\" : (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"bg-gradient-to-br\", tool.color)),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tool.icon, {\n                                                                        className: \"w-6 h-6\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                        lineNumber: 1109,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 1103,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                    className: \"w-5 h-5 text-gray-400 group-hover:text-white transition-colors\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 1111,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 1102,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-white mb-1\",\n                                                            children: tool.title\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 1114,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-400 mb-4\",\n                                                            children: tool.subtitle\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 1115,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between text-xs\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                    href: `/content?type=${tool.id === 'invincible-agent' ? 'invincible_research' : tool.id === 'blog-generator' ? 'blog' : tool.id === 'email-generator' ? 'email' : tool.id === 'youtube-script' ? 'youtube_script' : tool.id.replace('-', '_')}`,\n                                                                    onClick: (e)=>e.stopPropagation(),\n                                                                    className: \"hover:bg-white/10 rounded px-2 py-1 transition-colors group/stat\",\n                                                                    title: `View all ${tool.title} content`,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-500 group-hover/stat:text-gray-400\",\n                                                                            children: \"Generated\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                            lineNumber: 1124,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-white font-medium group-hover/stat:text-violet-200\",\n                                                                                    children: tool.stats.generated\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                                    lineNumber: 1126,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Cpu_Crown_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                                    className: \"w-3 h-3 text-gray-400 group-hover/stat:text-white opacity-0 group-hover/stat:opacity-100 transition-all\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                                    lineNumber: 1127,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                            lineNumber: 1125,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-black/90 text-white text-xs rounded opacity-0 group-hover/stat:opacity-100 transition-opacity pointer-events-none whitespace-nowrap\",\n                                                                            children: \"Click to view content\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                            lineNumber: 1130,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 1118,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-right\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-500\",\n                                                                            children: \"Quality\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                            lineNumber: 1135,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-white font-medium\",\n                                                                            children: [\n                                                                                tool.stats.quality,\n                                                                                \"/10\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                            lineNumber: 1136,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 1134,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 1117,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 1101,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 1090,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                    lineNumber: 1078,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, tool.id, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 1077,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 1075,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                lineNumber: 1073,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(OptimizedRecentContent, {\n                    limit: 5,\n                    showFilters: true\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                    lineNumber: 1149,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                lineNumber: 1148,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n        lineNumber: 998,\n        columnNumber: 5\n    }, undefined);\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2Rhc2hib2FyZC9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFa0Y7QUFDM0I7QUFDM0I7QUFDTTtBQUNVO0FBQ0Q7QUErQ3RCO0FBQ1c7QUFDc0I7QUFFdEQsd0RBQXdEO0FBQ3hELE1BQU1vQyx5QkFBeUI1Qix3REFBT0E7Ozs7Ozs7O0lBQ3BDNkIsS0FBSztJQUNMQyxTQUFTLGtCQUNQLDhEQUFDQztZQUFJQyxXQUFVO3NCQUNaQyxNQUFNQyxJQUFJLENBQUM7Z0JBQUVDLFFBQVE7WUFBRSxHQUFHQyxHQUFHLENBQUMsQ0FBQ0MsR0FBR0Msa0JBQ2pDLDhEQUFDUDtvQkFBWUMsV0FBVTs7c0NBQ3JCLDhEQUFDRDs0QkFBSUMsV0FBVTs7Ozs7O3NDQUNmLDhEQUFDRDs0QkFBSUMsV0FBVTs7Ozs7OzttQkFGUE07Ozs7Ozs7Ozs7O0FBU2xCLGlFQUFpRTtBQUNqRSxNQUFNQyx5QkFBeUJ2Qyx3REFBT0E7Ozs7Ozs7O0lBQ3BDNkIsS0FBSztJQUNMQyxTQUFTLGtCQUFNLDhEQUFDQztZQUFJQyxXQUFVOzs7Ozs7O0FBR2hDLE1BQU1RLHVCQUF1QnhDLHdEQUFPQTs7Ozs7Ozs7SUFDbEM2QixLQUFLO0lBQ0xDLFNBQVMsa0JBQU0sOERBQUNDO1lBQUlDLFdBQVU7Ozs7Ozs7QUFHaEMsTUFBTVMsd0JBQXdCekMsd0RBQU9BOzs7Ozs7OztJQUNuQzZCLEtBQUs7SUFDTEMsU0FBUyxrQkFBTSw4REFBQ0M7WUFBSUMsV0FBVTs7Ozs7OztBQUdoQyxNQUFNVSw4QkFBOEIxQyx3REFBT0E7Ozs7Ozs7O0lBQ3pDNkIsS0FBSztJQUNMQyxTQUFTLGtCQUFNLDhEQUFDQztZQUFJQyxXQUFVOzs7Ozs7O0FBR2hDLE1BQU1XLDhCQUE4QjNDLHdEQUFPQTs7Ozs7Ozs7SUFDekM2QixLQUFLO0lBQ0xDLFNBQVMsa0JBQU0sOERBQUNDO1lBQUlDLFdBQVU7Ozs7Ozs7QUFHaEMsTUFBTVksK0JBQStCNUMsd0RBQU9BOzs7Ozs7OztJQUMxQzZCLEtBQUs7SUFDTEMsU0FBUyxrQkFBTSw4REFBQ0M7WUFBSUMsV0FBVTs7Ozs7OztBQUdoQyxNQUFNYSwyQkFBMkI3Qyx3REFBT0E7Ozs7Ozs7O0lBQ3RDNkIsS0FBSztJQUNMQyxTQUFTLGtCQUFNLDhEQUFDQztZQUFJQyxXQUFVOzs7Ozs7O0FBS2hDLCtDQUErQztBQUMvQyxNQUFNYyxnQ0FBa0J0RCxpREFBVSxDQUFDLENBQUMsRUFDbEN3RCxNQUFNLEVBQ05DLE9BQU8sRUFDUEMsS0FBSyxFQUNMQyxZQUFZLEVBQ1pDLGVBQWUsRUFDZkMsV0FBVyxFQUNYQyxjQUFjLEVBU2Y7SUFDQyxxREFBcUQ7SUFDckQsTUFBTUMsa0JBQWtCM0QsOENBQU9BO29EQUFDLElBQU87Z0JBQ3JDNEQsTUFBTTtvQkFDSkMsR0FBRztvQkFDSEMsWUFBWTt3QkFBRUMsTUFBTTt3QkFBVUMsV0FBVzt3QkFBS0MsU0FBUztvQkFBRztnQkFDNUQ7Z0JBQ0FDLFFBQVE7b0JBQ05MLEdBQUcsQ0FBQztvQkFDSkMsWUFBWTt3QkFBRUMsTUFBTTt3QkFBVUMsV0FBVzt3QkFBS0MsU0FBUztvQkFBRztnQkFDNUQ7WUFDRjttREFBSSxFQUFFO0lBRU4sMENBQTBDO0lBQzFDLE1BQU1FLG1CQUFtQnBFLGtEQUFXQTt5REFBQyxDQUFDcUU7WUFDcENaLGdCQUFnQlk7UUFDbEI7d0RBQUc7UUFBQ1o7S0FBZ0I7SUFFcEIsTUFBTWEsa0JBQWtCdEUsa0RBQVdBO3dEQUFDLENBQUNxRTtZQUNuQ1YsZUFBZVU7UUFDakI7dURBQUc7UUFBQ1Y7S0FBZTtJQUVuQixxQkFDRTs7MEJBRUUsOERBQUN4RCwwREFBZUE7MEJBQ2JrRCx3QkFDQyw4REFBQ25ELGlEQUFNQSxDQUFDa0MsR0FBRztvQkFDVG1DLFNBQVM7d0JBQUVDLFNBQVM7b0JBQUU7b0JBQ3RCQyxTQUFTO3dCQUFFRCxTQUFTO29CQUFFO29CQUN0QkUsTUFBTTt3QkFBRUYsU0FBUztvQkFBRTtvQkFDbkJULFlBQVk7d0JBQUVZLFVBQVU7b0JBQUk7b0JBQzVCQyxTQUFTdEI7b0JBQ1RqQixXQUFVOzs7Ozs7Ozs7OzswQkFNaEIsOERBQUNuQyxpREFBTUEsQ0FBQzJFLEtBQUs7Z0JBQ1hOLFNBQVE7Z0JBQ1JFLFNBQVNwQixTQUFTLFNBQVM7Z0JBQzNCeUIsVUFBVWxCO2dCQUNWdkIsV0FBVTtnQkFDVjBDLE9BQU87b0JBQUVDLFlBQVk7Z0JBQVk7O2tDQUdqQyw4REFBQzVDO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDRDs0QkFBSUMsV0FBVTs7Ozs7Ozs7Ozs7a0NBSWpCLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBRWIsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDakMsa0RBQUlBO29DQUFDNkUsTUFBSztvQ0FBSTVDLFdBQVU7O3NEQUN2Qiw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFBSUMsV0FBVTs7Ozs7OzhEQUNmLDhEQUFDbkMsaURBQU1BLENBQUNrQyxHQUFHO29EQUNUOEMsWUFBWTt3REFBRUMsT0FBTztvREFBSztvREFDMUJDLFVBQVU7d0RBQUVELE9BQU87b0RBQUs7b0RBQ3hCcEIsWUFBWTt3REFBRVksVUFBVTtvREFBSTtvREFDNUJ0QyxXQUFVOzhEQUVWLDRFQUFDN0IsOE9BQVFBO3dEQUFDNkIsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBSXhCLDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNnRDtvREFBR2hELFdBQVU7OERBQWdDOzs7Ozs7OERBQzlDLDhEQUFDaUQ7b0RBQUVqRCxXQUFVOzhEQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBTTNDLDhEQUFDa0Q7Z0NBQUlsRCxXQUFVOztrREFFYiw4REFBQ25DLGlEQUFNQSxDQUFDc0YsTUFBTTt3Q0FDWlosU0FBUyxJQUFNUixpQkFBaUI7d0NBQ2hDYyxZQUFZOzRDQUFFcEIsR0FBRzt3Q0FBRTt3Q0FDbkJzQixVQUFVOzRDQUFFRCxPQUFPO3dDQUFLO3dDQUN4QnBCLFlBQVk7NENBQUVZLFVBQVU7d0NBQUk7d0NBQzVCdEMsV0FBV04sOENBQUVBLENBQ1gsZ0dBQ0EsQ0FBQ3lCLGVBQ0csNERBQ0E7OzBEQUdOLDhEQUFDcEI7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDRDt3REFBSUMsV0FBVTtrRUFDYiw0RUFBQ2YsOE9BQUlBOzREQUFDZSxXQUFVOzs7Ozs7Ozs7OztrRUFFbEIsOERBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ2lEO2dFQUFFakQsV0FBVTswRUFBZ0I7Ozs7OzswRUFDN0IsOERBQUNpRDtnRUFBRWpELFdBQVU7MEVBQXFCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7NENBSXJDLENBQUNtQiw4QkFDQSw4REFBQ3BCO2dEQUFJQyxXQUFVOzs7Ozs7Ozs7Ozs7a0RBS25CLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFBSUMsV0FBVTs7Ozs7OzhEQUNmLDhEQUFDaUQ7b0RBQUVqRCxXQUFVOzhEQUErRDs7Ozs7OzhEQUc1RSw4REFBQ0Q7b0RBQUlDLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUtuQiw4REFBQ2pDLGtEQUFJQTt3Q0FBQzZFLE1BQUs7a0RBQ1QsNEVBQUMvRSxpREFBTUEsQ0FBQ3NGLE1BQU07NENBQ1pOLFlBQVk7Z0RBQUVwQixHQUFHOzRDQUFFOzRDQUNuQnNCLFVBQVU7Z0RBQUVELE9BQU87NENBQUs7NENBQ3hCcEIsWUFBWTtnREFBRVksVUFBVTs0Q0FBSTs0Q0FDNUJ0QyxXQUFVOzs4REFFViw4REFBQ0Q7b0RBQUlDLFdBQVU7OERBQ2IsNEVBQUNuQiw4T0FBTUE7d0RBQUNtQixXQUFVOzs7Ozs7Ozs7Ozs4REFFcEIsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ2lEOzREQUFFakQsV0FBVTtzRUFBZ0I7Ozs7OztzRUFDN0IsOERBQUNpRDs0REFBRWpELFdBQVU7c0VBQXFCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztvQ0FNdkNrQixNQUFNZCxHQUFHLENBQUMsQ0FBQ2dELHFCQUNWLDhEQUFDdkYsaURBQU1BLENBQUNzRixNQUFNOzRDQUVaWixTQUFTLElBQU1SLGlCQUFpQnFCLEtBQUtDLEVBQUU7NENBQ3ZDQyxjQUFjLElBQU1yQixnQkFBZ0JtQixLQUFLQyxFQUFFOzRDQUMzQ0UsY0FBYyxJQUFNdEIsZ0JBQWdCOzRDQUNwQ1ksWUFBWTtnREFBRXBCLEdBQUc7NENBQUU7NENBQ25Cc0IsVUFBVTtnREFBRUQsT0FBTzs0Q0FBSzs0Q0FDeEJwQixZQUFZO2dEQUFFWSxVQUFVOzRDQUFJOzRDQUM1QnRDLFdBQVdOLDhDQUFFQSxDQUNYLGdHQUNBeUIsaUJBQWlCaUMsS0FBS0MsRUFBRSxHQUNwQixpRUFDQSxtREFDSmxDLGlCQUFpQmlDLEtBQUtDLEVBQUUsSUFBSUQsS0FBS0ksS0FBSzs7OERBR3hDLDhEQUFDekQ7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRDs0REFBSUMsV0FBV04sOENBQUVBLENBQ2hCLGlEQUNBeUIsaUJBQWlCaUMsS0FBS0MsRUFBRSxHQUNwQixnQkFDQTtzRUFFSiw0RUFBQ0QsS0FBS0ssSUFBSTtnRUFBQ3pELFdBQVU7Ozs7Ozs7Ozs7O3NFQUV2Qiw4REFBQ0Q7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDRDtvRUFBSUMsV0FBVTs7c0ZBQ2IsOERBQUNpRDs0RUFBRWpELFdBQVU7c0ZBQWlCb0QsS0FBS00sS0FBSzs7Ozs7O3dFQUN2Q04sS0FBS08sSUFBSSxrQkFDUiw4REFBQ0M7NEVBQUs1RCxXQUFVO3NGQUF1Rjs7Ozs7O3dFQUl4R29ELEtBQUtTLFVBQVUsa0JBQ2QsOERBQUNEOzRFQUFLNUQsV0FBVTtzRkFBdUY7Ozs7Ozs7Ozs7Ozs4RUFLM0csOERBQUNpRDtvRUFBRWpELFdBQVU7OEVBQXNCb0QsS0FBS1UsUUFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7O2dEQUluRDNDLGlCQUFpQmlDLEtBQUtDLEVBQUUsa0JBQ3ZCLDhEQUFDdEQ7b0RBQUlDLFdBQVU7Ozs7OztnREFJaEJxQixnQkFBZ0IrQixLQUFLQyxFQUFFLGtCQUN0Qiw4REFBQ3REO29EQUFJQyxXQUFVOzhEQUNab0QsS0FBS1csS0FBSyxDQUFDQyxTQUFTOzs7Ozs7OzJDQWpEcEJaLEtBQUtDLEVBQUU7Ozs7Ozs7Ozs7OzBDQXlEbEIsOERBQUN0RDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNqQyxrREFBSUE7d0NBQUM2RSxNQUFLO2tEQUNULDRFQUFDL0UsaURBQU1BLENBQUNzRixNQUFNOzRDQUNaTixZQUFZO2dEQUFFcEIsR0FBRzs0Q0FBRTs0Q0FDbkJzQixVQUFVO2dEQUFFRCxPQUFPOzRDQUFLOzRDQUN4QnBCLFlBQVk7Z0RBQUVZLFVBQVU7NENBQUk7NENBQzVCdEMsV0FBVTs7OERBRVYsOERBQUNEO29EQUFJQyxXQUFVOzhEQUNiLDRFQUFDakIsOE9BQVFBO3dEQUFDaUIsV0FBVTs7Ozs7Ozs7Ozs7OERBRXRCLDhEQUFDNEQ7b0RBQUs1RCxXQUFVOzhEQUFjOzs7Ozs7Ozs7Ozs7Ozs7OztrREFJbEMsOERBQUNuQyxpREFBTUEsQ0FBQ3NGLE1BQU07d0NBQ1pOLFlBQVk7NENBQUVwQixHQUFHO3dDQUFFO3dDQUNuQnNCLFVBQVU7NENBQUVELE9BQU87d0NBQUs7d0NBQ3hCcEIsWUFBWTs0Q0FBRVksVUFBVTt3Q0FBSTt3Q0FDNUJ0QyxXQUFVOzswREFFViw4REFBQ0Q7Z0RBQUlDLFdBQVU7MERBQ2IsNEVBQUNoQiw4T0FBVUE7b0RBQUNnQixXQUFVOzs7Ozs7Ozs7OzswREFFeEIsOERBQUM0RDtnREFBSzVELFdBQVU7MERBQWM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTzVDO0FBMkNBLG9EQUFvRDtBQUNyQyxTQUFTaUU7SUFDdEIsTUFBTSxFQUFFQyxNQUFNQyxPQUFPLEVBQUVDLE1BQU0sRUFBRSxHQUFHbkcsMkRBQVVBO0lBQzVDLE1BQU1vRyxTQUFTbkcsMERBQVNBO0lBQ3hCLE1BQU0sQ0FBQ2lELGNBQWNDLGdCQUFnQixHQUFHM0QsK0NBQVFBLENBQVM7SUFDekQsTUFBTSxDQUFDNkcsYUFBYUMsZUFBZSxHQUFHOUcsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDK0csYUFBYUMsZUFBZSxHQUFHaEgsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDaUgsbUJBQW1CQyxxQkFBcUIsR0FBR2xILCtDQUFRQSxDQUFDO0lBQzNELE1BQU0sQ0FBQzRELGFBQWFDLGVBQWUsR0FBRzdELCtDQUFRQSxDQUFnQjtJQUM5RCxNQUFNLENBQUNtSCxhQUFhQyxlQUFlLEdBQUdwSCwrQ0FBUUEsQ0FBcUI7SUFDbkUsTUFBTSxDQUFDcUgsa0JBQWtCQyxvQkFBb0IsR0FBR3RILCtDQUFRQSxDQUFDO0lBQ3pELE1BQU0sQ0FBQ3VILFdBQVdDLGFBQWEsR0FBR3hILCtDQUFRQSxDQUFNO0lBQ2hELE1BQU0sQ0FBQ3lILGdCQUFnQkMsa0JBQWtCLEdBQUcxSCwrQ0FBUUEsQ0FBQztJQUVyRCx5RUFBeUU7SUFDekUsTUFBTTJILGdCQUFnQnpILGtEQUFXQTtvREFBQyxJQUFNNEc7NERBQWVjLENBQUFBLE9BQVEsQ0FBQ0E7O21EQUFPLEVBQUU7SUFDekUsTUFBTUMsc0JBQXNCM0gsa0RBQVdBOzBEQUFDLElBQU1nSDtrRUFBcUJVLENBQUFBLE9BQVEsQ0FBQ0E7O3lEQUFPLEVBQUU7SUFDckYsTUFBTUUscUJBQXFCNUgsa0RBQVdBO3lEQUFDLENBQUM2SDtZQUN0Q2YsZUFBZWUsRUFBRUMsTUFBTSxDQUFDQyxLQUFLO1FBQy9CO3dEQUFHLEVBQUU7SUFFTCxnRkFBZ0Y7SUFDaEYsTUFBTUMsZUFBZWhJLGtEQUFXQTttREFBQyxDQUFDaUk7WUFDaEMsSUFBSSxDQUFDWixXQUFXYSxrQkFBa0I7Z0JBQ2hDLE9BQU87b0JBQUU3QixXQUFXO29CQUFHOEIsU0FBUztvQkFBS0MsU0FBUztnQkFBUTtZQUN4RDtZQUVBLE1BQU0vQixZQUFZZ0IsVUFBVWEsZ0JBQWdCLENBQUNELFNBQVMsSUFBSTtZQUMxRCxNQUFNRSxVQUFVRSxLQUFLQyxHQUFHLENBQUMsS0FBSyxNQUFPakMsWUFBWTtZQUVqRCxNQUFNa0MsV0FBbUM7Z0JBQ3ZDLHVCQUF1QjtnQkFDdkIsUUFBUTtnQkFDUixTQUFTO2dCQUNULGdCQUFnQjtnQkFDaEIsa0JBQWtCO2dCQUNsQixpQkFBaUI7WUFDbkI7WUFFQSxPQUFPO2dCQUNMbEM7Z0JBQ0E4QixTQUFTRSxLQUFLRyxLQUFLLENBQUNMLFVBQVUsTUFBTTtnQkFDcENDLFNBQVNHLFFBQVEsQ0FBQ04sU0FBUyxJQUFJO1lBQ2pDO1FBQ0Y7a0RBQUc7UUFBQ1osV0FBV2E7S0FBaUI7SUFFaEMsOEZBQThGO0lBQzlGLE1BQU0zRSxRQUFnQnRELDhDQUFPQTt3Q0FBQyxJQUFNO2dCQUNsQztvQkFDRXlGLElBQUk7b0JBQ0pLLE9BQU87b0JBQ1BJLFVBQVU7b0JBQ1ZzQyxhQUFhO29CQUNiM0MsTUFBTW5FLDhPQUFLQTtvQkFDWGtFLE9BQU87b0JBQ1A2QyxZQUFZO29CQUNaQyxhQUFhO29CQUNidkMsT0FBTzRCLGFBQWE7b0JBQ3BCWSxVQUFVO3dCQUFDO3dCQUFnQjt3QkFBd0I7d0JBQXVCO3dCQUFvQjt3QkFBb0I7cUJBQWlCO29CQUNuSTNELE1BQU07b0JBQ040RCx1QkFBUyw4REFBQ2pHOzs7OztnQkFDWjtnQkFDQTtvQkFDRThDLElBQUk7b0JBQ0pLLE9BQU87b0JBQ1BJLFVBQVU7b0JBQ1ZzQyxhQUFhO29CQUNiM0MsTUFBTXZFLDhPQUFJQTtvQkFDVnNFLE9BQU87b0JBQ1A2QyxZQUFZO29CQUNaQyxhQUFhO29CQUNidkMsT0FBTzRCLGFBQWE7b0JBQ3BCWSxVQUFVO3dCQUFDO3dCQUFtQjt3QkFBZTt3QkFBcUI7cUJBQW1CO29CQUNyRjNELE1BQU07Z0JBQ1I7Z0JBQ0E7b0JBQ0VTLElBQUk7b0JBQ0pLLE9BQU87b0JBQ1BJLFVBQVU7b0JBQ1ZzQyxhQUFhO29CQUNiM0MsTUFBTXJGLDhPQUFHQTtvQkFDVG9GLE9BQU87b0JBQ1A2QyxZQUFZO29CQUNaQyxhQUFhO29CQUNidkMsT0FBTzRCLGFBQWE7b0JBQ3BCWSxVQUFVO3dCQUFDO3dCQUFrQjt3QkFBcUI7d0JBQTJCO3FCQUFtQjtvQkFDaEczRCxNQUFNO2dCQUNSO2dCQUNBO29CQUNFUyxJQUFJO29CQUNKSyxPQUFPO29CQUNQSSxVQUFVO29CQUNWc0MsYUFBYTtvQkFDYjNDLE1BQU05RSw4T0FBUUE7b0JBQ2Q2RSxPQUFPO29CQUNQNkMsWUFBWTtvQkFDWkMsYUFBYTtvQkFDYnZDLE9BQU80QixhQUFhO29CQUNwQlksVUFBVTt3QkFBQzt3QkFBb0I7d0JBQXdCO3dCQUFxQjtxQkFBeUI7b0JBQ3JHM0QsTUFBTTtnQkFDUjtnQkFDQTtvQkFDRVMsSUFBSTtvQkFDSkssT0FBTztvQkFDUEksVUFBVTtvQkFDVnNDLGFBQWE7b0JBQ2IzQyxNQUFNbEUsOE9BQUtBO29CQUNYaUUsT0FBTztvQkFDUDZDLFlBQVk7b0JBQ1pDLGFBQWE7b0JBQ2J2QyxPQUFPNEIsYUFBYTtvQkFDcEJZLFVBQVU7d0JBQUM7d0JBQW1CO3dCQUEwQjt3QkFBbUI7cUJBQW1CO29CQUM5RjNELE1BQU07Z0JBQ1I7Z0JBQ0E7b0JBQ0VTLElBQUk7b0JBQ0pLLE9BQU87b0JBQ1BJLFVBQVU7b0JBQ1ZzQyxhQUFhO29CQUNiM0MsTUFBTXRGLDhPQUFRQTtvQkFDZHFGLE9BQU87b0JBQ1A2QyxZQUFZO29CQUNaQyxhQUFhO29CQUNidkMsT0FBTzRCLGFBQWE7b0JBQ3BCWSxVQUFVO3dCQUFDO3dCQUF1Qjt3QkFBZ0I7d0JBQWtCO3FCQUFlO29CQUNuRjNELE1BQU07b0JBQ05pQixZQUFZO2dCQUNkO2dCQUNBO29CQUNFUixJQUFJO29CQUNKSyxPQUFPO29CQUNQSSxVQUFVO29CQUNWc0MsYUFBYTtvQkFDYjNDLE1BQU1oRSw4T0FBR0E7b0JBQ1QrRCxPQUFPO29CQUNQNkMsWUFBWTtvQkFDWkMsYUFBYTtvQkFDYnZDLE9BQU80QixhQUFhO29CQUNwQlksVUFBVTt3QkFBQzt3QkFBc0I7d0JBQXVCO3dCQUF5QjtxQkFBcUI7b0JBQ3RHM0QsTUFBTTtvQkFDTmlCLFlBQVk7Z0JBQ2Q7Z0JBQ0E7b0JBQ0VSLElBQUk7b0JBQ0pLLE9BQU87b0JBQ1BJLFVBQVU7b0JBQ1ZzQyxhQUFhO29CQUNiM0MsTUFBTXBFLDhPQUFHQTtvQkFDVG1FLE9BQU87b0JBQ1A2QyxZQUFZO29CQUNaQyxhQUFhO29CQUNidkMsT0FBTzt3QkFBRUMsV0FBVzt3QkFBRzhCLFNBQVM7d0JBQUtDLFNBQVM7b0JBQVE7b0JBQ3REUSxVQUFVO3dCQUFDO3dCQUFzQjt3QkFBc0I7d0JBQXFCO3dCQUFvQjt3QkFBYTtxQkFBb0I7b0JBQ2pJM0QsTUFBTTtvQkFDTmUsTUFBTTtnQkFDUjthQUNEO3VDQUFFO1FBQUNnQztLQUFhO0lBRWpCLHlDQUF5QztJQUN6Q2pJLGdEQUFTQTttQ0FBQztZQUNSLElBQUkwRyxXQUFXLG1CQUFtQjtnQkFDaENDLE9BQU9vQyxJQUFJLENBQUM7WUFDZDtRQUNGO2tDQUFHO1FBQUNyQztRQUFRQztLQUFPO0lBRW5CLDBCQUEwQjtJQUMxQjNHLGdEQUFTQTttQ0FBQztZQUNSLElBQUl5RyxTQUFTdUMsTUFBTTtnQkFDakJDO2dCQUNBQztZQUNGO1FBQ0Y7a0NBQUc7UUFBQ3pDO0tBQVE7SUFFWixNQUFNd0MsbUJBQW1CO1FBQ3ZCLElBQUk7WUFDRixNQUFNRSxXQUFXLE1BQU1DLE1BQU07WUFDN0IsSUFBSUQsU0FBU0UsRUFBRSxFQUFFO2dCQUNmLE1BQU03QyxPQUFPLE1BQU0yQyxTQUFTRyxJQUFJO2dCQUNoQ25DLGVBQWVYO1lBQ2pCO1FBQ0YsRUFBRSxPQUFPK0MsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsZ0NBQWdDQTtRQUNoRCxTQUFVO1lBQ1JsQyxvQkFBb0I7UUFDdEI7SUFDRjtJQUVBLE1BQU02QixpQkFBaUI7UUFDckIsSUFBSTtZQUNGLE1BQU1DLFdBQVcsTUFBTUMsTUFBTTtZQUM3QixJQUFJRCxTQUFTRSxFQUFFLEVBQUU7Z0JBQ2YsTUFBTTdDLE9BQU8sTUFBTTJDLFNBQVNHLElBQUk7Z0JBQ2hDL0IsYUFBYWYsS0FBS0gsS0FBSztZQUN6QjtRQUNGLEVBQUUsT0FBT2tELE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDhCQUE4QkE7UUFDOUMsU0FBVTtZQUNSOUIsa0JBQWtCO1FBQ3BCO0lBQ0Y7SUFFQSxvQ0FBb0M7SUFDcEMsTUFBTWdDLGtCQUFrQjtRQUN0QixJQUFJdkMsYUFBYXdDLGFBQWF4QyxhQUFheUMsVUFBVTtZQUNuRCxPQUFPLEdBQUd6QyxZQUFZd0MsU0FBUyxDQUFDLEVBQUUsR0FBR3hDLFlBQVl5QyxRQUFRLENBQUMsRUFBRSxFQUFFO1FBQ2hFLE9BQU8sSUFBSXpDLGFBQWEwQyxNQUFNO1lBQzVCLE1BQU1DLFFBQVEzQyxZQUFZMEMsSUFBSSxDQUFDRSxLQUFLLENBQUM7WUFDckMsT0FBT0QsTUFBTXBILE1BQU0sR0FBRyxJQUFJLEdBQUdvSCxLQUFLLENBQUMsRUFBRSxDQUFDLEVBQUUsR0FBR0EsS0FBSyxDQUFDQSxNQUFNcEgsTUFBTSxHQUFHLEVBQUUsQ0FBQyxFQUFFLEVBQUUsR0FBR29ILEtBQUssQ0FBQyxFQUFFLENBQUMsRUFBRTtRQUN2RixPQUFPLElBQUkzQyxhQUFhNkMsT0FBTztZQUM3QixPQUFPN0MsWUFBWTZDLEtBQUssQ0FBQyxFQUFFLENBQUNDLFdBQVc7UUFDekM7UUFDQSxPQUFPO0lBQ1Q7SUFFQSxtQkFBbUI7SUFDbkIsTUFBTUMsaUJBQWlCO1FBQ3JCLElBQUkvQyxhQUFhd0MsYUFBYXhDLGFBQWF5QyxVQUFVO1lBQ25ELE9BQU8sR0FBR3pDLFlBQVl3QyxTQUFTLENBQUMsQ0FBQyxFQUFFeEMsWUFBWXlDLFFBQVEsRUFBRTtRQUMzRCxPQUFPLElBQUl6QyxhQUFhMEMsTUFBTTtZQUM1QixPQUFPMUMsWUFBWTBDLElBQUk7UUFDekIsT0FBTyxJQUFJMUMsYUFBYTZDLE9BQU87WUFDN0IsT0FBTzdDLFlBQVk2QyxLQUFLLENBQUNELEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRTtRQUN4QztRQUNBLE9BQU87SUFDVDtJQUVBLGdCQUFnQjtJQUNoQixJQUFJcEQsV0FBVyxhQUFhVSxrQkFBa0I7UUFDNUMscUJBQ0UsOERBQUMvRTtZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVOzs7Ozs7a0NBQ2YsOERBQUNpRDt3QkFBRWpELFdBQVU7a0NBQWdCOzs7Ozs7Ozs7Ozs7Ozs7OztJQUlyQztJQUVBLG9DQUFvQztJQUNwQyxJQUFJb0UsV0FBVyxtQkFBbUI7UUFDaEMsT0FBTztJQUNUO0lBRUEsTUFBTXdELGFBQWExRyxNQUFNMkcsSUFBSSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFekUsRUFBRSxLQUFLbEMsaUJBQWlCRCxLQUFLLENBQUMsRUFBRTtJQUVyRSxxQkFDRSw4REFBQ25CO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVOzs7Ozs7a0NBR2YsOERBQUNuQyxpREFBTUEsQ0FBQ2tDLEdBQUc7d0JBRVRtQyxTQUFTOzRCQUFFQyxTQUFTO3dCQUFFO3dCQUN0QkMsU0FBUzs0QkFBRUQsU0FBUzt3QkFBRTt3QkFDdEJULFlBQVk7NEJBQUVZLFVBQVU7NEJBQUt5RixNQUFNO2dDQUFDO2dDQUFLO2dDQUFHO2dDQUFLOzZCQUFFO3dCQUFDO3dCQUNwRC9ILFdBQVdOLDhDQUFFQSxDQUNYLGlEQUNBa0ksV0FBV3ZCLFVBQVU7dUJBTmxCdUIsV0FBV3ZFLEVBQUU7Ozs7O2tDQVdwQiw4REFBQ3REO3dCQUFJQyxXQUFVOzs7Ozs7a0NBQ2YsOERBQUNEO3dCQUFJQyxXQUFVO3dCQUE2RzBDLE9BQU87NEJBQUVzRixnQkFBZ0I7d0JBQUs7Ozs7Ozs7Ozs7OzswQkFJNUosOERBQUNqSTtnQkFBSUMsV0FBVTs7a0NBRWIsOERBQUNjO3dCQUNDRSxRQUFRc0Q7d0JBQ1JyRCxTQUFTLElBQU1zRCxlQUFlO3dCQUM5QnJELE9BQU9BO3dCQUNQQyxjQUFjQTt3QkFDZEMsaUJBQWlCQTt3QkFDakJDLGFBQWFBO3dCQUNiQyxnQkFBZ0JBOzs7Ozs7a0NBSWxCLDhEQUFDekQsaURBQU1BLENBQUNzRixNQUFNO3dCQUNaWixTQUFTNkM7d0JBQ1RoRCxTQUFTOzRCQUNQNkYsTUFBTTNELGNBQWMsTUFBTTt3QkFDNUI7d0JBQ0E1QyxZQUFZOzRCQUNWQyxNQUFNOzRCQUNOQyxXQUFXOzRCQUNYQyxTQUFTO3dCQUNYO3dCQUNBZ0IsWUFBWTs0QkFBRUMsT0FBTzt3QkFBSzt3QkFDMUJDLFVBQVU7NEJBQUVELE9BQU87d0JBQUs7d0JBQ3hCOUMsV0FBVTt3QkFDVjBDLE9BQU87NEJBQUVDLFlBQVk7d0JBQVk7a0NBRWhDMkIsNEJBQWMsOERBQUNsRiw4T0FBV0E7NEJBQUNZLFdBQVU7Ozs7O2lEQUEwQiw4REFBQ2IsOE9BQUlBOzRCQUFDYSxXQUFVOzs7Ozs7Ozs7OztrQ0FJbEYsOERBQUNuQyxpREFBTUEsQ0FBQ3FLLElBQUk7d0JBQ1Y5RixTQUFTOzRCQUNQK0YsWUFBWTdELGNBQWMsTUFBTTt3QkFDbEM7d0JBQ0E1QyxZQUFZOzRCQUNWQyxNQUFNOzRCQUNOVyxVQUFVOzRCQUNWeUYsTUFBTTtnQ0FBQztnQ0FBSztnQ0FBRztnQ0FBSzs2QkFBRTt3QkFDeEI7d0JBQ0EvSCxXQUFVO3dCQUNWMEMsT0FBTzs0QkFBRUMsWUFBWTt3QkFBYzs7MENBR3ZCLDhEQUFDeUY7Z0NBQU9wSSxXQUFVOzBDQUMxQiw0RUFBQ25DLGlEQUFNQSxDQUFDa0MsR0FBRztvQ0FDVHFDLFNBQVM7d0NBQUVpRyxhQUFhL0QsY0FBYyxLQUFLO29DQUFHO29DQUM5QzVDLFlBQVk7d0NBQ1ZDLE1BQU07d0NBQ05XLFVBQVU7d0NBQ1Z5RixNQUFNOzRDQUFDOzRDQUFLOzRDQUFHOzRDQUFLO3lDQUFFO29DQUN4QjtvQ0FDQS9ILFdBQVU7OENBRVosNEVBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNPLDhEQUFDc0k7d0RBQUd0SSxXQUFVO2tFQUM3Qm1CLGVBQWV5RyxXQUFXbEUsS0FBSyxHQUFHOzs7Ozs7a0VBSXJDLDhEQUFDM0Q7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDekIsOE9BQU1BO2dFQUFDeUIsV0FBVTs7Ozs7OzBFQUNsQiw4REFBQ3VJO2dFQUNDNUcsTUFBSztnRUFDTDZHLGFBQVk7Z0VBQ1o5QyxPQUFPbEI7Z0VBQ1BpRSxVQUFVbEQ7Z0VBQ1Z2RixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBTWhCLDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNtRDt3REFDQ25ELFdBQVU7d0RBQ1Z1QyxTQUFTK0M7OzBFQUVULDhEQUFDOUcsOE9BQUlBO2dFQUFDd0IsV0FBVTs7Ozs7OzBFQUNoQiw4REFBQzREO2dFQUFLNUQsV0FBVTs7Ozs7Ozs7Ozs7O2tFQUdsQiw4REFBQ0Q7d0RBQUlDLFdBQVU7Ozs7OztrRUFFZiw4REFBQ0wsaUVBQWFBO3dEQUNaaUYsYUFBYUE7d0RBQ2I1RSxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQVFwQiw4REFBQ25DLGlEQUFNQSxDQUFDa0MsR0FBRztnQ0FDVHFDLFNBQVM7b0NBQ1BpRyxhQUFhL0QsY0FBYyxLQUFLO2dDQUNsQztnQ0FDQTVDLFlBQVk7b0NBQ1ZDLE1BQU07b0NBQ05XLFVBQVU7b0NBQ1Z5RixNQUFNO3dDQUFDO3dDQUFLO3dDQUFHO3dDQUFLO3FDQUFFO2dDQUN4QjtnQ0FDQS9ILFdBQVU7MENBRVosNEVBQUNsQywwREFBZUE7b0NBQUM0SyxNQUFLOzhDQUNuQnZILDZCQUNDLDhEQUFDd0g7d0NBQStCdkYsTUFBTXdFO3VDQUFwQnpHOzs7OzZEQUVsQiw4REFBQ3lIO3dDQUFpQzFILE9BQU9BO3dDQUFPMEQsYUFBYUE7d0NBQWErQyxnQkFBZ0JBO3dDQUFnQjNDLFdBQVdBO3dDQUFXRSxnQkFBZ0JBO3VDQUF6SDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBVXZDO0FBRUEsb0RBQW9EO0FBQ3BELE1BQU15RCw0QkFBY25MLGlEQUFVLENBQUMsQ0FBQyxFQUFFNEYsSUFBSSxFQUFrQjtJQUN0RCxxQkFDRSw4REFBQ3ZGLGlEQUFNQSxDQUFDa0MsR0FBRztRQUNUbUMsU0FBUztZQUFFQyxTQUFTO1lBQUdXLE9BQU87UUFBSztRQUNuQ1YsU0FBUztZQUFFRCxTQUFTO1lBQUdXLE9BQU87UUFBRTtRQUNoQ1QsTUFBTTtZQUFFRixTQUFTO1lBQUdXLE9BQU87UUFBSztRQUNoQ3BCLFlBQVk7WUFBRVksVUFBVTtZQUFLeUYsTUFBTTtRQUFVO1FBQzdDL0gsV0FBVTs7MEJBR1YsOERBQUNuQyxpREFBTUEsQ0FBQ2tDLEdBQUc7Z0JBQ1RtQyxTQUFTO29CQUFFQyxTQUFTO29CQUFHMEcsR0FBRztnQkFBRztnQkFDN0J6RyxTQUFTO29CQUFFRCxTQUFTO29CQUFHMEcsR0FBRztnQkFBRTtnQkFDNUJuSCxZQUFZO29CQUFFWSxVQUFVO29CQUFLd0csT0FBTztnQkFBSTtnQkFDeEM5SSxXQUFXTiw4Q0FBRUEsQ0FDWCxvRUFDQTBELEtBQUtDLEVBQUUsS0FBSyxxQkFDUiwwRUFDQTs7b0JBR0xELEtBQUtDLEVBQUUsS0FBSyxvQ0FDWCw4REFBQ3REO3dCQUFJQyxXQUFVOzs7Ozs7a0NBR2pCLDhEQUFDRDt3QkFBSUMsV0FBV04sOENBQUVBLENBQ2hCLGlEQUNBMEQsS0FBS0ksS0FBSzs7Ozs7O2tDQUdaLDhEQUFDekQ7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVdOLDhDQUFFQSxDQUNoQix5Q0FDQTBELEtBQUtDLEVBQUUsS0FBSyxxQkFDUixrR0FDQTNELDhDQUFFQSxDQUFDLHFCQUFxQjBELEtBQUtJLEtBQUs7MERBRXRDLDRFQUFDSixLQUFLSyxJQUFJO29EQUFDekQsV0FBVTs7Ozs7Ozs7Ozs7MERBRXZCLDhEQUFDRDs7a0VBQ0MsOERBQUNpRDt3REFBR2hELFdBQVU7a0VBQWlDb0QsS0FBS00sS0FBSzs7Ozs7O2tFQUN6RCw4REFBQ1Q7d0RBQUVqRCxXQUFVO2tFQUF5Qm9ELEtBQUtVLFFBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFJdkQsOERBQUNiO3dDQUFFakQsV0FBVTtrREFDVm9ELEtBQUtnRCxXQUFXOzs7Ozs7a0RBR25CLDhEQUFDckc7d0NBQUlDLFdBQVU7a0RBQ1pvRCxLQUFLbUQsUUFBUSxDQUFDbkcsR0FBRyxDQUFDLENBQUMySSxTQUFTQyxzQkFDM0IsOERBQUNwRjtnREFFQzVELFdBQVdOLDhDQUFFQSxDQUNYLG9EQUNBMEQsS0FBS0MsRUFBRSxLQUFLLHFCQUNSLGlEQUNBOzBEQUdMMEY7K0NBUklDOzs7Ozs7Ozs7O2tEQWFYLDhEQUFDako7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUNqQyxrREFBSUE7NENBQUM2RSxNQUFNUSxLQUFLUixJQUFJO3NEQUNuQiw0RUFBQy9FLGlEQUFNQSxDQUFDc0YsTUFBTTtnREFDWk4sWUFBWTtvREFBRUMsT0FBTztnREFBSztnREFDMUJDLFVBQVU7b0RBQUVELE9BQU87Z0RBQUs7Z0RBQ3hCOUMsV0FBV04sOENBQUVBLENBQ1gsNkdBQ0EwRCxLQUFLQyxFQUFFLEtBQUsscUJBQ1IsaUpBQ0EzRCw4Q0FBRUEsQ0FBQyxvQkFBb0IwRCxLQUFLSSxLQUFLOztrRUFHdkMsOERBQUNuRiw4T0FBTUE7d0RBQUMyQixXQUFVOzs7Ozs7a0VBQ2xCLDhEQUFDNEQ7OzREQUFLOzREQUFRUixLQUFLTSxLQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FPaEMsOERBQUMzRDtnQ0FBSUMsV0FBV04sOENBQUVBLENBQ2hCLHlEQUNBMEQsS0FBS0MsRUFBRSxLQUFLLHFCQUNSLGlEQUNBOzBDQUVIRCxLQUFLb0QsT0FBTyxrQkFDWCw4REFBQ3pHO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNvRCxLQUFLSyxJQUFJO2dEQUFDekQsV0FBVTs7Ozs7OzBEQUNyQiw4REFBQ2lEO2dEQUFFakQsV0FBVTswREFBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBU3pDLDhEQUFDbkMsaURBQU1BLENBQUNrQyxHQUFHO2dCQUNUbUMsU0FBUztvQkFBRUMsU0FBUztvQkFBRzBHLEdBQUc7Z0JBQUc7Z0JBQzdCekcsU0FBUztvQkFBRUQsU0FBUztvQkFBRzBHLEdBQUc7Z0JBQUU7Z0JBQzVCbkgsWUFBWTtvQkFBRVksVUFBVTtvQkFBS3dHLE9BQU87Z0JBQUk7Z0JBQ3hDOUksV0FBVTs7a0NBRVYsOERBQUNqQyxrREFBSUE7d0JBQUM2RSxNQUFNLENBQUMsY0FBYyxFQUFFUSxLQUFLQyxFQUFFLEtBQUsscUJBQXFCLHdCQUF3QkQsS0FBS0MsRUFBRSxLQUFLLG1CQUFtQixTQUFTRCxLQUFLQyxFQUFFLEtBQUssb0JBQW9CLFVBQVVELEtBQUtDLEVBQUUsS0FBSyxtQkFBbUIsbUJBQW1CRCxLQUFLQyxFQUFFLENBQUM0RixPQUFPLENBQUMsS0FBSyxNQUFNO2tDQUNuUCw0RUFBQ3BMLGlEQUFNQSxDQUFDa0MsR0FBRzs0QkFDVG1DLFNBQVM7Z0NBQUVDLFNBQVM7Z0NBQUcwRyxHQUFHOzRCQUFHOzRCQUM3QnpHLFNBQVM7Z0NBQUVELFNBQVM7Z0NBQUcwRyxHQUFHOzRCQUFFOzRCQUM1Qm5ILFlBQVk7Z0NBQUVvSCxPQUFPOzRCQUFLOzRCQUMxQjlJLFdBQVdOLDhDQUFFQSxDQUNYLDhHQUNBMEQsS0FBS0MsRUFBRSxLQUFLLHFCQUNSLGdFQUNBOzRCQUVOSyxPQUFPLENBQUMsU0FBUyxFQUFFTixLQUFLTSxLQUFLLENBQUMsd0JBQXdCLENBQUM7OzhDQUV2RCw4REFBQzNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ3JCLDhPQUFRQTs0Q0FBQ3FCLFdBQVdOLDhDQUFFQSxDQUFDLG1FQUFtRSxDQUFDLEtBQUssRUFBRTBELEtBQUtrRCxXQUFXLENBQUMsSUFBSSxDQUFDOzs7Ozs7c0RBQ3pILDhEQUFDdkc7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDdkIsOE9BQVVBO29EQUFDdUIsV0FBVTs7Ozs7OzhEQUN0Qiw4REFBQzFCLDhPQUFVQTtvREFBQzBCLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FHMUIsOERBQUNpRDtvQ0FBRWpELFdBQVU7OENBQW9Gb0QsS0FBS1csS0FBSyxDQUFDQyxTQUFTOzs7Ozs7OENBQ3JILDhEQUFDZjtvQ0FBRWpELFdBQVU7OENBQW9FOzs7Ozs7OENBR2pGLDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDUiw4T0FBR0E7Z0RBQUNRLFdBQVU7Ozs7OzswREFDZiw4REFBQzREOzBEQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU1kLDhEQUFDL0YsaURBQU1BLENBQUNrQyxHQUFHO3dCQUNUbUMsU0FBUzs0QkFBRUMsU0FBUzs0QkFBRzBHLEdBQUc7d0JBQUc7d0JBQzdCekcsU0FBUzs0QkFBRUQsU0FBUzs0QkFBRzBHLEdBQUc7d0JBQUU7d0JBQzVCbkgsWUFBWTs0QkFBRW9ILE9BQU87d0JBQUs7d0JBQzFCOUksV0FBV04sOENBQUVBLENBQ1gsMkNBQ0EwRCxLQUFLQyxFQUFFLEtBQUsscUJBQ1IsMENBQ0E7OzBDQUdOLDhEQUFDdEQ7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDcEIsOE9BQUtBO3dDQUFDb0IsV0FBV04sOENBQUVBLENBQUMsV0FBVyxDQUFDLEtBQUssRUFBRTBELEtBQUtrRCxXQUFXLENBQUMsSUFBSSxDQUFDOzs7Ozs7a0RBQzlELDhEQUFDdkc7d0NBQUlDLFdBQVU7a0RBQ1o7NENBQUM7NENBQUc7NENBQUc7NENBQUc7NENBQUc7eUNBQUUsQ0FBQ0ksR0FBRyxDQUFDLENBQUM4SSxxQkFDcEIsOERBQUNuSjtnREFFQ0MsV0FBV04sOENBQUVBLENBQ1gsd0JBQ0F3SixRQUFRbEQsS0FBS0csS0FBSyxDQUFDL0MsS0FBS1csS0FBSyxDQUFDK0IsT0FBTyxHQUFHLEtBQ3BDLGtCQUNBOytDQUxEb0Q7Ozs7Ozs7Ozs7Ozs7Ozs7MENBV2IsOERBQUNqRztnQ0FBRWpELFdBQVU7O29DQUFzQ29ELEtBQUtXLEtBQUssQ0FBQytCLE9BQU87b0NBQUM7Ozs7Ozs7MENBQ3RFLDhEQUFDN0M7Z0NBQUVqRCxXQUFVOzBDQUF3Qjs7Ozs7Ozs7Ozs7O2tDQUd2Qyw4REFBQ25DLGlEQUFNQSxDQUFDa0MsR0FBRzt3QkFDVG1DLFNBQVM7NEJBQUVDLFNBQVM7NEJBQUcwRyxHQUFHO3dCQUFHO3dCQUM3QnpHLFNBQVM7NEJBQUVELFNBQVM7NEJBQUcwRyxHQUFHO3dCQUFFO3dCQUM1Qm5ILFlBQVk7NEJBQUVvSCxPQUFPO3dCQUFLO3dCQUMxQjlJLFdBQVdOLDhDQUFFQSxDQUNYLDJDQUNBMEQsS0FBS0MsRUFBRSxLQUFLLHFCQUNSLDBDQUNBOzswQ0FHTiw4REFBQ3REO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ3RCLDhPQUFLQTt3Q0FBQ3NCLFdBQVdOLDhDQUFFQSxDQUFDLFdBQVcsQ0FBQyxLQUFLLEVBQUUwRCxLQUFLa0QsV0FBVyxDQUFDLElBQUksQ0FBQzs7Ozs7O2tEQUM5RCw4REFBQ3hILDhPQUFRQTt3Q0FBQ2tCLFdBQVU7Ozs7Ozs7Ozs7OzswQ0FFdEIsOERBQUNpRDtnQ0FBRWpELFdBQVU7MENBQXNDb0QsS0FBS1csS0FBSyxDQUFDZ0MsT0FBTzs7Ozs7OzBDQUNyRSw4REFBQzlDO2dDQUFFakQsV0FBVTswQ0FBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUsvQztBQUVBLDBEQUEwRDtBQUMxRCxNQUFNNEksa0NBQW9CcEwsaURBQVUsQ0FBQyxDQUFDLEVBQUUwRCxLQUFLLEVBQUUwRCxXQUFXLEVBQUUrQyxjQUFjLEVBQUUzQyxTQUFTLEVBQUVFLGNBQWMsRUFNcEc7SUFDQyxxQkFDRSw4REFBQ3JILGlEQUFNQSxDQUFDa0MsR0FBRztRQUNUbUMsU0FBUztZQUFFQyxTQUFTO1lBQUdXLE9BQU87UUFBSztRQUNuQ1YsU0FBUztZQUFFRCxTQUFTO1lBQUdXLE9BQU87UUFBRTtRQUNoQ1QsTUFBTTtZQUFFRixTQUFTO1lBQUdXLE9BQU87UUFBSztRQUNoQ3BCLFlBQVk7WUFBRVksVUFBVTtZQUFLeUYsTUFBTTtRQUFVO1FBQzdDL0gsV0FBVTs7MEJBR0YsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FDckIsOERBQUNnRDt3QkFBR2hELFdBQVU7OzRCQUFxQzs0QkFBZTJIOzRCQUFpQjs7Ozs7OztrQ0FDbkYsOERBQUMxRTt3QkFBRWpELFdBQVU7a0NBQTZCOzs7Ozs7a0NBRTFDLDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FDWmtGLGlCQUNDLG1CQUFtQjt3QkFDbkJqRixNQUFNQyxJQUFJLENBQUM7NEJBQUVDLFFBQVE7d0JBQUUsR0FBR0MsR0FBRyxDQUFDLENBQUNDLEdBQUcySSxzQkFDaEMsOERBQUNuTCxpREFBTUEsQ0FBQ2tDLEdBQUc7Z0NBRVRtQyxTQUFTO29DQUFFQyxTQUFTO29DQUFHMEcsR0FBRztnQ0FBRztnQ0FDN0J6RyxTQUFTO29DQUFFRCxTQUFTO29DQUFHMEcsR0FBRztnQ0FBRTtnQ0FDNUJuSCxZQUFZO29DQUFFb0gsT0FBT0UsUUFBUTtnQ0FBSTtnQ0FDakNoSixXQUFVOztrREFFViw4REFBQ0Q7d0NBQUlDLFdBQVU7Ozs7OztrREFDZiw4REFBQ0Q7d0NBQUlDLFdBQVU7Ozs7OztrREFDZiw4REFBQ0Q7d0NBQUlDLFdBQVU7Ozs7Ozs7K0JBUlZnSjs7Ozs2Q0FZVDs0QkFDRTtnQ0FDRUcsT0FBTztnQ0FDUHpELE9BQU9WLFdBQVdvRSxjQUFjQyxjQUFjO2dDQUM5QzVGLE1BQU05RSw4T0FBUUE7Z0NBQ2QySyxRQUFRdEUsV0FBV3VFLFFBQVFDLGlCQUFpQjs0QkFDOUM7NEJBQ0E7Z0NBQ0VMLE9BQU87Z0NBQ1B6RCxPQUFPLEdBQUdWLFdBQVd5RSxrQkFBa0IsRUFBRSxJQUFJLENBQUM7Z0NBQzlDaEcsTUFBTS9FLDhPQUFLQTtnQ0FDWDRLLFFBQVF0RSxXQUFXdUUsUUFBUUcsa0JBQWtCOzRCQUMvQzs0QkFDQTtnQ0FDRVAsT0FBTztnQ0FDUHpELE9BQU8sR0FBR1YsV0FBVzJFLGdCQUFnQixJQUFJLEdBQUcsQ0FBQztnQ0FDN0NsRyxNQUFNN0UsOE9BQUtBO2dDQUNYMEssUUFBUXRFLFdBQVd1RSxRQUFRSyxzQkFBc0I7NEJBQ25EOzRCQUNBO2dDQUNFVCxPQUFPO2dDQUNQekQsT0FBT1YsV0FBV3VFLFFBQVFNLGFBQWFSLGNBQWM7Z0NBQ3JENUYsTUFBTTVFLDhPQUFNQTtnQ0FDWnlLLFFBQVEsTUFBT3RFLENBQUFBLFdBQVd1RSxRQUFRTSxlQUFlOzRCQUNuRDt5QkFDRCxDQUFDekosR0FBRyxDQUFDLENBQUMwSixNQUFNZCxzQkFDYiw4REFBQ25MLGlEQUFNQSxDQUFDa0MsR0FBRztnQ0FFVG1DLFNBQVM7b0NBQUVDLFNBQVM7b0NBQUcwRyxHQUFHO2dDQUFHO2dDQUM3QnpHLFNBQVM7b0NBQUVELFNBQVM7b0NBQUcwRyxHQUFHO2dDQUFFO2dDQUM1Qm5ILFlBQVk7b0NBQUVvSCxPQUFPRSxRQUFRO2dDQUFJO2dDQUNqQ2hKLFdBQVU7O2tEQUVWLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUM4SixLQUFLckcsSUFBSTtnREFBQ3pELFdBQVU7Ozs7OzswREFDckIsOERBQUM0RDtnREFBSzVELFdBQVU7MERBQTRCOEosS0FBS1IsTUFBTTs7Ozs7Ozs7Ozs7O2tEQUV6RCw4REFBQ3JHO3dDQUFFakQsV0FBVTtrREFBaUM4SixLQUFLcEUsS0FBSzs7Ozs7O2tEQUN4RCw4REFBQ3pDO3dDQUFFakQsV0FBVTtrREFBeUI4SixLQUFLWCxLQUFLOzs7Ozs7OytCQVgzQ1csS0FBS1gsS0FBSzs7Ozs7Ozs7Ozs7Ozs7OzswQkFtQnZCLDhEQUFDcEo7O2tDQUNDLDhEQUFDdUk7d0JBQUd0SSxXQUFVO2tDQUFxQzs7Ozs7O2tDQUNuRCw4REFBQ0Q7d0JBQUlDLFdBQVU7a0NBQ1prQixNQUFNZCxHQUFHLENBQUMsQ0FBQ2dELE1BQU00RixzQkFDaEIsOERBQUNqTCxrREFBSUE7Z0NBQWU2RSxNQUFNUSxLQUFLUixJQUFJOzBDQUNqQyw0RUFBQy9FLGlEQUFNQSxDQUFDa0MsR0FBRztvQ0FDVG1DLFNBQVM7d0NBQUVDLFNBQVM7d0NBQUdXLE9BQU87b0NBQUk7b0NBQ2xDVixTQUFTO3dDQUFFRCxTQUFTO3dDQUFHVyxPQUFPO29DQUFFO29DQUNoQ3BCLFlBQVk7d0NBQUVvSCxPQUFPRSxRQUFRO29DQUFJO29DQUNqQ25HLFlBQVk7d0NBQUVnRyxHQUFHLENBQUM7b0NBQUU7b0NBQ3BCN0ksV0FBVTs7c0RBRVYsOERBQUNEOzRDQUFJQyxXQUFXTiw4Q0FBRUEsQ0FDaEIsNEhBQ0EwRCxLQUFLSSxLQUFLOzs7Ozs7c0RBR1osOERBQUN6RDs0Q0FBSUMsV0FBV04sOENBQUVBLENBQ2hCLG1EQUNBMEQsS0FBS0MsRUFBRSxLQUFLLHFCQUNSLDZFQUNBOztnREFHSEQsS0FBS0MsRUFBRSxLQUFLLG9DQUNYLDhEQUFDdEQ7b0RBQUlDLFdBQVU7Ozs7Ozs4REFHakIsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0Q7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDRDtvRUFBSUMsV0FBV04sOENBQUVBLENBQ2hCLDZCQUNBMEQsS0FBS0MsRUFBRSxLQUFLLHFCQUNSLGtHQUNBM0QsOENBQUVBLENBQUMscUJBQXFCMEQsS0FBS0ksS0FBSzs4RUFFdEMsNEVBQUNKLEtBQUtLLElBQUk7d0VBQUN6RCxXQUFVOzs7Ozs7Ozs7Ozs4RUFFdkIsOERBQUMxQiw4T0FBVUE7b0VBQUMwQixXQUFVOzs7Ozs7Ozs7Ozs7c0VBR3hCLDhEQUFDK0o7NERBQUcvSixXQUFVO3NFQUF5Q29ELEtBQUtNLEtBQUs7Ozs7OztzRUFDakUsOERBQUNUOzREQUFFakQsV0FBVTtzRUFBOEJvRCxLQUFLVSxRQUFROzs7Ozs7c0VBRXhELDhEQUFDL0Q7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDakMsa0RBQUlBO29FQUNINkUsTUFBTSxDQUFDLGNBQWMsRUFBRVEsS0FBS0MsRUFBRSxLQUFLLHFCQUFxQix3QkFBd0JELEtBQUtDLEVBQUUsS0FBSyxtQkFBbUIsU0FBU0QsS0FBS0MsRUFBRSxLQUFLLG9CQUFvQixVQUFVRCxLQUFLQyxFQUFFLEtBQUssbUJBQW1CLG1CQUFtQkQsS0FBS0MsRUFBRSxDQUFDNEYsT0FBTyxDQUFDLEtBQUssTUFBTTtvRUFDL08xRyxTQUFTLENBQUNpRCxJQUFNQSxFQUFFd0UsZUFBZTtvRUFDakNoSyxXQUFVO29FQUNWMEQsT0FBTyxDQUFDLFNBQVMsRUFBRU4sS0FBS00sS0FBSyxDQUFDLFFBQVEsQ0FBQzs7c0ZBRXZDLDhEQUFDRTs0RUFBSzVELFdBQVU7c0ZBQStDOzs7Ozs7c0ZBQy9ELDhEQUFDRDs0RUFBSUMsV0FBVTs7OEZBQ2IsOERBQUNpRDtvRkFBRWpELFdBQVU7OEZBQTJEb0QsS0FBS1csS0FBSyxDQUFDQyxTQUFTOzs7Ozs7OEZBQzVGLDhEQUFDMUYsOE9BQVVBO29GQUFDMEIsV0FBVTs7Ozs7Ozs7Ozs7O3NGQUd4Qiw4REFBQ0Q7NEVBQUlDLFdBQVU7c0ZBQWlOOzs7Ozs7Ozs7Ozs7OEVBSWxPLDhEQUFDRDtvRUFBSUMsV0FBVTs7c0ZBQ2IsOERBQUM0RDs0RUFBSzVELFdBQVU7c0ZBQWdCOzs7Ozs7c0ZBQ2hDLDhEQUFDaUQ7NEVBQUVqRCxXQUFVOztnRkFBMEJvRCxLQUFLVyxLQUFLLENBQUMrQixPQUFPO2dGQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OytCQTNEM0QxQyxLQUFLQyxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7OzBCQXVFeEIsOERBQUN0RDtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0o7b0JBQXVCcUssT0FBTztvQkFBR0MsYUFBYTs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJdkQiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hYXl1c2htaXNocmEvRGVza3RvcC9vbGQgaW52aW5jaWJsZSB3aXRoIGRlZXByZXNlYXJjaC9zcmMvYXBwL2Rhc2hib2FyZC9wYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QsIFN1c3BlbnNlLCB1c2VDYWxsYmFjaywgdXNlTWVtbyB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgbW90aW9uLCBBbmltYXRlUHJlc2VuY2UgfSBmcm9tICdmcmFtZXItbW90aW9uJ1xuaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJ1xuaW1wb3J0IGR5bmFtaWMgZnJvbSAnbmV4dC9keW5hbWljJ1xuaW1wb3J0IHsgdXNlU2Vzc2lvbiB9IGZyb20gJ25leHQtYXV0aC9yZWFjdCdcbmltcG9ydCB7IHVzZVJvdXRlciB9IGZyb20gJ25leHQvbmF2aWdhdGlvbidcbmltcG9ydCB7IFxuICBTcGFya2xlcyxcbiAgWmFwLFxuICBHbG9iZSxcbiAgU2hpZWxkLFxuICBSb2NrZXQsXG4gIEFycm93UmlnaHQsXG4gIFBsdXMsXG4gIEZpbHRlcixcbiAgU2VhcmNoLFxuICBCZWxsLFxuICBHcmlkLFxuICBMaXN0LFxuICBDaGV2cm9uUmlnaHQsXG4gIFRyZW5kaW5nVXAsXG4gIENsb2NrLFxuICBGaWxlVGV4dCxcbiAgQXdhcmQsXG4gIExheWVycyxcbiAgQWN0aXZpdHksXG4gIENvbW1hbmQsXG4gIFNldHRpbmdzLFxuICBMb2dPdXQsXG4gIFVzZXIsXG4gIEhlbHBDaXJjbGUsXG4gIEhvbWUsXG4gIFBlblRvb2wsXG4gIE1haWwsXG4gIFNoYXJlMixcbiAgWW91dHViZSxcbiAgTWVudSxcbiAgWCxcbiAgQ2hldnJvbkxlZnQsXG4gIFBhbGV0dGUsXG4gIFdhbmQyLFxuICBMaWdodGJ1bGIsXG4gIFRhcmdldCxcbiAgQ29tcGFzcyxcbiAgQ3B1LFxuICBCYXJDaGFydCxcbiAgTWVzc2FnZVNxdWFyZSxcbiAgQ3Jvd24sXG4gIEVkaXQzLFxuICBWaWRlbyxcbiAgRXllLFxuICBCb3Rcbn0gZnJvbSAnbHVjaWRlLXJlYWN0J1xuaW1wb3J0IHsgY24gfSBmcm9tICdAL2xpYi91dGlscydcbmltcG9ydCBQcm9maWxlQnV0dG9uIGZyb20gJ0AvY29tcG9uZW50cy9Qcm9maWxlQnV0dG9uJ1xuXG4vLyBMYXp5IGxvYWQgUmVjZW50Q29udGVudCB0byByZWR1Y2UgaW5pdGlhbCBidW5kbGUgc2l6ZVxuY29uc3QgT3B0aW1pemVkUmVjZW50Q29udGVudCA9IGR5bmFtaWMoKCkgPT4gaW1wb3J0KCdAL2NvbXBvbmVudHMvZGFzaGJvYXJkL1JlY2VudENvbnRlbnQnKS50aGVuKG1vZCA9PiAoeyBkZWZhdWx0OiBSZWFjdC5tZW1vKG1vZC5kZWZhdWx0KSB9KSksIHtcbiAgc3NyOiBmYWxzZSxcbiAgbG9hZGluZzogKCkgPT4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICB7QXJyYXkuZnJvbSh7IGxlbmd0aDogMyB9KS5tYXAoKF8sIGkpID0+IChcbiAgICAgICAgPGRpdiBrZXk9e2l9IGNsYXNzTmFtZT1cImJnLXdoaXRlLzEwIHJvdW5kZWQtbGcgcC00IGFuaW1hdGUtcHVsc2VcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtNCBiZy13aGl0ZS8xMCByb3VuZGVkIG1iLTIgdy0zLzRcIj48L2Rpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtMyBiZy13aGl0ZS8xMCByb3VuZGVkIHctMS8yXCI+PC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKSl9XG4gICAgPC9kaXY+XG4gIClcbn0pXG5cbi8vIE9wdGltaXplZCBsYXp5IGxvYWRpbmcgd2l0aCBSZWFjdC5tZW1vIGFuZCByZWR1Y2VkIGJ1bmRsZSBzaXplXG5jb25zdCBPcHRpbWl6ZWRJbnZpbmNpYmxlT3JiID0gZHluYW1pYygoKSA9PiBpbXBvcnQoJ0AvY29tcG9uZW50cy9JbnZpbmNpYmxlT3JiJykudGhlbihtb2QgPT4gKHsgZGVmYXVsdDogUmVhY3QubWVtbyhtb2QuZGVmYXVsdCkgfSkpLCB7XG4gIHNzcjogZmFsc2UsXG4gIGxvYWRpbmc6ICgpID0+IDxkaXYgY2xhc3NOYW1lPVwidy0xNiBoLTE2IGJvcmRlci0yIGJvcmRlci12aW9sZXQtNTAwLzMwIGJvcmRlci10LXZpb2xldC01MDAgcm91bmRlZC1mdWxsIGFuaW1hdGUtc3BpbiBteC1hdXRvXCIgLz5cbn0pXG5cbmNvbnN0IE9wdGltaXplZEJsb2dQcmV2aWV3ID0gZHluYW1pYygoKSA9PiBpbXBvcnQoJ0AvY29tcG9uZW50cy9CbG9nUHJldmlldycpLnRoZW4obW9kID0+ICh7IGRlZmF1bHQ6IFJlYWN0Lm1lbW8obW9kLmRlZmF1bHQpIH0pKSwge1xuICBzc3I6IGZhbHNlLFxuICBsb2FkaW5nOiAoKSA9PiA8ZGl2IGNsYXNzTmFtZT1cInctMTYgaC0xNiBib3JkZXItMiBib3JkZXItcGluay01MDAvMzAgYm9yZGVyLXQtcGluay01MDAgcm91bmRlZC1mdWxsIGFuaW1hdGUtc3BpbiBteC1hdXRvXCIgLz5cbn0pXG5cbmNvbnN0IE9wdGltaXplZEVtYWlsUHJldmlldyA9IGR5bmFtaWMoKCkgPT4gaW1wb3J0KCdAL2NvbXBvbmVudHMvRW1haWxQcmV2aWV3JykudGhlbihtb2QgPT4gKHsgZGVmYXVsdDogUmVhY3QubWVtbyhtb2QuZGVmYXVsdCkgfSkpLCB7XG4gIHNzcjogZmFsc2UsXG4gIGxvYWRpbmc6ICgpID0+IDxkaXYgY2xhc3NOYW1lPVwidy0xNiBoLTE2IGJvcmRlci0yIGJvcmRlci1lbWVyYWxkLTUwMC8zMCBib3JkZXItdC1lbWVyYWxkLTUwMCByb3VuZGVkLWZ1bGwgYW5pbWF0ZS1zcGluIG14LWF1dG9cIiAvPlxufSlcblxuY29uc3QgT3B0aW1pemVkU29jaWFsTWVkaWFQcmV2aWV3ID0gZHluYW1pYygoKSA9PiBpbXBvcnQoJ0AvY29tcG9uZW50cy9Tb2NpYWxNZWRpYVByZXZpZXcnKS50aGVuKG1vZCA9PiAoeyBkZWZhdWx0OiBSZWFjdC5tZW1vKG1vZC5kZWZhdWx0KSB9KSksIHtcbiAgc3NyOiBmYWxzZSxcbiAgbG9hZGluZzogKCkgPT4gPGRpdiBjbGFzc05hbWU9XCJ3LTE2IGgtMTYgYm9yZGVyLTIgYm9yZGVyLWJsdWUtNTAwLzMwIGJvcmRlci10LWJsdWUtNTAwIHJvdW5kZWQtZnVsbCBhbmltYXRlLXNwaW4gbXgtYXV0b1wiIC8+XG59KVxuXG5jb25zdCBPcHRpbWl6ZWRWaWRlb1NjcmlwdFByZXZpZXcgPSBkeW5hbWljKCgpID0+IGltcG9ydCgnQC9jb21wb25lbnRzL1ZpZGVvU2NyaXB0UHJldmlldycpLnRoZW4obW9kID0+ICh7IGRlZmF1bHQ6IFJlYWN0Lm1lbW8obW9kLmRlZmF1bHQpIH0pKSwge1xuICBzc3I6IGZhbHNlLFxuICBsb2FkaW5nOiAoKSA9PiA8ZGl2IGNsYXNzTmFtZT1cInctMTYgaC0xNiBib3JkZXItMiBib3JkZXItcmVkLTUwMC8zMCBib3JkZXItdC1yZWQtNTAwIHJvdW5kZWQtZnVsbCBhbmltYXRlLXNwaW4gbXgtYXV0b1wiIC8+XG59KVxuXG5jb25zdCBPcHRpbWl6ZWRWaWRlb0FsY2hlbXlQcmV2aWV3ID0gZHluYW1pYygoKSA9PiBpbXBvcnQoJ0AvY29tcG9uZW50cy9WaWRlb0FsY2hlbXlQcmV2aWV3JykudGhlbihtb2QgPT4gKHsgZGVmYXVsdDogUmVhY3QubWVtbyhtb2QuZGVmYXVsdCkgfSkpLCB7XG4gIHNzcjogZmFsc2UsXG4gIGxvYWRpbmc6ICgpID0+IDxkaXYgY2xhc3NOYW1lPVwidy0xNiBoLTE2IGJvcmRlci0yIGJvcmRlci1wdXJwbGUtNTAwLzMwIGJvcmRlci10LXB1cnBsZS01MDAgcm91bmRlZC1mdWxsIGFuaW1hdGUtc3BpbiBteC1hdXRvXCIgLz5cbn0pXG5cbmNvbnN0IE9wdGltaXplZE1lZ2F0cm9uUHJldmlldyA9IGR5bmFtaWMoKCkgPT4gaW1wb3J0KCdAL2NvbXBvbmVudHMvTWVnYXRyb25QcmV2aWV3JykudGhlbihtb2QgPT4gKHsgZGVmYXVsdDogUmVhY3QubWVtbyhtb2QuZGVmYXVsdCkgfSkpLCB7XG4gIHNzcjogZmFsc2UsXG4gIGxvYWRpbmc6ICgpID0+IDxkaXYgY2xhc3NOYW1lPVwidy0xNiBoLTE2IGJvcmRlci0yIGJvcmRlci1yZWQtNTAwLzMwIGJvcmRlci10LXJlZC01MDAgcm91bmRlZC1mdWxsIGFuaW1hdGUtc3BpbiBteC1hdXRvXCIgLz5cbn0pXG5cblxuXG4vLyBPcHRpbWl6ZWQgU2lkZWJhciBDb21wb25lbnQgd2l0aCBNZW1vaXphdGlvblxuY29uc3QgRW5oYW5jZWRTaWRlYmFyID0gUmVhY3QubWVtbygoeyBcbiAgaXNPcGVuLCBcbiAgb25DbG9zZSwgXG4gIHRvb2xzLCBcbiAgc2VsZWN0ZWRUb29sLCBcbiAgc2V0U2VsZWN0ZWRUb29sLCBcbiAgaG92ZXJlZFRvb2wsIFxuICBzZXRIb3ZlcmVkVG9vbCBcbn06IHtcbiAgaXNPcGVuOiBib29sZWFuXG4gIG9uQ2xvc2U6ICgpID0+IHZvaWRcbiAgdG9vbHM6IFRvb2xbXVxuICBzZWxlY3RlZFRvb2w6IHN0cmluZ1xuICBzZXRTZWxlY3RlZFRvb2w6ICh0b29sOiBzdHJpbmcpID0+IHZvaWRcbiAgaG92ZXJlZFRvb2w6IHN0cmluZyB8IG51bGxcbiAgc2V0SG92ZXJlZFRvb2w6ICh0b29sOiBzdHJpbmcgfCBudWxsKSA9PiB2b2lkXG59KSA9PiB7XG4gIC8vIE1lbW9pemVkIGFuaW1hdGlvbiB2YXJpYW50cyBmb3IgYmV0dGVyIHBlcmZvcm1hbmNlXG4gIGNvbnN0IHNpZGViYXJWYXJpYW50cyA9IHVzZU1lbW8oKCkgPT4gKHtcbiAgICBvcGVuOiB7XG4gICAgICB4OiAwLFxuICAgICAgdHJhbnNpdGlvbjogeyB0eXBlOiBcInNwcmluZ1wiLCBzdGlmZm5lc3M6IDMwMCwgZGFtcGluZzogMzAgfVxuICAgIH0sXG4gICAgY2xvc2VkOiB7XG4gICAgICB4OiAtMzIwLFxuICAgICAgdHJhbnNpdGlvbjogeyB0eXBlOiBcInNwcmluZ1wiLCBzdGlmZm5lc3M6IDMwMCwgZGFtcGluZzogMzAgfVxuICAgIH1cbiAgfSksIFtdKVxuXG4gIC8vIE1lbW9pemVkIGhhbmRsZXJzIHRvIHByZXZlbnQgcmUtcmVuZGVyc1xuICBjb25zdCBoYW5kbGVUb29sU2VsZWN0ID0gdXNlQ2FsbGJhY2soKHRvb2xJZDogc3RyaW5nKSA9PiB7XG4gICAgc2V0U2VsZWN0ZWRUb29sKHRvb2xJZClcbiAgfSwgW3NldFNlbGVjdGVkVG9vbF0pXG5cbiAgY29uc3QgaGFuZGxlVG9vbEhvdmVyID0gdXNlQ2FsbGJhY2soKHRvb2xJZDogc3RyaW5nIHwgbnVsbCkgPT4ge1xuICAgIHNldEhvdmVyZWRUb29sKHRvb2xJZClcbiAgfSwgW3NldEhvdmVyZWRUb29sXSlcblxuICByZXR1cm4gKFxuICAgIDw+XG4gICAgICB7LyogT3B0aW1pemVkIE1vYmlsZSBCYWNrZHJvcCAqL31cbiAgICAgIDxBbmltYXRlUHJlc2VuY2U+XG4gICAgICAgIHtpc09wZW4gJiYgKFxuICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAgfX1cbiAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSB9fVxuICAgICAgICAgICAgZXhpdD17eyBvcGFjaXR5OiAwIH19XG4gICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjIgfX1cbiAgICAgICAgICAgIG9uQ2xpY2s9e29uQ2xvc2V9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJmaXhlZCBpbnNldC0wIGJnLWJsYWNrLzYwIGJhY2tkcm9wLWJsdXItc20gei00MCBsZzpoaWRkZW5cIlxuICAgICAgICAgIC8+XG4gICAgICAgICl9XG4gICAgICA8L0FuaW1hdGVQcmVzZW5jZT5cblxuICAgICAgey8qIE9wdGltaXplZCBTaWRlYmFyICovfVxuICAgICAgPG1vdGlvbi5hc2lkZVxuICAgICAgICBpbml0aWFsPVwiY2xvc2VkXCJcbiAgICAgICAgYW5pbWF0ZT17aXNPcGVuID8gXCJvcGVuXCIgOiBcImNsb3NlZFwifVxuICAgICAgICB2YXJpYW50cz17c2lkZWJhclZhcmlhbnRzfVxuICAgICAgICBjbGFzc05hbWU9XCJmaXhlZCBsZWZ0LTAgdG9wLTAgaC1zY3JlZW4gdy1bMzIwcHhdIHotNTAgbGc6ei0xMFwiXG4gICAgICAgIHN0eWxlPXt7IHdpbGxDaGFuZ2U6ICd0cmFuc2Zvcm0nIH19XG4gICAgICA+XG4gICAgICAgIHsvKiBTaW1wbGlmaWVkIEJhY2tncm91bmQgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBiZy1ibGFjay84NSBiYWNrZHJvcC1ibHVyLXhsIGJvcmRlci1yIGJvcmRlci13aGl0ZS8xMFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBiZy1ncmFkaWVudC10by1iciBmcm9tLXZpb2xldC05NTAvMTUgdmlhLWJsYWNrLzYwIHRvLWluZGlnby05NTAvMTVcIiAvPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogU2lkZWJhciBDb250ZW50ICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIHotMTAgaC1mdWxsIGZsZXggZmxleC1jb2xcIj5cbiAgICAgICAgICB7LyogU2ltcGxpZmllZCBMb2dvIFNlY3Rpb24gKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTYgYm9yZGVyLWIgYm9yZGVyLXdoaXRlLzEwXCI+XG4gICAgICAgICAgICA8TGluayBocmVmPVwiL1wiIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNCBncm91cFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIGJnLWdyYWRpZW50LXRvLXIgZnJvbS12aW9sZXQtNTAwIHRvLWluZGlnby01MDAgcm91bmRlZC14bCBibHVyLWxnIG9wYWNpdHktNTBcIiAvPlxuICAgICAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgICAgICB3aGlsZUhvdmVyPXt7IHNjYWxlOiAxLjA1IH19XG4gICAgICAgICAgICAgICAgICB3aGlsZVRhcD17eyBzY2FsZTogMC45NSB9fVxuICAgICAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC4yIH19XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJyZWxhdGl2ZSBiZy1ncmFkaWVudC10by1yIGZyb20tdmlvbGV0LTgwMCB0by1pbmRpZ28tODAwIHJvdW5kZWQteGwgcC0zIGJvcmRlciBib3JkZXItd2hpdGUvMjBcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxTcGFya2xlcyBjbGFzc05hbWU9XCJ3LTcgaC03IHRleHQtd2hpdGVcIiAvPlxuICAgICAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMVwiPlxuICAgICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC13aGl0ZVwiPkludmluY2libGU8L2gxPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTQwMFwiPkNyZWF0aXZlIEFJIFN1aXRlPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBPcHRpbWl6ZWQgTmF2aWdhdGlvbiAqL31cbiAgICAgICAgICA8bmF2IGNsYXNzTmFtZT1cImZsZXgtMSBwLTQgc3BhY2UteS0yIG92ZXJmbG93LXktYXV0byBjdXN0b20tc2Nyb2xsYmFyXCI+XG4gICAgICAgICAgICB7LyogRGFzaGJvYXJkIEhvbWUgKi99XG4gICAgICAgICAgICA8bW90aW9uLmJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVUb29sU2VsZWN0KCcnKX1cbiAgICAgICAgICAgICAgd2hpbGVIb3Zlcj17eyB4OiAyIH19XG4gICAgICAgICAgICAgIHdoaWxlVGFwPXt7IHNjYWxlOiAwLjk4IH19XG4gICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuMSB9fVxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgICAgICAgIFwidy1mdWxsIGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNCBweC00IHB5LTQgcm91bmRlZC14bCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgcmVsYXRpdmVcIixcbiAgICAgICAgICAgICAgICAhc2VsZWN0ZWRUb29sXG4gICAgICAgICAgICAgICAgICA/IFwiYmctd2hpdGUvMTUgdGV4dC13aGl0ZSBzaGFkb3ctbGcgYm9yZGVyIGJvcmRlci13aGl0ZS8yMFwiXG4gICAgICAgICAgICAgICAgICA6IFwidGV4dC1ncmF5LTQwMCBob3Zlcjp0ZXh0LXdoaXRlIGhvdmVyOmJnLXdoaXRlLzhcIlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC0yIHJvdW5kZWQtbGcgYmctd2hpdGUvMTBcIj5cbiAgICAgICAgICAgICAgICAgIDxIb21lIGNsYXNzTmFtZT1cInctNSBoLTVcIiAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1sZWZ0XCI+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkXCI+RGFzaGJvYXJkPC9wPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyBvcGFjaXR5LTcwXCI+T3ZlcnZpZXcgJiBTdGF0czwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICB7IXNlbGVjdGVkVG9vbCAmJiAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSByaWdodC0zIHctMSBoLTggYmctd2hpdGUgcm91bmRlZC1mdWxsXCIgLz5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvbW90aW9uLmJ1dHRvbj5cblxuICAgICAgICAgICAgey8qIFNlY3Rpb24gRGl2aWRlciAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHQtNiBwYi0zXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zIHB4LTRcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtcHggZmxleC0xIGJnLXdoaXRlLzIwXCIgLz5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTQwMCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXJcIj5cbiAgICAgICAgICAgICAgICAgIEFJIFRvb2xzXG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC1weCBmbGV4LTEgYmctd2hpdGUvMjBcIiAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogQ29udGVudCBMaWJyYXJ5ICovfVxuICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9jb250ZW50XCI+XG4gICAgICAgICAgICAgIDxtb3Rpb24uYnV0dG9uXG4gICAgICAgICAgICAgICAgd2hpbGVIb3Zlcj17eyB4OiAyIH19XG4gICAgICAgICAgICAgICAgd2hpbGVUYXA9e3sgc2NhbGU6IDAuOTggfX1cbiAgICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjIgfX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00IHB4LTQgcHktNCB0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtd2hpdGUgaG92ZXI6Ymctd2hpdGUvOCByb3VuZGVkLXhsIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtMiByb3VuZGVkLWxnIGJnLXdoaXRlLzVcIj5cbiAgICAgICAgICAgICAgICAgIDxMYXllcnMgY2xhc3NOYW1lPVwidy01IGgtNVwiIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWxlZnRcIj5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGRcIj5Db250ZW50IExpYnJhcnk8L3A+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIG9wYWNpdHktNzBcIj5WaWV3IFBhc3QgQ29udGVudDwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9tb3Rpb24uYnV0dG9uPlxuICAgICAgICAgICAgPC9MaW5rPlxuXG4gICAgICAgICAgICB7LyogT3B0aW1pemVkIFRvb2wgTmF2aWdhdGlvbiAqL31cbiAgICAgICAgICAgIHt0b29scy5tYXAoKHRvb2wpID0+IChcbiAgICAgICAgICAgICAgPG1vdGlvbi5idXR0b25cbiAgICAgICAgICAgICAgICBrZXk9e3Rvb2wuaWR9XG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlVG9vbFNlbGVjdCh0b29sLmlkKX1cbiAgICAgICAgICAgICAgICBvbk1vdXNlRW50ZXI9eygpID0+IGhhbmRsZVRvb2xIb3Zlcih0b29sLmlkKX1cbiAgICAgICAgICAgICAgICBvbk1vdXNlTGVhdmU9eygpID0+IGhhbmRsZVRvb2xIb3ZlcihudWxsKX1cbiAgICAgICAgICAgICAgICB3aGlsZUhvdmVyPXt7IHg6IDIgfX1cbiAgICAgICAgICAgICAgICB3aGlsZVRhcD17eyBzY2FsZTogMC45OCB9fVxuICAgICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuMSB9fVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgICAgICAgICBcInctZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTQgcHgtNCBweS00IHJvdW5kZWQteGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIHJlbGF0aXZlXCIsXG4gICAgICAgICAgICAgICAgICBzZWxlY3RlZFRvb2wgPT09IHRvb2wuaWRcbiAgICAgICAgICAgICAgICAgICAgPyBcImJnLWdyYWRpZW50LXRvLXIgdGV4dC13aGl0ZSBzaGFkb3ctbGcgYm9yZGVyIGJvcmRlci13aGl0ZS8yMFwiXG4gICAgICAgICAgICAgICAgICAgIDogXCJ0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtd2hpdGUgaG92ZXI6Ymctd2hpdGUvOFwiLFxuICAgICAgICAgICAgICAgICAgc2VsZWN0ZWRUb29sID09PSB0b29sLmlkICYmIHRvb2wuY29sb3JcbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgICAgICAgICAgXCJwLTIgcm91bmRlZC1sZyB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDBcIixcbiAgICAgICAgICAgICAgICAgICAgc2VsZWN0ZWRUb29sID09PSB0b29sLmlkXG4gICAgICAgICAgICAgICAgICAgICAgPyBcImJnLXdoaXRlLzIwXCJcbiAgICAgICAgICAgICAgICAgICAgICA6IFwiYmctd2hpdGUvMTBcIlxuICAgICAgICAgICAgICAgICAgKX0+XG4gICAgICAgICAgICAgICAgICAgIDx0b29sLmljb24gY2xhc3NOYW1lPVwidy01IGgtNVwiIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1sZWZ0XCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkXCI+e3Rvb2wudGl0bGV9PC9wPlxuICAgICAgICAgICAgICAgICAgICAgIHt0b29sLmJldGEgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwicHgtMS41IHB5LTAuNSB0ZXh0LXhzIGJnLWJsdWUtNTAwLzIwIHRleHQtYmx1ZS00MDAgcm91bmRlZCBib3JkZXIgYm9yZGVyLWJsdWUtNTAwLzMwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIEJFVEFcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgIHt0b29sLmNvbWluZ1Nvb24gJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwicHgtMS41IHB5LTAuNSB0ZXh0LXhzIGJnLWdyYXktNTAwLzIwIHRleHQtZ3JheS00MDAgcm91bmRlZCBib3JkZXIgYm9yZGVyLWdyYXktNTAwLzMwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIFNPT05cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyBvcGFjaXR5LTcwXCI+e3Rvb2wuc3VidGl0bGV9PC9wPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICB7c2VsZWN0ZWRUb29sID09PSB0b29sLmlkICYmIChcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgcmlnaHQtMyB3LTEgaC04IGJnLXdoaXRlIHJvdW5kZWQtZnVsbFwiIC8+XG4gICAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICAgIHsvKiBTaW1wbGlmaWVkIHN0YXRzIGRpc3BsYXkgKi99XG4gICAgICAgICAgICAgICAge2hvdmVyZWRUb29sID09PSB0b29sLmlkICYmIChcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgcmlnaHQtMyB0b3AtMyBiZy1ibGFjay82MCByb3VuZGVkLWZ1bGwgcHgtMiBweS0xIHRleHQteHMgdGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgICAgICAgICB7dG9vbC5zdGF0cy5nZW5lcmF0ZWR9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L21vdGlvbi5idXR0b24+XG4gICAgICAgICAgICApKX1cbiAgICAgICAgICA8L25hdj5cblxuICAgICAgICAgIHsvKiBTaW1wbGlmaWVkIEJvdHRvbSBBY3Rpb25zICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC00IGJvcmRlci10IGJvcmRlci13aGl0ZS8xMCBzcGFjZS15LTJcIj5cbiAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvc2V0dGluZ3NcIj5cbiAgICAgICAgICAgICAgPG1vdGlvbi5idXR0b25cbiAgICAgICAgICAgICAgICB3aGlsZUhvdmVyPXt7IHg6IDIgfX1cbiAgICAgICAgICAgICAgICB3aGlsZVRhcD17eyBzY2FsZTogMC45OCB9fVxuICAgICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuMiB9fVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTQgcHgtNCBweS0zIHRleHQtZ3JheS00MDAgaG92ZXI6dGV4dC13aGl0ZSBob3ZlcjpiZy13aGl0ZS84IHJvdW5kZWQteGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC0yIHJvdW5kZWQtbGcgYmctd2hpdGUvNVwiPlxuICAgICAgICAgICAgICAgICAgPFNldHRpbmdzIGNsYXNzTmFtZT1cInctNSBoLTVcIiAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+U2V0dGluZ3M8L3NwYW4+XG4gICAgICAgICAgICAgIDwvbW90aW9uLmJ1dHRvbj5cbiAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgIFxuICAgICAgICAgICAgPG1vdGlvbi5idXR0b25cbiAgICAgICAgICAgICAgd2hpbGVIb3Zlcj17eyB4OiAyIH19XG4gICAgICAgICAgICAgIHdoaWxlVGFwPXt7IHNjYWxlOiAwLjk4IH19XG4gICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuMiB9fVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00IHB4LTQgcHktMyB0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtd2hpdGUgaG92ZXI6Ymctd2hpdGUvOCByb3VuZGVkLXhsIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMFwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC0yIHJvdW5kZWQtbGcgYmctd2hpdGUvNVwiPlxuICAgICAgICAgICAgICAgIDxIZWxwQ2lyY2xlIGNsYXNzTmFtZT1cInctNSBoLTVcIiAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj5IZWxwICYgU3VwcG9ydDwvc3Bhbj5cbiAgICAgICAgICAgIDwvbW90aW9uLmJ1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L21vdGlvbi5hc2lkZT5cbiAgICA8Lz5cbiAgKVxufSlcblxuLy8gVXNlciBwcm9maWxlIGludGVyZmFjZVxuaW50ZXJmYWNlIFVzZXJQcm9maWxlIHtcbiAgaWQ6IHN0cmluZ1xuICBuYW1lOiBzdHJpbmcgfCBudWxsXG4gIGVtYWlsOiBzdHJpbmcgfCBudWxsXG4gIGltYWdlOiBzdHJpbmcgfCBudWxsXG4gIGZpcnN0TmFtZTogc3RyaW5nIHwgbnVsbFxuICBsYXN0TmFtZTogc3RyaW5nIHwgbnVsbFxuICBiaW86IHN0cmluZyB8IG51bGxcbiAgc3Vic2NyaXB0aW9uPzoge1xuICAgIHBsYW46IHN0cmluZ1xuICAgIHN0YXR1czogc3RyaW5nXG4gIH1cbiAgc3RhdHM/OiB7XG4gICAgdG90YWxDb250ZW50OiBudW1iZXJcbiAgICB0b3RhbFVzYWdlOiBudW1iZXJcbiAgfVxufVxuXG4vLyBUb29sIGludGVyZmFjZVxuaW50ZXJmYWNlIFRvb2wge1xuICBpZDogc3RyaW5nXG4gIHRpdGxlOiBzdHJpbmdcbiAgc3VidGl0bGU6IHN0cmluZ1xuICBkZXNjcmlwdGlvbjogc3RyaW5nXG4gIGljb246IGFueVxuICBjb2xvcjogc3RyaW5nXG4gIGJnR3JhZGllbnQ6IHN0cmluZ1xuICBhY2NlbnRDb2xvcjogc3RyaW5nXG4gIHN0YXRzOiB7XG4gICAgZ2VuZXJhdGVkOiBudW1iZXJcbiAgICBxdWFsaXR5OiBudW1iZXJcbiAgICBhdmdUaW1lOiBzdHJpbmdcbiAgfVxuICBmZWF0dXJlczogc3RyaW5nW11cbiAgaHJlZjogc3RyaW5nXG4gIHByZXZpZXc/OiBSZWFjdC5SZWFjdE5vZGVcbiAgY29taW5nU29vbj86IGJvb2xlYW5cbiAgYmV0YT86IGJvb2xlYW5cbn1cblxuLy8gT3B0aW1pemVkIERhc2hib2FyZCB3aXRoIFBlcmZvcm1hbmNlIEVuaGFuY2VtZW50c1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRGFzaGJvYXJkUGFnZSgpIHtcbiAgY29uc3QgeyBkYXRhOiBzZXNzaW9uLCBzdGF0dXMgfSA9IHVzZVNlc3Npb24oKVxuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKVxuICBjb25zdCBbc2VsZWN0ZWRUb29sLCBzZXRTZWxlY3RlZFRvb2xdID0gdXNlU3RhdGU8c3RyaW5nPignaW52aW5jaWJsZS1hZ2VudCcpXG4gIGNvbnN0IFtzaWRlYmFyT3Blbiwgc2V0U2lkZWJhck9wZW5dID0gdXNlU3RhdGUodHJ1ZSlcbiAgY29uc3QgW3NlYXJjaFF1ZXJ5LCBzZXRTZWFyY2hRdWVyeV0gPSB1c2VTdGF0ZSgnJylcbiAgY29uc3QgW3Nob3dOb3RpZmljYXRpb25zLCBzZXRTaG93Tm90aWZpY2F0aW9uc10gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW2hvdmVyZWRUb29sLCBzZXRIb3ZlcmVkVG9vbF0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKVxuICBjb25zdCBbdXNlclByb2ZpbGUsIHNldFVzZXJQcm9maWxlXSA9IHVzZVN0YXRlPFVzZXJQcm9maWxlIHwgbnVsbD4obnVsbClcbiAgY29uc3QgW2lzTG9hZGluZ1Byb2ZpbGUsIHNldElzTG9hZGluZ1Byb2ZpbGVdID0gdXNlU3RhdGUodHJ1ZSlcbiAgY29uc3QgW3VzZXJTdGF0cywgc2V0VXNlclN0YXRzXSA9IHVzZVN0YXRlPGFueT4obnVsbClcbiAgY29uc3QgW2lzTG9hZGluZ1N0YXRzLCBzZXRJc0xvYWRpbmdTdGF0c10gPSB1c2VTdGF0ZSh0cnVlKVxuXG4gIC8vIEFMTCB1c2VDYWxsYmFjayBob29rcyBtdXN0IGJlIGF0IHRoZSB0b3AgbGV2ZWwgYW5kIGluIGNvbnNpc3RlbnQgb3JkZXJcbiAgY29uc3QgdG9nZ2xlU2lkZWJhciA9IHVzZUNhbGxiYWNrKCgpID0+IHNldFNpZGViYXJPcGVuKHByZXYgPT4gIXByZXYpLCBbXSlcbiAgY29uc3QgdG9nZ2xlTm90aWZpY2F0aW9ucyA9IHVzZUNhbGxiYWNrKCgpID0+IHNldFNob3dOb3RpZmljYXRpb25zKHByZXYgPT4gIXByZXYpLCBbXSlcbiAgY29uc3QgaGFuZGxlU2VhcmNoQ2hhbmdlID0gdXNlQ2FsbGJhY2soKGU6IFJlYWN0LkNoYW5nZUV2ZW50PEhUTUxJbnB1dEVsZW1lbnQ+KSA9PiB7XG4gICAgc2V0U2VhcmNoUXVlcnkoZS50YXJnZXQudmFsdWUpXG4gIH0sIFtdKVxuXG4gIC8vIE1lbW9pemVkIHN0YXRzIGNhbGN1bGF0aW9uIGZvciB0b29scyB0byBwcmV2ZW50IHJlY2FsY3VsYXRpb24gb24gZXZlcnkgcmVuZGVyXG4gIGNvbnN0IGdldFRvb2xTdGF0cyA9IHVzZUNhbGxiYWNrKCh0b29sVHlwZTogc3RyaW5nKSA9PiB7XG4gICAgaWYgKCF1c2VyU3RhdHM/LmNvbnRlbnRCcmVha2Rvd24pIHtcbiAgICAgIHJldHVybiB7IGdlbmVyYXRlZDogMCwgcXVhbGl0eTogOS4wLCBhdmdUaW1lOiAnMCBtaW4nIH1cbiAgICB9XG5cbiAgICBjb25zdCBnZW5lcmF0ZWQgPSB1c2VyU3RhdHMuY29udGVudEJyZWFrZG93blt0b29sVHlwZV0gfHwgMFxuICAgIGNvbnN0IHF1YWxpdHkgPSBNYXRoLm1pbig5LjgsIDguNSArIChnZW5lcmF0ZWQgKiAwLjA1KSlcbiAgICBcbiAgICBjb25zdCBhdmdUaW1lczogUmVjb3JkPHN0cmluZywgc3RyaW5nPiA9IHtcbiAgICAgICdpbnZpbmNpYmxlX3Jlc2VhcmNoJzogJzQgbWluJyxcbiAgICAgICdibG9nJzogJzMgbWluJyxcbiAgICAgICdlbWFpbCc6ICcxIG1pbicsXG4gICAgICAnc29jaWFsX21lZGlhJzogJzMwIHNlYycsXG4gICAgICAneW91dHViZV9zY3JpcHQnOiAnNCBtaW4nLFxuICAgICAgJ3ZpZGVvX2FsY2hlbXknOiAnMyBtaW4nXG4gICAgfVxuXG4gICAgcmV0dXJuIHtcbiAgICAgIGdlbmVyYXRlZCxcbiAgICAgIHF1YWxpdHk6IE1hdGgucm91bmQocXVhbGl0eSAqIDEwKSAvIDEwLFxuICAgICAgYXZnVGltZTogYXZnVGltZXNbdG9vbFR5cGVdIHx8ICcyIG1pbidcbiAgICB9XG4gIH0sIFt1c2VyU3RhdHM/LmNvbnRlbnRCcmVha2Rvd25dKVxuXG4gIC8vIE1lbW9pemVkIHRvb2xzIGNvbmZpZ3VyYXRpb24gdG8gcHJldmVudCByZWNyZWF0aW9uIC0gTVVTVCBiZSBiZWZvcmUgYW55IGNvbmRpdGlvbmFsIHJldHVybnNcbiAgY29uc3QgdG9vbHM6IFRvb2xbXSA9IHVzZU1lbW8oKCkgPT4gW1xuICAgIHtcbiAgICAgIGlkOiAnaW52aW5jaWJsZS1hZ2VudCcsXG4gICAgICB0aXRsZTogJ0ludmluY2libGUgVi4xJyxcbiAgICAgIHN1YnRpdGxlOiAnU3VwZXJpb3IgQ29udGVudCcsXG4gICAgICBkZXNjcmlwdGlvbjogJ1JBRy1iYXNlZCBjb250ZW50IGdlbmVyYXRpb24gdGhhdCBhbmFseXplcyBjb21wZXRpdGlvbiwgdW5kZXJzdGFuZHMgaHVtYW4gd3JpdGluZyBwYXR0ZXJucywgYW5kIGNyZWF0ZXMgc3VwZXJpb3IgYXJ0aWNsZXMgdGhhdCBkb21pbmF0ZSBzZWFyY2ggcmVzdWx0cy4nLFxuICAgICAgaWNvbjogQ3Jvd24sXG4gICAgICBjb2xvcjogJ2Zyb20tdmlvbGV0LTgwMC84MCB0by1pbmRpZ28tODAwLzgwJyxcbiAgICAgIGJnR3JhZGllbnQ6ICdmcm9tLXZpb2xldC05NTAvMzAgdG8taW5kaWdvLTk1MC8zMCcsXG4gICAgICBhY2NlbnRDb2xvcjogJ3Zpb2xldCcsXG4gICAgICBzdGF0czogZ2V0VG9vbFN0YXRzKCdpbnZpbmNpYmxlX3Jlc2VhcmNoJyksXG4gICAgICBmZWF0dXJlczogWydSQUcgUmVzZWFyY2gnLCAnQ29tcGV0aXRpdmUgQW5hbHlzaXMnLCAnSHVtYW4gV3JpdGluZyBTdHlsZScsICdTRU8gT3B0aW1pemF0aW9uJywgJ1N1cGVyaW9yIFF1YWxpdHknLCAnS25vd2xlZGdlIEJhc2UnXSxcbiAgICAgIGhyZWY6ICcvaW52aW5jaWJsZScsXG4gICAgICBwcmV2aWV3OiA8T3B0aW1pemVkSW52aW5jaWJsZU9yYiAvPlxuICAgIH0sXG4gICAge1xuICAgICAgaWQ6ICdlbWFpbC1nZW5lcmF0b3InLFxuICAgICAgdGl0bGU6ICdFbWFpbCBHZW5lcmF0b3InLFxuICAgICAgc3VidGl0bGU6ICdQcm9mZXNzaW9uYWwgRW1haWxzJyxcbiAgICAgIGRlc2NyaXB0aW9uOiAnR2VuZXJhdGUgY29tcGVsbGluZyBlbWFpbCBjYW1wYWlnbnMsIG5ld3NsZXR0ZXJzLCBhbmQgcHJvZmVzc2lvbmFsIGNvbW11bmljYXRpb25zIHdpdGggQUktcG93ZXJlZCBwZXJzb25hbGl6YXRpb24uJyxcbiAgICAgIGljb246IE1haWwsXG4gICAgICBjb2xvcjogJ2Zyb20tZW1lcmFsZC01MDAgdG8tdGVhbC02MDAnLFxuICAgICAgYmdHcmFkaWVudDogJ2Zyb20tZW1lcmFsZC05NTAvMzAgdG8tdGVhbC05NTAvMzAnLFxuICAgICAgYWNjZW50Q29sb3I6ICdlbWVyYWxkJyxcbiAgICAgIHN0YXRzOiBnZXRUb29sU3RhdHMoJ2VtYWlsJyksXG4gICAgICBmZWF0dXJlczogWydQZXJzb25hbGl6YXRpb24nLCAnQS9CIFRlc3RpbmcnLCAnUHJvZmVzc2lvbmFsIFRvbmUnLCAnUXVpY2sgR2VuZXJhdGlvbiddLFxuICAgICAgaHJlZjogJy9lbWFpbC1nZW5lcmF0b3InXG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogJ3NvY2lhbC1tZWRpYS1nZW5lcmF0b3InLFxuICAgICAgdGl0bGU6ICdTb2NpYWwgTWVkaWEnLFxuICAgICAgc3VidGl0bGU6ICdWaXJhbCBDb250ZW50JyxcbiAgICAgIGRlc2NyaXB0aW9uOiAnQ3JlYXRlIGVuZ2FnaW5nIHNvY2lhbCBtZWRpYSBwb3N0cywgY2FwdGlvbnMsIGFuZCBjb250ZW50IHN0cmF0ZWdpZXMgdGhhdCByZXNvbmF0ZSB3aXRoIHlvdXIgYXVkaWVuY2UgYWNyb3NzIGFsbCBwbGF0Zm9ybXMuJyxcbiAgICAgIGljb246IFphcCxcbiAgICAgIGNvbG9yOiAnZnJvbS1waW5rLTUwMCB0by1yb3NlLTYwMCcsXG4gICAgICBiZ0dyYWRpZW50OiAnZnJvbS1waW5rLTk1MC8zMCB0by1yb3NlLTk1MC8zMCcsXG4gICAgICBhY2NlbnRDb2xvcjogJ3BpbmsnLFxuICAgICAgc3RhdHM6IGdldFRvb2xTdGF0cygnc29jaWFsX21lZGlhJyksXG4gICAgICBmZWF0dXJlczogWydNdWx0aS1QbGF0Zm9ybScsICdUcmVuZGluZyBIYXNodGFncycsICdFbmdhZ2VtZW50IE9wdGltaXphdGlvbicsICdRdWljayBHZW5lcmF0aW9uJ10sXG4gICAgICBocmVmOiAnL3NvY2lhbC1tZWRpYS1nZW5lcmF0b3InXG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogJ2Jsb2ctZ2VuZXJhdG9yJyxcbiAgICAgIHRpdGxlOiAnQmxvZyBHZW5lcmF0b3InLFxuICAgICAgc3VidGl0bGU6ICdTRU8gT3B0aW1pemVkJyxcbiAgICAgIGRlc2NyaXB0aW9uOiAnR2VuZXJhdGUgY29tcHJlaGVuc2l2ZSwgU0VPLW9wdGltaXplZCBibG9nIHBvc3RzIHdpdGggcmVzZWFyY2gsIHByb3BlciBzdHJ1Y3R1cmUsIGFuZCBlbmdhZ2luZyBjb250ZW50IHRoYXQgcmFua3Mgd2VsbC4nLFxuICAgICAgaWNvbjogRmlsZVRleHQsXG4gICAgICBjb2xvcjogJ2Zyb20tYmx1ZS01MDAgdG8taW5kaWdvLTYwMCcsXG4gICAgICBiZ0dyYWRpZW50OiAnZnJvbS1ibHVlLTk1MC8zMCB0by1pbmRpZ28tOTUwLzMwJyxcbiAgICAgIGFjY2VudENvbG9yOiAnYmx1ZScsXG4gICAgICBzdGF0czogZ2V0VG9vbFN0YXRzKCdibG9nJyksXG4gICAgICBmZWF0dXJlczogWydTRU8gT3B0aW1pemF0aW9uJywgJ1Jlc2VhcmNoIEludGVncmF0aW9uJywgJ0xvbmctZm9ybSBDb250ZW50JywgJ1Byb2Zlc3Npb25hbCBTdHJ1Y3R1cmUnXSxcbiAgICAgIGhyZWY6ICcvYmxvZy1nZW5lcmF0b3InXG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogJ3lvdXR1YmUtc2NyaXB0JyxcbiAgICAgIHRpdGxlOiAnWW91VHViZSBTY3JpcHRzJyxcbiAgICAgIHN1YnRpdGxlOiAnVmlkZW8gQ29udGVudCcsXG4gICAgICBkZXNjcmlwdGlvbjogJ0NyZWF0ZSBjb21wZWxsaW5nIFlvdVR1YmUgdmlkZW8gc2NyaXB0cyB3aXRoIGhvb2tzLCBlbmdhZ2VtZW50IHRlY2huaXF1ZXMsIGFuZCBzdHJ1Y3R1cmVkIGNvbnRlbnQgZm9yIG1heGltdW0gdmlld2VyIHJldGVudGlvbi4nLFxuICAgICAgaWNvbjogVmlkZW8sXG4gICAgICBjb2xvcjogJ2Zyb20tcmVkLTUwMCB0by1vcmFuZ2UtNjAwJyxcbiAgICAgIGJnR3JhZGllbnQ6ICdmcm9tLXJlZC05NTAvMzAgdG8tb3JhbmdlLTk1MC8zMCcsXG4gICAgICBhY2NlbnRDb2xvcjogJ3JlZCcsXG4gICAgICBzdGF0czogZ2V0VG9vbFN0YXRzKCd5b3V0dWJlX3NjcmlwdCcpLFxuICAgICAgZmVhdHVyZXM6IFsnSG9vayBHZW5lcmF0aW9uJywgJ1JldGVudGlvbiBPcHRpbWl6YXRpb24nLCAnQ1RBIEludGVncmF0aW9uJywgJ1NjcmlwdCBTdHJ1Y3R1cmUnXSxcbiAgICAgIGhyZWY6ICcveW91dHViZS1zY3JpcHQnXG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogJ3ZpZGVvLWFsY2hlbXknLFxuICAgICAgdGl0bGU6ICdWaWRlbyBBbGNoZW15JyxcbiAgICAgIHN1YnRpdGxlOiAnQ29taW5nIFNvb24nLFxuICAgICAgZGVzY3JpcHRpb246ICdUcmFuc2Zvcm0geW91ciBpZGVhcyBpbnRvIHN0dW5uaW5nIHZpZGVvIGNvbnRlbnQgd2l0aCBBSS1wb3dlcmVkIHZpZGVvIGdlbmVyYXRpb24gYW5kIGVkaXRpbmcgY2FwYWJpbGl0aWVzLicsXG4gICAgICBpY29uOiBTcGFya2xlcyxcbiAgICAgIGNvbG9yOiAnZnJvbS1wdXJwbGUtNTAwIHRvLXZpb2xldC02MDAnLFxuICAgICAgYmdHcmFkaWVudDogJ2Zyb20tcHVycGxlLTk1MC8zMCB0by12aW9sZXQtOTUwLzMwJyxcbiAgICAgIGFjY2VudENvbG9yOiAncHVycGxlJyxcbiAgICAgIHN0YXRzOiBnZXRUb29sU3RhdHMoJ3ZpZGVvX2FsY2hlbXknKSxcbiAgICAgIGZlYXR1cmVzOiBbJ0FJIFZpZGVvIEdlbmVyYXRpb24nLCAnQXV0byBFZGl0aW5nJywgJ1N0eWxlIFRyYW5zZmVyJywgJ1F1aWNrIEV4cG9ydCddLFxuICAgICAgaHJlZjogJyMnLFxuICAgICAgY29taW5nU29vbjogdHJ1ZVxuICAgIH0sXG4gICAge1xuICAgICAgaWQ6ICdtZWdhdHJvbicsXG4gICAgICB0aXRsZTogJ01lZ2F0cm9uJyxcbiAgICAgIHN1YnRpdGxlOiAnVWx0aW1hdGUgQUknLFxuICAgICAgZGVzY3JpcHRpb246ICdUaGUgbW9zdCBwb3dlcmZ1bCBBSSBhc3Npc3RhbnQgZm9yIGNvbXBsZXggdGFza3MsIHJlc2VhcmNoLCBhbmFseXNpcywgYW5kIGNyZWF0aXZlIHByb2plY3RzIHdpdGggdW5saW1pdGVkIHBvdGVudGlhbC4nLFxuICAgICAgaWNvbjogQm90LFxuICAgICAgY29sb3I6ICdmcm9tLWdyYXktNjAwIHRvLXNsYXRlLTcwMCcsXG4gICAgICBiZ0dyYWRpZW50OiAnZnJvbS1ncmF5LTk1MC8zMCB0by1zbGF0ZS05NTAvMzAnLFxuICAgICAgYWNjZW50Q29sb3I6ICdncmF5JyxcbiAgICAgIHN0YXRzOiBnZXRUb29sU3RhdHMoJ21lZ2F0cm9uJyksXG4gICAgICBmZWF0dXJlczogWydBZHZhbmNlZCBSZWFzb25pbmcnLCAnTXVsdGktdGFzayBIYW5kbGluZycsICdSZXNlYXJjaCBDYXBhYmlsaXRpZXMnLCAnQ3JlYXRpdmUgU29sdXRpb25zJ10sXG4gICAgICBocmVmOiAnIycsXG4gICAgICBjb21pbmdTb29uOiB0cnVlXG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogJ2ludmluY2libGUtdjMnLFxuICAgICAgdGl0bGU6ICdJbnZpbmNpYmxlIFYzJyxcbiAgICAgIHN1YnRpdGxlOiAnTmV4dC1HZW4gU3lzdGVtJyxcbiAgICAgIGRlc2NyaXB0aW9uOiAnUmV2b2x1dGlvbmFyeSBMYW5nR3JhcGgtcG93ZXJlZCB3b3JrZmxvdyB3aXRoIGVuaGFuY2VkIHJlc2VhcmNoLCBTRU8vR0VPIG9wdGltaXphdGlvbiwgYW5kIEFJIGRldGVjdGlvbiBieXBhc3MuJyxcbiAgICAgIGljb246IENwdSxcbiAgICAgIGNvbG9yOiAnZnJvbS1ibHVlLTYwMCB0by1wdXJwbGUtNzAwJyxcbiAgICAgIGJnR3JhZGllbnQ6ICdmcm9tLWJsdWUtOTUwLzMwIHRvLXB1cnBsZS05NTAvMzAnLFxuICAgICAgYWNjZW50Q29sb3I6ICdibHVlJyxcbiAgICAgIHN0YXRzOiB7IGdlbmVyYXRlZDogMCwgcXVhbGl0eTogOS41LCBhdmdUaW1lOiAnMyBtaW4nIH0sXG4gICAgICBmZWF0dXJlczogWydMYW5nR3JhcGggV29ya2Zsb3cnLCAnUmVhbC10aW1lIFByb2dyZXNzJywgJ0VuaGFuY2VkIFJlc2VhcmNoJywgJ0dFTyBPcHRpbWl6YXRpb24nLCAnQUkgQnlwYXNzJywgJ1F1YWxpdHkgQXNzdXJhbmNlJ10sXG4gICAgICBocmVmOiAnL2ludmluY2libGUtdjMnLFxuICAgICAgYmV0YTogdHJ1ZVxuICAgIH1cbiAgXSwgW2dldFRvb2xTdGF0c10pXG5cbiAgLy8gUmVkaXJlY3QgdG8gbG9naW4gaWYgbm90IGF1dGhlbnRpY2F0ZWRcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoc3RhdHVzID09PSAndW5hdXRoZW50aWNhdGVkJykge1xuICAgICAgcm91dGVyLnB1c2goJy9sb2dpbicpXG4gICAgfVxuICB9LCBbc3RhdHVzLCByb3V0ZXJdKVxuXG4gIC8vIEZldGNoIHVzZXIgcHJvZmlsZSBkYXRhXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHNlc3Npb24/LnVzZXIpIHtcbiAgICAgIGZldGNoVXNlclByb2ZpbGUoKVxuICAgICAgZmV0Y2hVc2VyU3RhdHMoKVxuICAgIH1cbiAgfSwgW3Nlc3Npb25dKVxuXG4gIGNvbnN0IGZldGNoVXNlclByb2ZpbGUgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvdXNlci9wcm9maWxlJylcbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpXG4gICAgICAgIHNldFVzZXJQcm9maWxlKGRhdGEpXG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIHVzZXIgcHJvZmlsZTonLCBlcnJvcilcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNMb2FkaW5nUHJvZmlsZShmYWxzZSlcbiAgICB9XG4gIH1cblxuICBjb25zdCBmZXRjaFVzZXJTdGF0cyA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9zdGF0cycpXG4gICAgICBpZiAocmVzcG9uc2Uub2spIHtcbiAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKVxuICAgICAgICBzZXRVc2VyU3RhdHMoZGF0YS5zdGF0cylcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgdXNlciBzdGF0czonLCBlcnJvcilcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNMb2FkaW5nU3RhdHMoZmFsc2UpXG4gICAgfVxuICB9XG5cbiAgLy8gR2VuZXJhdGUgdXNlciBpbml0aWFscyBmb3IgYXZhdGFyXG4gIGNvbnN0IGdldFVzZXJJbml0aWFscyA9ICgpID0+IHtcbiAgICBpZiAodXNlclByb2ZpbGU/LmZpcnN0TmFtZSAmJiB1c2VyUHJvZmlsZT8ubGFzdE5hbWUpIHtcbiAgICAgIHJldHVybiBgJHt1c2VyUHJvZmlsZS5maXJzdE5hbWVbMF19JHt1c2VyUHJvZmlsZS5sYXN0TmFtZVswXX1gXG4gICAgfSBlbHNlIGlmICh1c2VyUHJvZmlsZT8ubmFtZSkge1xuICAgICAgY29uc3QgbmFtZXMgPSB1c2VyUHJvZmlsZS5uYW1lLnNwbGl0KCcgJylcbiAgICAgIHJldHVybiBuYW1lcy5sZW5ndGggPiAxID8gYCR7bmFtZXNbMF1bMF19JHtuYW1lc1tuYW1lcy5sZW5ndGggLSAxXVswXX1gIDogbmFtZXNbMF1bMF1cbiAgICB9IGVsc2UgaWYgKHVzZXJQcm9maWxlPy5lbWFpbCkge1xuICAgICAgcmV0dXJuIHVzZXJQcm9maWxlLmVtYWlsWzBdLnRvVXBwZXJDYXNlKClcbiAgICB9XG4gICAgcmV0dXJuICdVJ1xuICB9XG5cbiAgLy8gR2V0IGRpc3BsYXkgbmFtZVxuICBjb25zdCBnZXREaXNwbGF5TmFtZSA9ICgpID0+IHtcbiAgICBpZiAodXNlclByb2ZpbGU/LmZpcnN0TmFtZSAmJiB1c2VyUHJvZmlsZT8ubGFzdE5hbWUpIHtcbiAgICAgIHJldHVybiBgJHt1c2VyUHJvZmlsZS5maXJzdE5hbWV9ICR7dXNlclByb2ZpbGUubGFzdE5hbWV9YFxuICAgIH0gZWxzZSBpZiAodXNlclByb2ZpbGU/Lm5hbWUpIHtcbiAgICAgIHJldHVybiB1c2VyUHJvZmlsZS5uYW1lXG4gICAgfSBlbHNlIGlmICh1c2VyUHJvZmlsZT8uZW1haWwpIHtcbiAgICAgIHJldHVybiB1c2VyUHJvZmlsZS5lbWFpbC5zcGxpdCgnQCcpWzBdXG4gICAgfVxuICAgIHJldHVybiAnVXNlcidcbiAgfVxuXG4gIC8vIExvYWRpbmcgc3RhdGVcbiAgaWYgKHN0YXR1cyA9PT0gJ2xvYWRpbmcnIHx8IGlzTG9hZGluZ1Byb2ZpbGUpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1ncmF5LTkwMCB2aWEtYmxhY2sgdG8tZ3JheS05MDAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBzcGFjZS15LTRcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiB3LTEyIGgtMTIgYm9yZGVyLTIgYm9yZGVyLXZpb2xldC00MDAgYm9yZGVyLXQtdHJhbnNwYXJlbnQgcm91bmRlZC1mdWxsIG14LWF1dG9cIj48L2Rpdj5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwXCI+TG9hZGluZyB5b3VyIGRhc2hib2FyZC4uLjwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApXG4gIH1cblxuICAvLyBEb24ndCByZW5kZXIgaWYgbm90IGF1dGhlbnRpY2F0ZWRcbiAgaWYgKHN0YXR1cyA9PT0gJ3VuYXV0aGVudGljYXRlZCcpIHtcbiAgICByZXR1cm4gbnVsbFxuICB9XG5cbiAgY29uc3QgYWN0aXZlVG9vbCA9IHRvb2xzLmZpbmQodCA9PiB0LmlkID09PSBzZWxlY3RlZFRvb2wpIHx8IHRvb2xzWzBdXG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ibGFja1wiPlxuICAgICAgey8qIEFuaW1hdGVkIEJhY2tncm91bmQgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZpeGVkIGluc2V0LTAgei0wXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBiZy1ncmFkaWVudC10by1iciBmcm9tLXZpb2xldC05NTAvMTAgdmlhLWJsYWNrIHRvLWluZGlnby05NTAvMTBcIiAvPlxuICAgICAgICBcbiAgICAgICAgey8qIER5bmFtaWMgZ3JhZGllbnQgYmFzZWQgb24gc2VsZWN0ZWQgdG9vbCAqL31cbiAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICBrZXk9e2FjdGl2ZVRvb2wuaWR9XG4gICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwIH19XG4gICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxIH19XG4gICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC41LCBlYXNlOiBbMC40LCAwLCAwLjIsIDFdIH19XG4gICAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgIFwiYWJzb2x1dGUgaW5zZXQtMCBiZy1ncmFkaWVudC10by1iciBvcGFjaXR5LTIwXCIsXG4gICAgICAgICAgICBhY3RpdmVUb29sLmJnR3JhZGllbnRcbiAgICAgICAgICApfVxuICAgICAgICAvPlxuICAgICAgICBcbiAgICAgICAgey8qIE9wdGltaXplZCBzdGF0aWMgb3JicyAtIHJlbW92ZWQgaGVhdnkgYW5pbWF0aW9ucyAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtMS80IGxlZnQtMS80IHctWzUwMHB4XSBoLVs1MDBweF0gYmctdmlvbGV0LTcwMC8xMCByb3VuZGVkLWZ1bGwgYmx1ci1bMTAwcHhdIGFuaW1hdGUtcHVsc2VcIiAvPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGJvdHRvbS0xLzQgcmlnaHQtMS80IHctWzYwMHB4XSBoLVs2MDBweF0gYmctaW5kaWdvLTcwMC8xMCByb3VuZGVkLWZ1bGwgYmx1ci1bMTIwcHhdIGFuaW1hdGUtcHVsc2VcIiBzdHlsZT17eyBhbmltYXRpb25EZWxheTogJzFzJyB9fSAvPlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBNYWluIExheW91dCAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgei0xMCBmbGV4IG1pbi1oLXNjcmVlblwiPlxuICAgICAgICB7LyogRW5oYW5jZWQgU2lkZWJhciAqL31cbiAgICAgICAgPEVuaGFuY2VkU2lkZWJhclxuICAgICAgICAgIGlzT3Blbj17c2lkZWJhck9wZW59XG4gICAgICAgICAgb25DbG9zZT17KCkgPT4gc2V0U2lkZWJhck9wZW4oZmFsc2UpfVxuICAgICAgICAgIHRvb2xzPXt0b29sc31cbiAgICAgICAgICBzZWxlY3RlZFRvb2w9e3NlbGVjdGVkVG9vbH1cbiAgICAgICAgICBzZXRTZWxlY3RlZFRvb2w9e3NldFNlbGVjdGVkVG9vbH1cbiAgICAgICAgICBob3ZlcmVkVG9vbD17aG92ZXJlZFRvb2x9XG4gICAgICAgICAgc2V0SG92ZXJlZFRvb2w9e3NldEhvdmVyZWRUb29sfVxuICAgICAgICAvPlxuXG4gICAgICAgIHsvKiBPcHRpbWl6ZWQgU2lkZWJhciBUb2dnbGUgKi99XG4gICAgICAgIDxtb3Rpb24uYnV0dG9uXG4gICAgICAgICAgb25DbGljaz17dG9nZ2xlU2lkZWJhcn1cbiAgICAgICAgICBhbmltYXRlPXt7IFxuICAgICAgICAgICAgbGVmdDogc2lkZWJhck9wZW4gPyAzMDggOiAyMFxuICAgICAgICAgIH19XG4gICAgICAgICAgdHJhbnNpdGlvbj17eyBcbiAgICAgICAgICAgIHR5cGU6IFwic3ByaW5nXCIsXG4gICAgICAgICAgICBzdGlmZm5lc3M6IDMwMCxcbiAgICAgICAgICAgIGRhbXBpbmc6IDMwXG4gICAgICAgICAgfX1cbiAgICAgICAgICB3aGlsZUhvdmVyPXt7IHNjYWxlOiAxLjAyIH19XG4gICAgICAgICAgd2hpbGVUYXA9e3sgc2NhbGU6IDAuOTggfX1cbiAgICAgICAgICBjbGFzc05hbWU9XCJmaXhlZCB0b3AtNiB6LTUwIHAtMyBiZy1ibGFjay84MCBiYWNrZHJvcC1ibHVyLXNtIGJvcmRlciBib3JkZXItd2hpdGUvMjAgcm91bmRlZC14bCBob3ZlcjpiZy1ibGFjay85MCB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDBcIlxuICAgICAgICAgIHN0eWxlPXt7IHdpbGxDaGFuZ2U6ICd0cmFuc2Zvcm0nIH19XG4gICAgICAgID5cbiAgICAgICAgICB7c2lkZWJhck9wZW4gPyA8Q2hldnJvbkxlZnQgY2xhc3NOYW1lPVwidy01IGgtNSB0ZXh0LXdoaXRlXCIgLz4gOiA8TWVudSBjbGFzc05hbWU9XCJ3LTUgaC01IHRleHQtd2hpdGVcIiAvPn1cbiAgICAgICAgPC9tb3Rpb24uYnV0dG9uPlxuXG4gICAgICAgIHsvKiBNYWluIENvbnRlbnQgQXJlYSAqL31cbiAgICAgICAgPG1vdGlvbi5tYWluXG4gICAgICAgICAgYW5pbWF0ZT17eyBcbiAgICAgICAgICAgIG1hcmdpbkxlZnQ6IHNpZGViYXJPcGVuID8gMzIwIDogMFxuICAgICAgICAgIH19XG4gICAgICAgICAgdHJhbnNpdGlvbj17eyBcbiAgICAgICAgICAgIHR5cGU6IFwidHdlZW5cIixcbiAgICAgICAgICAgIGR1cmF0aW9uOiAwLjMsXG4gICAgICAgICAgICBlYXNlOiBbMC40LCAwLCAwLjIsIDFdXG4gICAgICAgICAgfX1cbiAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4LTEgbWluLWgtc2NyZWVuXCJcbiAgICAgICAgICBzdHlsZT17eyB3aWxsQ2hhbmdlOiAnbWFyZ2luLWxlZnQnIH19XG4gICAgICAgID5cbiAgICAgICAgICB7LyogVG9wIEJhciAqL31cbiAgICAgICAgICAgICAgICAgICAgICA8aGVhZGVyIGNsYXNzTmFtZT1cInN0aWNreSB0b3AtMCB6LTQwIGJhY2tkcm9wLWJsdXIteGwgYmctYmxhY2svNjAgYm9yZGVyLWIgYm9yZGVyLXdoaXRlLzEwXCI+XG4gICAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgICAgYW5pbWF0ZT17eyBwYWRkaW5nTGVmdDogc2lkZWJhck9wZW4gPyAzMiA6IDgwIH19XG4gICAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBcbiAgICAgICAgICAgICAgICAgIHR5cGU6IFwidHdlZW5cIixcbiAgICAgICAgICAgICAgICAgIGR1cmF0aW9uOiAwLjMsXG4gICAgICAgICAgICAgICAgICBlYXNlOiBbMC40LCAwLCAwLjIsIDFdXG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC04IHB5LTZcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LThcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgICAgICAgICAgICAge3NlbGVjdGVkVG9vbCA/IGFjdGl2ZVRvb2wudGl0bGUgOiAnRGFzaGJvYXJkIE92ZXJ2aWV3J31cbiAgICAgICAgICAgICAgICAgICAgPC9oMj5cbiAgICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICAgIHsvKiBPcHRpbWl6ZWQgU2VhcmNoICovfVxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPFNlYXJjaCBjbGFzc05hbWU9XCJhYnNvbHV0ZSBsZWZ0LTQgdG9wLTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS15LTEvMiB3LTUgaC01IHRleHQtZ3JheS00MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJTZWFyY2ggdG9vbHMsIGZlYXR1cmVzLi4uXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtzZWFyY2hRdWVyeX1cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVTZWFyY2hDaGFuZ2V9XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LTk2IHBsLTEyIHByLTQgcHktMyBiZy13aGl0ZS8xMCBiYWNrZHJvcC1ibHVyLXNtIGJvcmRlciBib3JkZXItd2hpdGUvMjAgcm91bmRlZC14bCB0ZXh0LXdoaXRlIHBsYWNlaG9sZGVyLWdyYXktNDAwIGZvY3VzOmJnLXdoaXRlLzE1IGZvY3VzOmJvcmRlci12aW9sZXQtNTAwLzUwIGZvY3VzOm91dGxpbmUtbm9uZSB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDBcIlxuICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgey8qIE9wdGltaXplZCBBY3Rpb25zICovfVxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInJlbGF0aXZlIHAtMyB0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtd2hpdGUgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwIGJnLXdoaXRlLzEwIGJhY2tkcm9wLWJsdXItc20gYm9yZGVyIGJvcmRlci13aGl0ZS8yMCByb3VuZGVkLXhsIGhvdmVyOmJnLXdoaXRlLzE1XCJcbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXt0b2dnbGVOb3RpZmljYXRpb25zfVxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgPEJlbGwgY2xhc3NOYW1lPVwidy01IGgtNVwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTIgcmlnaHQtMiB3LTIgaC0yIGJnLXZpb2xldC01MDAgcm91bmRlZC1mdWxsXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTggdy1weCBiZy13aGl0ZS8yMFwiIC8+XG5cbiAgICAgICAgICAgICAgICAgICAgPFByb2ZpbGVCdXR0b24gXG4gICAgICAgICAgICAgICAgICAgICAgdXNlclByb2ZpbGU9e3VzZXJQcm9maWxlfSBcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwbC0wXCJcbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICA8L2hlYWRlcj5cblxuICAgICAgICAgICAgICAgICAgICAgIHsvKiBPcHRpbWl6ZWQgQ29udGVudCBBcmVhICovfVxuICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgYW5pbWF0ZT17eyBcbiAgICAgICAgICAgICAgICBwYWRkaW5nTGVmdDogc2lkZWJhck9wZW4gPyAzMiA6IDgwXG4gICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgXG4gICAgICAgICAgICAgICAgdHlwZTogXCJ0d2VlblwiLFxuICAgICAgICAgICAgICAgIGR1cmF0aW9uOiAwLjMsXG4gICAgICAgICAgICAgICAgZWFzZTogWzAuNCwgMCwgMC4yLCAxXVxuICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTggcGItMTYgcHItOFwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICA8QW5pbWF0ZVByZXNlbmNlIG1vZGU9XCJ3YWl0XCI+XG4gICAgICAgICAgICAgIHtzZWxlY3RlZFRvb2wgPyAoXG4gICAgICAgICAgICAgICAgPFRvb2xEZXRhaWxzIGtleT17c2VsZWN0ZWRUb29sfSB0b29sPXthY3RpdmVUb29sfSAvPlxuICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgIDxEYXNoYm9hcmRPdmVydmlldyBrZXk9XCJvdmVydmlld1wiIHRvb2xzPXt0b29sc30gdXNlclByb2ZpbGU9e3VzZXJQcm9maWxlfSBnZXREaXNwbGF5TmFtZT17Z2V0RGlzcGxheU5hbWV9IHVzZXJTdGF0cz17dXNlclN0YXRzfSBpc0xvYWRpbmdTdGF0cz17aXNMb2FkaW5nU3RhdHN9IC8+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L0FuaW1hdGVQcmVzZW5jZT5cbiAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgIDwvbW90aW9uLm1haW4+XG4gICAgICA8L2Rpdj5cblxuXG4gICAgPC9kaXY+XG4gIClcbn1cblxuLy8gT3B0aW1pemVkIFRvb2wgRGV0YWlscyBDb21wb25lbnQgd2l0aCBNZW1vaXphdGlvblxuY29uc3QgVG9vbERldGFpbHMgPSBSZWFjdC5tZW1vKCh7IHRvb2wgfTogeyB0b29sOiBUb29sIH0pID0+IHtcbiAgcmV0dXJuIChcbiAgICA8bW90aW9uLmRpdlxuICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCBzY2FsZTogMC45NSB9fVxuICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCBzY2FsZTogMSB9fVxuICAgICAgZXhpdD17eyBvcGFjaXR5OiAwLCBzY2FsZTogMC45NSB9fVxuICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC4yLCBlYXNlOiBcImVhc2VPdXRcIiB9fVxuICAgICAgY2xhc3NOYW1lPVwic3BhY2UteS04XCJcbiAgICA+XG4gICAgICB7LyogSGVybyBTZWN0aW9uICovfVxuICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAyMCB9fVxuICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC4zLCBkZWxheTogMC4xIH19XG4gICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgXCJyZWxhdGl2ZSBvdmVyZmxvdy1oaWRkZW4gcm91bmRlZC0zeGwgYmFja2Ryb3AtYmx1ci14bCBib3JkZXIgcC04XCIsXG4gICAgICAgICAgdG9vbC5pZCA9PT0gJ2ludmluY2libGUtYWdlbnQnXG4gICAgICAgICAgICA/IFwiYmctZ3JhZGllbnQtdG8tYnIgZnJvbS13aGl0ZS8xMCB0by13aGl0ZS81IGJvcmRlci13aGl0ZS8yMCBzaGFkb3ctMnhsXCJcbiAgICAgICAgICAgIDogXCJiZy1ncmFkaWVudC10by1iciBmcm9tLXdoaXRlLzUgdG8td2hpdGUvMCBib3JkZXItd2hpdGUvMTBcIlxuICAgICAgICApfT5cbiAgICAgICAgey8qIEdsYXNzIHJlZmxlY3Rpb24gb3ZlcmxheSBmb3IgSW52aW5jaWJsZSAqL31cbiAgICAgICAge3Rvb2wuaWQgPT09ICdpbnZpbmNpYmxlLWFnZW50JyAmJiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIGJnLWdyYWRpZW50LXRvLWJyIGZyb20td2hpdGUvMjAgdmlhLXRyYW5zcGFyZW50IHRvLXRyYW5zcGFyZW50IHJvdW5kZWQtM3hsXCIgLz5cbiAgICAgICAgKX1cbiAgICAgICAgXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICBcImFic29sdXRlIGluc2V0LTAgYmctZ3JhZGllbnQtdG8tYnIgb3BhY2l0eS0yMFwiLFxuICAgICAgICAgIHRvb2wuY29sb3JcbiAgICAgICAgKX0gLz5cbiAgICAgICAgXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgei0xMCBncmlkIGdyaWQtY29scy0xIGxnOmdyaWQtY29scy0yIGdhcC04XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgICAgICBcInAtNCByb3VuZGVkLTJ4bCB0ZXh0LXdoaXRlIHNoYWRvdy0yeGxcIixcbiAgICAgICAgICAgICAgICB0b29sLmlkID09PSAnaW52aW5jaWJsZS1hZ2VudCdcbiAgICAgICAgICAgICAgICAgID8gXCJiZy1ncmFkaWVudC10by1iciBmcm9tLXZpb2xldC04MDAvNzAgdG8taW5kaWdvLTgwMC83MCBiYWNrZHJvcC1ibHVyLXNtIGJvcmRlciBib3JkZXItd2hpdGUvMzBcIlxuICAgICAgICAgICAgICAgICAgOiBjbihcImJnLWdyYWRpZW50LXRvLWJyXCIsIHRvb2wuY29sb3IpXG4gICAgICAgICAgICAgICl9PlxuICAgICAgICAgICAgICAgIDx0b29sLmljb24gY2xhc3NOYW1lPVwidy04IGgtOFwiIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC13aGl0ZVwiPnt0b29sLnRpdGxlfTwvaDE+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1sZyB0ZXh0LWdyYXktMzAwXCI+e3Rvb2wuc3VidGl0bGV9PC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktMzAwIGxlYWRpbmctcmVsYXhlZFwiPlxuICAgICAgICAgICAgICB7dG9vbC5kZXNjcmlwdGlvbn1cbiAgICAgICAgICAgIDwvcD5cblxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtd3JhcCBnYXAtM1wiPlxuICAgICAgICAgICAgICB7dG9vbC5mZWF0dXJlcy5tYXAoKGZlYXR1cmUsIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgPHNwYW5cbiAgICAgICAgICAgICAgICAgIGtleT17aW5kZXh9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgICAgICAgICAgICBcInB4LTQgcHktMiByb3VuZGVkLWZ1bGwgdGV4dC1zbSB0ZXh0LXdoaXRlIGJvcmRlclwiLFxuICAgICAgICAgICAgICAgICAgICB0b29sLmlkID09PSAnaW52aW5jaWJsZS1hZ2VudCdcbiAgICAgICAgICAgICAgICAgICAgICA/IFwiYmctd2hpdGUvMTUgYmFja2Ryb3AtYmx1ci1zbSBib3JkZXItd2hpdGUvMzBcIlxuICAgICAgICAgICAgICAgICAgICAgIDogXCJiZy13aGl0ZS8xMCBiYWNrZHJvcC1ibHVyLXNtIGJvcmRlci13aGl0ZS8yMFwiXG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIHtmZWF0dXJlfVxuICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICAgICAgPExpbmsgaHJlZj17dG9vbC5ocmVmfT5cbiAgICAgICAgICAgICAgICA8bW90aW9uLmJ1dHRvblxuICAgICAgICAgICAgICAgICAgd2hpbGVIb3Zlcj17eyBzY2FsZTogMS4wNSB9fVxuICAgICAgICAgICAgICAgICAgd2hpbGVUYXA9e3sgc2NhbGU6IDAuOTUgfX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgICAgICAgICAgIFwicHgtMTIgcHktNiByb3VuZGVkLTJ4bCBmb250LWJvbGQgdGV4dC1sZyB0ZXh0LXdoaXRlIHNoYWRvdy0yeGwgdHJhbnNpdGlvbi1hbGwgZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCIsXG4gICAgICAgICAgICAgICAgICAgIHRvb2wuaWQgPT09ICdpbnZpbmNpYmxlLWFnZW50J1xuICAgICAgICAgICAgICAgICAgICAgID8gXCJiZy1ncmFkaWVudC10by1yIGZyb20tdmlvbGV0LTgwMC84MCB0by1pbmRpZ28tODAwLzgwIGJhY2tkcm9wLWJsdXItc20gYm9yZGVyIGJvcmRlci13aGl0ZS8zMCBob3Zlcjpmcm9tLXZpb2xldC03MDAvOTAgaG92ZXI6dG8taW5kaWdvLTcwMC85MFwiXG4gICAgICAgICAgICAgICAgICAgICAgOiBjbihcImJnLWdyYWRpZW50LXRvLXJcIiwgdG9vbC5jb2xvcilcbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPFJvY2tldCBjbGFzc05hbWU9XCJ3LTYgaC02XCIgLz5cbiAgICAgICAgICAgICAgICAgIDxzcGFuPkxhdW5jaCB7dG9vbC50aXRsZX08L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9tb3Rpb24uYnV0dG9uPlxuICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBQcmV2aWV3IEFyZWEgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgICAgXCJyZWxhdGl2ZSBoLVs0MDBweF0gcm91bmRlZC0yeGwgb3ZlcmZsb3ctaGlkZGVuIGJvcmRlclwiLFxuICAgICAgICAgICAgdG9vbC5pZCA9PT0gJ2ludmluY2libGUtYWdlbnQnXG4gICAgICAgICAgICAgID8gXCJiZy1ibGFjay8zMCBiYWNrZHJvcC1ibHVyLXNtIGJvcmRlci13aGl0ZS8yMFwiXG4gICAgICAgICAgICAgIDogXCJiZy1ibGFjay80MCBib3JkZXItd2hpdGUvMTBcIlxuICAgICAgICAgICl9PlxuICAgICAgICAgICAge3Rvb2wucHJldmlldyB8fCAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgc3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgICA8dG9vbC5pY29uIGNsYXNzTmFtZT1cInctMjQgaC0yNCB0ZXh0LXdoaXRlLzIwIG14LWF1dG9cIiAvPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMFwiPkludGVyYWN0aXZlIHByZXZpZXcgY29taW5nIHNvb248L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L21vdGlvbi5kaXY+XG5cbiAgICAgIHsvKiBTdGF0cyBDYXJkcyAqL31cbiAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMjAgfX1cbiAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuMywgZGVsYXk6IDAuMiB9fVxuICAgICAgICBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0zIGdhcC02XCJcbiAgICAgID5cbiAgICAgICAgPExpbmsgaHJlZj17YC9jb250ZW50P3R5cGU9JHt0b29sLmlkID09PSAnaW52aW5jaWJsZS1hZ2VudCcgPyAnaW52aW5jaWJsZV9yZXNlYXJjaCcgOiB0b29sLmlkID09PSAnYmxvZy1nZW5lcmF0b3InID8gJ2Jsb2cnIDogdG9vbC5pZCA9PT0gJ2VtYWlsLWdlbmVyYXRvcicgPyAnZW1haWwnIDogdG9vbC5pZCA9PT0gJ3lvdXR1YmUtc2NyaXB0JyA/ICd5b3V0dWJlX3NjcmlwdCcgOiB0b29sLmlkLnJlcGxhY2UoJy0nLCAnXycpfWB9PlxuICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDIwIH19XG4gICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZGVsYXk6IDAuMjUgfX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgICAgIFwiYmFja2Ryb3AtYmx1ci14bCBib3JkZXIgcm91bmRlZC0yeGwgcC02IGN1cnNvci1wb2ludGVyIGhvdmVyOmJnLXdoaXRlLzEwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCBncm91cFwiLFxuICAgICAgICAgICAgICB0b29sLmlkID09PSAnaW52aW5jaWJsZS1hZ2VudCdcbiAgICAgICAgICAgICAgICA/IFwiYmctd2hpdGUvMTAgYm9yZGVyLXdoaXRlLzIwIHNoYWRvdy14bCBob3Zlcjpib3JkZXItd2hpdGUvMzBcIlxuICAgICAgICAgICAgICAgIDogXCJiZy13aGl0ZS81IGJvcmRlci13aGl0ZS8xMCBob3Zlcjpib3JkZXItd2hpdGUvMjBcIlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIHRpdGxlPXtgVmlldyBhbGwgJHt0b29sLnRpdGxlfSBjb250ZW50IGluIHlvdXIgbGlicmFyeWB9XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItNFwiPlxuICAgICAgICAgICAgICA8RmlsZVRleHQgY2xhc3NOYW1lPXtjbihcInctOCBoLTggZ3JvdXAtaG92ZXI6c2NhbGUtMTEwIHRyYW5zaXRpb24tdHJhbnNmb3JtIGR1cmF0aW9uLTIwMFwiLCBgdGV4dC0ke3Rvb2wuYWNjZW50Q29sb3J9LTQwMGApfSAvPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMVwiPlxuICAgICAgICAgICAgICAgIDxUcmVuZGluZ1VwIGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC1lbWVyYWxkLTQwMFwiIC8+XG4gICAgICAgICAgICAgICAgPEFycm93UmlnaHQgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LWdyYXktNDAwIGdyb3VwLWhvdmVyOnRleHQtd2hpdGUgdHJhbnNpdGlvbi1jb2xvcnNcIiAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtd2hpdGUgbWItMSBncm91cC1ob3Zlcjp0ZXh0LXZpb2xldC0yMDAgdHJhbnNpdGlvbi1jb2xvcnNcIj57dG9vbC5zdGF0cy5nZW5lcmF0ZWR9PC9wPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNDAwIGdyb3VwLWhvdmVyOnRleHQtZ3JheS0zMDAgdHJhbnNpdGlvbi1jb2xvcnNcIj5Db250ZW50IEdlbmVyYXRlZCAtIENsaWNrIHRvIHZpZXc8L3A+XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIHsvKiBTdWJ0bGUgaG92ZXIgaW5kaWNhdG9yICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0zIG9wYWNpdHktMCBncm91cC1ob3ZlcjpvcGFjaXR5LTEwMCB0cmFuc2l0aW9uLW9wYWNpdHkgZHVyYXRpb24tMjAwXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgdGV4dC14cyB0ZXh0LXZpb2xldC0zMDBcIj5cbiAgICAgICAgICAgICAgICA8RXllIGNsYXNzTmFtZT1cInctMyBoLTMgbXItMVwiIC8+XG4gICAgICAgICAgICAgICAgPHNwYW4+VmlldyBpbiBDb250ZW50IExpYnJhcnk8L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICA8L0xpbms+XG5cbiAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDIwIH19XG4gICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICAgICAgdHJhbnNpdGlvbj17eyBkZWxheTogMC4zNSB9fVxuICAgICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgICBcImJhY2tkcm9wLWJsdXIteGwgYm9yZGVyIHJvdW5kZWQtMnhsIHAtNlwiLFxuICAgICAgICAgICAgdG9vbC5pZCA9PT0gJ2ludmluY2libGUtYWdlbnQnXG4gICAgICAgICAgICAgID8gXCJiZy13aGl0ZS8xMCBib3JkZXItd2hpdGUvMjAgc2hhZG93LXhsXCJcbiAgICAgICAgICAgICAgOiBcImJnLXdoaXRlLzUgYm9yZGVyLXdoaXRlLzEwXCJcbiAgICAgICAgICApfVxuICAgICAgICA+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItNFwiPlxuICAgICAgICAgICAgPEF3YXJkIGNsYXNzTmFtZT17Y24oXCJ3LTggaC04XCIsIGB0ZXh0LSR7dG9vbC5hY2NlbnRDb2xvcn0tNDAwYCl9IC8+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMVwiPlxuICAgICAgICAgICAgICB7WzEsIDIsIDMsIDQsIDVdLm1hcCgoc3RhcikgPT4gKFxuICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgIGtleT17c3Rhcn1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgICAgICAgICAgIFwidy0zIGgtMyByb3VuZGVkLWZ1bGxcIixcbiAgICAgICAgICAgICAgICAgICAgc3RhciA8PSBNYXRoLnJvdW5kKHRvb2wuc3RhdHMucXVhbGl0eSAvIDIpXG4gICAgICAgICAgICAgICAgICAgICAgPyBcImJnLXllbGxvdy00MDBcIlxuICAgICAgICAgICAgICAgICAgICAgIDogXCJiZy1ncmF5LTYwMFwiXG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtd2hpdGUgbWItMVwiPnt0b29sLnN0YXRzLnF1YWxpdHl9LzEwPC9wPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTQwMFwiPlF1YWxpdHkgU2NvcmU8L3A+XG4gICAgICAgIDwvbW90aW9uLmRpdj5cblxuICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMjAgfX1cbiAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICB0cmFuc2l0aW9uPXt7IGRlbGF5OiAwLjQ1IH19XG4gICAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgIFwiYmFja2Ryb3AtYmx1ci14bCBib3JkZXIgcm91bmRlZC0yeGwgcC02XCIsXG4gICAgICAgICAgICB0b29sLmlkID09PSAnaW52aW5jaWJsZS1hZ2VudCdcbiAgICAgICAgICAgICAgPyBcImJnLXdoaXRlLzEwIGJvcmRlci13aGl0ZS8yMCBzaGFkb3cteGxcIlxuICAgICAgICAgICAgICA6IFwiYmctd2hpdGUvNSBib3JkZXItd2hpdGUvMTBcIlxuICAgICAgICAgICl9XG4gICAgICAgID5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi00XCI+XG4gICAgICAgICAgICA8Q2xvY2sgY2xhc3NOYW1lPXtjbihcInctOCBoLThcIiwgYHRleHQtJHt0b29sLmFjY2VudENvbG9yfS00MDBgKX0gLz5cbiAgICAgICAgICAgIDxBY3Rpdml0eSBjbGFzc05hbWU9XCJ3LTUgaC01IHRleHQtYmx1ZS00MDBcIiAvPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlIG1iLTFcIj57dG9vbC5zdGF0cy5hdmdUaW1lfTwvcD5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS00MDBcIj5BdmVyYWdlIFRpbWU8L3A+XG4gICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgIDwvbW90aW9uLmRpdj5cbiAgICA8L21vdGlvbi5kaXY+XG4gIClcbn0pXG5cbi8vIE9wdGltaXplZCBEYXNoYm9hcmQgT3ZlcnZpZXcgQ29tcG9uZW50IHdpdGggTWVtb2l6YXRpb25cbmNvbnN0IERhc2hib2FyZE92ZXJ2aWV3ID0gUmVhY3QubWVtbygoeyB0b29scywgdXNlclByb2ZpbGUsIGdldERpc3BsYXlOYW1lLCB1c2VyU3RhdHMsIGlzTG9hZGluZ1N0YXRzIH06IHsgXG4gIHRvb2xzOiBUb29sW11cbiAgdXNlclByb2ZpbGU6IFVzZXJQcm9maWxlIHwgbnVsbFxuICBnZXREaXNwbGF5TmFtZTogKCkgPT4gc3RyaW5nXG4gIHVzZXJTdGF0czogYW55XG4gIGlzTG9hZGluZ1N0YXRzOiBib29sZWFuXG59KSA9PiB7XG4gIHJldHVybiAoXG4gICAgPG1vdGlvbi5kaXZcbiAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgc2NhbGU6IDAuOTUgfX1cbiAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgc2NhbGU6IDEgfX1cbiAgICAgIGV4aXQ9e3sgb3BhY2l0eTogMCwgc2NhbGU6IDAuOTUgfX1cbiAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuMiwgZWFzZTogXCJlYXNlT3V0XCIgfX1cbiAgICAgIGNsYXNzTmFtZT1cInNwYWNlLXktOFwiXG4gICAgPlxuICAgICAgey8qIFdlbGNvbWUgU2VjdGlvbiAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmFkaWVudC10by1yIGZyb20tdmlvbGV0LTgwMC8yMCB0by1pbmRpZ28tODAwLzIwIGJhY2tkcm9wLWJsdXIteGwgYm9yZGVyIGJvcmRlci13aGl0ZS8xMCByb3VuZGVkLTN4bCBwLThcIj5cbiAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtNHhsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlIG1iLTRcIj5XZWxjb21lIGJhY2ssIHtnZXREaXNwbGF5TmFtZSgpfSEg4pyoPC9oMT5cbiAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14bCB0ZXh0LWdyYXktMzAwIG1iLTZcIj5Zb3VyIGNyZWF0aXZlIEFJIHRvb2xraXQgaXMgcmVhZHkuIFdoYXQgd2lsbCB5b3UgY3JlYXRlIHRvZGF5PzwvcD5cbiAgICAgICAgXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtNCBnYXAtNFwiPlxuICAgICAgICAgIHtpc0xvYWRpbmdTdGF0cyA/IChcbiAgICAgICAgICAgIC8vIExvYWRpbmcgc2tlbGV0b25cbiAgICAgICAgICAgIEFycmF5LmZyb20oeyBsZW5ndGg6IDQgfSkubWFwKChfLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgICAgIGtleT17aW5kZXh9XG4gICAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAyMCB9fVxuICAgICAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxuICAgICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZGVsYXk6IGluZGV4ICogMC4xIH19XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctYmxhY2svNDAgYmFja2Ryb3AtYmx1ci1zbSBib3JkZXIgYm9yZGVyLXdoaXRlLzEwIHJvdW5kZWQteGwgcC00IGFuaW1hdGUtcHVsc2VcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTUgYmctd2hpdGUvMTAgcm91bmRlZCBtYi0yXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTggYmctd2hpdGUvMTAgcm91bmRlZCBtYi0xXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTQgYmctd2hpdGUvMTAgcm91bmRlZFwiPjwvZGl2PlxuICAgICAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgICAgICApKVxuICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICBbXG4gICAgICAgICAgICAgIHsgXG4gICAgICAgICAgICAgICAgbGFiZWw6ICdUb3RhbCBDcmVhdGVkJywgXG4gICAgICAgICAgICAgICAgdmFsdWU6IHVzZXJTdGF0cz8udG90YWxDb250ZW50Py50b1N0cmluZygpIHx8ICcwJywgXG4gICAgICAgICAgICAgICAgaWNvbjogRmlsZVRleHQsIFxuICAgICAgICAgICAgICAgIGNoYW5nZTogdXNlclN0YXRzPy50cmVuZHM/LmNvbnRlbnRHcm93dGggfHwgJyswJScgXG4gICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgIHsgXG4gICAgICAgICAgICAgICAgbGFiZWw6ICdUaW1lIFNhdmVkJywgXG4gICAgICAgICAgICAgICAgdmFsdWU6IGAke3VzZXJTdGF0cz8udGltZVNhdmVkSG91cnMgfHwgMH0gaHJzYCwgXG4gICAgICAgICAgICAgICAgaWNvbjogQ2xvY2ssIFxuICAgICAgICAgICAgICAgIGNoYW5nZTogdXNlclN0YXRzPy50cmVuZHM/LnRpbWVFZmZpY2llbmN5IHx8ICcrMCUnIFxuICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICB7IFxuICAgICAgICAgICAgICAgIGxhYmVsOiAnUXVhbGl0eSBTY29yZScsIFxuICAgICAgICAgICAgICAgIHZhbHVlOiBgJHt1c2VyU3RhdHM/LnF1YWxpdHlTY29yZSB8fCA5LjB9LzEwYCwgXG4gICAgICAgICAgICAgICAgaWNvbjogQXdhcmQsIFxuICAgICAgICAgICAgICAgIGNoYW5nZTogdXNlclN0YXRzPy50cmVuZHM/LnF1YWxpdHlJbXByb3ZlbWVudCB8fCAnKzAuMCcgXG4gICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgIHsgXG4gICAgICAgICAgICAgICAgbGFiZWw6ICdBY3RpdmUgVG9vbHMnLCBcbiAgICAgICAgICAgICAgICB2YWx1ZTogdXNlclN0YXRzPy50cmVuZHM/LnRvb2xzQWN0aXZlPy50b1N0cmluZygpIHx8ICcwJywgXG4gICAgICAgICAgICAgICAgaWNvbjogTGF5ZXJzLCBcbiAgICAgICAgICAgICAgICBjaGFuZ2U6ICcrJyArICh1c2VyU3RhdHM/LnRyZW5kcz8udG9vbHNBY3RpdmUgfHwgMClcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgXS5tYXAoKHN0YXQsIGluZGV4KSA9PiAoXG4gICAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgICBrZXk9e3N0YXQubGFiZWx9XG4gICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMjAgfX1cbiAgICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZGVsYXk6IGluZGV4ICogMC4xIH19XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWJsYWNrLzQwIGJhY2tkcm9wLWJsdXItc20gYm9yZGVyIGJvcmRlci13aGl0ZS8xMCByb3VuZGVkLXhsIHAtNFwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTJcIj5cbiAgICAgICAgICAgICAgICA8c3RhdC5pY29uIGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC12aW9sZXQtNDAwXCIgLz5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZW1lcmFsZC00MDBcIj57c3RhdC5jaGFuZ2V9PC9zcGFuPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtd2hpdGVcIj57c3RhdC52YWx1ZX08L3A+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTQwMFwiPntzdGF0LmxhYmVsfTwvcD5cbiAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICApKVxuICAgICAgICAgICl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBUb29scyBHcmlkICovfVxuICAgICAgPGRpdj5cbiAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlIG1iLTZcIj5Zb3VyIEFJIFRvb2xzPC9oMj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy0zIGdhcC02XCI+XG4gICAgICAgICAge3Rvb2xzLm1hcCgodG9vbCwgaW5kZXgpID0+IChcbiAgICAgICAgICAgIDxMaW5rIGtleT17dG9vbC5pZH0gaHJlZj17dG9vbC5ocmVmfT5cbiAgICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHNjYWxlOiAwLjkgfX1cbiAgICAgICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHNjYWxlOiAxIH19XG4gICAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkZWxheTogaW5kZXggKiAwLjEgfX1cbiAgICAgICAgICAgICAgICB3aGlsZUhvdmVyPXt7IHk6IC04IH19XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZ3JvdXAgcmVsYXRpdmUgY3Vyc29yLXBvaW50ZXJcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgICAgICAgICAgXCJhYnNvbHV0ZSBpbnNldC0wIGJnLWdyYWRpZW50LXRvLWJyIG9wYWNpdHktMCBncm91cC1ob3ZlcjpvcGFjaXR5LTEwMCB0cmFuc2l0aW9uLW9wYWNpdHkgZHVyYXRpb24tMzAwIHJvdW5kZWQtMnhsIGJsdXIteGxcIixcbiAgICAgICAgICAgICAgICAgIHRvb2wuY29sb3JcbiAgICAgICAgICAgICAgICApfSAvPlxuICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgICAgICAgIFwicmVsYXRpdmUgYmFja2Ryb3AtYmx1ci14bCBib3JkZXIgdHJhbnNpdGlvbi1hbGxcIixcbiAgICAgICAgICAgICAgICAgIHRvb2wuaWQgPT09ICdpbnZpbmNpYmxlLWFnZW50JyBcbiAgICAgICAgICAgICAgICAgICAgPyBcImJnLWJsYWNrLzQwIGJvcmRlci13aGl0ZS8yMCBob3Zlcjpib3JkZXItd2hpdGUvMzAgcm91bmRlZC0yeGwgc2hhZG93LTJ4bFwiXG4gICAgICAgICAgICAgICAgICAgIDogXCJiZy1ibGFjay82MCBib3JkZXItd2hpdGUvMTAgaG92ZXI6Ym9yZGVyLXdoaXRlLzIwIHJvdW5kZWQtMnhsXCJcbiAgICAgICAgICAgICAgICApfT5cbiAgICAgICAgICAgICAgICAgIHsvKiBHbGFzcyByZWZsZWN0aW9uIGVmZmVjdCBmb3IgSW52aW5jaWJsZSAqL31cbiAgICAgICAgICAgICAgICAgIHt0b29sLmlkID09PSAnaW52aW5jaWJsZS1hZ2VudCcgJiYgKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgYmctZ3JhZGllbnQtdG8tYnIgZnJvbS13aGl0ZS8xMCB2aWEtdHJhbnNwYXJlbnQgdG8tdHJhbnNwYXJlbnQgcm91bmRlZC0yeGxcIiAvPlxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBwLTZcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IGp1c3RpZnktYmV0d2VlbiBtYi00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgICAgICAgICAgICAgICAgXCJwLTMgcm91bmRlZC14bCB0ZXh0LXdoaXRlXCIsXG4gICAgICAgICAgICAgICAgICAgICAgICB0b29sLmlkID09PSAnaW52aW5jaWJsZS1hZ2VudCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcImJnLWdyYWRpZW50LXRvLWJyIGZyb20tdmlvbGV0LTgwMC82MCB0by1pbmRpZ28tODAwLzYwIGJhY2tkcm9wLWJsdXItc20gYm9yZGVyIGJvcmRlci13aGl0ZS8yMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDogY24oXCJiZy1ncmFkaWVudC10by1iclwiLCB0b29sLmNvbG9yKVxuICAgICAgICAgICAgICAgICAgICAgICl9PlxuICAgICAgICAgICAgICAgICAgICAgICAgPHRvb2wuaWNvbiBjbGFzc05hbWU9XCJ3LTYgaC02XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8QXJyb3dSaWdodCBjbGFzc05hbWU9XCJ3LTUgaC01IHRleHQtZ3JheS00MDAgZ3JvdXAtaG92ZXI6dGV4dC13aGl0ZSB0cmFuc2l0aW9uLWNvbG9yc1wiIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZSBtYi0xXCI+e3Rvb2wudGl0bGV9PC9oMz5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNDAwIG1iLTRcIj57dG9vbC5zdWJ0aXRsZX08L3A+XG5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gdGV4dC14c1wiPlxuICAgICAgICAgICAgICAgICAgICAgIDxMaW5rIFxuICAgICAgICAgICAgICAgICAgICAgICAgaHJlZj17YC9jb250ZW50P3R5cGU9JHt0b29sLmlkID09PSAnaW52aW5jaWJsZS1hZ2VudCcgPyAnaW52aW5jaWJsZV9yZXNlYXJjaCcgOiB0b29sLmlkID09PSAnYmxvZy1nZW5lcmF0b3InID8gJ2Jsb2cnIDogdG9vbC5pZCA9PT0gJ2VtYWlsLWdlbmVyYXRvcicgPyAnZW1haWwnIDogdG9vbC5pZCA9PT0gJ3lvdXR1YmUtc2NyaXB0JyA/ICd5b3V0dWJlX3NjcmlwdCcgOiB0b29sLmlkLnJlcGxhY2UoJy0nLCAnXycpfWB9XG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoZSkgPT4gZS5zdG9wUHJvcGFnYXRpb24oKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImhvdmVyOmJnLXdoaXRlLzEwIHJvdW5kZWQgcHgtMiBweS0xIHRyYW5zaXRpb24tY29sb3JzIGdyb3VwL3N0YXRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU9e2BWaWV3IGFsbCAke3Rvb2wudGl0bGV9IGNvbnRlbnRgfVxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDAgZ3JvdXAtaG92ZXIvc3RhdDp0ZXh0LWdyYXktNDAwXCI+R2VuZXJhdGVkPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBmb250LW1lZGl1bSBncm91cC1ob3Zlci9zdGF0OnRleHQtdmlvbGV0LTIwMFwiPnt0b29sLnN0YXRzLmdlbmVyYXRlZH08L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxBcnJvd1JpZ2h0IGNsYXNzTmFtZT1cInctMyBoLTMgdGV4dC1ncmF5LTQwMCBncm91cC1ob3Zlci9zdGF0OnRleHQtd2hpdGUgb3BhY2l0eS0wIGdyb3VwLWhvdmVyL3N0YXQ6b3BhY2l0eS0xMDAgdHJhbnNpdGlvbi1hbGxcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICB7LyogVG9vbHRpcCBvbiBob3ZlciAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgYm90dG9tLWZ1bGwgbGVmdC0xLzIgdHJhbnNmb3JtIC10cmFuc2xhdGUteC0xLzIgbWItMiBweC0yIHB5LTEgYmctYmxhY2svOTAgdGV4dC13aGl0ZSB0ZXh0LXhzIHJvdW5kZWQgb3BhY2l0eS0wIGdyb3VwLWhvdmVyL3N0YXQ6b3BhY2l0eS0xMDAgdHJhbnNpdGlvbi1vcGFjaXR5IHBvaW50ZXItZXZlbnRzLW5vbmUgd2hpdGVzcGFjZS1ub3dyYXBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgQ2xpY2sgdG8gdmlldyBjb250ZW50XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXJpZ2h0XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwXCI+UXVhbGl0eTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1tZWRpdW1cIj57dG9vbC5zdGF0cy5xdWFsaXR5fS8xMDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICkpfVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogUmVjZW50IENvbnRlbnQgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlLzUgYmFja2Ryb3AtYmx1ci14bCBib3JkZXIgYm9yZGVyLXdoaXRlLzEwIHJvdW5kZWQtMnhsIHAtNlwiPlxuICAgICAgICA8T3B0aW1pemVkUmVjZW50Q29udGVudCBsaW1pdD17NX0gc2hvd0ZpbHRlcnM9e3RydWV9IC8+XG4gICAgICA8L2Rpdj5cbiAgICA8L21vdGlvbi5kaXY+XG4gIClcbn0pXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZUNhbGxiYWNrIiwidXNlTWVtbyIsIm1vdGlvbiIsIkFuaW1hdGVQcmVzZW5jZSIsIkxpbmsiLCJkeW5hbWljIiwidXNlU2Vzc2lvbiIsInVzZVJvdXRlciIsIlNwYXJrbGVzIiwiWmFwIiwiUm9ja2V0IiwiQXJyb3dSaWdodCIsIlNlYXJjaCIsIkJlbGwiLCJUcmVuZGluZ1VwIiwiQ2xvY2siLCJGaWxlVGV4dCIsIkF3YXJkIiwiTGF5ZXJzIiwiQWN0aXZpdHkiLCJTZXR0aW5ncyIsIkhlbHBDaXJjbGUiLCJIb21lIiwiTWFpbCIsIk1lbnUiLCJDaGV2cm9uTGVmdCIsIkNwdSIsIkNyb3duIiwiVmlkZW8iLCJFeWUiLCJCb3QiLCJjbiIsIlByb2ZpbGVCdXR0b24iLCJPcHRpbWl6ZWRSZWNlbnRDb250ZW50Iiwic3NyIiwibG9hZGluZyIsImRpdiIsImNsYXNzTmFtZSIsIkFycmF5IiwiZnJvbSIsImxlbmd0aCIsIm1hcCIsIl8iLCJpIiwiT3B0aW1pemVkSW52aW5jaWJsZU9yYiIsIk9wdGltaXplZEJsb2dQcmV2aWV3IiwiT3B0aW1pemVkRW1haWxQcmV2aWV3IiwiT3B0aW1pemVkU29jaWFsTWVkaWFQcmV2aWV3IiwiT3B0aW1pemVkVmlkZW9TY3JpcHRQcmV2aWV3IiwiT3B0aW1pemVkVmlkZW9BbGNoZW15UHJldmlldyIsIk9wdGltaXplZE1lZ2F0cm9uUHJldmlldyIsIkVuaGFuY2VkU2lkZWJhciIsIm1lbW8iLCJpc09wZW4iLCJvbkNsb3NlIiwidG9vbHMiLCJzZWxlY3RlZFRvb2wiLCJzZXRTZWxlY3RlZFRvb2wiLCJob3ZlcmVkVG9vbCIsInNldEhvdmVyZWRUb29sIiwic2lkZWJhclZhcmlhbnRzIiwib3BlbiIsIngiLCJ0cmFuc2l0aW9uIiwidHlwZSIsInN0aWZmbmVzcyIsImRhbXBpbmciLCJjbG9zZWQiLCJoYW5kbGVUb29sU2VsZWN0IiwidG9vbElkIiwiaGFuZGxlVG9vbEhvdmVyIiwiaW5pdGlhbCIsIm9wYWNpdHkiLCJhbmltYXRlIiwiZXhpdCIsImR1cmF0aW9uIiwib25DbGljayIsImFzaWRlIiwidmFyaWFudHMiLCJzdHlsZSIsIndpbGxDaGFuZ2UiLCJocmVmIiwid2hpbGVIb3ZlciIsInNjYWxlIiwid2hpbGVUYXAiLCJoMSIsInAiLCJuYXYiLCJidXR0b24iLCJ0b29sIiwiaWQiLCJvbk1vdXNlRW50ZXIiLCJvbk1vdXNlTGVhdmUiLCJjb2xvciIsImljb24iLCJ0aXRsZSIsImJldGEiLCJzcGFuIiwiY29taW5nU29vbiIsInN1YnRpdGxlIiwic3RhdHMiLCJnZW5lcmF0ZWQiLCJEYXNoYm9hcmRQYWdlIiwiZGF0YSIsInNlc3Npb24iLCJzdGF0dXMiLCJyb3V0ZXIiLCJzaWRlYmFyT3BlbiIsInNldFNpZGViYXJPcGVuIiwic2VhcmNoUXVlcnkiLCJzZXRTZWFyY2hRdWVyeSIsInNob3dOb3RpZmljYXRpb25zIiwic2V0U2hvd05vdGlmaWNhdGlvbnMiLCJ1c2VyUHJvZmlsZSIsInNldFVzZXJQcm9maWxlIiwiaXNMb2FkaW5nUHJvZmlsZSIsInNldElzTG9hZGluZ1Byb2ZpbGUiLCJ1c2VyU3RhdHMiLCJzZXRVc2VyU3RhdHMiLCJpc0xvYWRpbmdTdGF0cyIsInNldElzTG9hZGluZ1N0YXRzIiwidG9nZ2xlU2lkZWJhciIsInByZXYiLCJ0b2dnbGVOb3RpZmljYXRpb25zIiwiaGFuZGxlU2VhcmNoQ2hhbmdlIiwiZSIsInRhcmdldCIsInZhbHVlIiwiZ2V0VG9vbFN0YXRzIiwidG9vbFR5cGUiLCJjb250ZW50QnJlYWtkb3duIiwicXVhbGl0eSIsImF2Z1RpbWUiLCJNYXRoIiwibWluIiwiYXZnVGltZXMiLCJyb3VuZCIsImRlc2NyaXB0aW9uIiwiYmdHcmFkaWVudCIsImFjY2VudENvbG9yIiwiZmVhdHVyZXMiLCJwcmV2aWV3IiwicHVzaCIsInVzZXIiLCJmZXRjaFVzZXJQcm9maWxlIiwiZmV0Y2hVc2VyU3RhdHMiLCJyZXNwb25zZSIsImZldGNoIiwib2siLCJqc29uIiwiZXJyb3IiLCJjb25zb2xlIiwiZ2V0VXNlckluaXRpYWxzIiwiZmlyc3ROYW1lIiwibGFzdE5hbWUiLCJuYW1lIiwibmFtZXMiLCJzcGxpdCIsImVtYWlsIiwidG9VcHBlckNhc2UiLCJnZXREaXNwbGF5TmFtZSIsImFjdGl2ZVRvb2wiLCJmaW5kIiwidCIsImVhc2UiLCJhbmltYXRpb25EZWxheSIsImxlZnQiLCJtYWluIiwibWFyZ2luTGVmdCIsImhlYWRlciIsInBhZGRpbmdMZWZ0IiwiaDIiLCJpbnB1dCIsInBsYWNlaG9sZGVyIiwib25DaGFuZ2UiLCJtb2RlIiwiVG9vbERldGFpbHMiLCJEYXNoYm9hcmRPdmVydmlldyIsInkiLCJkZWxheSIsImZlYXR1cmUiLCJpbmRleCIsInJlcGxhY2UiLCJzdGFyIiwibGFiZWwiLCJ0b3RhbENvbnRlbnQiLCJ0b1N0cmluZyIsImNoYW5nZSIsInRyZW5kcyIsImNvbnRlbnRHcm93dGgiLCJ0aW1lU2F2ZWRIb3VycyIsInRpbWVFZmZpY2llbmN5IiwicXVhbGl0eVNjb3JlIiwicXVhbGl0eUltcHJvdmVtZW50IiwidG9vbHNBY3RpdmUiLCJzdGF0IiwiaDMiLCJzdG9wUHJvcGFnYXRpb24iLCJsaW1pdCIsInNob3dGaWx0ZXJzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ProfileButton.tsx":
/*!******************************************!*\
  !*** ./src/components/ProfileButton.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProfileButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_ChevronDown_Clock_Crown_HelpCircle_LogOut_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,ChevronDown,Clock,Crown,HelpCircle,LogOut,Settings,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_ChevronDown_Clock_Crown_HelpCircle_LogOut_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,ChevronDown,Clock,Crown,HelpCircle,LogOut,Settings,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_ChevronDown_Clock_Crown_HelpCircle_LogOut_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,ChevronDown,Clock,Crown,HelpCircle,LogOut,Settings,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_ChevronDown_Clock_Crown_HelpCircle_LogOut_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,ChevronDown,Clock,Crown,HelpCircle,LogOut,Settings,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_ChevronDown_Clock_Crown_HelpCircle_LogOut_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,ChevronDown,Clock,Crown,HelpCircle,LogOut,Settings,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_ChevronDown_Clock_Crown_HelpCircle_LogOut_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,ChevronDown,Clock,Crown,HelpCircle,LogOut,Settings,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_ChevronDown_Clock_Crown_HelpCircle_LogOut_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,ChevronDown,Clock,Crown,HelpCircle,LogOut,Settings,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_ChevronDown_Clock_Crown_HelpCircle_LogOut_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,ChevronDown,Clock,Crown,HelpCircle,LogOut,Settings,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_ChevronDown_Clock_Crown_HelpCircle_LogOut_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,ChevronDown,Clock,Crown,HelpCircle,LogOut,Settings,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-help.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_ChevronDown_Clock_Crown_HelpCircle_LogOut_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,ChevronDown,Clock,Crown,HelpCircle,LogOut,Settings,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction ProfileButton({ userProfile, className = \"\", showDropdown = true }) {\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [isDropdownOpen, setIsDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Close dropdown when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProfileButton.useEffect\": ()=>{\n            function handleClickOutside(event) {\n                if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                    setIsDropdownOpen(false);\n                }\n            }\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"ProfileButton.useEffect\": ()=>document.removeEventListener('mousedown', handleClickOutside)\n            })[\"ProfileButton.useEffect\"];\n        }\n    }[\"ProfileButton.useEffect\"], []);\n    const getUserInitials = ()=>{\n        if (userProfile?.firstName && userProfile?.lastName) {\n            return `${userProfile.firstName[0]}${userProfile.lastName[0]}`;\n        } else if (userProfile?.name) {\n            const names = userProfile.name.split(' ');\n            return names.length > 1 ? `${names[0][0]}${names[names.length - 1][0]}` : names[0][0];\n        } else if (userProfile?.email) {\n            return userProfile.email[0].toUpperCase();\n        }\n        return 'U';\n    };\n    const getDisplayName = ()=>{\n        if (userProfile?.firstName && userProfile?.lastName) {\n            return `${userProfile.firstName} ${userProfile.lastName}`;\n        } else if (userProfile?.name) {\n            return userProfile.name;\n        } else if (userProfile?.email) {\n            return userProfile.email.split('@')[0];\n        }\n        return 'User';\n    };\n    const getSubscriptionDisplay = ()=>{\n        return userProfile?.subscription?.plan === 'free' ? 'Free Plan' : userProfile?.subscription?.plan === 'pro' ? 'Pro Member' : 'Member';\n    };\n    const getSubscriptionColor = ()=>{\n        return userProfile?.subscription?.plan === 'free' ? 'text-gray-400' : userProfile?.subscription?.plan === 'pro' ? 'text-yellow-400' : 'text-blue-400';\n    };\n    const handleSignOut = async ()=>{\n        await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.signOut)({\n            callbackUrl: '/login'\n        });\n    };\n    if (!userProfile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `flex items-center space-x-3 ${className}`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-10 h-10 bg-gray-600 rounded-xl animate-pulse\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-20 h-3 bg-gray-600 rounded animate-pulse\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-16 h-2 bg-gray-700 rounded animate-pulse\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n            lineNumber: 104,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `relative ${className}`,\n        ref: dropdownRef,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                onClick: ()=>showDropdown && setIsDropdownOpen(!isDropdownOpen),\n                className: \"flex items-center space-x-3 p-2 rounded-xl hover:bg-white/5 transition-all group\",\n                whileHover: {\n                    scale: 1.02\n                },\n                whileTap: {\n                    scale: 0.98\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            userProfile.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: userProfile.image,\n                                alt: getDisplayName(),\n                                className: \"w-10 h-10 rounded-xl object-cover border-2 border-white/20 group-hover:border-violet-400/50 transition-colors\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-10 h-10 bg-gradient-to-br from-violet-500 to-indigo-500 rounded-xl flex items-center justify-center text-white font-semibold group-hover:from-violet-400 group-hover:to-indigo-400 transition-all\",\n                                children: getUserInitials()\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-emerald-400 border-2 border-gray-900 rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-left\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-medium text-white group-hover:text-violet-300 transition-colors\",\n                                children: getDisplayName()\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_ChevronDown_Clock_Crown_HelpCircle_LogOut_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: `w-3 h-3 ${getSubscriptionColor()}`\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: `text-xs ${getSubscriptionColor()}`,\n                                        children: getSubscriptionDisplay()\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this),\n                    showDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        animate: {\n                            rotate: isDropdownOpen ? 180 : 0\n                        },\n                        transition: {\n                            duration: 0.2\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_ChevronDown_Clock_Crown_HelpCircle_LogOut_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-4 h-4 text-gray-400 group-hover:text-white transition-colors\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, this),\n            showDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n                children: isDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 10,\n                        scale: 0.95\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0,\n                        scale: 1\n                    },\n                    exit: {\n                        opacity: 0,\n                        y: 10,\n                        scale: 0.95\n                    },\n                    transition: {\n                        duration: 0.2\n                    },\n                    className: \"absolute top-full right-0 mt-2 w-80 bg-black/90 backdrop-blur-xl border border-white/10 rounded-2xl shadow-2xl z-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 border-b border-white/10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    userProfile.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: userProfile.image,\n                                        alt: getDisplayName(),\n                                        className: \"w-12 h-12 rounded-xl object-cover border-2 border-white/20\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 21\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-gradient-to-br from-violet-500 to-indigo-500 rounded-xl flex items-center justify-center text-white font-semibold text-lg\",\n                                        children: getUserInitials()\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-white\",\n                                                children: getDisplayName()\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: userProfile.email\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-1 mt-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_ChevronDown_Clock_Crown_HelpCircle_LogOut_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: `w-3 h-3 ${getSubscriptionColor()}`\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: `text-xs ${getSubscriptionColor()}`,\n                                                        children: getSubscriptionDisplay()\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 border-b border-white/10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-3 gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center mx-auto mb-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_ChevronDown_Clock_Crown_HelpCircle_LogOut_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-4 h-4 text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-400\",\n                                                children: \"Content\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-semibold text-white\",\n                                                children: userProfile.stats?.totalContent || 0\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-emerald-500/20 rounded-lg flex items-center justify-center mx-auto mb-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_ChevronDown_Clock_Crown_HelpCircle_LogOut_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-4 h-4 text-emerald-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-400\",\n                                                children: \"Quality\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-semibold text-white\",\n                                                children: \"9.8/10\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-purple-500/20 rounded-lg flex items-center justify-center mx-auto mb-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_ChevronDown_Clock_Crown_HelpCircle_LogOut_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-4 h-4 text-purple-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-400\",\n                                                children: \"Saved\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-semibold text-white\",\n                                                children: \"48h\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/profile\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                                        whileHover: {\n                                            backgroundColor: 'rgba(255, 255, 255, 0.05)'\n                                        },\n                                        className: \"w-full flex items-center space-x-3 p-3 rounded-xl text-left transition-colors\",\n                                        onClick: ()=>setIsDropdownOpen(false),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_ChevronDown_Clock_Crown_HelpCircle_LogOut_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-4 h-4 text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white font-medium\",\n                                                        children: \"View Profile\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                                        lineNumber: 240,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-400\",\n                                                        children: \"Manage your profile information\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/settings\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                                        whileHover: {\n                                            backgroundColor: 'rgba(255, 255, 255, 0.05)'\n                                        },\n                                        className: \"w-full flex items-center space-x-3 p-3 rounded-xl text-left transition-colors\",\n                                        onClick: ()=>setIsDropdownOpen(false),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-emerald-500/20 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_ChevronDown_Clock_Crown_HelpCircle_LogOut_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-4 h-4 text-emerald-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white font-medium\",\n                                                        children: \"Settings\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-400\",\n                                                        children: \"Preferences and configuration\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                                    whileHover: {\n                                        backgroundColor: 'rgba(255, 255, 255, 0.05)'\n                                    },\n                                    className: \"w-full flex items-center space-x-3 p-3 rounded-xl text-left transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-yellow-500/20 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_ChevronDown_Clock_Crown_HelpCircle_LogOut_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-4 h-4 text-yellow-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-white font-medium\",\n                                                    children: \"Notifications\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-400\",\n                                                    children: \"Manage your alerts\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-violet-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 17\n                                }, this),\n                                userProfile.subscription?.plan === 'free' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                                    whileHover: {\n                                        backgroundColor: 'rgba(255, 255, 255, 0.05)'\n                                    },\n                                    className: \"w-full flex items-center space-x-3 p-3 rounded-xl text-left transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-gradient-to-br from-yellow-400 to-orange-400 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_ChevronDown_Clock_Crown_HelpCircle_LogOut_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-4 h-4 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-white font-medium\",\n                                                    children: \"Upgrade to Pro\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-400\",\n                                                    children: \"Unlock premium features\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                                    whileHover: {\n                                        backgroundColor: 'rgba(255, 255, 255, 0.05)'\n                                    },\n                                    className: \"w-full flex items-center space-x-3 p-3 rounded-xl text-left transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-purple-500/20 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_ChevronDown_Clock_Crown_HelpCircle_LogOut_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-4 h-4 text-purple-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-white font-medium\",\n                                                    children: \"Help & Support\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-400\",\n                                                    children: \"Get help and documentation\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-2 border-t border-white/10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                                whileHover: {\n                                    backgroundColor: 'rgba(239, 68, 68, 0.1)'\n                                },\n                                onClick: handleSignOut,\n                                className: \"w-full flex items-center space-x-3 p-3 rounded-xl text-left transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-red-500/20 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_ChevronDown_Clock_Crown_HelpCircle_LogOut_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-4 h-4 text-red-400\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-400 font-medium\",\n                                                children: \"Sign Out\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-400\",\n                                                children: \"Sign out of your account\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                            lineNumber: 308,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n                lineNumber: 165,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ProfileButton.tsx\",\n        lineNumber: 115,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ProfileButton.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/SessionProvider.tsx":
/*!********************************************!*\
  !*** ./src/components/SessionProvider.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SessionProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction SessionProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SessionProvider.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9TZXNzaW9uUHJvdmlkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUU0RTtBQU83RCxTQUFTQSxnQkFBZ0IsRUFBRUUsUUFBUSxFQUF3QjtJQUN4RSxxQkFDRSw4REFBQ0QsNERBQXVCQTtrQkFDckJDOzs7Ozs7QUFHUCIsInNvdXJjZXMiOlsiL1VzZXJzL2FheXVzaG1pc2hyYS9EZXNrdG9wL29sZCBpbnZpbmNpYmxlIHdpdGggZGVlcHJlc2VhcmNoL3NyYy9jb21wb25lbnRzL1Nlc3Npb25Qcm92aWRlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IFNlc3Npb25Qcm92aWRlciBhcyBOZXh0QXV0aFNlc3Npb25Qcm92aWRlciB9IGZyb20gJ25leHQtYXV0aC9yZWFjdCdcbmltcG9ydCB7IFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0J1xuXG5pbnRlcmZhY2UgU2Vzc2lvblByb3ZpZGVyUHJvcHMge1xuICBjaGlsZHJlbjogUmVhY3ROb2RlXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFNlc3Npb25Qcm92aWRlcih7IGNoaWxkcmVuIH06IFNlc3Npb25Qcm92aWRlclByb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPE5leHRBdXRoU2Vzc2lvblByb3ZpZGVyPlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvTmV4dEF1dGhTZXNzaW9uUHJvdmlkZXI+XG4gIClcbn0gIl0sIm5hbWVzIjpbIlNlc3Npb25Qcm92aWRlciIsIk5leHRBdXRoU2Vzc2lvblByb3ZpZGVyIiwiY2hpbGRyZW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/SessionProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   safeDecodeURIComponent: () => (/* binding */ safeDecodeURIComponent),\n/* harmony export */   safeEncodeURIComponent: () => (/* binding */ safeEncodeURIComponent)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\n * Safely decode URI component with fallback handling\n * @param encodedString - The URI encoded string to decode\n * @param fallback - Optional fallback value if decoding fails\n * @returns Decoded string or fallback/original string if decoding fails\n */ function safeDecodeURIComponent(encodedString, fallback) {\n    if (!encodedString) {\n        return fallback || '';\n    }\n    try {\n        return decodeURIComponent(encodedString);\n    } catch (error) {\n        console.warn('URI decode failed, using fallback:', error);\n        return fallback || encodedString;\n    }\n}\n/**\n * Safely encode URI component with error handling\n * @param str - The string to encode\n * @returns Encoded string or empty string if encoding fails\n */ function safeEncodeURIComponent(str) {\n    if (!str) return '';\n    try {\n        return encodeURIComponent(str);\n    } catch (error) {\n        console.warn('URI encode failed:', error);\n        return '';\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/framer-motion","vendor-chunks/tailwind-merge","vendor-chunks/motion-dom","vendor-chunks/lucide-react","vendor-chunks/motion-utils","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();