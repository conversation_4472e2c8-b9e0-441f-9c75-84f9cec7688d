"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_knowledge-base-optimizer_ts";
exports.ids = ["_rsc_src_lib_knowledge-base-optimizer_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/knowledge-base-optimizer.ts":
/*!*********************************************!*\
  !*** ./src/lib/knowledge-base-optimizer.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   KnowledgeBaseOptimizer: () => (/* binding */ KnowledgeBaseOptimizer),\n/* harmony export */   SmartCache: () => (/* binding */ SmartCache)\n/* harmony export */ });\n/* harmony import */ var _knowledge_base__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./knowledge-base */ \"(rsc)/./src/lib/knowledge-base.ts\");\n/**\n * Knowledge Base Optimizer for YouTube Generation Workflow\n * Optimizes content generation by reusing previous analyses and learning from patterns\n */ \nclass KnowledgeBaseOptimizer {\n    static{\n        this.globalCache = new Map();\n    }\n    /**\n   * Get or create a global knowledge base for a topic\n   */ static getGlobalKnowledgeBase(topicKey) {\n        const key = topicKey.toLowerCase().replace(/[^a-z0-9]/g, '_');\n        if (!this.globalCache.has(key)) {\n            this.globalCache.set(key, new _knowledge_base__WEBPACK_IMPORTED_MODULE_0__.KnowledgeBase(key));\n        }\n        return this.globalCache.get(key);\n    }\n    /**\n   * Find cached competitor analyses across all knowledge bases\n   */ static findCachedAnalyses(topic, limit = 5) {\n        console.log(`🔍 Searching global cache for analyses on \"${topic}\"...`);\n        const cachedAnalyses = [];\n        const searchTerms = topic.toLowerCase().split(' ');\n        // Search across all cached knowledge bases\n        this.globalCache.forEach((kb, key)=>{\n            const competitive = kb.getEntriesByType('competitive');\n            competitive.forEach((entry)=>{\n                // Calculate relevance\n                const relevance = this.calculateRelevance(searchTerms, entry);\n                if (relevance >= 0.5) {\n                    const videoId = entry.url?.split('v=')[1] || '';\n                    cachedAnalyses.push({\n                        videoId,\n                        title: entry.title || '',\n                        analysis: entry.content,\n                        timestamp: entry.metadata?.timestamp || Date.now(),\n                        relevanceScore: relevance\n                    });\n                }\n            });\n        });\n        // Sort by relevance and recency\n        cachedAnalyses.sort((a, b)=>{\n            const scoreA = (a.relevanceScore || 0) + 1 / (Date.now() - a.timestamp + 1);\n            const scoreB = (b.relevanceScore || 0) + 1 / (Date.now() - b.timestamp + 1);\n            return scoreB - scoreA;\n        });\n        console.log(`📊 Found ${cachedAnalyses.length} relevant cached analyses`);\n        return cachedAnalyses.slice(0, limit);\n    }\n    /**\n   * Find cached captions across all knowledge bases\n   */ static findCachedCaptions(videoIds) {\n        console.log(`🔍 Checking global cache for captions of ${videoIds.length} videos...`);\n        const cachedCaptions = new Map();\n        this.globalCache.forEach((kb)=>{\n            const extracted = kb.getEntriesByType('extracted_content');\n            extracted.forEach((entry)=>{\n                const videoId = entry.url?.split('v=')[1];\n                if (videoId && videoIds.includes(videoId)) {\n                    // Check if recent enough\n                    const age = Date.now() - (entry.metadata?.timestamp || 0);\n                    if (age < 7 * 24 * 60 * 60 * 1000) {\n                        cachedCaptions.set(videoId, entry.content);\n                    }\n                }\n            });\n        });\n        console.log(`💾 Found ${cachedCaptions.size} cached captions`);\n        return cachedCaptions;\n    }\n    /**\n   * Build topic profile from all related knowledge\n   */ static buildTopicProfile(topic) {\n        console.log(`📈 Building topic profile for \"${topic}\"...`);\n        const searchTerms = topic.toLowerCase().split(' ');\n        const commonThemes = new Map();\n        const contentGaps = new Map();\n        const successfulPatterns = [];\n        const bestPractices = [];\n        // Analyze all knowledge bases\n        this.globalCache.forEach((kb)=>{\n            const allEntries = [\n                ...kb.getEntriesByType('research'),\n                ...kb.getEntriesByType('competitive'),\n                ...kb.getEntriesByType('extracted_content')\n            ];\n            allEntries.forEach((entry)=>{\n                const relevance = this.calculateRelevance(searchTerms, entry);\n                if (relevance > 0.3) {\n                    // Extract themes\n                    entry.metadata?.keywords?.forEach((keyword)=>{\n                        commonThemes.set(keyword, (commonThemes.get(keyword) || 0) + 1);\n                    });\n                    // Extract gaps\n                    entry.metadata?.gaps?.forEach((gap)=>{\n                        contentGaps.set(gap, (contentGaps.get(gap) || 0) + 1);\n                    });\n                    // Extract successful patterns\n                    if (entry.metadata?.keyInsights) {\n                        successfulPatterns.push(...entry.metadata.keyInsights);\n                    }\n                    // Extract best practices\n                    entry.metadata?.statistics?.forEach((stat)=>{\n                        if (stat.includes('views') || stat.includes('engagement')) {\n                            bestPractices.push(stat);\n                        }\n                    });\n                }\n            });\n        });\n        if (commonThemes.size === 0) {\n            console.log('📊 No historical data found for topic profile');\n            return null;\n        }\n        // Sort and select top items\n        const sortedThemes = Array.from(commonThemes.entries()).sort((a, b)=>b[1] - a[1]).map(([theme])=>theme).slice(0, 10);\n        const sortedGaps = Array.from(contentGaps.entries()).sort((a, b)=>b[1] - a[1]).map(([gap])=>gap).slice(0, 10);\n        const profile = {\n            topic,\n            commonThemes: sortedThemes,\n            successfulPatterns: [\n                ...new Set(successfulPatterns)\n            ].slice(0, 10),\n            contentGaps: sortedGaps,\n            bestPractices: [\n                ...new Set(bestPractices)\n            ].slice(0, 5),\n            lastUpdated: Date.now()\n        };\n        console.log(`📊 Topic profile created with ${profile.commonThemes.length} themes, ${profile.contentGaps.length} gaps`);\n        return profile;\n    }\n    /**\n   * Find successful script patterns\n   */ static findSuccessfulPatterns(style, duration) {\n        console.log(`🎯 Finding successful patterns for ${style} style, ${duration} duration...`);\n        const patterns = [];\n        this.globalCache.forEach((kb)=>{\n            const scripts = kb.searchContent('Final Script');\n            scripts.forEach((entry)=>{\n                if (entry.metadata?.keywords?.includes(style) || entry.metadata?.keywords?.includes(duration)) {\n                    // Extract opening hooks\n                    const matches = entry.content.match(/\\[([\\d:]+)\\]\\s*([^[]+)/g);\n                    if (matches) {\n                        const hooks = matches.slice(0, 3).map((m)=>m.replace(/\\[[\\d:]+\\]\\s*/, ''));\n                        patterns.push(...hooks);\n                    }\n                }\n            });\n        });\n        console.log(`✨ Found ${patterns.length} successful patterns`);\n        return [\n            ...new Set(patterns)\n        ].slice(0, 5);\n    }\n    /**\n   * Calculate relevance score\n   */ static calculateRelevance(searchTerms, entry) {\n        let score = 0;\n        const titleWords = (entry.title || '').toLowerCase().split(' ');\n        const contentPreview = entry.content.substring(0, 500).toLowerCase();\n        searchTerms.forEach((term)=>{\n            // Title match (highest weight)\n            if (titleWords.includes(term)) score += 0.3;\n            // Content match\n            if (contentPreview.includes(term)) score += 0.1;\n            // Keyword match\n            if (entry.metadata?.keywords?.some((k)=>k.toLowerCase().includes(term))) {\n                score += 0.2;\n            }\n        });\n        // Recency bonus\n        const ageInDays = (Date.now() - (entry.metadata?.timestamp || 0)) / (24 * 60 * 60 * 1000);\n        if (ageInDays < 1) score += 0.2;\n        else if (ageInDays < 3) score += 0.1;\n        else if (ageInDays < 7) score += 0.05;\n        return Math.min(score, 1.0);\n    }\n    /**\n   * Get optimization recommendations\n   */ static getOptimizationRecommendations(topic) {\n        console.log(`💡 Generating optimization recommendations for \"${topic}\"...`);\n        const recommendations = [];\n        // Check cached analyses\n        const cachedAnalyses = this.findCachedAnalyses(topic, 3);\n        if (cachedAnalyses.length > 0) {\n            recommendations.push(`🔄 Found ${cachedAnalyses.length} cached competitor analyses - reuse to save API calls`);\n        }\n        // Check topic profile\n        const profile = this.buildTopicProfile(topic);\n        if (profile) {\n            if (profile.contentGaps.length > 0) {\n                recommendations.push(`📊 Common content gaps: ${profile.contentGaps.slice(0, 3).join(', ')}`);\n            }\n            if (profile.bestPractices.length > 0) {\n                recommendations.push(`✨ Best practice: ${profile.bestPractices[0]}`);\n            }\n            if (profile.commonThemes.length > 0) {\n                recommendations.push(`🎯 Focus on themes: ${profile.commonThemes.slice(0, 3).join(', ')}`);\n            }\n        }\n        // Check patterns\n        const patterns = this.findSuccessfulPatterns('educational', '5-10 minutes');\n        if (patterns.length > 0) {\n            recommendations.push(`💡 Successful hook: \"${patterns[0].substring(0, 50)}...\"`);\n        }\n        console.log(`💡 Generated ${recommendations.length} recommendations`);\n        return recommendations;\n    }\n    /**\n   * Track optimization metrics\n   */ static trackMetrics(sessionId, metrics) {\n        const kb = this.getGlobalKnowledgeBase('_optimization_metrics');\n        kb.addEntry({\n            type: 'research',\n            title: `Optimization Metrics: ${sessionId}`,\n            content: JSON.stringify(metrics, null, 2),\n            metadata: {\n                source: 'workflow_optimization',\n                timestamp: Date.now(),\n                statistics: [\n                    `${metrics.cachedAnalysesFound} cached analyses found`,\n                    `${metrics.cachedCaptionsFound} cached captions found`,\n                    `${metrics.apiCallsSaved} API calls saved`,\n                    `${metrics.timeSaved}s time saved`\n                ],\n                keywords: [\n                    'optimization',\n                    'metrics',\n                    'performance'\n                ]\n            }\n        });\n        console.log(`📊 Tracked optimization metrics: ${metrics.apiCallsSaved} API calls saved`);\n    }\n    /**\n   * Clear old cache entries\n   */ static cleanupCache(maxAge = 30 * 24 * 60 * 60 * 1000) {\n        console.log('🧹 Cleaning up old cache entries...');\n        let totalRemoved = 0;\n        this.globalCache.forEach((kb, key)=>{\n            const allEntries = [\n                ...kb.getEntriesByType('research'),\n                ...kb.getEntriesByType('competitive'),\n                ...kb.getEntriesByType('extracted_content'),\n                ...kb.getEntriesByType('writing_style')\n            ];\n            allEntries.forEach((entry)=>{\n                const age = Date.now() - (entry.metadata?.timestamp || 0);\n                if (age > maxAge) {\n                    kb.removeEntry(entry.id);\n                    totalRemoved++;\n                }\n            });\n            // Remove empty knowledge bases\n            if (kb.getSize() === 0) {\n                this.globalCache.delete(key);\n            }\n        });\n        console.log(`🧹 Removed ${totalRemoved} old entries`);\n    }\n    /**\n   * Get global cache size for debugging\n   */ static getGlobalCacheSize() {\n        return this.globalCache.size;\n    }\n    /**\n   * Clear entire global cache (for debugging or cleanup)\n   */ static clearGlobalCache() {\n        console.log('🧹 Clearing entire global knowledge base cache...');\n        const entriesCleared = this.globalCache.size;\n        this.globalCache.clear();\n        console.log(`🧹 Cleared ${entriesCleared} global cache entries`);\n    }\n    constructor(){\n        this.sessionCache = new Map();\n        // Cache settings\n        this.CACHE_DURATION = 7 * 24 * 60 * 60 * 1000 // 7 days\n        ;\n        this.RELEVANCE_THRESHOLD = 0.7;\n    }\n}\n/**\n * Intelligent caching strategy for YouTube workflow\n */ class SmartCache {\n    static getInstance() {\n        if (!SmartCache.instance) {\n            SmartCache.instance = new SmartCache();\n        }\n        return SmartCache.instance;\n    }\n    set(key, data) {\n        this.cache.set(key, {\n            data,\n            timestamp: Date.now()\n        });\n    }\n    get(key) {\n        const cached = this.cache.get(key);\n        if (!cached) return null;\n        if (Date.now() - cached.timestamp > this.TTL) {\n            this.cache.delete(key);\n            return null;\n        }\n        return cached.data;\n    }\n    clear() {\n        this.cache.clear();\n    }\n    constructor(){\n        this.cache = new Map();\n        this.TTL = 60 * 60 * 1000 // 1 hour\n        ;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/knowledge-base-optimizer.ts\n");

/***/ })

};
;