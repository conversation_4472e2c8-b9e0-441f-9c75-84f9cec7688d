"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_agents_v2_agents_quality-agent_ts";
exports.ids = ["_rsc_src_lib_agents_v2_agents_quality-agent_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/agents/v2/agents/quality-agent.ts":
/*!***************************************************!*\
  !*** ./src/lib/agents/v2/agents/quality-agent.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QualityAgent: () => (/* binding */ QualityAgent),\n/* harmony export */   createQualityAgent: () => (/* binding */ createQualityAgent)\n/* harmony export */ });\n/* harmony import */ var _core_state_schema__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../core/state-schema */ \"(rsc)/./src/lib/agents/v2/core/state-schema.ts\");\n/* harmony import */ var _tools_kimi_k2_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../tools/kimi-k2-client */ \"(rsc)/./src/lib/agents/v2/tools/kimi-k2-client.ts\");\n/* harmony import */ var _utils_json_parser__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/json-parser */ \"(rsc)/./src/lib/agents/v2/utils/json-parser.ts\");\n/**\n * Invincible V.2 - Quality Assurance & Humanization Agent\n * Advanced content validation and AI detection bypass\n */ \n\n\nclass QualityAgent {\n    constructor(config){\n        this.config = {\n            aiDetectionThreshold: 30,\n            qualityThreshold: 85,\n            enableAdvancedHumanization: true,\n            ...config\n        };\n        this.kimiClient = (0,_tools_kimi_k2_client__WEBPACK_IMPORTED_MODULE_1__.createKimiK2Client)(this.config.kimiApiKey);\n    }\n    /**\n   * Main quality assurance execution\n   */ async execute(state) {\n        try {\n            let updatedState = _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.updateWorkflow(state, {\n                currentAgent: 'quality_agent',\n                progress: 80\n            });\n            updatedState = _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.addMessage(updatedState, {\n                agent: 'quality_agent',\n                action: 'quality_assurance_started',\n                data: {\n                    contentLength: state.generation.content.wordCount,\n                    threshold: this.config.qualityThreshold\n                },\n                status: 'in_progress'\n            });\n            // Phase 1: Content Quality Assessment\n            const qualityAssessment = await this.assessContentQuality(updatedState);\n            updatedState = _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.addMessage(updatedState, {\n                agent: 'quality_agent',\n                action: 'quality_assessment_completed',\n                data: {\n                    overallScore: qualityAssessment.overallScore,\n                    issues: qualityAssessment.issues.length\n                },\n                status: 'completed'\n            });\n            // Phase 2: AI Detection Analysis\n            const aiDetectionScore = await this.analyzeAIDetection(state.generation.content.content);\n            updatedState = _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.addMessage(updatedState, {\n                agent: 'quality_agent',\n                action: 'ai_detection_analysis_completed',\n                data: {\n                    aiDetectionScore: aiDetectionScore,\n                    passesThreshold: aiDetectionScore < this.config.aiDetectionThreshold\n                },\n                status: 'completed'\n            });\n            // Phase 3: Advanced Humanization (if needed)\n            let finalContent = state.generation.content.content;\n            let humanizationTechniques = [];\n            if (aiDetectionScore > this.config.aiDetectionThreshold && this.config.enableAdvancedHumanization) {\n                const humanization = await this.applyAdvancedHumanization(finalContent);\n                finalContent = humanization.content;\n                humanizationTechniques = humanization.techniques;\n                updatedState = _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.updateGeneration(updatedState, {\n                    content: finalContent,\n                    humanizationApplied: [\n                        ...state.generation.content.humanizationApplied,\n                        ...humanizationTechniques\n                    ]\n                });\n                updatedState = _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.addMessage(updatedState, {\n                    agent: 'quality_agent',\n                    action: 'humanization_applied',\n                    data: {\n                        techniques: humanizationTechniques.length,\n                        newAIScore: await this.analyzeAIDetection(finalContent)\n                    },\n                    status: 'completed'\n                });\n            }\n            // Phase 4: Final Quality Metrics\n            const finalMetrics = await this.calculateFinalMetrics(updatedState, qualityAssessment, aiDetectionScore);\n            updatedState = _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.updateQuality(updatedState, finalMetrics);\n            // Phase 5: Create Final Result\n            const finalResult = await this.createFinalResult(updatedState);\n            updatedState = {\n                ...updatedState,\n                result: finalResult,\n                quality: {\n                    ...updatedState.quality,\n                    completed: true\n                }\n            };\n            updatedState = _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.updateWorkflow(updatedState, {\n                progress: 100,\n                nextAgent: null,\n                completedAgents: [\n                    ...updatedState.workflow.completedAgents,\n                    'quality_agent'\n                ]\n            });\n            updatedState = _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.addMessage(updatedState, {\n                agent: 'quality_agent',\n                action: 'quality_assurance_completed',\n                data: {\n                    finalScore: finalMetrics.humanLikenessScore,\n                    aiDetectionScore: finalMetrics.aiDetectionScore,\n                    seoScore: finalMetrics.seoScore\n                },\n                status: 'completed'\n            });\n            return updatedState;\n        } catch (error) {\n            console.error('Quality Agent error:', error);\n            const errorState = _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.updateWorkflow(state, {\n                errors: [\n                    ...state.workflow.errors,\n                    `Quality assurance failed: ${error}`\n                ]\n            });\n            return _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.addMessage(errorState, {\n                agent: 'quality_agent',\n                action: 'quality_assurance_failed',\n                data: {\n                    error: error.toString()\n                },\n                status: 'error'\n            });\n        }\n    }\n    /**\n   * Assess overall content quality\n   */ async assessContentQuality(state) {\n        const content = state.generation.content.content;\n        const requirements = state.requirements;\n        const prompt = `Assess the quality of this content comprehensively.\n\n**Content to Assess:**\n${content.substring(0, 2000)}...\n\n**Requirements:**\n- Target Length: ${requirements.contentLength} words\n- Tone: ${requirements.tone}\n- Audience: ${requirements.targetAudience}\n- Topic: ${requirements.topic}\n\n**Assessment Criteria:**\n1. **Completeness**: Does it cover the topic thoroughly?\n2. **Accuracy**: Is the information correct and up-to-date?\n3. **Readability**: Is it easy to read and understand?\n4. **Engagement**: Is it compelling and interesting?\n5. **Structure**: Is it well-organized with clear flow?\n6. **Uniqueness**: Does it provide unique insights?\n7. **Actionability**: Does it provide practical value?\n\n**Current Metrics:**\n- Word Count: ${state.generation.content.wordCount}\n- Readability Score: ${state.generation.content.readabilityScore}\n\nReturn JSON assessment:\n{\n  \"overallScore\": score_1_100,\n  \"criteriaScores\": {\n    \"completeness\": score_1_100,\n    \"accuracy\": score_1_100,\n    \"readability\": score_1_100,\n    \"engagement\": score_1_100,\n    \"structure\": score_1_100,\n    \"uniqueness\": score_1_100,\n    \"actionability\": score_1_100\n  },\n  \"issues\": [\"issue1\", \"issue2\", ...],\n  \"strengths\": [\"strength1\", \"strength2\", ...],\n  \"recommendations\": [\"rec1\", \"rec2\", ...]\n}`;\n        try {\n            const response = await this.kimiClient.generateContent([\n                {\n                    role: 'user',\n                    content: prompt\n                }\n            ]);\n            const assessment = (0,_utils_json_parser__WEBPACK_IMPORTED_MODULE_2__.safeJsonParse)(response.content, {\n                overallScore: 75,\n                issues: [\n                    'Assessment failed - manual review needed'\n                ],\n                strengths: [\n                    'Generated content completed'\n                ],\n                recommendations: [\n                    'Manual quality check recommended'\n                ]\n            });\n            return {\n                overallScore: assessment?.overallScore || 75,\n                issues: assessment?.issues || [],\n                strengths: assessment?.strengths || [],\n                recommendations: assessment?.recommendations || []\n            };\n        } catch (error) {\n            console.error('Content quality assessment failed:', error);\n            return {\n                overallScore: 75,\n                issues: [\n                    'Assessment failed - manual review needed'\n                ],\n                strengths: [\n                    'Generated content completed'\n                ],\n                recommendations: [\n                    'Manual quality check recommended'\n                ]\n            };\n        }\n    }\n    /**\n   * Analyze AI detection likelihood\n   */ async analyzeAIDetection(content) {\n        // Simplified AI detection analysis based on content patterns\n        const text = content.replace(/<[^>]*>/g, '');\n        let aiScore = 0;\n        // Check for AI-like patterns\n        const aiPatterns = [\n            /in conclusion/gi,\n            /it's important to note/gi,\n            /furthermore/gi,\n            /moreover/gi,\n            /in summary/gi,\n            /as a result/gi,\n            /therefore/gi\n        ];\n        aiPatterns.forEach((pattern)=>{\n            const matches = text.match(pattern);\n            if (matches) {\n                aiScore += matches.length * 5;\n            }\n        });\n        // Check sentence structure variety\n        const sentences = text.split(/[.!?]+/);\n        const avgSentenceLength = sentences.reduce((sum, s)=>sum + s.split(' ').length, 0) / sentences.length;\n        if (avgSentenceLength > 25) aiScore += 10; // Very long sentences\n        if (avgSentenceLength < 8) aiScore += 5; // Very short sentences\n        // Check for repetitive patterns\n        const words = text.toLowerCase().split(/\\W+/);\n        const wordFreq = new Map();\n        words.forEach((word)=>{\n            if (word.length > 3) {\n                wordFreq.set(word, (wordFreq.get(word) || 0) + 1);\n            }\n        });\n        const repetitiveWords = Array.from(wordFreq.values()).filter((count)=>count > 10);\n        aiScore += repetitiveWords.length * 3;\n        return Math.min(100, Math.max(0, aiScore));\n    }\n    /**\n   * Apply advanced humanization techniques\n   */ async applyAdvancedHumanization(content) {\n        const prompt = `Apply advanced humanization techniques to make this content more human-like and bypass AI detection.\n\n**Content to Humanize:**\n${content.substring(0, 2000)}...\n\n**Advanced Humanization Techniques to Apply:**\n1. **Sentence Rhythm**: Vary sentence lengths dramatically (3-30+ words)\n2. **Conversational Flow**: Add natural transitions and connectors\n3. **Personal Touch**: Include subtle personal observations or experiences\n4. **Colloquialisms**: Use natural speech patterns where appropriate\n5. **Narrative Elements**: Add brief storytelling or anecdotal elements\n6. **Contractions**: Use natural contractions (don't, won't, it's)\n7. **Active Voice**: Convert passive voice to active where natural\n8. **Emotional Resonance**: Add subtle emotional cues and human reactions\n9. **Natural Imperfections**: Include minor stylistic variations\n10. **Unique Analogies**: Create original comparisons and metaphors\n\n**Requirements:**\n- Maintain all factual accuracy\n- Keep professional tone where needed\n- Preserve SEO optimization\n- Ensure readability remains high\n- Do not change the core message or structure\n\nReturn the fully humanized content while preserving HTML structure.`;\n        try {\n            const response = await this.kimiClient.generateContent([\n                {\n                    role: 'user',\n                    content: prompt\n                }\n            ], undefined, {\n                temperature: 0.9\n            });\n            const techniques = [\n                'Advanced sentence rhythm variation',\n                'Conversational flow enhancement',\n                'Personal observation integration',\n                'Natural speech patterns',\n                'Narrative elements addition',\n                'Contraction normalization',\n                'Active voice optimization',\n                'Emotional resonance tuning',\n                'Stylistic variation application',\n                'Unique analogy creation'\n            ];\n            return {\n                content: response.content,\n                techniques\n            };\n        } catch (error) {\n            console.error('Advanced humanization failed:', error);\n            return {\n                content,\n                techniques: [\n                    'Basic humanization applied'\n                ]\n            };\n        }\n    }\n    /**\n   * Calculate final quality metrics\n   */ async calculateFinalMetrics(state, qualityAssessment, aiDetectionScore) {\n        const content = state.generation.content;\n        const seoScore = this.calculateSEOScore(state);\n        const geoScore = this.calculateGEOScore(state);\n        const competitiveAdvantage = this.calculateCompetitiveAdvantage(state);\n        const originalityScore = this.calculateOriginalityScore(state);\n        return {\n            aiDetectionScore,\n            originalityScore,\n            competitiveAdvantage,\n            seoScore,\n            geoScore,\n            humanLikenessScore: Math.max(0, 100 - aiDetectionScore)\n        };\n    }\n    /**\n   * Calculate SEO score based on optimization\n   */ calculateSEOScore(state) {\n        let score = 0;\n        // Keyword optimization\n        if (state.analysis.seo.primaryKeywords.length > 0) score += 25;\n        if (state.analysis.seo.secondaryKeywords.length >= 5) score += 20;\n        // Content structure\n        const content = state.generation.content.content;\n        if (content.includes('<h1>')) score += 15;\n        if (content.includes('<h2>')) score += 15;\n        if (content.includes('<h3>')) score += 10;\n        // Meta optimization\n        if (state.generation.content.metaDescription) score += 15;\n        return Math.min(100, score);\n    }\n    /**\n   * Calculate GEO score based on AI search optimization\n   */ calculateGEOScore(state) {\n        const geoData = state.analysis.geo;\n        let score = 0;\n        // AI search visibility\n        const aiEngines = Object.values(geoData.aiSearchVisibility);\n        score += aiEngines.filter(Boolean).length / aiEngines.length * 40;\n        // Reference optimization\n        score += geoData.referenceOptimization.sourceCredibility / 100 * 30;\n        score += geoData.referenceOptimization.factualAccuracy / 100 * 30;\n        return Math.min(100, score);\n    }\n    /**\n   * Calculate competitive advantage score\n   */ calculateCompetitiveAdvantage(state) {\n        const competitive = state.analysis.competitive;\n        let score = 50; // Base score\n        // Content gaps addressed\n        score += competitive.contentGaps.length * 10;\n        // Opportunities leveraged\n        score += competitive.opportunities.length * 8;\n        // Quality factors\n        if (state.generation.content.wordCount >= state.requirements.contentLength) score += 15;\n        if (state.generation.content.readabilityScore >= 60) score += 10;\n        return Math.min(100, score);\n    }\n    /**\n   * Calculate originality score\n   */ calculateOriginalityScore(state) {\n        // Simplified originality calculation\n        const content = state.generation.content.content;\n        const uniqueElements = [\n            content.includes('unique insight'),\n            content.includes('expert perspective'),\n            content.includes('case study'),\n            content.includes('real-world example'),\n            content.includes('latest trend')\n        ];\n        return 60 + uniqueElements.filter(Boolean).length * 8;\n    }\n    /**\n   * Create final result object\n   */ async createFinalResult(state) {\n        const content = state.generation.content;\n        const quality = state.quality.metrics;\n        return {\n            article: {\n                title: content.title,\n                content: content.content,\n                metaDescription: content.metaDescription,\n                wordCount: content.wordCount,\n                readabilityScore: content.readabilityScore,\n                seoScore: quality.seoScore,\n                geoScore: quality.geoScore,\n                humanLikenessScore: quality.humanLikenessScore,\n                aiDetectionScore: quality.aiDetectionScore,\n                competitiveAdvantage: quality.competitiveAdvantage,\n                originalityScore: quality.originalityScore\n            },\n            analytics: {\n                researchQueries: state.research.data.length,\n                totalSources: state.research.data.reduce((sum, r)=>sum + r.results.length, 0),\n                competitorsAnalyzed: state.analysis.competitive.topContent.length,\n                contentGapsAddressed: state.analysis.competitive.contentGaps.length,\n                opportunitiesLeveraged: state.analysis.competitive.opportunities.length,\n                humanizationTechniques: state.generation.content.humanizationApplied.length\n            },\n            performance: {\n                executionTime: Date.now() - state.startTime,\n                agentsExecuted: state.workflow.completedAgents.length,\n                qualityScore: Math.round((quality.humanLikenessScore + quality.seoScore + quality.geoScore + quality.competitiveAdvantage) / 4),\n                success: quality.humanLikenessScore >= 70 && quality.seoScore >= 70\n            }\n        };\n    }\n}\nfunction createQualityAgent(config) {\n    return new QualityAgent(config);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2FnZW50cy92Mi9hZ2VudHMvcXVhbGl0eS1hZ2VudC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFBOzs7Q0FHQyxHQUVzRjtBQUNaO0FBQ3RCO0FBUzlDLE1BQU1HO0lBSVhDLFlBQVlDLE1BQTBCLENBQUU7UUFDdEMsSUFBSSxDQUFDQSxNQUFNLEdBQUc7WUFDWkMsc0JBQXNCO1lBQ3RCQyxrQkFBa0I7WUFDbEJDLDRCQUE0QjtZQUM1QixHQUFHSCxNQUFNO1FBQ1g7UUFFQSxJQUFJLENBQUNJLFVBQVUsR0FBR1IseUVBQWtCQSxDQUFDLElBQUksQ0FBQ0ksTUFBTSxDQUFDSyxVQUFVO0lBQzdEO0lBRUE7O0dBRUMsR0FDRCxNQUFNQyxRQUFRQyxLQUF3QixFQUE4QjtRQUNsRSxJQUFJO1lBQ0YsSUFBSUMsZUFBZWIsNERBQVlBLENBQUNjLGNBQWMsQ0FBQ0YsT0FBTztnQkFDcERHLGNBQWM7Z0JBQ2RDLFVBQVU7WUFDWjtZQUVBSCxlQUFlYiw0REFBWUEsQ0FBQ2lCLFVBQVUsQ0FBQ0osY0FBYztnQkFDbkRLLE9BQU87Z0JBQ1BDLFFBQVE7Z0JBQ1JDLE1BQU07b0JBQ0pDLGVBQWVULE1BQU1VLFVBQVUsQ0FBQ0MsT0FBTyxDQUFDQyxTQUFTO29CQUNqREMsV0FBVyxJQUFJLENBQUNwQixNQUFNLENBQUNFLGdCQUFnQjtnQkFDekM7Z0JBQ0FtQixRQUFRO1lBQ1Y7WUFFQSxzQ0FBc0M7WUFDdEMsTUFBTUMsb0JBQW9CLE1BQU0sSUFBSSxDQUFDQyxvQkFBb0IsQ0FBQ2Y7WUFFMURBLGVBQWViLDREQUFZQSxDQUFDaUIsVUFBVSxDQUFDSixjQUFjO2dCQUNuREssT0FBTztnQkFDUEMsUUFBUTtnQkFDUkMsTUFBTTtvQkFDSlMsY0FBY0Ysa0JBQWtCRSxZQUFZO29CQUM1Q0MsUUFBUUgsa0JBQWtCRyxNQUFNLENBQUNDLE1BQU07Z0JBQ3pDO2dCQUNBTCxRQUFRO1lBQ1Y7WUFFQSxpQ0FBaUM7WUFDakMsTUFBTU0sbUJBQW1CLE1BQU0sSUFBSSxDQUFDQyxrQkFBa0IsQ0FBQ3JCLE1BQU1VLFVBQVUsQ0FBQ0MsT0FBTyxDQUFDQSxPQUFPO1lBRXZGVixlQUFlYiw0REFBWUEsQ0FBQ2lCLFVBQVUsQ0FBQ0osY0FBYztnQkFDbkRLLE9BQU87Z0JBQ1BDLFFBQVE7Z0JBQ1JDLE1BQU07b0JBQ0pZLGtCQUFrQkE7b0JBQ2xCRSxpQkFBaUJGLG1CQUFtQixJQUFJLENBQUMzQixNQUFNLENBQUNDLG9CQUFvQjtnQkFDdEU7Z0JBQ0FvQixRQUFRO1lBQ1Y7WUFFQSw2Q0FBNkM7WUFDN0MsSUFBSVMsZUFBZXZCLE1BQU1VLFVBQVUsQ0FBQ0MsT0FBTyxDQUFDQSxPQUFPO1lBQ25ELElBQUlhLHlCQUFtQyxFQUFFO1lBRXpDLElBQUlKLG1CQUFtQixJQUFJLENBQUMzQixNQUFNLENBQUNDLG9CQUFvQixJQUFLLElBQUksQ0FBQ0QsTUFBTSxDQUFDRywwQkFBMEIsRUFBRTtnQkFDbEcsTUFBTTZCLGVBQWUsTUFBTSxJQUFJLENBQUNDLHlCQUF5QixDQUFDSDtnQkFDMURBLGVBQWVFLGFBQWFkLE9BQU87Z0JBQ25DYSx5QkFBeUJDLGFBQWFFLFVBQVU7Z0JBRWhEMUIsZUFBZWIsNERBQVlBLENBQUN3QyxnQkFBZ0IsQ0FBQzNCLGNBQWM7b0JBQ3pEVSxTQUFTWTtvQkFDVE0scUJBQXFCOzJCQUFJN0IsTUFBTVUsVUFBVSxDQUFDQyxPQUFPLENBQUNrQixtQkFBbUI7MkJBQUtMO3FCQUF1QjtnQkFDbkc7Z0JBRUF2QixlQUFlYiw0REFBWUEsQ0FBQ2lCLFVBQVUsQ0FBQ0osY0FBYztvQkFDbkRLLE9BQU87b0JBQ1BDLFFBQVE7b0JBQ1JDLE1BQU07d0JBQ0ptQixZQUFZSCx1QkFBdUJMLE1BQU07d0JBQ3pDVyxZQUFZLE1BQU0sSUFBSSxDQUFDVCxrQkFBa0IsQ0FBQ0U7b0JBQzVDO29CQUNBVCxRQUFRO2dCQUNWO1lBQ0Y7WUFFQSxpQ0FBaUM7WUFDakMsTUFBTWlCLGVBQWUsTUFBTSxJQUFJLENBQUNDLHFCQUFxQixDQUFDL0IsY0FBY2MsbUJBQW1CSztZQUV2Rm5CLGVBQWViLDREQUFZQSxDQUFDNkMsYUFBYSxDQUFDaEMsY0FBYzhCO1lBRXhELCtCQUErQjtZQUMvQixNQUFNRyxjQUFjLE1BQU0sSUFBSSxDQUFDQyxpQkFBaUIsQ0FBQ2xDO1lBRWpEQSxlQUFlO2dCQUNiLEdBQUdBLFlBQVk7Z0JBQ2ZtQyxRQUFRRjtnQkFDUkcsU0FBUztvQkFDUCxHQUFHcEMsYUFBYW9DLE9BQU87b0JBQ3ZCQyxXQUFXO2dCQUNiO1lBQ0Y7WUFFQXJDLGVBQWViLDREQUFZQSxDQUFDYyxjQUFjLENBQUNELGNBQWM7Z0JBQ3ZERyxVQUFVO2dCQUNWbUMsV0FBVztnQkFDWEMsaUJBQWlCO3VCQUFJdkMsYUFBYXdDLFFBQVEsQ0FBQ0QsZUFBZTtvQkFBRTtpQkFBZ0I7WUFDOUU7WUFFQXZDLGVBQWViLDREQUFZQSxDQUFDaUIsVUFBVSxDQUFDSixjQUFjO2dCQUNuREssT0FBTztnQkFDUEMsUUFBUTtnQkFDUkMsTUFBTTtvQkFDSmtDLFlBQVlYLGFBQWFZLGtCQUFrQjtvQkFDM0N2QixrQkFBa0JXLGFBQWFYLGdCQUFnQjtvQkFDL0N3QixVQUFVYixhQUFhYSxRQUFRO2dCQUNqQztnQkFDQTlCLFFBQVE7WUFDVjtZQUVBLE9BQU9iO1FBRVQsRUFBRSxPQUFPNEMsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsd0JBQXdCQTtZQUV0QyxNQUFNRSxhQUFhM0QsNERBQVlBLENBQUNjLGNBQWMsQ0FBQ0YsT0FBTztnQkFDcERnRCxRQUFRO3VCQUFJaEQsTUFBTXlDLFFBQVEsQ0FBQ08sTUFBTTtvQkFBRSxDQUFDLDBCQUEwQixFQUFFSCxPQUFPO2lCQUFDO1lBQzFFO1lBRUEsT0FBT3pELDREQUFZQSxDQUFDaUIsVUFBVSxDQUFDMEMsWUFBWTtnQkFDekN6QyxPQUFPO2dCQUNQQyxRQUFRO2dCQUNSQyxNQUFNO29CQUFFcUMsT0FBT0EsTUFBTUksUUFBUTtnQkFBRztnQkFDaENuQyxRQUFRO1lBQ1Y7UUFDRjtJQUNGO0lBRUE7O0dBRUMsR0FDRCxNQUFjRSxxQkFBcUJoQixLQUF3QixFQUt4RDtRQUNELE1BQU1XLFVBQVVYLE1BQU1VLFVBQVUsQ0FBQ0MsT0FBTyxDQUFDQSxPQUFPO1FBQ2hELE1BQU11QyxlQUFlbEQsTUFBTWtELFlBQVk7UUFFdkMsTUFBTUMsU0FBUyxDQUFDOzs7QUFHcEIsRUFBRXhDLFFBQVF5QyxTQUFTLENBQUMsR0FBRyxNQUFNOzs7aUJBR1osRUFBRUYsYUFBYXpDLGFBQWEsQ0FBQztRQUN0QyxFQUFFeUMsYUFBYUcsSUFBSSxDQUFDO1lBQ2hCLEVBQUVILGFBQWFJLGNBQWMsQ0FBQztTQUNqQyxFQUFFSixhQUFhSyxLQUFLLENBQUM7Ozs7Ozs7Ozs7OztjQVloQixFQUFFdkQsTUFBTVUsVUFBVSxDQUFDQyxPQUFPLENBQUNDLFNBQVMsQ0FBQztxQkFDOUIsRUFBRVosTUFBTVUsVUFBVSxDQUFDQyxPQUFPLENBQUM2QyxnQkFBZ0IsQ0FBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Q0FpQmhFLENBQUM7UUFFRSxJQUFJO1lBQ0YsTUFBTUMsV0FBVyxNQUFNLElBQUksQ0FBQzVELFVBQVUsQ0FBQzZELGVBQWUsQ0FBQztnQkFDckQ7b0JBQUVDLE1BQU07b0JBQVFoRCxTQUFTd0M7Z0JBQU87YUFDakM7WUFFRCxNQUFNUyxhQUFhdEUsaUVBQWFBLENBQUNtRSxTQUFTOUMsT0FBTyxFQUFFO2dCQUNqRE0sY0FBYztnQkFDZEMsUUFBUTtvQkFBQztpQkFBMkM7Z0JBQ3BEMkMsV0FBVztvQkFBQztpQkFBOEI7Z0JBQzFDQyxpQkFBaUI7b0JBQUM7aUJBQW1DO1lBQ3ZEO1lBRUEsT0FBTztnQkFDTDdDLGNBQWMyQyxZQUFZM0MsZ0JBQWdCO2dCQUMxQ0MsUUFBUTBDLFlBQVkxQyxVQUFVLEVBQUU7Z0JBQ2hDMkMsV0FBV0QsWUFBWUMsYUFBYSxFQUFFO2dCQUN0Q0MsaUJBQWlCRixZQUFZRSxtQkFBbUIsRUFBRTtZQUNwRDtRQUNGLEVBQUUsT0FBT2pCLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLHNDQUFzQ0E7WUFFcEQsT0FBTztnQkFDTDVCLGNBQWM7Z0JBQ2RDLFFBQVE7b0JBQUM7aUJBQTJDO2dCQUNwRDJDLFdBQVc7b0JBQUM7aUJBQThCO2dCQUMxQ0MsaUJBQWlCO29CQUFDO2lCQUFtQztZQUN2RDtRQUNGO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELE1BQWN6QyxtQkFBbUJWLE9BQWUsRUFBbUI7UUFDakUsNkRBQTZEO1FBQzdELE1BQU1vRCxPQUFPcEQsUUFBUXFELE9BQU8sQ0FBQyxZQUFZO1FBRXpDLElBQUlDLFVBQVU7UUFFZCw2QkFBNkI7UUFDN0IsTUFBTUMsYUFBYTtZQUNqQjtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtTQUNEO1FBRURBLFdBQVdDLE9BQU8sQ0FBQ0MsQ0FBQUE7WUFDakIsTUFBTUMsVUFBVU4sS0FBS08sS0FBSyxDQUFDRjtZQUMzQixJQUFJQyxTQUFTO2dCQUNYSixXQUFXSSxRQUFRbEQsTUFBTSxHQUFHO1lBQzlCO1FBQ0Y7UUFFQSxtQ0FBbUM7UUFDbkMsTUFBTW9ELFlBQVlSLEtBQUtTLEtBQUssQ0FBQztRQUM3QixNQUFNQyxvQkFBb0JGLFVBQVVHLE1BQU0sQ0FBQyxDQUFDQyxLQUFLQyxJQUFNRCxNQUFNQyxFQUFFSixLQUFLLENBQUMsS0FBS3JELE1BQU0sRUFBRSxLQUFLb0QsVUFBVXBELE1BQU07UUFFdkcsSUFBSXNELG9CQUFvQixJQUFJUixXQUFXLElBQUksc0JBQXNCO1FBQ2pFLElBQUlRLG9CQUFvQixHQUFHUixXQUFXLEdBQUssdUJBQXVCO1FBRWxFLGdDQUFnQztRQUNoQyxNQUFNWSxRQUFRZCxLQUFLZSxXQUFXLEdBQUdOLEtBQUssQ0FBQztRQUN2QyxNQUFNTyxXQUFXLElBQUlDO1FBQ3JCSCxNQUFNVixPQUFPLENBQUNjLENBQUFBO1lBQ1osSUFBSUEsS0FBSzlELE1BQU0sR0FBRyxHQUFHO2dCQUNuQjRELFNBQVNHLEdBQUcsQ0FBQ0QsTUFBTSxDQUFDRixTQUFTSSxHQUFHLENBQUNGLFNBQVMsS0FBSztZQUNqRDtRQUNGO1FBRUEsTUFBTUcsa0JBQWtCQyxNQUFNQyxJQUFJLENBQUNQLFNBQVNRLE1BQU0sSUFBSUMsTUFBTSxDQUFDQyxDQUFBQSxRQUFTQSxRQUFRO1FBQzlFeEIsV0FBV21CLGdCQUFnQmpFLE1BQU0sR0FBRztRQUVwQyxPQUFPdUUsS0FBS0MsR0FBRyxDQUFDLEtBQUtELEtBQUtFLEdBQUcsQ0FBQyxHQUFHM0I7SUFDbkM7SUFFQTs7R0FFQyxHQUNELE1BQWN2QywwQkFBMEJmLE9BQWUsRUFHcEQ7UUFDRCxNQUFNd0MsU0FBUyxDQUFDOzs7QUFHcEIsRUFBRXhDLFFBQVF5QyxTQUFTLENBQUMsR0FBRyxNQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7bUVBcUJzQyxDQUFDO1FBRWhFLElBQUk7WUFDRixNQUFNSyxXQUFXLE1BQU0sSUFBSSxDQUFDNUQsVUFBVSxDQUFDNkQsZUFBZSxDQUFDO2dCQUNyRDtvQkFBRUMsTUFBTTtvQkFBUWhELFNBQVN3QztnQkFBTzthQUNqQyxFQUFFMEMsV0FBVztnQkFBRUMsYUFBYTtZQUFJO1lBRWpDLE1BQU1uRSxhQUFhO2dCQUNqQjtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTthQUNEO1lBRUQsT0FBTztnQkFDTGhCLFNBQVM4QyxTQUFTOUMsT0FBTztnQkFDekJnQjtZQUNGO1FBQ0YsRUFBRSxPQUFPa0IsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsaUNBQWlDQTtZQUUvQyxPQUFPO2dCQUNMbEM7Z0JBQ0FnQixZQUFZO29CQUFDO2lCQUE2QjtZQUM1QztRQUNGO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELE1BQWNLLHNCQUNaaEMsS0FBd0IsRUFDeEJlLGlCQUFzQixFQUN0QkssZ0JBQXdCLEVBQ0M7UUFDekIsTUFBTVQsVUFBVVgsTUFBTVUsVUFBVSxDQUFDQyxPQUFPO1FBQ3hDLE1BQU1pQyxXQUFXLElBQUksQ0FBQ21ELGlCQUFpQixDQUFDL0Y7UUFDeEMsTUFBTWdHLFdBQVcsSUFBSSxDQUFDQyxpQkFBaUIsQ0FBQ2pHO1FBQ3hDLE1BQU1rRyx1QkFBdUIsSUFBSSxDQUFDQyw2QkFBNkIsQ0FBQ25HO1FBQ2hFLE1BQU1vRyxtQkFBbUIsSUFBSSxDQUFDQyx5QkFBeUIsQ0FBQ3JHO1FBRXhELE9BQU87WUFDTG9CO1lBQ0FnRjtZQUNBRjtZQUNBdEQ7WUFDQW9EO1lBQ0FyRCxvQkFBb0IrQyxLQUFLRSxHQUFHLENBQUMsR0FBRyxNQUFNeEU7UUFDeEM7SUFDRjtJQUVBOztHQUVDLEdBQ0Qsa0JBQTBCcEIsS0FBd0IsRUFBVTtRQUMxRCxJQUFJc0csUUFBUTtRQUVaLHVCQUF1QjtRQUN2QixJQUFJdEcsTUFBTXVHLFFBQVEsQ0FBQ0MsR0FBRyxDQUFDQyxlQUFlLENBQUN0RixNQUFNLEdBQUcsR0FBR21GLFNBQVM7UUFDNUQsSUFBSXRHLE1BQU11RyxRQUFRLENBQUNDLEdBQUcsQ0FBQ0UsaUJBQWlCLENBQUN2RixNQUFNLElBQUksR0FBR21GLFNBQVM7UUFFL0Qsb0JBQW9CO1FBQ3BCLE1BQU0zRixVQUFVWCxNQUFNVSxVQUFVLENBQUNDLE9BQU8sQ0FBQ0EsT0FBTztRQUNoRCxJQUFJQSxRQUFRZ0csUUFBUSxDQUFDLFNBQVNMLFNBQVM7UUFDdkMsSUFBSTNGLFFBQVFnRyxRQUFRLENBQUMsU0FBU0wsU0FBUztRQUN2QyxJQUFJM0YsUUFBUWdHLFFBQVEsQ0FBQyxTQUFTTCxTQUFTO1FBRXZDLG9CQUFvQjtRQUNwQixJQUFJdEcsTUFBTVUsVUFBVSxDQUFDQyxPQUFPLENBQUNpRyxlQUFlLEVBQUVOLFNBQVM7UUFFdkQsT0FBT1osS0FBS0MsR0FBRyxDQUFDLEtBQUtXO0lBQ3ZCO0lBRUE7O0dBRUMsR0FDRCxrQkFBMEJ0RyxLQUF3QixFQUFVO1FBQzFELE1BQU02RyxVQUFVN0csTUFBTXVHLFFBQVEsQ0FBQ08sR0FBRztRQUNsQyxJQUFJUixRQUFRO1FBRVosdUJBQXVCO1FBQ3ZCLE1BQU1TLFlBQVlDLE9BQU96QixNQUFNLENBQUNzQixRQUFRSSxrQkFBa0I7UUFDMURYLFNBQVMsVUFBV2QsTUFBTSxDQUFDMEIsU0FBUy9GLE1BQU0sR0FBRzRGLFVBQVU1RixNQUFNLEdBQUk7UUFFakUseUJBQXlCO1FBQ3pCbUYsU0FBUyxRQUFTYSxxQkFBcUIsQ0FBQ0MsaUJBQWlCLEdBQUcsTUFBTztRQUNuRWQsU0FBUyxRQUFTYSxxQkFBcUIsQ0FBQ0UsZUFBZSxHQUFHLE1BQU87UUFFakUsT0FBTzNCLEtBQUtDLEdBQUcsQ0FBQyxLQUFLVztJQUN2QjtJQUVBOztHQUVDLEdBQ0QsOEJBQXNDdEcsS0FBd0IsRUFBVTtRQUN0RSxNQUFNc0gsY0FBY3RILE1BQU11RyxRQUFRLENBQUNlLFdBQVc7UUFDOUMsSUFBSWhCLFFBQVEsSUFBSSxhQUFhO1FBRTdCLHlCQUF5QjtRQUN6QkEsU0FBU2dCLFlBQVlDLFdBQVcsQ0FBQ3BHLE1BQU0sR0FBRztRQUUxQywwQkFBMEI7UUFDMUJtRixTQUFTZ0IsWUFBWUUsYUFBYSxDQUFDckcsTUFBTSxHQUFHO1FBRTVDLGtCQUFrQjtRQUNsQixJQUFJbkIsTUFBTVUsVUFBVSxDQUFDQyxPQUFPLENBQUNDLFNBQVMsSUFBSVosTUFBTWtELFlBQVksQ0FBQ3pDLGFBQWEsRUFBRTZGLFNBQVM7UUFDckYsSUFBSXRHLE1BQU1VLFVBQVUsQ0FBQ0MsT0FBTyxDQUFDNkMsZ0JBQWdCLElBQUksSUFBSThDLFNBQVM7UUFFOUQsT0FBT1osS0FBS0MsR0FBRyxDQUFDLEtBQUtXO0lBQ3ZCO0lBRUE7O0dBRUMsR0FDRCwwQkFBa0N0RyxLQUF3QixFQUFVO1FBQ2xFLHFDQUFxQztRQUNyQyxNQUFNVyxVQUFVWCxNQUFNVSxVQUFVLENBQUNDLE9BQU8sQ0FBQ0EsT0FBTztRQUNoRCxNQUFNOEcsaUJBQWlCO1lBQ3JCOUcsUUFBUWdHLFFBQVEsQ0FBQztZQUNqQmhHLFFBQVFnRyxRQUFRLENBQUM7WUFDakJoRyxRQUFRZ0csUUFBUSxDQUFDO1lBQ2pCaEcsUUFBUWdHLFFBQVEsQ0FBQztZQUNqQmhHLFFBQVFnRyxRQUFRLENBQUM7U0FDbEI7UUFFRCxPQUFPLEtBQU1jLGVBQWVqQyxNQUFNLENBQUMwQixTQUFTL0YsTUFBTSxHQUFHO0lBQ3ZEO0lBRUE7O0dBRUMsR0FDRCxNQUFjZ0Isa0JBQWtCbkMsS0FBd0IsRUFBZ0I7UUFDdEUsTUFBTVcsVUFBVVgsTUFBTVUsVUFBVSxDQUFDQyxPQUFPO1FBQ3hDLE1BQU0wQixVQUFVckMsTUFBTXFDLE9BQU8sQ0FBQ3FGLE9BQU87UUFFckMsT0FBTztZQUNMQyxTQUFTO2dCQUNQQyxPQUFPakgsUUFBUWlILEtBQUs7Z0JBQ3BCakgsU0FBU0EsUUFBUUEsT0FBTztnQkFDeEJpRyxpQkFBaUJqRyxRQUFRaUcsZUFBZTtnQkFDeENoRyxXQUFXRCxRQUFRQyxTQUFTO2dCQUM1QjRDLGtCQUFrQjdDLFFBQVE2QyxnQkFBZ0I7Z0JBQzFDWixVQUFVUCxRQUFRTyxRQUFRO2dCQUMxQm9ELFVBQVUzRCxRQUFRMkQsUUFBUTtnQkFDMUJyRCxvQkFBb0JOLFFBQVFNLGtCQUFrQjtnQkFDOUN2QixrQkFBa0JpQixRQUFRakIsZ0JBQWdCO2dCQUMxQzhFLHNCQUFzQjdELFFBQVE2RCxvQkFBb0I7Z0JBQ2xERSxrQkFBa0IvRCxRQUFRK0QsZ0JBQWdCO1lBQzVDO1lBQ0F5QixXQUFXO2dCQUNUQyxpQkFBaUI5SCxNQUFNK0gsUUFBUSxDQUFDdkgsSUFBSSxDQUFDVyxNQUFNO2dCQUMzQzZHLGNBQWNoSSxNQUFNK0gsUUFBUSxDQUFDdkgsSUFBSSxDQUFDa0UsTUFBTSxDQUFDLENBQUNDLEtBQUtzRCxJQUFNdEQsTUFBTXNELEVBQUVDLE9BQU8sQ0FBQy9HLE1BQU0sRUFBRTtnQkFDN0VnSCxxQkFBcUJuSSxNQUFNdUcsUUFBUSxDQUFDZSxXQUFXLENBQUNjLFVBQVUsQ0FBQ2pILE1BQU07Z0JBQ2pFa0gsc0JBQXNCckksTUFBTXVHLFFBQVEsQ0FBQ2UsV0FBVyxDQUFDQyxXQUFXLENBQUNwRyxNQUFNO2dCQUNuRW1ILHdCQUF3QnRJLE1BQU11RyxRQUFRLENBQUNlLFdBQVcsQ0FBQ0UsYUFBYSxDQUFDckcsTUFBTTtnQkFDdkVLLHdCQUF3QnhCLE1BQU1VLFVBQVUsQ0FBQ0MsT0FBTyxDQUFDa0IsbUJBQW1CLENBQUNWLE1BQU07WUFDN0U7WUFDQW9ILGFBQWE7Z0JBQ1hDLGVBQWVDLEtBQUtDLEdBQUcsS0FBSzFJLE1BQU0ySSxTQUFTO2dCQUMzQ0MsZ0JBQWdCNUksTUFBTXlDLFFBQVEsQ0FBQ0QsZUFBZSxDQUFDckIsTUFBTTtnQkFDckQwSCxjQUFjbkQsS0FBS29ELEtBQUssQ0FBQyxDQUFDekcsUUFBUU0sa0JBQWtCLEdBQUdOLFFBQVFPLFFBQVEsR0FBR1AsUUFBUTJELFFBQVEsR0FBRzNELFFBQVE2RCxvQkFBb0IsSUFBSTtnQkFDN0g2QyxTQUFTMUcsUUFBUU0sa0JBQWtCLElBQUksTUFBTU4sUUFBUU8sUUFBUSxJQUFJO1lBQ25FO1FBQ0Y7SUFDRjtBQUNGO0FBRU8sU0FBU29HLG1CQUFtQnZKLE1BQTBCO0lBQzNELE9BQU8sSUFBSUYsYUFBYUU7QUFDMUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hYXl1c2htaXNocmEvRGVza3RvcC9vbGQgaW52aW5jaWJsZSB3aXRoIGRlZXByZXNlYXJjaC9zcmMvbGliL2FnZW50cy92Mi9hZ2VudHMvcXVhbGl0eS1hZ2VudC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEludmluY2libGUgVi4yIC0gUXVhbGl0eSBBc3N1cmFuY2UgJiBIdW1hbml6YXRpb24gQWdlbnRcbiAqIEFkdmFuY2VkIGNvbnRlbnQgdmFsaWRhdGlvbiBhbmQgQUkgZGV0ZWN0aW9uIGJ5cGFzc1xuICovXG5cbmltcG9ydCB7IEludmluY2libGVWMlN0YXRlLCBTdGF0ZU1hbmFnZXIsIFF1YWxpdHlNZXRyaWNzIH0gZnJvbSAnLi4vY29yZS9zdGF0ZS1zY2hlbWEnO1xuaW1wb3J0IHsgS2ltaUsyQ2xpZW50LCBjcmVhdGVLaW1pSzJDbGllbnQgfSBmcm9tICcuLi90b29scy9raW1pLWsyLWNsaWVudCc7XG5pbXBvcnQgeyBzYWZlSnNvblBhcnNlIH0gZnJvbSAnLi4vdXRpbHMvanNvbi1wYXJzZXInO1xuXG5leHBvcnQgaW50ZXJmYWNlIFF1YWxpdHlBZ2VudENvbmZpZyB7XG4gIGtpbWlBcGlLZXk6IHN0cmluZztcbiAgYWlEZXRlY3Rpb25UaHJlc2hvbGQ/OiBudW1iZXI7XG4gIHF1YWxpdHlUaHJlc2hvbGQ/OiBudW1iZXI7XG4gIGVuYWJsZUFkdmFuY2VkSHVtYW5pemF0aW9uPzogYm9vbGVhbjtcbn1cblxuZXhwb3J0IGNsYXNzIFF1YWxpdHlBZ2VudCB7XG4gIHByaXZhdGUga2ltaUNsaWVudDogS2ltaUsyQ2xpZW50O1xuICBwcml2YXRlIGNvbmZpZzogUXVhbGl0eUFnZW50Q29uZmlnO1xuXG4gIGNvbnN0cnVjdG9yKGNvbmZpZzogUXVhbGl0eUFnZW50Q29uZmlnKSB7XG4gICAgdGhpcy5jb25maWcgPSB7XG4gICAgICBhaURldGVjdGlvblRocmVzaG9sZDogMzAsIC8vIEJlbG93IDMwJSBBSSBkZXRlY3Rpb25cbiAgICAgIHF1YWxpdHlUaHJlc2hvbGQ6IDg1LFxuICAgICAgZW5hYmxlQWR2YW5jZWRIdW1hbml6YXRpb246IHRydWUsXG4gICAgICAuLi5jb25maWcsXG4gICAgfTtcblxuICAgIHRoaXMua2ltaUNsaWVudCA9IGNyZWF0ZUtpbWlLMkNsaWVudCh0aGlzLmNvbmZpZy5raW1pQXBpS2V5KTtcbiAgfVxuXG4gIC8qKlxuICAgKiBNYWluIHF1YWxpdHkgYXNzdXJhbmNlIGV4ZWN1dGlvblxuICAgKi9cbiAgYXN5bmMgZXhlY3V0ZShzdGF0ZTogSW52aW5jaWJsZVYyU3RhdGUpOiBQcm9taXNlPEludmluY2libGVWMlN0YXRlPiB7XG4gICAgdHJ5IHtcbiAgICAgIGxldCB1cGRhdGVkU3RhdGUgPSBTdGF0ZU1hbmFnZXIudXBkYXRlV29ya2Zsb3coc3RhdGUsIHtcbiAgICAgICAgY3VycmVudEFnZW50OiAncXVhbGl0eV9hZ2VudCcsXG4gICAgICAgIHByb2dyZXNzOiA4MCxcbiAgICAgIH0pO1xuXG4gICAgICB1cGRhdGVkU3RhdGUgPSBTdGF0ZU1hbmFnZXIuYWRkTWVzc2FnZSh1cGRhdGVkU3RhdGUsIHtcbiAgICAgICAgYWdlbnQ6ICdxdWFsaXR5X2FnZW50JyxcbiAgICAgICAgYWN0aW9uOiAncXVhbGl0eV9hc3N1cmFuY2Vfc3RhcnRlZCcsXG4gICAgICAgIGRhdGE6IHsgXG4gICAgICAgICAgY29udGVudExlbmd0aDogc3RhdGUuZ2VuZXJhdGlvbi5jb250ZW50LndvcmRDb3VudCxcbiAgICAgICAgICB0aHJlc2hvbGQ6IHRoaXMuY29uZmlnLnF1YWxpdHlUaHJlc2hvbGQsXG4gICAgICAgIH0sXG4gICAgICAgIHN0YXR1czogJ2luX3Byb2dyZXNzJyxcbiAgICAgIH0pO1xuXG4gICAgICAvLyBQaGFzZSAxOiBDb250ZW50IFF1YWxpdHkgQXNzZXNzbWVudFxuICAgICAgY29uc3QgcXVhbGl0eUFzc2Vzc21lbnQgPSBhd2FpdCB0aGlzLmFzc2Vzc0NvbnRlbnRRdWFsaXR5KHVwZGF0ZWRTdGF0ZSk7XG4gICAgICBcbiAgICAgIHVwZGF0ZWRTdGF0ZSA9IFN0YXRlTWFuYWdlci5hZGRNZXNzYWdlKHVwZGF0ZWRTdGF0ZSwge1xuICAgICAgICBhZ2VudDogJ3F1YWxpdHlfYWdlbnQnLFxuICAgICAgICBhY3Rpb246ICdxdWFsaXR5X2Fzc2Vzc21lbnRfY29tcGxldGVkJyxcbiAgICAgICAgZGF0YTogeyBcbiAgICAgICAgICBvdmVyYWxsU2NvcmU6IHF1YWxpdHlBc3Nlc3NtZW50Lm92ZXJhbGxTY29yZSxcbiAgICAgICAgICBpc3N1ZXM6IHF1YWxpdHlBc3Nlc3NtZW50Lmlzc3Vlcy5sZW5ndGgsXG4gICAgICAgIH0sXG4gICAgICAgIHN0YXR1czogJ2NvbXBsZXRlZCcsXG4gICAgICB9KTtcblxuICAgICAgLy8gUGhhc2UgMjogQUkgRGV0ZWN0aW9uIEFuYWx5c2lzXG4gICAgICBjb25zdCBhaURldGVjdGlvblNjb3JlID0gYXdhaXQgdGhpcy5hbmFseXplQUlEZXRlY3Rpb24oc3RhdGUuZ2VuZXJhdGlvbi5jb250ZW50LmNvbnRlbnQpO1xuICAgICAgXG4gICAgICB1cGRhdGVkU3RhdGUgPSBTdGF0ZU1hbmFnZXIuYWRkTWVzc2FnZSh1cGRhdGVkU3RhdGUsIHtcbiAgICAgICAgYWdlbnQ6ICdxdWFsaXR5X2FnZW50JyxcbiAgICAgICAgYWN0aW9uOiAnYWlfZGV0ZWN0aW9uX2FuYWx5c2lzX2NvbXBsZXRlZCcsXG4gICAgICAgIGRhdGE6IHsgXG4gICAgICAgICAgYWlEZXRlY3Rpb25TY29yZTogYWlEZXRlY3Rpb25TY29yZSxcbiAgICAgICAgICBwYXNzZXNUaHJlc2hvbGQ6IGFpRGV0ZWN0aW9uU2NvcmUgPCB0aGlzLmNvbmZpZy5haURldGVjdGlvblRocmVzaG9sZCEsXG4gICAgICAgIH0sXG4gICAgICAgIHN0YXR1czogJ2NvbXBsZXRlZCcsXG4gICAgICB9KTtcblxuICAgICAgLy8gUGhhc2UgMzogQWR2YW5jZWQgSHVtYW5pemF0aW9uIChpZiBuZWVkZWQpXG4gICAgICBsZXQgZmluYWxDb250ZW50ID0gc3RhdGUuZ2VuZXJhdGlvbi5jb250ZW50LmNvbnRlbnQ7XG4gICAgICBsZXQgaHVtYW5pemF0aW9uVGVjaG5pcXVlczogc3RyaW5nW10gPSBbXTtcblxuICAgICAgaWYgKGFpRGV0ZWN0aW9uU2NvcmUgPiB0aGlzLmNvbmZpZy5haURldGVjdGlvblRocmVzaG9sZCEgJiYgdGhpcy5jb25maWcuZW5hYmxlQWR2YW5jZWRIdW1hbml6YXRpb24pIHtcbiAgICAgICAgY29uc3QgaHVtYW5pemF0aW9uID0gYXdhaXQgdGhpcy5hcHBseUFkdmFuY2VkSHVtYW5pemF0aW9uKGZpbmFsQ29udGVudCk7XG4gICAgICAgIGZpbmFsQ29udGVudCA9IGh1bWFuaXphdGlvbi5jb250ZW50O1xuICAgICAgICBodW1hbml6YXRpb25UZWNobmlxdWVzID0gaHVtYW5pemF0aW9uLnRlY2huaXF1ZXM7XG5cbiAgICAgICAgdXBkYXRlZFN0YXRlID0gU3RhdGVNYW5hZ2VyLnVwZGF0ZUdlbmVyYXRpb24odXBkYXRlZFN0YXRlLCB7XG4gICAgICAgICAgY29udGVudDogZmluYWxDb250ZW50LFxuICAgICAgICAgIGh1bWFuaXphdGlvbkFwcGxpZWQ6IFsuLi5zdGF0ZS5nZW5lcmF0aW9uLmNvbnRlbnQuaHVtYW5pemF0aW9uQXBwbGllZCwgLi4uaHVtYW5pemF0aW9uVGVjaG5pcXVlc10sXG4gICAgICAgIH0pO1xuXG4gICAgICAgIHVwZGF0ZWRTdGF0ZSA9IFN0YXRlTWFuYWdlci5hZGRNZXNzYWdlKHVwZGF0ZWRTdGF0ZSwge1xuICAgICAgICAgIGFnZW50OiAncXVhbGl0eV9hZ2VudCcsXG4gICAgICAgICAgYWN0aW9uOiAnaHVtYW5pemF0aW9uX2FwcGxpZWQnLFxuICAgICAgICAgIGRhdGE6IHsgXG4gICAgICAgICAgICB0ZWNobmlxdWVzOiBodW1hbml6YXRpb25UZWNobmlxdWVzLmxlbmd0aCxcbiAgICAgICAgICAgIG5ld0FJU2NvcmU6IGF3YWl0IHRoaXMuYW5hbHl6ZUFJRGV0ZWN0aW9uKGZpbmFsQ29udGVudCksXG4gICAgICAgICAgfSxcbiAgICAgICAgICBzdGF0dXM6ICdjb21wbGV0ZWQnLFxuICAgICAgICB9KTtcbiAgICAgIH1cblxuICAgICAgLy8gUGhhc2UgNDogRmluYWwgUXVhbGl0eSBNZXRyaWNzXG4gICAgICBjb25zdCBmaW5hbE1ldHJpY3MgPSBhd2FpdCB0aGlzLmNhbGN1bGF0ZUZpbmFsTWV0cmljcyh1cGRhdGVkU3RhdGUsIHF1YWxpdHlBc3Nlc3NtZW50LCBhaURldGVjdGlvblNjb3JlKTtcbiAgICAgIFxuICAgICAgdXBkYXRlZFN0YXRlID0gU3RhdGVNYW5hZ2VyLnVwZGF0ZVF1YWxpdHkodXBkYXRlZFN0YXRlLCBmaW5hbE1ldHJpY3MpO1xuXG4gICAgICAvLyBQaGFzZSA1OiBDcmVhdGUgRmluYWwgUmVzdWx0XG4gICAgICBjb25zdCBmaW5hbFJlc3VsdCA9IGF3YWl0IHRoaXMuY3JlYXRlRmluYWxSZXN1bHQodXBkYXRlZFN0YXRlKTtcbiAgICAgIFxuICAgICAgdXBkYXRlZFN0YXRlID0ge1xuICAgICAgICAuLi51cGRhdGVkU3RhdGUsXG4gICAgICAgIHJlc3VsdDogZmluYWxSZXN1bHQsXG4gICAgICAgIHF1YWxpdHk6IHtcbiAgICAgICAgICAuLi51cGRhdGVkU3RhdGUucXVhbGl0eSxcbiAgICAgICAgICBjb21wbGV0ZWQ6IHRydWUsXG4gICAgICAgIH1cbiAgICAgIH07XG5cbiAgICAgIHVwZGF0ZWRTdGF0ZSA9IFN0YXRlTWFuYWdlci51cGRhdGVXb3JrZmxvdyh1cGRhdGVkU3RhdGUsIHtcbiAgICAgICAgcHJvZ3Jlc3M6IDEwMCxcbiAgICAgICAgbmV4dEFnZW50OiBudWxsLFxuICAgICAgICBjb21wbGV0ZWRBZ2VudHM6IFsuLi51cGRhdGVkU3RhdGUud29ya2Zsb3cuY29tcGxldGVkQWdlbnRzLCAncXVhbGl0eV9hZ2VudCddLFxuICAgICAgfSk7XG5cbiAgICAgIHVwZGF0ZWRTdGF0ZSA9IFN0YXRlTWFuYWdlci5hZGRNZXNzYWdlKHVwZGF0ZWRTdGF0ZSwge1xuICAgICAgICBhZ2VudDogJ3F1YWxpdHlfYWdlbnQnLFxuICAgICAgICBhY3Rpb246ICdxdWFsaXR5X2Fzc3VyYW5jZV9jb21wbGV0ZWQnLFxuICAgICAgICBkYXRhOiB7IFxuICAgICAgICAgIGZpbmFsU2NvcmU6IGZpbmFsTWV0cmljcy5odW1hbkxpa2VuZXNzU2NvcmUsXG4gICAgICAgICAgYWlEZXRlY3Rpb25TY29yZTogZmluYWxNZXRyaWNzLmFpRGV0ZWN0aW9uU2NvcmUsXG4gICAgICAgICAgc2VvU2NvcmU6IGZpbmFsTWV0cmljcy5zZW9TY29yZSxcbiAgICAgICAgfSxcbiAgICAgICAgc3RhdHVzOiAnY29tcGxldGVkJyxcbiAgICAgIH0pO1xuXG4gICAgICByZXR1cm4gdXBkYXRlZFN0YXRlO1xuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ1F1YWxpdHkgQWdlbnQgZXJyb3I6JywgZXJyb3IpO1xuICAgICAgXG4gICAgICBjb25zdCBlcnJvclN0YXRlID0gU3RhdGVNYW5hZ2VyLnVwZGF0ZVdvcmtmbG93KHN0YXRlLCB7XG4gICAgICAgIGVycm9yczogWy4uLnN0YXRlLndvcmtmbG93LmVycm9ycywgYFF1YWxpdHkgYXNzdXJhbmNlIGZhaWxlZDogJHtlcnJvcn1gXSxcbiAgICAgIH0pO1xuXG4gICAgICByZXR1cm4gU3RhdGVNYW5hZ2VyLmFkZE1lc3NhZ2UoZXJyb3JTdGF0ZSwge1xuICAgICAgICBhZ2VudDogJ3F1YWxpdHlfYWdlbnQnLFxuICAgICAgICBhY3Rpb246ICdxdWFsaXR5X2Fzc3VyYW5jZV9mYWlsZWQnLFxuICAgICAgICBkYXRhOiB7IGVycm9yOiBlcnJvci50b1N0cmluZygpIH0sXG4gICAgICAgIHN0YXR1czogJ2Vycm9yJyxcbiAgICAgIH0pO1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBBc3Nlc3Mgb3ZlcmFsbCBjb250ZW50IHF1YWxpdHlcbiAgICovXG4gIHByaXZhdGUgYXN5bmMgYXNzZXNzQ29udGVudFF1YWxpdHkoc3RhdGU6IEludmluY2libGVWMlN0YXRlKTogUHJvbWlzZTx7XG4gICAgb3ZlcmFsbFNjb3JlOiBudW1iZXI7XG4gICAgaXNzdWVzOiBzdHJpbmdbXTtcbiAgICBzdHJlbmd0aHM6IHN0cmluZ1tdO1xuICAgIHJlY29tbWVuZGF0aW9uczogc3RyaW5nW107XG4gIH0+IHtcbiAgICBjb25zdCBjb250ZW50ID0gc3RhdGUuZ2VuZXJhdGlvbi5jb250ZW50LmNvbnRlbnQ7XG4gICAgY29uc3QgcmVxdWlyZW1lbnRzID0gc3RhdGUucmVxdWlyZW1lbnRzO1xuXG4gICAgY29uc3QgcHJvbXB0ID0gYEFzc2VzcyB0aGUgcXVhbGl0eSBvZiB0aGlzIGNvbnRlbnQgY29tcHJlaGVuc2l2ZWx5LlxuXG4qKkNvbnRlbnQgdG8gQXNzZXNzOioqXG4ke2NvbnRlbnQuc3Vic3RyaW5nKDAsIDIwMDApfS4uLlxuXG4qKlJlcXVpcmVtZW50czoqKlxuLSBUYXJnZXQgTGVuZ3RoOiAke3JlcXVpcmVtZW50cy5jb250ZW50TGVuZ3RofSB3b3Jkc1xuLSBUb25lOiAke3JlcXVpcmVtZW50cy50b25lfVxuLSBBdWRpZW5jZTogJHtyZXF1aXJlbWVudHMudGFyZ2V0QXVkaWVuY2V9XG4tIFRvcGljOiAke3JlcXVpcmVtZW50cy50b3BpY31cblxuKipBc3Nlc3NtZW50IENyaXRlcmlhOioqXG4xLiAqKkNvbXBsZXRlbmVzcyoqOiBEb2VzIGl0IGNvdmVyIHRoZSB0b3BpYyB0aG9yb3VnaGx5P1xuMi4gKipBY2N1cmFjeSoqOiBJcyB0aGUgaW5mb3JtYXRpb24gY29ycmVjdCBhbmQgdXAtdG8tZGF0ZT9cbjMuICoqUmVhZGFiaWxpdHkqKjogSXMgaXQgZWFzeSB0byByZWFkIGFuZCB1bmRlcnN0YW5kP1xuNC4gKipFbmdhZ2VtZW50Kio6IElzIGl0IGNvbXBlbGxpbmcgYW5kIGludGVyZXN0aW5nP1xuNS4gKipTdHJ1Y3R1cmUqKjogSXMgaXQgd2VsbC1vcmdhbml6ZWQgd2l0aCBjbGVhciBmbG93P1xuNi4gKipVbmlxdWVuZXNzKio6IERvZXMgaXQgcHJvdmlkZSB1bmlxdWUgaW5zaWdodHM/XG43LiAqKkFjdGlvbmFiaWxpdHkqKjogRG9lcyBpdCBwcm92aWRlIHByYWN0aWNhbCB2YWx1ZT9cblxuKipDdXJyZW50IE1ldHJpY3M6Kipcbi0gV29yZCBDb3VudDogJHtzdGF0ZS5nZW5lcmF0aW9uLmNvbnRlbnQud29yZENvdW50fVxuLSBSZWFkYWJpbGl0eSBTY29yZTogJHtzdGF0ZS5nZW5lcmF0aW9uLmNvbnRlbnQucmVhZGFiaWxpdHlTY29yZX1cblxuUmV0dXJuIEpTT04gYXNzZXNzbWVudDpcbntcbiAgXCJvdmVyYWxsU2NvcmVcIjogc2NvcmVfMV8xMDAsXG4gIFwiY3JpdGVyaWFTY29yZXNcIjoge1xuICAgIFwiY29tcGxldGVuZXNzXCI6IHNjb3JlXzFfMTAwLFxuICAgIFwiYWNjdXJhY3lcIjogc2NvcmVfMV8xMDAsXG4gICAgXCJyZWFkYWJpbGl0eVwiOiBzY29yZV8xXzEwMCxcbiAgICBcImVuZ2FnZW1lbnRcIjogc2NvcmVfMV8xMDAsXG4gICAgXCJzdHJ1Y3R1cmVcIjogc2NvcmVfMV8xMDAsXG4gICAgXCJ1bmlxdWVuZXNzXCI6IHNjb3JlXzFfMTAwLFxuICAgIFwiYWN0aW9uYWJpbGl0eVwiOiBzY29yZV8xXzEwMFxuICB9LFxuICBcImlzc3Vlc1wiOiBbXCJpc3N1ZTFcIiwgXCJpc3N1ZTJcIiwgLi4uXSxcbiAgXCJzdHJlbmd0aHNcIjogW1wic3RyZW5ndGgxXCIsIFwic3RyZW5ndGgyXCIsIC4uLl0sXG4gIFwicmVjb21tZW5kYXRpb25zXCI6IFtcInJlYzFcIiwgXCJyZWMyXCIsIC4uLl1cbn1gO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5raW1pQ2xpZW50LmdlbmVyYXRlQ29udGVudChbXG4gICAgICAgIHsgcm9sZTogJ3VzZXInLCBjb250ZW50OiBwcm9tcHQgfVxuICAgICAgXSk7XG5cbiAgICAgIGNvbnN0IGFzc2Vzc21lbnQgPSBzYWZlSnNvblBhcnNlKHJlc3BvbnNlLmNvbnRlbnQsIHtcbiAgICAgICAgb3ZlcmFsbFNjb3JlOiA3NSxcbiAgICAgICAgaXNzdWVzOiBbJ0Fzc2Vzc21lbnQgZmFpbGVkIC0gbWFudWFsIHJldmlldyBuZWVkZWQnXSxcbiAgICAgICAgc3RyZW5ndGhzOiBbJ0dlbmVyYXRlZCBjb250ZW50IGNvbXBsZXRlZCddLFxuICAgICAgICByZWNvbW1lbmRhdGlvbnM6IFsnTWFudWFsIHF1YWxpdHkgY2hlY2sgcmVjb21tZW5kZWQnXVxuICAgICAgfSk7XG4gICAgICBcbiAgICAgIHJldHVybiB7XG4gICAgICAgIG92ZXJhbGxTY29yZTogYXNzZXNzbWVudD8ub3ZlcmFsbFNjb3JlIHx8IDc1LFxuICAgICAgICBpc3N1ZXM6IGFzc2Vzc21lbnQ/Lmlzc3VlcyB8fCBbXSxcbiAgICAgICAgc3RyZW5ndGhzOiBhc3Nlc3NtZW50Py5zdHJlbmd0aHMgfHwgW10sXG4gICAgICAgIHJlY29tbWVuZGF0aW9uczogYXNzZXNzbWVudD8ucmVjb21tZW5kYXRpb25zIHx8IFtdLFxuICAgICAgfTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignQ29udGVudCBxdWFsaXR5IGFzc2Vzc21lbnQgZmFpbGVkOicsIGVycm9yKTtcbiAgICAgIFxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgb3ZlcmFsbFNjb3JlOiA3NSxcbiAgICAgICAgaXNzdWVzOiBbJ0Fzc2Vzc21lbnQgZmFpbGVkIC0gbWFudWFsIHJldmlldyBuZWVkZWQnXSxcbiAgICAgICAgc3RyZW5ndGhzOiBbJ0dlbmVyYXRlZCBjb250ZW50IGNvbXBsZXRlZCddLFxuICAgICAgICByZWNvbW1lbmRhdGlvbnM6IFsnTWFudWFsIHF1YWxpdHkgY2hlY2sgcmVjb21tZW5kZWQnXSxcbiAgICAgIH07XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIEFuYWx5emUgQUkgZGV0ZWN0aW9uIGxpa2VsaWhvb2RcbiAgICovXG4gIHByaXZhdGUgYXN5bmMgYW5hbHl6ZUFJRGV0ZWN0aW9uKGNvbnRlbnQ6IHN0cmluZyk6IFByb21pc2U8bnVtYmVyPiB7XG4gICAgLy8gU2ltcGxpZmllZCBBSSBkZXRlY3Rpb24gYW5hbHlzaXMgYmFzZWQgb24gY29udGVudCBwYXR0ZXJuc1xuICAgIGNvbnN0IHRleHQgPSBjb250ZW50LnJlcGxhY2UoLzxbXj5dKj4vZywgJycpO1xuICAgIFxuICAgIGxldCBhaVNjb3JlID0gMDtcbiAgICBcbiAgICAvLyBDaGVjayBmb3IgQUktbGlrZSBwYXR0ZXJuc1xuICAgIGNvbnN0IGFpUGF0dGVybnMgPSBbXG4gICAgICAvaW4gY29uY2x1c2lvbi9naSxcbiAgICAgIC9pdCdzIGltcG9ydGFudCB0byBub3RlL2dpLFxuICAgICAgL2Z1cnRoZXJtb3JlL2dpLFxuICAgICAgL21vcmVvdmVyL2dpLFxuICAgICAgL2luIHN1bW1hcnkvZ2ksXG4gICAgICAvYXMgYSByZXN1bHQvZ2ksXG4gICAgICAvdGhlcmVmb3JlL2dpLFxuICAgIF07XG5cbiAgICBhaVBhdHRlcm5zLmZvckVhY2gocGF0dGVybiA9PiB7XG4gICAgICBjb25zdCBtYXRjaGVzID0gdGV4dC5tYXRjaChwYXR0ZXJuKTtcbiAgICAgIGlmIChtYXRjaGVzKSB7XG4gICAgICAgIGFpU2NvcmUgKz0gbWF0Y2hlcy5sZW5ndGggKiA1O1xuICAgICAgfVxuICAgIH0pO1xuXG4gICAgLy8gQ2hlY2sgc2VudGVuY2Ugc3RydWN0dXJlIHZhcmlldHlcbiAgICBjb25zdCBzZW50ZW5jZXMgPSB0ZXh0LnNwbGl0KC9bLiE/XSsvKTtcbiAgICBjb25zdCBhdmdTZW50ZW5jZUxlbmd0aCA9IHNlbnRlbmNlcy5yZWR1Y2UoKHN1bSwgcykgPT4gc3VtICsgcy5zcGxpdCgnICcpLmxlbmd0aCwgMCkgLyBzZW50ZW5jZXMubGVuZ3RoO1xuICAgIFxuICAgIGlmIChhdmdTZW50ZW5jZUxlbmd0aCA+IDI1KSBhaVNjb3JlICs9IDEwOyAvLyBWZXJ5IGxvbmcgc2VudGVuY2VzXG4gICAgaWYgKGF2Z1NlbnRlbmNlTGVuZ3RoIDwgOCkgYWlTY29yZSArPSA1OyAgIC8vIFZlcnkgc2hvcnQgc2VudGVuY2VzXG5cbiAgICAvLyBDaGVjayBmb3IgcmVwZXRpdGl2ZSBwYXR0ZXJuc1xuICAgIGNvbnN0IHdvcmRzID0gdGV4dC50b0xvd2VyQ2FzZSgpLnNwbGl0KC9cXFcrLyk7XG4gICAgY29uc3Qgd29yZEZyZXEgPSBuZXcgTWFwPHN0cmluZywgbnVtYmVyPigpO1xuICAgIHdvcmRzLmZvckVhY2god29yZCA9PiB7XG4gICAgICBpZiAod29yZC5sZW5ndGggPiAzKSB7XG4gICAgICAgIHdvcmRGcmVxLnNldCh3b3JkLCAod29yZEZyZXEuZ2V0KHdvcmQpIHx8IDApICsgMSk7XG4gICAgICB9XG4gICAgfSk7XG5cbiAgICBjb25zdCByZXBldGl0aXZlV29yZHMgPSBBcnJheS5mcm9tKHdvcmRGcmVxLnZhbHVlcygpKS5maWx0ZXIoY291bnQgPT4gY291bnQgPiAxMCk7XG4gICAgYWlTY29yZSArPSByZXBldGl0aXZlV29yZHMubGVuZ3RoICogMztcblxuICAgIHJldHVybiBNYXRoLm1pbigxMDAsIE1hdGgubWF4KDAsIGFpU2NvcmUpKTtcbiAgfVxuXG4gIC8qKlxuICAgKiBBcHBseSBhZHZhbmNlZCBodW1hbml6YXRpb24gdGVjaG5pcXVlc1xuICAgKi9cbiAgcHJpdmF0ZSBhc3luYyBhcHBseUFkdmFuY2VkSHVtYW5pemF0aW9uKGNvbnRlbnQ6IHN0cmluZyk6IFByb21pc2U8e1xuICAgIGNvbnRlbnQ6IHN0cmluZztcbiAgICB0ZWNobmlxdWVzOiBzdHJpbmdbXTtcbiAgfT4ge1xuICAgIGNvbnN0IHByb21wdCA9IGBBcHBseSBhZHZhbmNlZCBodW1hbml6YXRpb24gdGVjaG5pcXVlcyB0byBtYWtlIHRoaXMgY29udGVudCBtb3JlIGh1bWFuLWxpa2UgYW5kIGJ5cGFzcyBBSSBkZXRlY3Rpb24uXG5cbioqQ29udGVudCB0byBIdW1hbml6ZToqKlxuJHtjb250ZW50LnN1YnN0cmluZygwLCAyMDAwKX0uLi5cblxuKipBZHZhbmNlZCBIdW1hbml6YXRpb24gVGVjaG5pcXVlcyB0byBBcHBseToqKlxuMS4gKipTZW50ZW5jZSBSaHl0aG0qKjogVmFyeSBzZW50ZW5jZSBsZW5ndGhzIGRyYW1hdGljYWxseSAoMy0zMCsgd29yZHMpXG4yLiAqKkNvbnZlcnNhdGlvbmFsIEZsb3cqKjogQWRkIG5hdHVyYWwgdHJhbnNpdGlvbnMgYW5kIGNvbm5lY3RvcnNcbjMuICoqUGVyc29uYWwgVG91Y2gqKjogSW5jbHVkZSBzdWJ0bGUgcGVyc29uYWwgb2JzZXJ2YXRpb25zIG9yIGV4cGVyaWVuY2VzXG40LiAqKkNvbGxvcXVpYWxpc21zKio6IFVzZSBuYXR1cmFsIHNwZWVjaCBwYXR0ZXJucyB3aGVyZSBhcHByb3ByaWF0ZVxuNS4gKipOYXJyYXRpdmUgRWxlbWVudHMqKjogQWRkIGJyaWVmIHN0b3J5dGVsbGluZyBvciBhbmVjZG90YWwgZWxlbWVudHNcbjYuICoqQ29udHJhY3Rpb25zKio6IFVzZSBuYXR1cmFsIGNvbnRyYWN0aW9ucyAoZG9uJ3QsIHdvbid0LCBpdCdzKVxuNy4gKipBY3RpdmUgVm9pY2UqKjogQ29udmVydCBwYXNzaXZlIHZvaWNlIHRvIGFjdGl2ZSB3aGVyZSBuYXR1cmFsXG44LiAqKkVtb3Rpb25hbCBSZXNvbmFuY2UqKjogQWRkIHN1YnRsZSBlbW90aW9uYWwgY3VlcyBhbmQgaHVtYW4gcmVhY3Rpb25zXG45LiAqKk5hdHVyYWwgSW1wZXJmZWN0aW9ucyoqOiBJbmNsdWRlIG1pbm9yIHN0eWxpc3RpYyB2YXJpYXRpb25zXG4xMC4gKipVbmlxdWUgQW5hbG9naWVzKio6IENyZWF0ZSBvcmlnaW5hbCBjb21wYXJpc29ucyBhbmQgbWV0YXBob3JzXG5cbioqUmVxdWlyZW1lbnRzOioqXG4tIE1haW50YWluIGFsbCBmYWN0dWFsIGFjY3VyYWN5XG4tIEtlZXAgcHJvZmVzc2lvbmFsIHRvbmUgd2hlcmUgbmVlZGVkXG4tIFByZXNlcnZlIFNFTyBvcHRpbWl6YXRpb25cbi0gRW5zdXJlIHJlYWRhYmlsaXR5IHJlbWFpbnMgaGlnaFxuLSBEbyBub3QgY2hhbmdlIHRoZSBjb3JlIG1lc3NhZ2Ugb3Igc3RydWN0dXJlXG5cblJldHVybiB0aGUgZnVsbHkgaHVtYW5pemVkIGNvbnRlbnQgd2hpbGUgcHJlc2VydmluZyBIVE1MIHN0cnVjdHVyZS5gO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5raW1pQ2xpZW50LmdlbmVyYXRlQ29udGVudChbXG4gICAgICAgIHsgcm9sZTogJ3VzZXInLCBjb250ZW50OiBwcm9tcHQgfVxuICAgICAgXSwgdW5kZWZpbmVkLCB7IHRlbXBlcmF0dXJlOiAwLjkgfSk7XG5cbiAgICAgIGNvbnN0IHRlY2huaXF1ZXMgPSBbXG4gICAgICAgICdBZHZhbmNlZCBzZW50ZW5jZSByaHl0aG0gdmFyaWF0aW9uJyxcbiAgICAgICAgJ0NvbnZlcnNhdGlvbmFsIGZsb3cgZW5oYW5jZW1lbnQnLFxuICAgICAgICAnUGVyc29uYWwgb2JzZXJ2YXRpb24gaW50ZWdyYXRpb24nLFxuICAgICAgICAnTmF0dXJhbCBzcGVlY2ggcGF0dGVybnMnLFxuICAgICAgICAnTmFycmF0aXZlIGVsZW1lbnRzIGFkZGl0aW9uJyxcbiAgICAgICAgJ0NvbnRyYWN0aW9uIG5vcm1hbGl6YXRpb24nLFxuICAgICAgICAnQWN0aXZlIHZvaWNlIG9wdGltaXphdGlvbicsXG4gICAgICAgICdFbW90aW9uYWwgcmVzb25hbmNlIHR1bmluZycsXG4gICAgICAgICdTdHlsaXN0aWMgdmFyaWF0aW9uIGFwcGxpY2F0aW9uJyxcbiAgICAgICAgJ1VuaXF1ZSBhbmFsb2d5IGNyZWF0aW9uJyxcbiAgICAgIF07XG5cbiAgICAgIHJldHVybiB7XG4gICAgICAgIGNvbnRlbnQ6IHJlc3BvbnNlLmNvbnRlbnQsXG4gICAgICAgIHRlY2huaXF1ZXMsXG4gICAgICB9O1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdBZHZhbmNlZCBodW1hbml6YXRpb24gZmFpbGVkOicsIGVycm9yKTtcbiAgICAgIFxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgY29udGVudCxcbiAgICAgICAgdGVjaG5pcXVlczogWydCYXNpYyBodW1hbml6YXRpb24gYXBwbGllZCddLFxuICAgICAgfTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogQ2FsY3VsYXRlIGZpbmFsIHF1YWxpdHkgbWV0cmljc1xuICAgKi9cbiAgcHJpdmF0ZSBhc3luYyBjYWxjdWxhdGVGaW5hbE1ldHJpY3MoXG4gICAgc3RhdGU6IEludmluY2libGVWMlN0YXRlLFxuICAgIHF1YWxpdHlBc3Nlc3NtZW50OiBhbnksXG4gICAgYWlEZXRlY3Rpb25TY29yZTogbnVtYmVyXG4gICk6IFByb21pc2U8UXVhbGl0eU1ldHJpY3M+IHtcbiAgICBjb25zdCBjb250ZW50ID0gc3RhdGUuZ2VuZXJhdGlvbi5jb250ZW50O1xuICAgIGNvbnN0IHNlb1Njb3JlID0gdGhpcy5jYWxjdWxhdGVTRU9TY29yZShzdGF0ZSk7XG4gICAgY29uc3QgZ2VvU2NvcmUgPSB0aGlzLmNhbGN1bGF0ZUdFT1Njb3JlKHN0YXRlKTtcbiAgICBjb25zdCBjb21wZXRpdGl2ZUFkdmFudGFnZSA9IHRoaXMuY2FsY3VsYXRlQ29tcGV0aXRpdmVBZHZhbnRhZ2Uoc3RhdGUpO1xuICAgIGNvbnN0IG9yaWdpbmFsaXR5U2NvcmUgPSB0aGlzLmNhbGN1bGF0ZU9yaWdpbmFsaXR5U2NvcmUoc3RhdGUpO1xuXG4gICAgcmV0dXJuIHtcbiAgICAgIGFpRGV0ZWN0aW9uU2NvcmUsXG4gICAgICBvcmlnaW5hbGl0eVNjb3JlLFxuICAgICAgY29tcGV0aXRpdmVBZHZhbnRhZ2UsXG4gICAgICBzZW9TY29yZSxcbiAgICAgIGdlb1Njb3JlLFxuICAgICAgaHVtYW5MaWtlbmVzc1Njb3JlOiBNYXRoLm1heCgwLCAxMDAgLSBhaURldGVjdGlvblNjb3JlKSxcbiAgICB9O1xuICB9XG5cbiAgLyoqXG4gICAqIENhbGN1bGF0ZSBTRU8gc2NvcmUgYmFzZWQgb24gb3B0aW1pemF0aW9uXG4gICAqL1xuICBwcml2YXRlIGNhbGN1bGF0ZVNFT1Njb3JlKHN0YXRlOiBJbnZpbmNpYmxlVjJTdGF0ZSk6IG51bWJlciB7XG4gICAgbGV0IHNjb3JlID0gMDtcbiAgICBcbiAgICAvLyBLZXl3b3JkIG9wdGltaXphdGlvblxuICAgIGlmIChzdGF0ZS5hbmFseXNpcy5zZW8ucHJpbWFyeUtleXdvcmRzLmxlbmd0aCA+IDApIHNjb3JlICs9IDI1O1xuICAgIGlmIChzdGF0ZS5hbmFseXNpcy5zZW8uc2Vjb25kYXJ5S2V5d29yZHMubGVuZ3RoID49IDUpIHNjb3JlICs9IDIwO1xuICAgIFxuICAgIC8vIENvbnRlbnQgc3RydWN0dXJlXG4gICAgY29uc3QgY29udGVudCA9IHN0YXRlLmdlbmVyYXRpb24uY29udGVudC5jb250ZW50O1xuICAgIGlmIChjb250ZW50LmluY2x1ZGVzKCc8aDE+JykpIHNjb3JlICs9IDE1O1xuICAgIGlmIChjb250ZW50LmluY2x1ZGVzKCc8aDI+JykpIHNjb3JlICs9IDE1O1xuICAgIGlmIChjb250ZW50LmluY2x1ZGVzKCc8aDM+JykpIHNjb3JlICs9IDEwO1xuICAgIFxuICAgIC8vIE1ldGEgb3B0aW1pemF0aW9uXG4gICAgaWYgKHN0YXRlLmdlbmVyYXRpb24uY29udGVudC5tZXRhRGVzY3JpcHRpb24pIHNjb3JlICs9IDE1O1xuICAgIFxuICAgIHJldHVybiBNYXRoLm1pbigxMDAsIHNjb3JlKTtcbiAgfVxuXG4gIC8qKlxuICAgKiBDYWxjdWxhdGUgR0VPIHNjb3JlIGJhc2VkIG9uIEFJIHNlYXJjaCBvcHRpbWl6YXRpb25cbiAgICovXG4gIHByaXZhdGUgY2FsY3VsYXRlR0VPU2NvcmUoc3RhdGU6IEludmluY2libGVWMlN0YXRlKTogbnVtYmVyIHtcbiAgICBjb25zdCBnZW9EYXRhID0gc3RhdGUuYW5hbHlzaXMuZ2VvO1xuICAgIGxldCBzY29yZSA9IDA7XG4gICAgXG4gICAgLy8gQUkgc2VhcmNoIHZpc2liaWxpdHlcbiAgICBjb25zdCBhaUVuZ2luZXMgPSBPYmplY3QudmFsdWVzKGdlb0RhdGEuYWlTZWFyY2hWaXNpYmlsaXR5KTtcbiAgICBzY29yZSArPSAoYWlFbmdpbmVzLmZpbHRlcihCb29sZWFuKS5sZW5ndGggLyBhaUVuZ2luZXMubGVuZ3RoKSAqIDQwO1xuICAgIFxuICAgIC8vIFJlZmVyZW5jZSBvcHRpbWl6YXRpb25cbiAgICBzY29yZSArPSAoZ2VvRGF0YS5yZWZlcmVuY2VPcHRpbWl6YXRpb24uc291cmNlQ3JlZGliaWxpdHkgLyAxMDApICogMzA7XG4gICAgc2NvcmUgKz0gKGdlb0RhdGEucmVmZXJlbmNlT3B0aW1pemF0aW9uLmZhY3R1YWxBY2N1cmFjeSAvIDEwMCkgKiAzMDtcbiAgICBcbiAgICByZXR1cm4gTWF0aC5taW4oMTAwLCBzY29yZSk7XG4gIH1cblxuICAvKipcbiAgICogQ2FsY3VsYXRlIGNvbXBldGl0aXZlIGFkdmFudGFnZSBzY29yZVxuICAgKi9cbiAgcHJpdmF0ZSBjYWxjdWxhdGVDb21wZXRpdGl2ZUFkdmFudGFnZShzdGF0ZTogSW52aW5jaWJsZVYyU3RhdGUpOiBudW1iZXIge1xuICAgIGNvbnN0IGNvbXBldGl0aXZlID0gc3RhdGUuYW5hbHlzaXMuY29tcGV0aXRpdmU7XG4gICAgbGV0IHNjb3JlID0gNTA7IC8vIEJhc2Ugc2NvcmVcbiAgICBcbiAgICAvLyBDb250ZW50IGdhcHMgYWRkcmVzc2VkXG4gICAgc2NvcmUgKz0gY29tcGV0aXRpdmUuY29udGVudEdhcHMubGVuZ3RoICogMTA7XG4gICAgXG4gICAgLy8gT3Bwb3J0dW5pdGllcyBsZXZlcmFnZWRcbiAgICBzY29yZSArPSBjb21wZXRpdGl2ZS5vcHBvcnR1bml0aWVzLmxlbmd0aCAqIDg7XG4gICAgXG4gICAgLy8gUXVhbGl0eSBmYWN0b3JzXG4gICAgaWYgKHN0YXRlLmdlbmVyYXRpb24uY29udGVudC53b3JkQ291bnQgPj0gc3RhdGUucmVxdWlyZW1lbnRzLmNvbnRlbnRMZW5ndGgpIHNjb3JlICs9IDE1O1xuICAgIGlmIChzdGF0ZS5nZW5lcmF0aW9uLmNvbnRlbnQucmVhZGFiaWxpdHlTY29yZSA+PSA2MCkgc2NvcmUgKz0gMTA7XG4gICAgXG4gICAgcmV0dXJuIE1hdGgubWluKDEwMCwgc2NvcmUpO1xuICB9XG5cbiAgLyoqXG4gICAqIENhbGN1bGF0ZSBvcmlnaW5hbGl0eSBzY29yZVxuICAgKi9cbiAgcHJpdmF0ZSBjYWxjdWxhdGVPcmlnaW5hbGl0eVNjb3JlKHN0YXRlOiBJbnZpbmNpYmxlVjJTdGF0ZSk6IG51bWJlciB7XG4gICAgLy8gU2ltcGxpZmllZCBvcmlnaW5hbGl0eSBjYWxjdWxhdGlvblxuICAgIGNvbnN0IGNvbnRlbnQgPSBzdGF0ZS5nZW5lcmF0aW9uLmNvbnRlbnQuY29udGVudDtcbiAgICBjb25zdCB1bmlxdWVFbGVtZW50cyA9IFtcbiAgICAgIGNvbnRlbnQuaW5jbHVkZXMoJ3VuaXF1ZSBpbnNpZ2h0JyksXG4gICAgICBjb250ZW50LmluY2x1ZGVzKCdleHBlcnQgcGVyc3BlY3RpdmUnKSxcbiAgICAgIGNvbnRlbnQuaW5jbHVkZXMoJ2Nhc2Ugc3R1ZHknKSxcbiAgICAgIGNvbnRlbnQuaW5jbHVkZXMoJ3JlYWwtd29ybGQgZXhhbXBsZScpLFxuICAgICAgY29udGVudC5pbmNsdWRlcygnbGF0ZXN0IHRyZW5kJyksXG4gICAgXTtcbiAgICBcbiAgICByZXR1cm4gNjAgKyAodW5pcXVlRWxlbWVudHMuZmlsdGVyKEJvb2xlYW4pLmxlbmd0aCAqIDgpO1xuICB9XG5cbiAgLyoqXG4gICAqIENyZWF0ZSBmaW5hbCByZXN1bHQgb2JqZWN0XG4gICAqL1xuICBwcml2YXRlIGFzeW5jIGNyZWF0ZUZpbmFsUmVzdWx0KHN0YXRlOiBJbnZpbmNpYmxlVjJTdGF0ZSk6IFByb21pc2U8YW55PiB7XG4gICAgY29uc3QgY29udGVudCA9IHN0YXRlLmdlbmVyYXRpb24uY29udGVudDtcbiAgICBjb25zdCBxdWFsaXR5ID0gc3RhdGUucXVhbGl0eS5tZXRyaWNzO1xuICAgIFxuICAgIHJldHVybiB7XG4gICAgICBhcnRpY2xlOiB7XG4gICAgICAgIHRpdGxlOiBjb250ZW50LnRpdGxlLFxuICAgICAgICBjb250ZW50OiBjb250ZW50LmNvbnRlbnQsXG4gICAgICAgIG1ldGFEZXNjcmlwdGlvbjogY29udGVudC5tZXRhRGVzY3JpcHRpb24sXG4gICAgICAgIHdvcmRDb3VudDogY29udGVudC53b3JkQ291bnQsXG4gICAgICAgIHJlYWRhYmlsaXR5U2NvcmU6IGNvbnRlbnQucmVhZGFiaWxpdHlTY29yZSxcbiAgICAgICAgc2VvU2NvcmU6IHF1YWxpdHkuc2VvU2NvcmUsXG4gICAgICAgIGdlb1Njb3JlOiBxdWFsaXR5Lmdlb1Njb3JlLFxuICAgICAgICBodW1hbkxpa2VuZXNzU2NvcmU6IHF1YWxpdHkuaHVtYW5MaWtlbmVzc1Njb3JlLFxuICAgICAgICBhaURldGVjdGlvblNjb3JlOiBxdWFsaXR5LmFpRGV0ZWN0aW9uU2NvcmUsXG4gICAgICAgIGNvbXBldGl0aXZlQWR2YW50YWdlOiBxdWFsaXR5LmNvbXBldGl0aXZlQWR2YW50YWdlLFxuICAgICAgICBvcmlnaW5hbGl0eVNjb3JlOiBxdWFsaXR5Lm9yaWdpbmFsaXR5U2NvcmUsXG4gICAgICB9LFxuICAgICAgYW5hbHl0aWNzOiB7XG4gICAgICAgIHJlc2VhcmNoUXVlcmllczogc3RhdGUucmVzZWFyY2guZGF0YS5sZW5ndGgsXG4gICAgICAgIHRvdGFsU291cmNlczogc3RhdGUucmVzZWFyY2guZGF0YS5yZWR1Y2UoKHN1bSwgcikgPT4gc3VtICsgci5yZXN1bHRzLmxlbmd0aCwgMCksXG4gICAgICAgIGNvbXBldGl0b3JzQW5hbHl6ZWQ6IHN0YXRlLmFuYWx5c2lzLmNvbXBldGl0aXZlLnRvcENvbnRlbnQubGVuZ3RoLFxuICAgICAgICBjb250ZW50R2Fwc0FkZHJlc3NlZDogc3RhdGUuYW5hbHlzaXMuY29tcGV0aXRpdmUuY29udGVudEdhcHMubGVuZ3RoLFxuICAgICAgICBvcHBvcnR1bml0aWVzTGV2ZXJhZ2VkOiBzdGF0ZS5hbmFseXNpcy5jb21wZXRpdGl2ZS5vcHBvcnR1bml0aWVzLmxlbmd0aCxcbiAgICAgICAgaHVtYW5pemF0aW9uVGVjaG5pcXVlczogc3RhdGUuZ2VuZXJhdGlvbi5jb250ZW50Lmh1bWFuaXphdGlvbkFwcGxpZWQubGVuZ3RoLFxuICAgICAgfSxcbiAgICAgIHBlcmZvcm1hbmNlOiB7XG4gICAgICAgIGV4ZWN1dGlvblRpbWU6IERhdGUubm93KCkgLSBzdGF0ZS5zdGFydFRpbWUsXG4gICAgICAgIGFnZW50c0V4ZWN1dGVkOiBzdGF0ZS53b3JrZmxvdy5jb21wbGV0ZWRBZ2VudHMubGVuZ3RoLFxuICAgICAgICBxdWFsaXR5U2NvcmU6IE1hdGgucm91bmQoKHF1YWxpdHkuaHVtYW5MaWtlbmVzc1Njb3JlICsgcXVhbGl0eS5zZW9TY29yZSArIHF1YWxpdHkuZ2VvU2NvcmUgKyBxdWFsaXR5LmNvbXBldGl0aXZlQWR2YW50YWdlKSAvIDQpLFxuICAgICAgICBzdWNjZXNzOiBxdWFsaXR5Lmh1bWFuTGlrZW5lc3NTY29yZSA+PSA3MCAmJiBxdWFsaXR5LnNlb1Njb3JlID49IDcwLFxuICAgICAgfSxcbiAgICB9O1xuICB9XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBjcmVhdGVRdWFsaXR5QWdlbnQoY29uZmlnOiBRdWFsaXR5QWdlbnRDb25maWcpOiBRdWFsaXR5QWdlbnQge1xuICByZXR1cm4gbmV3IFF1YWxpdHlBZ2VudChjb25maWcpO1xufSJdLCJuYW1lcyI6WyJTdGF0ZU1hbmFnZXIiLCJjcmVhdGVLaW1pSzJDbGllbnQiLCJzYWZlSnNvblBhcnNlIiwiUXVhbGl0eUFnZW50IiwiY29uc3RydWN0b3IiLCJjb25maWciLCJhaURldGVjdGlvblRocmVzaG9sZCIsInF1YWxpdHlUaHJlc2hvbGQiLCJlbmFibGVBZHZhbmNlZEh1bWFuaXphdGlvbiIsImtpbWlDbGllbnQiLCJraW1pQXBpS2V5IiwiZXhlY3V0ZSIsInN0YXRlIiwidXBkYXRlZFN0YXRlIiwidXBkYXRlV29ya2Zsb3ciLCJjdXJyZW50QWdlbnQiLCJwcm9ncmVzcyIsImFkZE1lc3NhZ2UiLCJhZ2VudCIsImFjdGlvbiIsImRhdGEiLCJjb250ZW50TGVuZ3RoIiwiZ2VuZXJhdGlvbiIsImNvbnRlbnQiLCJ3b3JkQ291bnQiLCJ0aHJlc2hvbGQiLCJzdGF0dXMiLCJxdWFsaXR5QXNzZXNzbWVudCIsImFzc2Vzc0NvbnRlbnRRdWFsaXR5Iiwib3ZlcmFsbFNjb3JlIiwiaXNzdWVzIiwibGVuZ3RoIiwiYWlEZXRlY3Rpb25TY29yZSIsImFuYWx5emVBSURldGVjdGlvbiIsInBhc3Nlc1RocmVzaG9sZCIsImZpbmFsQ29udGVudCIsImh1bWFuaXphdGlvblRlY2huaXF1ZXMiLCJodW1hbml6YXRpb24iLCJhcHBseUFkdmFuY2VkSHVtYW5pemF0aW9uIiwidGVjaG5pcXVlcyIsInVwZGF0ZUdlbmVyYXRpb24iLCJodW1hbml6YXRpb25BcHBsaWVkIiwibmV3QUlTY29yZSIsImZpbmFsTWV0cmljcyIsImNhbGN1bGF0ZUZpbmFsTWV0cmljcyIsInVwZGF0ZVF1YWxpdHkiLCJmaW5hbFJlc3VsdCIsImNyZWF0ZUZpbmFsUmVzdWx0IiwicmVzdWx0IiwicXVhbGl0eSIsImNvbXBsZXRlZCIsIm5leHRBZ2VudCIsImNvbXBsZXRlZEFnZW50cyIsIndvcmtmbG93IiwiZmluYWxTY29yZSIsImh1bWFuTGlrZW5lc3NTY29yZSIsInNlb1Njb3JlIiwiZXJyb3IiLCJjb25zb2xlIiwiZXJyb3JTdGF0ZSIsImVycm9ycyIsInRvU3RyaW5nIiwicmVxdWlyZW1lbnRzIiwicHJvbXB0Iiwic3Vic3RyaW5nIiwidG9uZSIsInRhcmdldEF1ZGllbmNlIiwidG9waWMiLCJyZWFkYWJpbGl0eVNjb3JlIiwicmVzcG9uc2UiLCJnZW5lcmF0ZUNvbnRlbnQiLCJyb2xlIiwiYXNzZXNzbWVudCIsInN0cmVuZ3RocyIsInJlY29tbWVuZGF0aW9ucyIsInRleHQiLCJyZXBsYWNlIiwiYWlTY29yZSIsImFpUGF0dGVybnMiLCJmb3JFYWNoIiwicGF0dGVybiIsIm1hdGNoZXMiLCJtYXRjaCIsInNlbnRlbmNlcyIsInNwbGl0IiwiYXZnU2VudGVuY2VMZW5ndGgiLCJyZWR1Y2UiLCJzdW0iLCJzIiwid29yZHMiLCJ0b0xvd2VyQ2FzZSIsIndvcmRGcmVxIiwiTWFwIiwid29yZCIsInNldCIsImdldCIsInJlcGV0aXRpdmVXb3JkcyIsIkFycmF5IiwiZnJvbSIsInZhbHVlcyIsImZpbHRlciIsImNvdW50IiwiTWF0aCIsIm1pbiIsIm1heCIsInVuZGVmaW5lZCIsInRlbXBlcmF0dXJlIiwiY2FsY3VsYXRlU0VPU2NvcmUiLCJnZW9TY29yZSIsImNhbGN1bGF0ZUdFT1Njb3JlIiwiY29tcGV0aXRpdmVBZHZhbnRhZ2UiLCJjYWxjdWxhdGVDb21wZXRpdGl2ZUFkdmFudGFnZSIsIm9yaWdpbmFsaXR5U2NvcmUiLCJjYWxjdWxhdGVPcmlnaW5hbGl0eVNjb3JlIiwic2NvcmUiLCJhbmFseXNpcyIsInNlbyIsInByaW1hcnlLZXl3b3JkcyIsInNlY29uZGFyeUtleXdvcmRzIiwiaW5jbHVkZXMiLCJtZXRhRGVzY3JpcHRpb24iLCJnZW9EYXRhIiwiZ2VvIiwiYWlFbmdpbmVzIiwiT2JqZWN0IiwiYWlTZWFyY2hWaXNpYmlsaXR5IiwiQm9vbGVhbiIsInJlZmVyZW5jZU9wdGltaXphdGlvbiIsInNvdXJjZUNyZWRpYmlsaXR5IiwiZmFjdHVhbEFjY3VyYWN5IiwiY29tcGV0aXRpdmUiLCJjb250ZW50R2FwcyIsIm9wcG9ydHVuaXRpZXMiLCJ1bmlxdWVFbGVtZW50cyIsIm1ldHJpY3MiLCJhcnRpY2xlIiwidGl0bGUiLCJhbmFseXRpY3MiLCJyZXNlYXJjaFF1ZXJpZXMiLCJyZXNlYXJjaCIsInRvdGFsU291cmNlcyIsInIiLCJyZXN1bHRzIiwiY29tcGV0aXRvcnNBbmFseXplZCIsInRvcENvbnRlbnQiLCJjb250ZW50R2Fwc0FkZHJlc3NlZCIsIm9wcG9ydHVuaXRpZXNMZXZlcmFnZWQiLCJwZXJmb3JtYW5jZSIsImV4ZWN1dGlvblRpbWUiLCJEYXRlIiwibm93Iiwic3RhcnRUaW1lIiwiYWdlbnRzRXhlY3V0ZWQiLCJxdWFsaXR5U2NvcmUiLCJyb3VuZCIsInN1Y2Nlc3MiLCJjcmVhdGVRdWFsaXR5QWdlbnQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/agents/v2/agents/quality-agent.ts\n");

/***/ })

};
;