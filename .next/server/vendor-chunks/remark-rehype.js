"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/remark-rehype";
exports.ids = ["vendor-chunks/remark-rehype"];
exports.modules = {

/***/ "(ssr)/./node_modules/remark-rehype/lib/index.js":
/*!*************************************************!*\
  !*** ./node_modules/remark-rehype/lib/index.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ remarkRehype)\n/* harmony export */ });\n/* harmony import */ var mdast_util_to_hast__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mdast-util-to-hast */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/index.js\");\n/**\n * @import {Root as HastRoot} from 'hast'\n * @import {Root as MdastRoot} from 'mdast'\n * @import {Options as ToHastOptions} from 'mdast-util-to-hast'\n * @import {Processor} from 'unified'\n * @import {VFile} from 'vfile'\n */\n\n/**\n * @typedef {Omit<ToHastOptions, 'file'>} Options\n *\n * @callback TransformBridge\n *   Bridge-mode.\n *\n *   Runs the destination with the new hast tree.\n *   Discards result.\n * @param {MdastRoot} tree\n *   Tree.\n * @param {VFile} file\n *   File.\n * @returns {Promise<undefined>}\n *   Nothing.\n *\n * @callback TransformMutate\n *  Mutate-mode.\n *\n *  Further transformers run on the hast tree.\n * @param {MdastRoot} tree\n *   Tree.\n * @param {VFile} file\n *   File.\n * @returns {HastRoot}\n *   Tree (hast).\n */\n\n\n\n/**\n * Turn markdown into HTML.\n *\n * ##### Notes\n *\n * ###### Signature\n *\n * * if a processor is given,\n *   runs the (rehype) plugins used on it with a hast tree,\n *   then discards the result (*bridge mode*)\n * * otherwise,\n *   returns a hast tree,\n *   the plugins used after `remarkRehype` are rehype plugins (*mutate mode*)\n *\n * > 👉 **Note**:\n * > It’s highly unlikely that you want to pass a `processor`.\n *\n * ###### HTML\n *\n * Raw HTML is available in mdast as `html` nodes and can be embedded in hast\n * as semistandard `raw` nodes.\n * Most plugins ignore `raw` nodes but two notable ones don’t:\n *\n * * `rehype-stringify` also has an option `allowDangerousHtml` which will\n *   output the raw HTML.\n *   This is typically discouraged as noted by the option name but is useful if\n *   you completely trust authors\n * * `rehype-raw` can handle the raw embedded HTML strings by parsing them\n *   into standard hast nodes (`element`, `text`, etc);\n *   this is a heavy task as it needs a full HTML parser,\n *   but it is the only way to support untrusted content\n *\n * ###### Footnotes\n *\n * Many options supported here relate to footnotes.\n * Footnotes are not specified by CommonMark,\n * which we follow by default.\n * They are supported by GitHub,\n * so footnotes can be enabled in markdown with `remark-gfm`.\n *\n * The options `footnoteBackLabel` and `footnoteLabel` define natural language\n * that explains footnotes,\n * which is hidden for sighted users but shown to assistive technology.\n * When your page is not in English,\n * you must define translated values.\n *\n * Back references use ARIA attributes,\n * but the section label itself uses a heading that is hidden with an\n * `sr-only` class.\n * To show it to sighted users,\n * define different attributes in `footnoteLabelProperties`.\n *\n * ###### Clobbering\n *\n * Footnotes introduces a problem,\n * as it links footnote calls to footnote definitions on the page through `id`\n * attributes generated from user content,\n * which results in DOM clobbering.\n *\n * DOM clobbering is this:\n *\n * ```html\n * <p id=x></p>\n * <script>alert(x) // `x` now refers to the DOM `p#x` element</script>\n * ```\n *\n * Elements by their ID are made available by browsers on the `window` object,\n * which is a security risk.\n * Using a prefix solves this problem.\n *\n * More information on how to handle clobbering and the prefix is explained in\n * *Example: headings (DOM clobbering)* in `rehype-sanitize`.\n *\n * ###### Unknown nodes\n *\n * Unknown nodes are nodes with a type that isn’t in `handlers` or `passThrough`.\n * The default behavior for unknown nodes is:\n *\n * * when the node has a `value`\n *   (and doesn’t have `data.hName`, `data.hProperties`, or `data.hChildren`,\n *   see later),\n *   create a hast `text` node\n * * otherwise,\n *   create a `<div>` element (which could be changed with `data.hName`),\n *   with its children mapped from mdast to hast as well\n *\n * This behavior can be changed by passing an `unknownHandler`.\n *\n * @overload\n * @param {Processor} processor\n * @param {Readonly<Options> | null | undefined} [options]\n * @returns {TransformBridge}\n *\n * @overload\n * @param {Readonly<Options> | null | undefined} [options]\n * @returns {TransformMutate}\n *\n * @overload\n * @param {Readonly<Options> | Processor | null | undefined} [destination]\n * @param {Readonly<Options> | null | undefined} [options]\n * @returns {TransformBridge | TransformMutate}\n *\n * @param {Readonly<Options> | Processor | null | undefined} [destination]\n *   Processor or configuration (optional).\n * @param {Readonly<Options> | null | undefined} [options]\n *   When a processor was given,\n *   configuration (optional).\n * @returns {TransformBridge | TransformMutate}\n *   Transform.\n */\nfunction remarkRehype(destination, options) {\n  if (destination && 'run' in destination) {\n    /**\n     * @type {TransformBridge}\n     */\n    return async function (tree, file) {\n      // Cast because root in -> root out.\n      const hastTree = /** @type {HastRoot} */ (\n        (0,mdast_util_to_hast__WEBPACK_IMPORTED_MODULE_0__.toHast)(tree, {file, ...options})\n      )\n      await destination.run(hastTree, file)\n    }\n  }\n\n  /**\n   * @type {TransformMutate}\n   */\n  return function (tree, file) {\n    // Cast because root in -> root out.\n    // To do: in the future, disallow ` || options` fallback.\n    // With `unified-engine`, `destination` can be `undefined` but\n    // `options` will be the file set.\n    // We should not pass that as `options`.\n    return /** @type {HastRoot} */ (\n      (0,mdast_util_to_hast__WEBPACK_IMPORTED_MODULE_0__.toHast)(tree, {file, ...(destination || options)})\n    )\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/remark-rehype/lib/index.js\n");

/***/ })

};
;