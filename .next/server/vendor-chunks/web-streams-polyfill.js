"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/web-streams-polyfill";
exports.ids = ["vendor-chunks/web-streams-polyfill"];
exports.modules = {

/***/ "(rsc)/./node_modules/web-streams-polyfill/dist/ponyfill.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/web-streams-polyfill/dist/ponyfill.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ByteLengthQueuingStrategy: () => (/* binding */ ByteLengthQueuingStrategy),\n/* harmony export */   CountQueuingStrategy: () => (/* binding */ CountQueuingStrategy),\n/* harmony export */   ReadableByteStreamController: () => (/* binding */ ReadableByteStreamController),\n/* harmony export */   ReadableStream: () => (/* binding */ ReadableStream),\n/* harmony export */   ReadableStreamBYOBReader: () => (/* binding */ ReadableStreamBYOBReader),\n/* harmony export */   ReadableStreamBYOBRequest: () => (/* binding */ ReadableStreamBYOBRequest),\n/* harmony export */   ReadableStreamDefaultController: () => (/* binding */ ReadableStreamDefaultController),\n/* harmony export */   ReadableStreamDefaultReader: () => (/* binding */ ReadableStreamDefaultReader),\n/* harmony export */   TransformStream: () => (/* binding */ TransformStream),\n/* harmony export */   TransformStreamDefaultController: () => (/* binding */ TransformStreamDefaultController),\n/* harmony export */   WritableStream: () => (/* binding */ WritableStream),\n/* harmony export */   WritableStreamDefaultController: () => (/* binding */ WritableStreamDefaultController),\n/* harmony export */   WritableStreamDefaultWriter: () => (/* binding */ WritableStreamDefaultWriter)\n/* harmony export */ });\n/**\n * @license\n * web-streams-polyfill v4.0.0-beta.3\n * Copyright 2021 Mattias Buelens, Diwank Singh Tomer and other contributors.\n * This code is released under the MIT license.\n * SPDX-License-Identifier: MIT\n */\nconst e=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?Symbol:e=>`Symbol(${e})`;function t(){}function r(e){return\"object\"==typeof e&&null!==e||\"function\"==typeof e}const o=t;function n(e,t){try{Object.defineProperty(e,\"name\",{value:t,configurable:!0})}catch(e){}}const a=Promise,i=Promise.prototype.then,l=Promise.resolve.bind(a),s=Promise.reject.bind(a);function u(e){return new a(e)}function c(e){return l(e)}function d(e){return s(e)}function f(e,t,r){return i.call(e,t,r)}function b(e,t,r){f(f(e,t,r),void 0,o)}function h(e,t){b(e,t)}function _(e,t){b(e,void 0,t)}function p(e,t,r){return f(e,t,r)}function m(e){f(e,void 0,o)}let y=e=>{if(\"function\"==typeof queueMicrotask)y=queueMicrotask;else{const e=c(void 0);y=t=>f(e,t)}return y(e)};function g(e,t,r){if(\"function\"!=typeof e)throw new TypeError(\"Argument is not a function\");return Function.prototype.apply.call(e,t,r)}function w(e,t,r){try{return c(g(e,t,r))}catch(e){return d(e)}}class S{constructor(){this._cursor=0,this._size=0,this._front={_elements:[],_next:void 0},this._back=this._front,this._cursor=0,this._size=0}get length(){return this._size}push(e){const t=this._back;let r=t;16383===t._elements.length&&(r={_elements:[],_next:void 0}),t._elements.push(e),r!==t&&(this._back=r,t._next=r),++this._size}shift(){const e=this._front;let t=e;const r=this._cursor;let o=r+1;const n=e._elements,a=n[r];return 16384===o&&(t=e._next,o=0),--this._size,this._cursor=o,e!==t&&(this._front=t),n[r]=void 0,a}forEach(e){let t=this._cursor,r=this._front,o=r._elements;for(;!(t===o.length&&void 0===r._next||t===o.length&&(r=r._next,o=r._elements,t=0,0===o.length));)e(o[t]),++t}peek(){const e=this._front,t=this._cursor;return e._elements[t]}}const v=e(\"[[AbortSteps]]\"),R=e(\"[[ErrorSteps]]\"),T=e(\"[[CancelSteps]]\"),q=e(\"[[PullSteps]]\"),C=e(\"[[ReleaseSteps]]\");function E(e,t){e._ownerReadableStream=t,t._reader=e,\"readable\"===t._state?O(e):\"closed\"===t._state?function(e){O(e),j(e)}(e):B(e,t._storedError)}function P(e,t){return Gt(e._ownerReadableStream,t)}function W(e){const t=e._ownerReadableStream;\"readable\"===t._state?A(e,new TypeError(\"Reader was released and can no longer be used to monitor the stream's closedness\")):function(e,t){B(e,t)}(e,new TypeError(\"Reader was released and can no longer be used to monitor the stream's closedness\")),t._readableStreamController[C](),t._reader=void 0,e._ownerReadableStream=void 0}function k(e){return new TypeError(\"Cannot \"+e+\" a stream using a released reader\")}function O(e){e._closedPromise=u(((t,r)=>{e._closedPromise_resolve=t,e._closedPromise_reject=r}))}function B(e,t){O(e),A(e,t)}function A(e,t){void 0!==e._closedPromise_reject&&(m(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}function j(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}const z=Number.isFinite||function(e){return\"number\"==typeof e&&isFinite(e)},L=Math.trunc||function(e){return e<0?Math.ceil(e):Math.floor(e)};function F(e,t){if(void 0!==e&&(\"object\"!=typeof(r=e)&&\"function\"!=typeof r))throw new TypeError(`${t} is not an object.`);var r}function I(e,t){if(\"function\"!=typeof e)throw new TypeError(`${t} is not a function.`)}function D(e,t){if(!function(e){return\"object\"==typeof e&&null!==e||\"function\"==typeof e}(e))throw new TypeError(`${t} is not an object.`)}function $(e,t,r){if(void 0===e)throw new TypeError(`Parameter ${t} is required in '${r}'.`)}function M(e,t,r){if(void 0===e)throw new TypeError(`${t} is required in '${r}'.`)}function Y(e){return Number(e)}function Q(e){return 0===e?0:e}function N(e,t){const r=Number.MAX_SAFE_INTEGER;let o=Number(e);if(o=Q(o),!z(o))throw new TypeError(`${t} is not a finite number`);if(o=function(e){return Q(L(e))}(o),o<0||o>r)throw new TypeError(`${t} is outside the accepted range of 0 to ${r}, inclusive`);return z(o)&&0!==o?o:0}function H(e){if(!r(e))return!1;if(\"function\"!=typeof e.getReader)return!1;try{return\"boolean\"==typeof e.locked}catch(e){return!1}}function x(e){if(!r(e))return!1;if(\"function\"!=typeof e.getWriter)return!1;try{return\"boolean\"==typeof e.locked}catch(e){return!1}}function V(e,t){if(!Vt(e))throw new TypeError(`${t} is not a ReadableStream.`)}function U(e,t){e._reader._readRequests.push(t)}function G(e,t,r){const o=e._reader._readRequests.shift();r?o._closeSteps():o._chunkSteps(t)}function X(e){return e._reader._readRequests.length}function J(e){const t=e._reader;return void 0!==t&&!!K(t)}class ReadableStreamDefaultReader{constructor(e){if($(e,1,\"ReadableStreamDefaultReader\"),V(e,\"First parameter\"),Ut(e))throw new TypeError(\"This stream has already been locked for exclusive reading by another reader\");E(this,e),this._readRequests=new S}get closed(){return K(this)?this._closedPromise:d(ee(\"closed\"))}cancel(e){return K(this)?void 0===this._ownerReadableStream?d(k(\"cancel\")):P(this,e):d(ee(\"cancel\"))}read(){if(!K(this))return d(ee(\"read\"));if(void 0===this._ownerReadableStream)return d(k(\"read from\"));let e,t;const r=u(((r,o)=>{e=r,t=o}));return function(e,t){const r=e._ownerReadableStream;r._disturbed=!0,\"closed\"===r._state?t._closeSteps():\"errored\"===r._state?t._errorSteps(r._storedError):r._readableStreamController[q](t)}(this,{_chunkSteps:t=>e({value:t,done:!1}),_closeSteps:()=>e({value:void 0,done:!0}),_errorSteps:e=>t(e)}),r}releaseLock(){if(!K(this))throw ee(\"releaseLock\");void 0!==this._ownerReadableStream&&function(e){W(e);const t=new TypeError(\"Reader was released\");Z(e,t)}(this)}}function K(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,\"_readRequests\")&&e instanceof ReadableStreamDefaultReader)}function Z(e,t){const r=e._readRequests;e._readRequests=new S,r.forEach((e=>{e._errorSteps(t)}))}function ee(e){return new TypeError(`ReadableStreamDefaultReader.prototype.${e} can only be used on a ReadableStreamDefaultReader`)}Object.defineProperties(ReadableStreamDefaultReader.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),n(ReadableStreamDefaultReader.prototype.cancel,\"cancel\"),n(ReadableStreamDefaultReader.prototype.read,\"read\"),n(ReadableStreamDefaultReader.prototype.releaseLock,\"releaseLock\"),\"symbol\"==typeof e.toStringTag&&Object.defineProperty(ReadableStreamDefaultReader.prototype,e.toStringTag,{value:\"ReadableStreamDefaultReader\",configurable:!0});class te{constructor(e,t){this._ongoingPromise=void 0,this._isFinished=!1,this._reader=e,this._preventCancel=t}next(){const e=()=>this._nextSteps();return this._ongoingPromise=this._ongoingPromise?p(this._ongoingPromise,e,e):e(),this._ongoingPromise}return(e){const t=()=>this._returnSteps(e);return this._ongoingPromise?p(this._ongoingPromise,t,t):t()}_nextSteps(){if(this._isFinished)return Promise.resolve({value:void 0,done:!0});const e=this._reader;return void 0===e?d(k(\"iterate\")):f(e.read(),(e=>{var t;return this._ongoingPromise=void 0,e.done&&(this._isFinished=!0,null===(t=this._reader)||void 0===t||t.releaseLock(),this._reader=void 0),e}),(e=>{var t;throw this._ongoingPromise=void 0,this._isFinished=!0,null===(t=this._reader)||void 0===t||t.releaseLock(),this._reader=void 0,e}))}_returnSteps(e){if(this._isFinished)return Promise.resolve({value:e,done:!0});this._isFinished=!0;const t=this._reader;if(void 0===t)return d(k(\"finish iterating\"));if(this._reader=void 0,!this._preventCancel){const r=t.cancel(e);return t.releaseLock(),p(r,(()=>({value:e,done:!0})))}return t.releaseLock(),c({value:e,done:!0})}}const re={next(){return oe(this)?this._asyncIteratorImpl.next():d(ne(\"next\"))},return(e){return oe(this)?this._asyncIteratorImpl.return(e):d(ne(\"return\"))}};function oe(e){if(!r(e))return!1;if(!Object.prototype.hasOwnProperty.call(e,\"_asyncIteratorImpl\"))return!1;try{return e._asyncIteratorImpl instanceof te}catch(e){return!1}}function ne(e){return new TypeError(`ReadableStreamAsyncIterator.${e} can only be used on a ReadableSteamAsyncIterator`)}\"symbol\"==typeof e.asyncIterator&&Object.defineProperty(re,e.asyncIterator,{value(){return this},writable:!0,configurable:!0});const ae=Number.isNaN||function(e){return e!=e};function ie(e,t,r,o,n){new Uint8Array(e).set(new Uint8Array(r,o,n),t)}function le(e){const t=function(e,t,r){if(e.slice)return e.slice(t,r);const o=r-t,n=new ArrayBuffer(o);return ie(n,0,e,t,o),n}(e.buffer,e.byteOffset,e.byteOffset+e.byteLength);return new Uint8Array(t)}function se(e){const t=e._queue.shift();return e._queueTotalSize-=t.size,e._queueTotalSize<0&&(e._queueTotalSize=0),t.value}function ue(e,t,r){if(\"number\"!=typeof(o=r)||ae(o)||o<0||r===1/0)throw new RangeError(\"Size must be a finite, non-NaN, non-negative number.\");var o;e._queue.push({value:t,size:r}),e._queueTotalSize+=r}function ce(e){e._queue=new S,e._queueTotalSize=0}class ReadableStreamBYOBRequest{constructor(){throw new TypeError(\"Illegal constructor\")}get view(){if(!fe(this))throw Be(\"view\");return this._view}respond(e){if(!fe(this))throw Be(\"respond\");if($(e,1,\"respond\"),e=N(e,\"First parameter\"),void 0===this._associatedReadableByteStreamController)throw new TypeError(\"This BYOB request has been invalidated\");this._view.buffer,function(e,t){const r=e._pendingPullIntos.peek();if(\"closed\"===e._controlledReadableByteStream._state){if(0!==t)throw new TypeError(\"bytesWritten must be 0 when calling respond() on a closed stream\")}else{if(0===t)throw new TypeError(\"bytesWritten must be greater than 0 when calling respond() on a readable stream\");if(r.bytesFilled+t>r.byteLength)throw new RangeError(\"bytesWritten out of range\")}r.buffer=r.buffer,qe(e,t)}(this._associatedReadableByteStreamController,e)}respondWithNewView(e){if(!fe(this))throw Be(\"respondWithNewView\");if($(e,1,\"respondWithNewView\"),!ArrayBuffer.isView(e))throw new TypeError(\"You can only respond with array buffer views\");if(void 0===this._associatedReadableByteStreamController)throw new TypeError(\"This BYOB request has been invalidated\");e.buffer,function(e,t){const r=e._pendingPullIntos.peek();if(\"closed\"===e._controlledReadableByteStream._state){if(0!==t.byteLength)throw new TypeError(\"The view's length must be 0 when calling respondWithNewView() on a closed stream\")}else if(0===t.byteLength)throw new TypeError(\"The view's length must be greater than 0 when calling respondWithNewView() on a readable stream\");if(r.byteOffset+r.bytesFilled!==t.byteOffset)throw new RangeError(\"The region specified by view does not match byobRequest\");if(r.bufferByteLength!==t.buffer.byteLength)throw new RangeError(\"The buffer of view has different capacity than byobRequest\");if(r.bytesFilled+t.byteLength>r.byteLength)throw new RangeError(\"The region specified by view is larger than byobRequest\");const o=t.byteLength;r.buffer=t.buffer,qe(e,o)}(this._associatedReadableByteStreamController,e)}}Object.defineProperties(ReadableStreamBYOBRequest.prototype,{respond:{enumerable:!0},respondWithNewView:{enumerable:!0},view:{enumerable:!0}}),n(ReadableStreamBYOBRequest.prototype.respond,\"respond\"),n(ReadableStreamBYOBRequest.prototype.respondWithNewView,\"respondWithNewView\"),\"symbol\"==typeof e.toStringTag&&Object.defineProperty(ReadableStreamBYOBRequest.prototype,e.toStringTag,{value:\"ReadableStreamBYOBRequest\",configurable:!0});class ReadableByteStreamController{constructor(){throw new TypeError(\"Illegal constructor\")}get byobRequest(){if(!de(this))throw Ae(\"byobRequest\");return function(e){if(null===e._byobRequest&&e._pendingPullIntos.length>0){const t=e._pendingPullIntos.peek(),r=new Uint8Array(t.buffer,t.byteOffset+t.bytesFilled,t.byteLength-t.bytesFilled),o=Object.create(ReadableStreamBYOBRequest.prototype);!function(e,t,r){e._associatedReadableByteStreamController=t,e._view=r}(o,e,r),e._byobRequest=o}return e._byobRequest}(this)}get desiredSize(){if(!de(this))throw Ae(\"desiredSize\");return ke(this)}close(){if(!de(this))throw Ae(\"close\");if(this._closeRequested)throw new TypeError(\"The stream has already been closed; do not close it again!\");const e=this._controlledReadableByteStream._state;if(\"readable\"!==e)throw new TypeError(`The stream (in ${e} state) is not in the readable state and cannot be closed`);!function(e){const t=e._controlledReadableByteStream;if(e._closeRequested||\"readable\"!==t._state)return;if(e._queueTotalSize>0)return void(e._closeRequested=!0);if(e._pendingPullIntos.length>0){if(e._pendingPullIntos.peek().bytesFilled>0){const t=new TypeError(\"Insufficient bytes to fill elements in the given buffer\");throw Pe(e,t),t}}Ee(e),Xt(t)}(this)}enqueue(e){if(!de(this))throw Ae(\"enqueue\");if($(e,1,\"enqueue\"),!ArrayBuffer.isView(e))throw new TypeError(\"chunk must be an array buffer view\");if(0===e.byteLength)throw new TypeError(\"chunk must have non-zero byteLength\");if(0===e.buffer.byteLength)throw new TypeError(\"chunk's buffer must have non-zero byteLength\");if(this._closeRequested)throw new TypeError(\"stream is closed or draining\");const t=this._controlledReadableByteStream._state;if(\"readable\"!==t)throw new TypeError(`The stream (in ${t} state) is not in the readable state and cannot be enqueued to`);!function(e,t){const r=e._controlledReadableByteStream;if(e._closeRequested||\"readable\"!==r._state)return;const o=t.buffer,n=t.byteOffset,a=t.byteLength,i=o;if(e._pendingPullIntos.length>0){const t=e._pendingPullIntos.peek();t.buffer,0,Re(e),t.buffer=t.buffer,\"none\"===t.readerType&&ge(e,t)}if(J(r))if(function(e){const t=e._controlledReadableByteStream._reader;for(;t._readRequests.length>0;){if(0===e._queueTotalSize)return;We(e,t._readRequests.shift())}}(e),0===X(r))me(e,i,n,a);else{e._pendingPullIntos.length>0&&Ce(e);G(r,new Uint8Array(i,n,a),!1)}else Le(r)?(me(e,i,n,a),Te(e)):me(e,i,n,a);be(e)}(this,e)}error(e){if(!de(this))throw Ae(\"error\");Pe(this,e)}[T](e){he(this),ce(this);const t=this._cancelAlgorithm(e);return Ee(this),t}[q](e){const t=this._controlledReadableByteStream;if(this._queueTotalSize>0)return void We(this,e);const r=this._autoAllocateChunkSize;if(void 0!==r){let t;try{t=new ArrayBuffer(r)}catch(t){return void e._errorSteps(t)}const o={buffer:t,bufferByteLength:r,byteOffset:0,byteLength:r,bytesFilled:0,elementSize:1,viewConstructor:Uint8Array,readerType:\"default\"};this._pendingPullIntos.push(o)}U(t,e),be(this)}[C](){if(this._pendingPullIntos.length>0){const e=this._pendingPullIntos.peek();e.readerType=\"none\",this._pendingPullIntos=new S,this._pendingPullIntos.push(e)}}}function de(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,\"_controlledReadableByteStream\")&&e instanceof ReadableByteStreamController)}function fe(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,\"_associatedReadableByteStreamController\")&&e instanceof ReadableStreamBYOBRequest)}function be(e){const t=function(e){const t=e._controlledReadableByteStream;if(\"readable\"!==t._state)return!1;if(e._closeRequested)return!1;if(!e._started)return!1;if(J(t)&&X(t)>0)return!0;if(Le(t)&&ze(t)>0)return!0;if(ke(e)>0)return!0;return!1}(e);if(!t)return;if(e._pulling)return void(e._pullAgain=!0);e._pulling=!0;b(e._pullAlgorithm(),(()=>(e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,be(e)),null)),(t=>(Pe(e,t),null)))}function he(e){Re(e),e._pendingPullIntos=new S}function _e(e,t){let r=!1;\"closed\"===e._state&&(r=!0);const o=pe(t);\"default\"===t.readerType?G(e,o,r):function(e,t,r){const o=e._reader._readIntoRequests.shift();r?o._closeSteps(t):o._chunkSteps(t)}(e,o,r)}function pe(e){const t=e.bytesFilled,r=e.elementSize;return new e.viewConstructor(e.buffer,e.byteOffset,t/r)}function me(e,t,r,o){e._queue.push({buffer:t,byteOffset:r,byteLength:o}),e._queueTotalSize+=o}function ye(e,t,r,o){let n;try{n=t.slice(r,r+o)}catch(t){throw Pe(e,t),t}me(e,n,0,o)}function ge(e,t){t.bytesFilled>0&&ye(e,t.buffer,t.byteOffset,t.bytesFilled),Ce(e)}function we(e,t){const r=t.elementSize,o=t.bytesFilled-t.bytesFilled%r,n=Math.min(e._queueTotalSize,t.byteLength-t.bytesFilled),a=t.bytesFilled+n,i=a-a%r;let l=n,s=!1;i>o&&(l=i-t.bytesFilled,s=!0);const u=e._queue;for(;l>0;){const r=u.peek(),o=Math.min(l,r.byteLength),n=t.byteOffset+t.bytesFilled;ie(t.buffer,n,r.buffer,r.byteOffset,o),r.byteLength===o?u.shift():(r.byteOffset+=o,r.byteLength-=o),e._queueTotalSize-=o,Se(e,o,t),l-=o}return s}function Se(e,t,r){r.bytesFilled+=t}function ve(e){0===e._queueTotalSize&&e._closeRequested?(Ee(e),Xt(e._controlledReadableByteStream)):be(e)}function Re(e){null!==e._byobRequest&&(e._byobRequest._associatedReadableByteStreamController=void 0,e._byobRequest._view=null,e._byobRequest=null)}function Te(e){for(;e._pendingPullIntos.length>0;){if(0===e._queueTotalSize)return;const t=e._pendingPullIntos.peek();we(e,t)&&(Ce(e),_e(e._controlledReadableByteStream,t))}}function qe(e,t){const r=e._pendingPullIntos.peek();Re(e);\"closed\"===e._controlledReadableByteStream._state?function(e,t){\"none\"===t.readerType&&Ce(e);const r=e._controlledReadableByteStream;if(Le(r))for(;ze(r)>0;)_e(r,Ce(e))}(e,r):function(e,t,r){if(Se(0,t,r),\"none\"===r.readerType)return ge(e,r),void Te(e);if(r.bytesFilled<r.elementSize)return;Ce(e);const o=r.bytesFilled%r.elementSize;if(o>0){const t=r.byteOffset+r.bytesFilled;ye(e,r.buffer,t-o,o)}r.bytesFilled-=o,_e(e._controlledReadableByteStream,r),Te(e)}(e,t,r),be(e)}function Ce(e){return e._pendingPullIntos.shift()}function Ee(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0}function Pe(e,t){const r=e._controlledReadableByteStream;\"readable\"===r._state&&(he(e),ce(e),Ee(e),Jt(r,t))}function We(e,t){const r=e._queue.shift();e._queueTotalSize-=r.byteLength,ve(e);const o=new Uint8Array(r.buffer,r.byteOffset,r.byteLength);t._chunkSteps(o)}function ke(e){const t=e._controlledReadableByteStream._state;return\"errored\"===t?null:\"closed\"===t?0:e._strategyHWM-e._queueTotalSize}function Oe(e,t,r){const o=Object.create(ReadableByteStreamController.prototype);let n,a,i;n=void 0!==t.start?()=>t.start(o):()=>{},a=void 0!==t.pull?()=>t.pull(o):()=>c(void 0),i=void 0!==t.cancel?e=>t.cancel(e):()=>c(void 0);const l=t.autoAllocateChunkSize;if(0===l)throw new TypeError(\"autoAllocateChunkSize must be greater than 0\");!function(e,t,r,o,n,a,i){t._controlledReadableByteStream=e,t._pullAgain=!1,t._pulling=!1,t._byobRequest=null,t._queue=t._queueTotalSize=void 0,ce(t),t._closeRequested=!1,t._started=!1,t._strategyHWM=a,t._pullAlgorithm=o,t._cancelAlgorithm=n,t._autoAllocateChunkSize=i,t._pendingPullIntos=new S,e._readableStreamController=t,b(c(r()),(()=>(t._started=!0,be(t),null)),(e=>(Pe(t,e),null)))}(e,o,n,a,i,r,l)}function Be(e){return new TypeError(`ReadableStreamBYOBRequest.prototype.${e} can only be used on a ReadableStreamBYOBRequest`)}function Ae(e){return new TypeError(`ReadableByteStreamController.prototype.${e} can only be used on a ReadableByteStreamController`)}function je(e,t){e._reader._readIntoRequests.push(t)}function ze(e){return e._reader._readIntoRequests.length}function Le(e){const t=e._reader;return void 0!==t&&!!Fe(t)}Object.defineProperties(ReadableByteStreamController.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},byobRequest:{enumerable:!0},desiredSize:{enumerable:!0}}),n(ReadableByteStreamController.prototype.close,\"close\"),n(ReadableByteStreamController.prototype.enqueue,\"enqueue\"),n(ReadableByteStreamController.prototype.error,\"error\"),\"symbol\"==typeof e.toStringTag&&Object.defineProperty(ReadableByteStreamController.prototype,e.toStringTag,{value:\"ReadableByteStreamController\",configurable:!0});class ReadableStreamBYOBReader{constructor(e){if($(e,1,\"ReadableStreamBYOBReader\"),V(e,\"First parameter\"),Ut(e))throw new TypeError(\"This stream has already been locked for exclusive reading by another reader\");if(!de(e._readableStreamController))throw new TypeError(\"Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source\");E(this,e),this._readIntoRequests=new S}get closed(){return Fe(this)?this._closedPromise:d(De(\"closed\"))}cancel(e){return Fe(this)?void 0===this._ownerReadableStream?d(k(\"cancel\")):P(this,e):d(De(\"cancel\"))}read(e){if(!Fe(this))return d(De(\"read\"));if(!ArrayBuffer.isView(e))return d(new TypeError(\"view must be an array buffer view\"));if(0===e.byteLength)return d(new TypeError(\"view must have non-zero byteLength\"));if(0===e.buffer.byteLength)return d(new TypeError(\"view's buffer must have non-zero byteLength\"));if(e.buffer,void 0===this._ownerReadableStream)return d(k(\"read from\"));let t,r;const o=u(((e,o)=>{t=e,r=o}));return function(e,t,r){const o=e._ownerReadableStream;o._disturbed=!0,\"errored\"===o._state?r._errorSteps(o._storedError):function(e,t,r){const o=e._controlledReadableByteStream;let n=1;t.constructor!==DataView&&(n=t.constructor.BYTES_PER_ELEMENT);const a=t.constructor,i=t.buffer,l={buffer:i,bufferByteLength:i.byteLength,byteOffset:t.byteOffset,byteLength:t.byteLength,bytesFilled:0,elementSize:n,viewConstructor:a,readerType:\"byob\"};if(e._pendingPullIntos.length>0)return e._pendingPullIntos.push(l),void je(o,r);if(\"closed\"!==o._state){if(e._queueTotalSize>0){if(we(e,l)){const t=pe(l);return ve(e),void r._chunkSteps(t)}if(e._closeRequested){const t=new TypeError(\"Insufficient bytes to fill elements in the given buffer\");return Pe(e,t),void r._errorSteps(t)}}e._pendingPullIntos.push(l),je(o,r),be(e)}else{const e=new a(l.buffer,l.byteOffset,0);r._closeSteps(e)}}(o._readableStreamController,t,r)}(this,e,{_chunkSteps:e=>t({value:e,done:!1}),_closeSteps:e=>t({value:e,done:!0}),_errorSteps:e=>r(e)}),o}releaseLock(){if(!Fe(this))throw De(\"releaseLock\");void 0!==this._ownerReadableStream&&function(e){W(e);const t=new TypeError(\"Reader was released\");Ie(e,t)}(this)}}function Fe(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,\"_readIntoRequests\")&&e instanceof ReadableStreamBYOBReader)}function Ie(e,t){const r=e._readIntoRequests;e._readIntoRequests=new S,r.forEach((e=>{e._errorSteps(t)}))}function De(e){return new TypeError(`ReadableStreamBYOBReader.prototype.${e} can only be used on a ReadableStreamBYOBReader`)}function $e(e,t){const{highWaterMark:r}=e;if(void 0===r)return t;if(ae(r)||r<0)throw new RangeError(\"Invalid highWaterMark\");return r}function Me(e){const{size:t}=e;return t||(()=>1)}function Ye(e,t){F(e,t);const r=null==e?void 0:e.highWaterMark,o=null==e?void 0:e.size;return{highWaterMark:void 0===r?void 0:Y(r),size:void 0===o?void 0:Qe(o,`${t} has member 'size' that`)}}function Qe(e,t){return I(e,t),t=>Y(e(t))}function Ne(e,t,r){return I(e,r),r=>w(e,t,[r])}function He(e,t,r){return I(e,r),()=>w(e,t,[])}function xe(e,t,r){return I(e,r),r=>g(e,t,[r])}function Ve(e,t,r){return I(e,r),(r,o)=>w(e,t,[r,o])}Object.defineProperties(ReadableStreamBYOBReader.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),n(ReadableStreamBYOBReader.prototype.cancel,\"cancel\"),n(ReadableStreamBYOBReader.prototype.read,\"read\"),n(ReadableStreamBYOBReader.prototype.releaseLock,\"releaseLock\"),\"symbol\"==typeof e.toStringTag&&Object.defineProperty(ReadableStreamBYOBReader.prototype,e.toStringTag,{value:\"ReadableStreamBYOBReader\",configurable:!0});const Ue=\"function\"==typeof AbortController;class WritableStream{constructor(e={},t={}){void 0===e?e=null:D(e,\"First parameter\");const r=Ye(t,\"Second parameter\"),o=function(e,t){F(e,t);const r=null==e?void 0:e.abort,o=null==e?void 0:e.close,n=null==e?void 0:e.start,a=null==e?void 0:e.type,i=null==e?void 0:e.write;return{abort:void 0===r?void 0:Ne(r,e,`${t} has member 'abort' that`),close:void 0===o?void 0:He(o,e,`${t} has member 'close' that`),start:void 0===n?void 0:xe(n,e,`${t} has member 'start' that`),write:void 0===i?void 0:Ve(i,e,`${t} has member 'write' that`),type:a}}(e,\"First parameter\");var n;(n=this)._state=\"writable\",n._storedError=void 0,n._writer=void 0,n._writableStreamController=void 0,n._writeRequests=new S,n._inFlightWriteRequest=void 0,n._closeRequest=void 0,n._inFlightCloseRequest=void 0,n._pendingAbortRequest=void 0,n._backpressure=!1;if(void 0!==o.type)throw new RangeError(\"Invalid type is specified\");const a=Me(r);!function(e,t,r,o){const n=Object.create(WritableStreamDefaultController.prototype);let a,i,l,s;a=void 0!==t.start?()=>t.start(n):()=>{};i=void 0!==t.write?e=>t.write(e,n):()=>c(void 0);l=void 0!==t.close?()=>t.close():()=>c(void 0);s=void 0!==t.abort?e=>t.abort(e):()=>c(void 0);!function(e,t,r,o,n,a,i,l){t._controlledWritableStream=e,e._writableStreamController=t,t._queue=void 0,t._queueTotalSize=void 0,ce(t),t._abortReason=void 0,t._abortController=function(){if(Ue)return new AbortController}(),t._started=!1,t._strategySizeAlgorithm=l,t._strategyHWM=i,t._writeAlgorithm=o,t._closeAlgorithm=n,t._abortAlgorithm=a;const s=bt(t);nt(e,s);const u=r();b(c(u),(()=>(t._started=!0,dt(t),null)),(r=>(t._started=!0,Ze(e,r),null)))}(e,n,a,i,l,s,r,o)}(this,o,$e(r,1),a)}get locked(){if(!Ge(this))throw _t(\"locked\");return Xe(this)}abort(e){return Ge(this)?Xe(this)?d(new TypeError(\"Cannot abort a stream that already has a writer\")):Je(this,e):d(_t(\"abort\"))}close(){return Ge(this)?Xe(this)?d(new TypeError(\"Cannot close a stream that already has a writer\")):rt(this)?d(new TypeError(\"Cannot close an already-closing stream\")):Ke(this):d(_t(\"close\"))}getWriter(){if(!Ge(this))throw _t(\"getWriter\");return new WritableStreamDefaultWriter(this)}}function Ge(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,\"_writableStreamController\")&&e instanceof WritableStream)}function Xe(e){return void 0!==e._writer}function Je(e,t){var r;if(\"closed\"===e._state||\"errored\"===e._state)return c(void 0);e._writableStreamController._abortReason=t,null===(r=e._writableStreamController._abortController)||void 0===r||r.abort(t);const o=e._state;if(\"closed\"===o||\"errored\"===o)return c(void 0);if(void 0!==e._pendingAbortRequest)return e._pendingAbortRequest._promise;let n=!1;\"erroring\"===o&&(n=!0,t=void 0);const a=u(((r,o)=>{e._pendingAbortRequest={_promise:void 0,_resolve:r,_reject:o,_reason:t,_wasAlreadyErroring:n}}));return e._pendingAbortRequest._promise=a,n||et(e,t),a}function Ke(e){const t=e._state;if(\"closed\"===t||\"errored\"===t)return d(new TypeError(`The stream (in ${t} state) is not in the writable state and cannot be closed`));const r=u(((t,r)=>{const o={_resolve:t,_reject:r};e._closeRequest=o})),o=e._writer;var n;return void 0!==o&&e._backpressure&&\"writable\"===t&&Et(o),ue(n=e._writableStreamController,lt,0),dt(n),r}function Ze(e,t){\"writable\"!==e._state?tt(e):et(e,t)}function et(e,t){const r=e._writableStreamController;e._state=\"erroring\",e._storedError=t;const o=e._writer;void 0!==o&&it(o,t),!function(e){if(void 0===e._inFlightWriteRequest&&void 0===e._inFlightCloseRequest)return!1;return!0}(e)&&r._started&&tt(e)}function tt(e){e._state=\"errored\",e._writableStreamController[R]();const t=e._storedError;if(e._writeRequests.forEach((e=>{e._reject(t)})),e._writeRequests=new S,void 0===e._pendingAbortRequest)return void ot(e);const r=e._pendingAbortRequest;if(e._pendingAbortRequest=void 0,r._wasAlreadyErroring)return r._reject(t),void ot(e);b(e._writableStreamController[v](r._reason),(()=>(r._resolve(),ot(e),null)),(t=>(r._reject(t),ot(e),null)))}function rt(e){return void 0!==e._closeRequest||void 0!==e._inFlightCloseRequest}function ot(e){void 0!==e._closeRequest&&(e._closeRequest._reject(e._storedError),e._closeRequest=void 0);const t=e._writer;void 0!==t&&St(t,e._storedError)}function nt(e,t){const r=e._writer;void 0!==r&&t!==e._backpressure&&(t?function(e){Rt(e)}(r):Et(r)),e._backpressure=t}Object.defineProperties(WritableStream.prototype,{abort:{enumerable:!0},close:{enumerable:!0},getWriter:{enumerable:!0},locked:{enumerable:!0}}),n(WritableStream.prototype.abort,\"abort\"),n(WritableStream.prototype.close,\"close\"),n(WritableStream.prototype.getWriter,\"getWriter\"),\"symbol\"==typeof e.toStringTag&&Object.defineProperty(WritableStream.prototype,e.toStringTag,{value:\"WritableStream\",configurable:!0});class WritableStreamDefaultWriter{constructor(e){if($(e,1,\"WritableStreamDefaultWriter\"),function(e,t){if(!Ge(e))throw new TypeError(`${t} is not a WritableStream.`)}(e,\"First parameter\"),Xe(e))throw new TypeError(\"This stream has already been locked for exclusive writing by another writer\");this._ownerWritableStream=e,e._writer=this;const t=e._state;if(\"writable\"===t)!rt(e)&&e._backpressure?Rt(this):qt(this),gt(this);else if(\"erroring\"===t)Tt(this,e._storedError),gt(this);else if(\"closed\"===t)qt(this),gt(r=this),vt(r);else{const t=e._storedError;Tt(this,t),wt(this,t)}var r}get closed(){return at(this)?this._closedPromise:d(mt(\"closed\"))}get desiredSize(){if(!at(this))throw mt(\"desiredSize\");if(void 0===this._ownerWritableStream)throw yt(\"desiredSize\");return function(e){const t=e._ownerWritableStream,r=t._state;if(\"errored\"===r||\"erroring\"===r)return null;if(\"closed\"===r)return 0;return ct(t._writableStreamController)}(this)}get ready(){return at(this)?this._readyPromise:d(mt(\"ready\"))}abort(e){return at(this)?void 0===this._ownerWritableStream?d(yt(\"abort\")):function(e,t){return Je(e._ownerWritableStream,t)}(this,e):d(mt(\"abort\"))}close(){if(!at(this))return d(mt(\"close\"));const e=this._ownerWritableStream;return void 0===e?d(yt(\"close\")):rt(e)?d(new TypeError(\"Cannot close an already-closing stream\")):Ke(this._ownerWritableStream)}releaseLock(){if(!at(this))throw mt(\"releaseLock\");void 0!==this._ownerWritableStream&&function(e){const t=e._ownerWritableStream,r=new TypeError(\"Writer was released and can no longer be used to monitor the stream's closedness\");it(e,r),function(e,t){\"pending\"===e._closedPromiseState?St(e,t):function(e,t){wt(e,t)}(e,t)}(e,r),t._writer=void 0,e._ownerWritableStream=void 0}(this)}write(e){return at(this)?void 0===this._ownerWritableStream?d(yt(\"write to\")):function(e,t){const r=e._ownerWritableStream,o=r._writableStreamController,n=function(e,t){try{return e._strategySizeAlgorithm(t)}catch(t){return ft(e,t),1}}(o,t);if(r!==e._ownerWritableStream)return d(yt(\"write to\"));const a=r._state;if(\"errored\"===a)return d(r._storedError);if(rt(r)||\"closed\"===a)return d(new TypeError(\"The stream is closing or closed and cannot be written to\"));if(\"erroring\"===a)return d(r._storedError);const i=function(e){return u(((t,r)=>{const o={_resolve:t,_reject:r};e._writeRequests.push(o)}))}(r);return function(e,t,r){try{ue(e,t,r)}catch(t){return void ft(e,t)}const o=e._controlledWritableStream;if(!rt(o)&&\"writable\"===o._state){nt(o,bt(e))}dt(e)}(o,t,n),i}(this,e):d(mt(\"write\"))}}function at(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,\"_ownerWritableStream\")&&e instanceof WritableStreamDefaultWriter)}function it(e,t){\"pending\"===e._readyPromiseState?Ct(e,t):function(e,t){Tt(e,t)}(e,t)}Object.defineProperties(WritableStreamDefaultWriter.prototype,{abort:{enumerable:!0},close:{enumerable:!0},releaseLock:{enumerable:!0},write:{enumerable:!0},closed:{enumerable:!0},desiredSize:{enumerable:!0},ready:{enumerable:!0}}),n(WritableStreamDefaultWriter.prototype.abort,\"abort\"),n(WritableStreamDefaultWriter.prototype.close,\"close\"),n(WritableStreamDefaultWriter.prototype.releaseLock,\"releaseLock\"),n(WritableStreamDefaultWriter.prototype.write,\"write\"),\"symbol\"==typeof e.toStringTag&&Object.defineProperty(WritableStreamDefaultWriter.prototype,e.toStringTag,{value:\"WritableStreamDefaultWriter\",configurable:!0});const lt={};class WritableStreamDefaultController{constructor(){throw new TypeError(\"Illegal constructor\")}get abortReason(){if(!st(this))throw pt(\"abortReason\");return this._abortReason}get signal(){if(!st(this))throw pt(\"signal\");if(void 0===this._abortController)throw new TypeError(\"WritableStreamDefaultController.prototype.signal is not supported\");return this._abortController.signal}error(e){if(!st(this))throw pt(\"error\");\"writable\"===this._controlledWritableStream._state&&ht(this,e)}[v](e){const t=this._abortAlgorithm(e);return ut(this),t}[R](){ce(this)}}function st(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,\"_controlledWritableStream\")&&e instanceof WritableStreamDefaultController)}function ut(e){e._writeAlgorithm=void 0,e._closeAlgorithm=void 0,e._abortAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function ct(e){return e._strategyHWM-e._queueTotalSize}function dt(e){const t=e._controlledWritableStream;if(!e._started)return;if(void 0!==t._inFlightWriteRequest)return;if(\"erroring\"===t._state)return void tt(t);if(0===e._queue.length)return;const r=e._queue.peek().value;r===lt?function(e){const t=e._controlledWritableStream;(function(e){e._inFlightCloseRequest=e._closeRequest,e._closeRequest=void 0})(t),se(e);const r=e._closeAlgorithm();ut(e),b(r,(()=>(function(e){e._inFlightCloseRequest._resolve(void 0),e._inFlightCloseRequest=void 0,\"erroring\"===e._state&&(e._storedError=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._resolve(),e._pendingAbortRequest=void 0)),e._state=\"closed\";const t=e._writer;void 0!==t&&vt(t)}(t),null)),(e=>(function(e,t){e._inFlightCloseRequest._reject(t),e._inFlightCloseRequest=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._reject(t),e._pendingAbortRequest=void 0),Ze(e,t)}(t,e),null)))}(e):function(e,t){const r=e._controlledWritableStream;!function(e){e._inFlightWriteRequest=e._writeRequests.shift()}(r);b(e._writeAlgorithm(t),(()=>{!function(e){e._inFlightWriteRequest._resolve(void 0),e._inFlightWriteRequest=void 0}(r);const t=r._state;if(se(e),!rt(r)&&\"writable\"===t){const t=bt(e);nt(r,t)}return dt(e),null}),(t=>(\"writable\"===r._state&&ut(e),function(e,t){e._inFlightWriteRequest._reject(t),e._inFlightWriteRequest=void 0,Ze(e,t)}(r,t),null)))}(e,r)}function ft(e,t){\"writable\"===e._controlledWritableStream._state&&ht(e,t)}function bt(e){return ct(e)<=0}function ht(e,t){const r=e._controlledWritableStream;ut(e),et(r,t)}function _t(e){return new TypeError(`WritableStream.prototype.${e} can only be used on a WritableStream`)}function pt(e){return new TypeError(`WritableStreamDefaultController.prototype.${e} can only be used on a WritableStreamDefaultController`)}function mt(e){return new TypeError(`WritableStreamDefaultWriter.prototype.${e} can only be used on a WritableStreamDefaultWriter`)}function yt(e){return new TypeError(\"Cannot \"+e+\" a stream using a released writer\")}function gt(e){e._closedPromise=u(((t,r)=>{e._closedPromise_resolve=t,e._closedPromise_reject=r,e._closedPromiseState=\"pending\"}))}function wt(e,t){gt(e),St(e,t)}function St(e,t){void 0!==e._closedPromise_reject&&(m(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState=\"rejected\")}function vt(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState=\"resolved\")}function Rt(e){e._readyPromise=u(((t,r)=>{e._readyPromise_resolve=t,e._readyPromise_reject=r})),e._readyPromiseState=\"pending\"}function Tt(e,t){Rt(e),Ct(e,t)}function qt(e){Rt(e),Et(e)}function Ct(e,t){void 0!==e._readyPromise_reject&&(m(e._readyPromise),e._readyPromise_reject(t),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState=\"rejected\")}function Et(e){void 0!==e._readyPromise_resolve&&(e._readyPromise_resolve(void 0),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState=\"fulfilled\")}Object.defineProperties(WritableStreamDefaultController.prototype,{abortReason:{enumerable:!0},signal:{enumerable:!0},error:{enumerable:!0}}),\"symbol\"==typeof e.toStringTag&&Object.defineProperty(WritableStreamDefaultController.prototype,e.toStringTag,{value:\"WritableStreamDefaultController\",configurable:!0});const Pt=\"undefined\"!=typeof DOMException?DOMException:void 0;const Wt=function(e){if(\"function\"!=typeof e&&\"object\"!=typeof e)return!1;try{return new e,!0}catch(e){return!1}}(Pt)?Pt:function(){const e=function(e,t){this.message=e||\"\",this.name=t||\"Error\",Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)};return e.prototype=Object.create(Error.prototype),Object.defineProperty(e.prototype,\"constructor\",{value:e,writable:!0,configurable:!0}),e}();function kt(e,t,r,o,n,a){const i=e.getReader(),l=t.getWriter();Vt(e)&&(e._disturbed=!0);let s,_,g,w=!1,S=!1,v=\"readable\",R=\"writable\",T=!1,q=!1;const C=u((e=>{g=e}));let E=Promise.resolve(void 0);return u(((P,W)=>{let k;function O(){if(w)return;const e=u(((e,t)=>{!function r(o){o?e():f(function(){if(w)return c(!0);return f(l.ready,(()=>f(i.read(),(e=>!!e.done||(E=l.write(e.value),m(E),!1)))))}(),r,t)}(!1)}));m(e)}function B(){return v=\"closed\",r?L():z((()=>(Ge(t)&&(T=rt(t),R=t._state),T||\"closed\"===R?c(void 0):\"erroring\"===R||\"errored\"===R?d(_):(T=!0,l.close()))),!1,void 0),null}function A(e){return w||(v=\"errored\",s=e,o?L(!0,e):z((()=>l.abort(e)),!0,e)),null}function j(e){return S||(R=\"errored\",_=e,n?L(!0,e):z((()=>i.cancel(e)),!0,e)),null}if(void 0!==a&&(k=()=>{const e=void 0!==a.reason?a.reason:new Wt(\"Aborted\",\"AbortError\"),t=[];o||t.push((()=>\"writable\"===R?l.abort(e):c(void 0))),n||t.push((()=>\"readable\"===v?i.cancel(e):c(void 0))),z((()=>Promise.all(t.map((e=>e())))),!0,e)},a.aborted?k():a.addEventListener(\"abort\",k)),Vt(e)&&(v=e._state,s=e._storedError),Ge(t)&&(R=t._state,_=t._storedError,T=rt(t)),Vt(e)&&Ge(t)&&(q=!0,g()),\"errored\"===v)A(s);else if(\"erroring\"===R||\"errored\"===R)j(_);else if(\"closed\"===v)B();else if(T||\"closed\"===R){const e=new TypeError(\"the destination writable stream closed before all data could be piped to it\");n?L(!0,e):z((()=>i.cancel(e)),!0,e)}function z(e,t,r){function o(){return\"writable\"!==R||T?n():h(function(){let e;return c(function t(){if(e!==E)return e=E,p(E,t,t)}())}(),n),null}function n(){return e?b(e(),(()=>F(t,r)),(e=>F(!0,e))):F(t,r),null}w||(w=!0,q?o():h(C,o))}function L(e,t){z(void 0,e,t)}function F(e,t){return S=!0,l.releaseLock(),i.releaseLock(),void 0!==a&&a.removeEventListener(\"abort\",k),e?W(t):P(void 0),null}w||(b(i.closed,B,A),b(l.closed,(function(){return S||(R=\"closed\"),null}),j)),q?O():y((()=>{q=!0,g(),O()}))}))}function Ot(e,t){return function(e){try{return e.getReader({mode:\"byob\"}).releaseLock(),!0}catch(e){return!1}}(e)?function(e){let t,r,o,n,a,i=e.getReader(),l=!1,s=!1,d=!1,f=!1,h=!1,p=!1;const m=u((e=>{a=e}));function y(e){_(e.closed,(t=>(e!==i||(o.error(t),n.error(t),h&&p||a(void 0)),null)))}function g(){l&&(i.releaseLock(),i=e.getReader(),y(i),l=!1),b(i.read(),(e=>{var t,r;if(d=!1,f=!1,e.done)return h||o.close(),p||n.close(),null===(t=o.byobRequest)||void 0===t||t.respond(0),null===(r=n.byobRequest)||void 0===r||r.respond(0),h&&p||a(void 0),null;const l=e.value,u=l;let c=l;if(!h&&!p)try{c=le(l)}catch(e){return o.error(e),n.error(e),a(i.cancel(e)),null}return h||o.enqueue(u),p||n.enqueue(c),s=!1,d?S():f&&v(),null}),(()=>(s=!1,null)))}function w(t,r){l||(i.releaseLock(),i=e.getReader({mode:\"byob\"}),y(i),l=!0);const u=r?n:o,c=r?o:n;b(i.read(t),(e=>{var t;d=!1,f=!1;const o=r?p:h,n=r?h:p;if(e.done){o||u.close(),n||c.close();const r=e.value;return void 0!==r&&(o||u.byobRequest.respondWithNewView(r),n||null===(t=c.byobRequest)||void 0===t||t.respond(0)),o&&n||a(void 0),null}const l=e.value;if(n)o||u.byobRequest.respondWithNewView(l);else{let e;try{e=le(l)}catch(e){return u.error(e),c.error(e),a(i.cancel(e)),null}o||u.byobRequest.respondWithNewView(l),c.enqueue(e)}return s=!1,d?S():f&&v(),null}),(()=>(s=!1,null)))}function S(){if(s)return d=!0,c(void 0);s=!0;const e=o.byobRequest;return null===e?g():w(e.view,!1),c(void 0)}function v(){if(s)return f=!0,c(void 0);s=!0;const e=n.byobRequest;return null===e?g():w(e.view,!0),c(void 0)}function R(e){if(h=!0,t=e,p){const e=[t,r],o=i.cancel(e);a(o)}return m}function T(e){if(p=!0,r=e,h){const e=[t,r],o=i.cancel(e);a(o)}return m}const q=new ReadableStream({type:\"bytes\",start(e){o=e},pull:S,cancel:R}),C=new ReadableStream({type:\"bytes\",start(e){n=e},pull:v,cancel:T});return y(i),[q,C]}(e):function(e,t){const r=e.getReader();let o,n,a,i,l,s=!1,d=!1,f=!1,h=!1;const p=u((e=>{l=e}));function m(){return s?(d=!0,c(void 0)):(s=!0,b(r.read(),(e=>{if(d=!1,e.done)return f||a.close(),h||i.close(),f&&h||l(void 0),null;const t=e.value,r=t,o=t;return f||a.enqueue(r),h||i.enqueue(o),s=!1,d&&m(),null}),(()=>(s=!1,null))),c(void 0))}function y(e){if(f=!0,o=e,h){const e=[o,n],t=r.cancel(e);l(t)}return p}function g(e){if(h=!0,n=e,f){const e=[o,n],t=r.cancel(e);l(t)}return p}const w=new ReadableStream({start(e){a=e},pull:m,cancel:y}),S=new ReadableStream({start(e){i=e},pull:m,cancel:g});return _(r.closed,(e=>(a.error(e),i.error(e),f&&h||l(void 0),null))),[w,S]}(e)}class ReadableStreamDefaultController{constructor(){throw new TypeError(\"Illegal constructor\")}get desiredSize(){if(!Bt(this))throw Dt(\"desiredSize\");return Lt(this)}close(){if(!Bt(this))throw Dt(\"close\");if(!Ft(this))throw new TypeError(\"The stream is not in a state that permits close\");!function(e){if(!Ft(e))return;const t=e._controlledReadableStream;e._closeRequested=!0,0===e._queue.length&&(jt(e),Xt(t))}(this)}enqueue(e){if(!Bt(this))throw Dt(\"enqueue\");if(!Ft(this))throw new TypeError(\"The stream is not in a state that permits enqueue\");return function(e,t){if(!Ft(e))return;const r=e._controlledReadableStream;if(Ut(r)&&X(r)>0)G(r,t,!1);else{let r;try{r=e._strategySizeAlgorithm(t)}catch(t){throw zt(e,t),t}try{ue(e,t,r)}catch(t){throw zt(e,t),t}}At(e)}(this,e)}error(e){if(!Bt(this))throw Dt(\"error\");zt(this,e)}[T](e){ce(this);const t=this._cancelAlgorithm(e);return jt(this),t}[q](e){const t=this._controlledReadableStream;if(this._queue.length>0){const r=se(this);this._closeRequested&&0===this._queue.length?(jt(this),Xt(t)):At(this),e._chunkSteps(r)}else U(t,e),At(this)}[C](){}}function Bt(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,\"_controlledReadableStream\")&&e instanceof ReadableStreamDefaultController)}function At(e){const t=function(e){const t=e._controlledReadableStream;if(!Ft(e))return!1;if(!e._started)return!1;if(Ut(t)&&X(t)>0)return!0;if(Lt(e)>0)return!0;return!1}(e);if(!t)return;if(e._pulling)return void(e._pullAgain=!0);e._pulling=!0;b(e._pullAlgorithm(),(()=>(e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,At(e)),null)),(t=>(zt(e,t),null)))}function jt(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function zt(e,t){const r=e._controlledReadableStream;\"readable\"===r._state&&(ce(e),jt(e),Jt(r,t))}function Lt(e){const t=e._controlledReadableStream._state;return\"errored\"===t?null:\"closed\"===t?0:e._strategyHWM-e._queueTotalSize}function Ft(e){return!e._closeRequested&&\"readable\"===e._controlledReadableStream._state}function It(e,t,r,o){const n=Object.create(ReadableStreamDefaultController.prototype);let a,i,l;a=void 0!==t.start?()=>t.start(n):()=>{},i=void 0!==t.pull?()=>t.pull(n):()=>c(void 0),l=void 0!==t.cancel?e=>t.cancel(e):()=>c(void 0),function(e,t,r,o,n,a,i){t._controlledReadableStream=e,t._queue=void 0,t._queueTotalSize=void 0,ce(t),t._started=!1,t._closeRequested=!1,t._pullAgain=!1,t._pulling=!1,t._strategySizeAlgorithm=i,t._strategyHWM=a,t._pullAlgorithm=o,t._cancelAlgorithm=n,e._readableStreamController=t,b(c(r()),(()=>(t._started=!0,At(t),null)),(e=>(zt(t,e),null)))}(e,n,a,i,l,r,o)}function Dt(e){return new TypeError(`ReadableStreamDefaultController.prototype.${e} can only be used on a ReadableStreamDefaultController`)}function $t(e,t,r){return I(e,r),r=>w(e,t,[r])}function Mt(e,t,r){return I(e,r),r=>w(e,t,[r])}function Yt(e,t,r){return I(e,r),r=>g(e,t,[r])}function Qt(e,t){if(\"bytes\"!==(e=`${e}`))throw new TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamType`);return e}function Nt(e,t){if(\"byob\"!==(e=`${e}`))throw new TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamReaderMode`);return e}function Ht(e,t){F(e,t);const r=null==e?void 0:e.preventAbort,o=null==e?void 0:e.preventCancel,n=null==e?void 0:e.preventClose,a=null==e?void 0:e.signal;return void 0!==a&&function(e,t){if(!function(e){if(\"object\"!=typeof e||null===e)return!1;try{return\"boolean\"==typeof e.aborted}catch(e){return!1}}(e))throw new TypeError(`${t} is not an AbortSignal.`)}(a,`${t} has member 'signal' that`),{preventAbort:Boolean(r),preventCancel:Boolean(o),preventClose:Boolean(n),signal:a}}function xt(e,t){F(e,t);const r=null==e?void 0:e.readable;M(r,\"readable\",\"ReadableWritablePair\"),function(e,t){if(!H(e))throw new TypeError(`${t} is not a ReadableStream.`)}(r,`${t} has member 'readable' that`);const o=null==e?void 0:e.writable;return M(o,\"writable\",\"ReadableWritablePair\"),function(e,t){if(!x(e))throw new TypeError(`${t} is not a WritableStream.`)}(o,`${t} has member 'writable' that`),{readable:r,writable:o}}Object.defineProperties(ReadableStreamDefaultController.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},desiredSize:{enumerable:!0}}),n(ReadableStreamDefaultController.prototype.close,\"close\"),n(ReadableStreamDefaultController.prototype.enqueue,\"enqueue\"),n(ReadableStreamDefaultController.prototype.error,\"error\"),\"symbol\"==typeof e.toStringTag&&Object.defineProperty(ReadableStreamDefaultController.prototype,e.toStringTag,{value:\"ReadableStreamDefaultController\",configurable:!0});class ReadableStream{constructor(e={},t={}){void 0===e?e=null:D(e,\"First parameter\");const r=Ye(t,\"Second parameter\"),o=function(e,t){F(e,t);const r=e,o=null==r?void 0:r.autoAllocateChunkSize,n=null==r?void 0:r.cancel,a=null==r?void 0:r.pull,i=null==r?void 0:r.start,l=null==r?void 0:r.type;return{autoAllocateChunkSize:void 0===o?void 0:N(o,`${t} has member 'autoAllocateChunkSize' that`),cancel:void 0===n?void 0:$t(n,r,`${t} has member 'cancel' that`),pull:void 0===a?void 0:Mt(a,r,`${t} has member 'pull' that`),start:void 0===i?void 0:Yt(i,r,`${t} has member 'start' that`),type:void 0===l?void 0:Qt(l,`${t} has member 'type' that`)}}(e,\"First parameter\");var n;if((n=this)._state=\"readable\",n._reader=void 0,n._storedError=void 0,n._disturbed=!1,\"bytes\"===o.type){if(void 0!==r.size)throw new RangeError(\"The strategy for a byte stream cannot have a size function\");Oe(this,o,$e(r,0))}else{const e=Me(r);It(this,o,$e(r,1),e)}}get locked(){if(!Vt(this))throw Kt(\"locked\");return Ut(this)}cancel(e){return Vt(this)?Ut(this)?d(new TypeError(\"Cannot cancel a stream that already has a reader\")):Gt(this,e):d(Kt(\"cancel\"))}getReader(e){if(!Vt(this))throw Kt(\"getReader\");return void 0===function(e,t){F(e,t);const r=null==e?void 0:e.mode;return{mode:void 0===r?void 0:Nt(r,`${t} has member 'mode' that`)}}(e,\"First parameter\").mode?new ReadableStreamDefaultReader(this):function(e){return new ReadableStreamBYOBReader(e)}(this)}pipeThrough(e,t={}){if(!H(this))throw Kt(\"pipeThrough\");$(e,1,\"pipeThrough\");const r=xt(e,\"First parameter\"),o=Ht(t,\"Second parameter\");if(this.locked)throw new TypeError(\"ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream\");if(r.writable.locked)throw new TypeError(\"ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream\");return m(kt(this,r.writable,o.preventClose,o.preventAbort,o.preventCancel,o.signal)),r.readable}pipeTo(e,t={}){if(!H(this))return d(Kt(\"pipeTo\"));if(void 0===e)return d(\"Parameter 1 is required in 'pipeTo'.\");if(!x(e))return d(new TypeError(\"ReadableStream.prototype.pipeTo's first argument must be a WritableStream\"));let r;try{r=Ht(t,\"Second parameter\")}catch(e){return d(e)}return this.locked?d(new TypeError(\"ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream\")):e.locked?d(new TypeError(\"ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream\")):kt(this,e,r.preventClose,r.preventAbort,r.preventCancel,r.signal)}tee(){if(!H(this))throw Kt(\"tee\");if(this.locked)throw new TypeError(\"Cannot tee a stream that already has a reader\");return Ot(this)}values(e){if(!H(this))throw Kt(\"values\");return function(e,t){const r=e.getReader(),o=new te(r,t),n=Object.create(re);return n._asyncIteratorImpl=o,n}(this,function(e,t){F(e,t);const r=null==e?void 0:e.preventCancel;return{preventCancel:Boolean(r)}}(e,\"First parameter\").preventCancel)}}function Vt(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,\"_readableStreamController\")&&e instanceof ReadableStream)}function Ut(e){return void 0!==e._reader}function Gt(e,r){if(e._disturbed=!0,\"closed\"===e._state)return c(void 0);if(\"errored\"===e._state)return d(e._storedError);Xt(e);const o=e._reader;if(void 0!==o&&Fe(o)){const e=o._readIntoRequests;o._readIntoRequests=new S,e.forEach((e=>{e._closeSteps(void 0)}))}return p(e._readableStreamController[T](r),t)}function Xt(e){e._state=\"closed\";const t=e._reader;if(void 0!==t&&(j(t),K(t))){const e=t._readRequests;t._readRequests=new S,e.forEach((e=>{e._closeSteps()}))}}function Jt(e,t){e._state=\"errored\",e._storedError=t;const r=e._reader;void 0!==r&&(A(r,t),K(r)?Z(r,t):Ie(r,t))}function Kt(e){return new TypeError(`ReadableStream.prototype.${e} can only be used on a ReadableStream`)}function Zt(e,t){F(e,t);const r=null==e?void 0:e.highWaterMark;return M(r,\"highWaterMark\",\"QueuingStrategyInit\"),{highWaterMark:Y(r)}}Object.defineProperties(ReadableStream.prototype,{cancel:{enumerable:!0},getReader:{enumerable:!0},pipeThrough:{enumerable:!0},pipeTo:{enumerable:!0},tee:{enumerable:!0},values:{enumerable:!0},locked:{enumerable:!0}}),n(ReadableStream.prototype.cancel,\"cancel\"),n(ReadableStream.prototype.getReader,\"getReader\"),n(ReadableStream.prototype.pipeThrough,\"pipeThrough\"),n(ReadableStream.prototype.pipeTo,\"pipeTo\"),n(ReadableStream.prototype.tee,\"tee\"),n(ReadableStream.prototype.values,\"values\"),\"symbol\"==typeof e.toStringTag&&Object.defineProperty(ReadableStream.prototype,e.toStringTag,{value:\"ReadableStream\",configurable:!0}),\"symbol\"==typeof e.asyncIterator&&Object.defineProperty(ReadableStream.prototype,e.asyncIterator,{value:ReadableStream.prototype.values,writable:!0,configurable:!0});const er=e=>e.byteLength;n(er,\"size\");class ByteLengthQueuingStrategy{constructor(e){$(e,1,\"ByteLengthQueuingStrategy\"),e=Zt(e,\"First parameter\"),this._byteLengthQueuingStrategyHighWaterMark=e.highWaterMark}get highWaterMark(){if(!rr(this))throw tr(\"highWaterMark\");return this._byteLengthQueuingStrategyHighWaterMark}get size(){if(!rr(this))throw tr(\"size\");return er}}function tr(e){return new TypeError(`ByteLengthQueuingStrategy.prototype.${e} can only be used on a ByteLengthQueuingStrategy`)}function rr(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,\"_byteLengthQueuingStrategyHighWaterMark\")&&e instanceof ByteLengthQueuingStrategy)}Object.defineProperties(ByteLengthQueuingStrategy.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),\"symbol\"==typeof e.toStringTag&&Object.defineProperty(ByteLengthQueuingStrategy.prototype,e.toStringTag,{value:\"ByteLengthQueuingStrategy\",configurable:!0});const or=()=>1;n(or,\"size\");class CountQueuingStrategy{constructor(e){$(e,1,\"CountQueuingStrategy\"),e=Zt(e,\"First parameter\"),this._countQueuingStrategyHighWaterMark=e.highWaterMark}get highWaterMark(){if(!ar(this))throw nr(\"highWaterMark\");return this._countQueuingStrategyHighWaterMark}get size(){if(!ar(this))throw nr(\"size\");return or}}function nr(e){return new TypeError(`CountQueuingStrategy.prototype.${e} can only be used on a CountQueuingStrategy`)}function ar(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,\"_countQueuingStrategyHighWaterMark\")&&e instanceof CountQueuingStrategy)}function ir(e,t,r){return I(e,r),r=>w(e,t,[r])}function lr(e,t,r){return I(e,r),r=>g(e,t,[r])}function sr(e,t,r){return I(e,r),(r,o)=>w(e,t,[r,o])}Object.defineProperties(CountQueuingStrategy.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),\"symbol\"==typeof e.toStringTag&&Object.defineProperty(CountQueuingStrategy.prototype,e.toStringTag,{value:\"CountQueuingStrategy\",configurable:!0});class TransformStream{constructor(e={},t={},r={}){void 0===e&&(e=null);const o=Ye(t,\"Second parameter\"),n=Ye(r,\"Third parameter\"),a=function(e,t){F(e,t);const r=null==e?void 0:e.flush,o=null==e?void 0:e.readableType,n=null==e?void 0:e.start,a=null==e?void 0:e.transform,i=null==e?void 0:e.writableType;return{flush:void 0===r?void 0:ir(r,e,`${t} has member 'flush' that`),readableType:o,start:void 0===n?void 0:lr(n,e,`${t} has member 'start' that`),transform:void 0===a?void 0:sr(a,e,`${t} has member 'transform' that`),writableType:i}}(e,\"First parameter\");if(void 0!==a.readableType)throw new RangeError(\"Invalid readableType specified\");if(void 0!==a.writableType)throw new RangeError(\"Invalid writableType specified\");const i=$e(n,0),l=Me(n),s=$e(o,1),f=Me(o);let b;!function(e,t,r,o,n,a){function i(){return t}function l(t){return function(e,t){const r=e._transformStreamController;if(e._backpressure){return p(e._backpressureChangePromise,(()=>{if(\"erroring\"===(Ge(e._writable)?e._writable._state:e._writableState))throw Ge(e._writable)?e._writable._storedError:e._writableStoredError;return pr(r,t)}))}return pr(r,t)}(e,t)}function s(t){return function(e,t){return cr(e,t),c(void 0)}(e,t)}function u(){return function(e){const t=e._transformStreamController,r=t._flushAlgorithm();return hr(t),p(r,(()=>{if(\"errored\"===e._readableState)throw e._readableStoredError;gr(e)&&wr(e)}),(t=>{throw cr(e,t),e._readableStoredError}))}(e)}function d(){return function(e){return fr(e,!1),e._backpressureChangePromise}(e)}function f(t){return dr(e,t),c(void 0)}e._writableState=\"writable\",e._writableStoredError=void 0,e._writableHasInFlightOperation=!1,e._writableStarted=!1,e._writable=function(e,t,r,o,n,a,i){return new WritableStream({start(r){e._writableController=r;try{const t=r.signal;void 0!==t&&t.addEventListener(\"abort\",(()=>{\"writable\"===e._writableState&&(e._writableState=\"erroring\",t.reason&&(e._writableStoredError=t.reason))}))}catch(e){}return p(t(),(()=>(e._writableStarted=!0,Cr(e),null)),(t=>{throw e._writableStarted=!0,Rr(e,t),t}))},write:t=>(function(e){e._writableHasInFlightOperation=!0}(e),p(r(t),(()=>(function(e){e._writableHasInFlightOperation=!1}(e),Cr(e),null)),(t=>{throw function(e,t){e._writableHasInFlightOperation=!1,Rr(e,t)}(e,t),t}))),close:()=>(function(e){e._writableHasInFlightOperation=!0}(e),p(o(),(()=>(function(e){e._writableHasInFlightOperation=!1;\"erroring\"===e._writableState&&(e._writableStoredError=void 0);e._writableState=\"closed\"}(e),null)),(t=>{throw function(e,t){e._writableHasInFlightOperation=!1,e._writableState,Rr(e,t)}(e,t),t}))),abort:t=>(e._writableState=\"errored\",e._writableStoredError=t,n(t))},{highWaterMark:a,size:i})}(e,i,l,u,s,r,o),e._readableState=\"readable\",e._readableStoredError=void 0,e._readableCloseRequested=!1,e._readablePulling=!1,e._readable=function(e,t,r,o,n,a){return new ReadableStream({start:r=>(e._readableController=r,t().catch((t=>{Sr(e,t)}))),pull:()=>(e._readablePulling=!0,r().catch((t=>{Sr(e,t)}))),cancel:t=>(e._readableState=\"closed\",o(t))},{highWaterMark:n,size:a})}(e,i,d,f,n,a),e._backpressure=void 0,e._backpressureChangePromise=void 0,e._backpressureChangePromise_resolve=void 0,fr(e,!0),e._transformStreamController=void 0}(this,u((e=>{b=e})),s,f,i,l),function(e,t){const r=Object.create(TransformStreamDefaultController.prototype);let o,n;o=void 0!==t.transform?e=>t.transform(e,r):e=>{try{return _r(r,e),c(void 0)}catch(e){return d(e)}};n=void 0!==t.flush?()=>t.flush(r):()=>c(void 0);!function(e,t,r,o){t._controlledTransformStream=e,e._transformStreamController=t,t._transformAlgorithm=r,t._flushAlgorithm=o}(e,r,o,n)}(this,a),void 0!==a.start?b(a.start(this._transformStreamController)):b(void 0)}get readable(){if(!ur(this))throw yr(\"readable\");return this._readable}get writable(){if(!ur(this))throw yr(\"writable\");return this._writable}}function ur(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,\"_transformStreamController\")&&e instanceof TransformStream)}function cr(e,t){Sr(e,t),dr(e,t)}function dr(e,t){hr(e._transformStreamController),function(e,t){e._writableController.error(t);\"writable\"===e._writableState&&Tr(e,t)}(e,t),e._backpressure&&fr(e,!1)}function fr(e,t){void 0!==e._backpressureChangePromise&&e._backpressureChangePromise_resolve(),e._backpressureChangePromise=u((t=>{e._backpressureChangePromise_resolve=t})),e._backpressure=t}Object.defineProperties(TransformStream.prototype,{readable:{enumerable:!0},writable:{enumerable:!0}}),\"symbol\"==typeof e.toStringTag&&Object.defineProperty(TransformStream.prototype,e.toStringTag,{value:\"TransformStream\",configurable:!0});class TransformStreamDefaultController{constructor(){throw new TypeError(\"Illegal constructor\")}get desiredSize(){if(!br(this))throw mr(\"desiredSize\");return vr(this._controlledTransformStream)}enqueue(e){if(!br(this))throw mr(\"enqueue\");_r(this,e)}error(e){if(!br(this))throw mr(\"error\");var t;t=e,cr(this._controlledTransformStream,t)}terminate(){if(!br(this))throw mr(\"terminate\");!function(e){const t=e._controlledTransformStream;gr(t)&&wr(t);const r=new TypeError(\"TransformStream terminated\");dr(t,r)}(this)}}function br(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,\"_controlledTransformStream\")&&e instanceof TransformStreamDefaultController)}function hr(e){e._transformAlgorithm=void 0,e._flushAlgorithm=void 0}function _r(e,t){const r=e._controlledTransformStream;if(!gr(r))throw new TypeError(\"Readable side is not in a state that permits enqueue\");try{!function(e,t){e._readablePulling=!1;try{e._readableController.enqueue(t)}catch(t){throw Sr(e,t),t}}(r,t)}catch(e){throw dr(r,e),r._readableStoredError}const o=function(e){return!function(e){if(!gr(e))return!1;if(e._readablePulling)return!0;if(vr(e)>0)return!0;return!1}(e)}(r);o!==r._backpressure&&fr(r,!0)}function pr(e,t){return p(e._transformAlgorithm(t),void 0,(t=>{throw cr(e._controlledTransformStream,t),t}))}function mr(e){return new TypeError(`TransformStreamDefaultController.prototype.${e} can only be used on a TransformStreamDefaultController`)}function yr(e){return new TypeError(`TransformStream.prototype.${e} can only be used on a TransformStream`)}function gr(e){return!e._readableCloseRequested&&\"readable\"===e._readableState}function wr(e){e._readableState=\"closed\",e._readableCloseRequested=!0,e._readableController.close()}function Sr(e,t){\"readable\"===e._readableState&&(e._readableState=\"errored\",e._readableStoredError=t),e._readableController.error(t)}function vr(e){return e._readableController.desiredSize}function Rr(e,t){\"writable\"!==e._writableState?qr(e):Tr(e,t)}function Tr(e,t){e._writableState=\"erroring\",e._writableStoredError=t,!function(e){return e._writableHasInFlightOperation}(e)&&e._writableStarted&&qr(e)}function qr(e){e._writableState=\"errored\"}function Cr(e){\"erroring\"===e._writableState&&qr(e)}Object.defineProperties(TransformStreamDefaultController.prototype,{enqueue:{enumerable:!0},error:{enumerable:!0},terminate:{enumerable:!0},desiredSize:{enumerable:!0}}),n(TransformStreamDefaultController.prototype.enqueue,\"enqueue\"),n(TransformStreamDefaultController.prototype.error,\"error\"),n(TransformStreamDefaultController.prototype.terminate,\"terminate\"),\"symbol\"==typeof e.toStringTag&&Object.defineProperty(TransformStreamDefaultController.prototype,e.toStringTag,{value:\"TransformStreamDefaultController\",configurable:!0});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/web-streams-polyfill/dist/ponyfill.mjs\n");

/***/ })

};
;