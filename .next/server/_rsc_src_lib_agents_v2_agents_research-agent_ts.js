"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_agents_v2_agents_research-agent_ts";
exports.ids = ["_rsc_src_lib_agents_v2_agents_research-agent_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/agents/v2/agents/research-agent.ts":
/*!****************************************************!*\
  !*** ./src/lib/agents/v2/agents/research-agent.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ResearchAgent: () => (/* binding */ ResearchAgent),\n/* harmony export */   createResearchAgent: () => (/* binding */ createResearchAgent)\n/* harmony export */ });\n/* harmony import */ var _core_state_schema__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../core/state-schema */ \"(rsc)/./src/lib/agents/v2/core/state-schema.ts\");\n/* harmony import */ var _tools_tavily_search__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../tools/tavily-search */ \"(rsc)/./src/lib/agents/v2/tools/tavily-search.ts\");\n/* harmony import */ var _utils_logger__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/logger */ \"(rsc)/./src/lib/agents/v2/utils/logger.ts\");\n/**\n * Invincible V.2 - Advanced Research Agent\n * Autonomous research with competitive intelligence and market analysis\n */ \n\n\nclass ResearchAgent {\n    constructor(config){\n        this.config = {\n            maxSearchQueries: 10,\n            searchDepth: 'advanced',\n            competitiveAnalysis: true,\n            trendAnalysis: true,\n            ...config\n        };\n        this.searchTool = new _tools_tavily_search__WEBPACK_IMPORTED_MODULE_1__.TavilySearchTool({\n            apiKey: this.config.tavilyApiKey,\n            searchDepth: this.config.searchDepth,\n            maxResults: 12,\n            includeImages: true,\n            includeAnswer: true,\n            includeRawContent: true\n        });\n        this.researchLogger = (0,_utils_logger__WEBPACK_IMPORTED_MODULE_2__.createLogger)({\n            agentName: 'ResearchAgent',\n            phase: 'research'\n        });\n        this.researchLogger.info('Research agent ready');\n    }\n    /**\n   * Main research execution method\n   */ async execute(state) {\n        const researchStartTime = Date.now();\n        this.researchLogger.info(`Research started: \"${state.requirements.topic}\"`);\n        try {\n            // Update workflow status\n            let updatedState = _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.updateWorkflow(state, {\n                currentAgent: 'research_agent',\n                progress: 10\n            });\n            updatedState = _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.addMessage(updatedState, {\n                agent: 'research_agent',\n                action: 'starting_research',\n                data: {\n                    topic: state.requirements.topic\n                },\n                status: 'in_progress'\n            });\n            // Phase 1: Core topic research\n            const coreResearch = await this.conductCoreResearch(state.requirements.topic);\n            updatedState = _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.updateResearch(updatedState, coreResearch);\n            this.researchLogger.info(`Core research: ${coreResearch.results.length} results in ${Date.now() - researchStartTime}ms`);\n            updatedState = _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.updateWorkflow(updatedState, {\n                progress: 25\n            });\n            updatedState = _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.addMessage(updatedState, {\n                agent: 'research_agent',\n                action: 'core_research_completed',\n                data: {\n                    resultsCount: coreResearch.results.length\n                },\n                status: 'completed'\n            });\n            // Phase 2: Competitive analysis\n            if (this.config.competitiveAnalysis) {\n                const competitiveAnalysis = await this.conductCompetitiveAnalysis(state.requirements.topic);\n                updatedState = _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.updateAnalysis(updatedState, {\n                    competitive: {\n                        topContent: competitiveAnalysis.competitorContent,\n                        contentGaps: competitiveAnalysis.contentGaps,\n                        opportunities: competitiveAnalysis.opportunities\n                    }\n                });\n                updatedState = _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.updateWorkflow(updatedState, {\n                    progress: 50\n                });\n                updatedState = _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.addMessage(updatedState, {\n                    agent: 'research_agent',\n                    action: 'competitive_analysis_completed',\n                    data: {\n                        competitors: competitiveAnalysis.topCompetitors.length,\n                        opportunities: competitiveAnalysis.opportunities.length\n                    },\n                    status: 'completed'\n                });\n            }\n            // Phase 3: SEO intelligence gathering\n            const seoIntelligence = await this.gatherSEOIntelligence(state.requirements.topic);\n            updatedState = _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.updateAnalysis(updatedState, {\n                seo: {\n                    ...updatedState.analysis.seo,\n                    searchIntent: seoIntelligence.searchIntent,\n                    primaryKeywords: seoIntelligence.relatedQueries.slice(0, 5),\n                    secondaryKeywords: seoIntelligence.relatedQueries.slice(5, 15)\n                }\n            });\n            updatedState = _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.updateWorkflow(updatedState, {\n                progress: 75\n            });\n            updatedState = _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.addMessage(updatedState, {\n                agent: 'research_agent',\n                action: 'seo_intelligence_completed',\n                data: {\n                    searchIntent: seoIntelligence.searchIntent,\n                    keywordsFound: seoIntelligence.relatedQueries.length\n                },\n                status: 'completed'\n            });\n            // Phase 4: Trend analysis (if enabled)\n            if (this.config.trendAnalysis) {\n                const trendAnalysis = await this.conductTrendAnalysis(state.requirements.topic);\n                updatedState = _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.addMessage(updatedState, {\n                    agent: 'research_agent',\n                    action: 'trend_analysis_completed',\n                    data: {\n                        emergingTrends: trendAnalysis.emergingTrends,\n                        recentDevelopments: trendAnalysis.recentDevelopments\n                    },\n                    status: 'completed'\n                });\n            }\n            // Phase 5: Comprehensive query variations\n            const variationResearch = await this.conductVariationResearch(state.requirements.topic);\n            variationResearch.forEach((research)=>{\n                updatedState = _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.updateResearch(updatedState, research);\n            });\n            // Mark research as completed\n            updatedState = {\n                ...updatedState,\n                research: {\n                    ...updatedState.research,\n                    completed: true\n                }\n            };\n            updatedState = _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.updateWorkflow(updatedState, {\n                progress: 100,\n                nextAgent: 'content_agent',\n                completedAgents: [\n                    ...updatedState.workflow.completedAgents,\n                    'research_agent'\n                ]\n            });\n            updatedState = _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.addMessage(updatedState, {\n                agent: 'research_agent',\n                action: 'research_completed',\n                data: {\n                    totalQueries: updatedState.research.data.length,\n                    totalResults: updatedState.research.data.reduce((sum, r)=>sum + r.results.length, 0)\n                },\n                status: 'completed'\n            });\n            return updatedState;\n        } catch (error) {\n            console.error('Research Agent error:', error);\n            const errorState = _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.updateWorkflow(state, {\n                errors: [\n                    ...state.workflow.errors,\n                    `Research failed: ${error}`\n                ]\n            });\n            return _core_state_schema__WEBPACK_IMPORTED_MODULE_0__.StateManager.addMessage(errorState, {\n                agent: 'research_agent',\n                action: 'research_failed',\n                data: {\n                    error: error.toString()\n                },\n                status: 'error'\n            });\n        }\n    }\n    /**\n   * Conduct core research on the main topic\n   */ async conductCoreResearch(topic) {\n        const searchResponse = await this.searchTool.enhancedSearch(`${topic} comprehensive guide complete overview`, {\n            maxResults: 15,\n            searchDepth: 'advanced'\n        });\n        return {\n            query: `Core research: ${topic}`,\n            results: searchResponse.results,\n            competitorAnalysis: {\n                topCompetitors: [],\n                contentGaps: [],\n                strengthsWeaknesses: []\n            },\n            searchDepth: 'advanced',\n            timestamp: Date.now()\n        };\n    }\n    /**\n   * Conduct comprehensive competitive analysis\n   */ async conductCompetitiveAnalysis(topic) {\n        return await this.searchTool.competitiveAnalysis(topic);\n    }\n    /**\n   * Gather SEO intelligence and search insights\n   */ async gatherSEOIntelligence(topic) {\n        return await this.searchTool.seoIntelligence(topic);\n    }\n    /**\n   * Conduct trend analysis for current market insights\n   */ async conductTrendAnalysis(topic) {\n        return await this.searchTool.trendAnalysis(topic);\n    }\n    /**\n   * Research topic variations and related queries\n   */ async conductVariationResearch(topic) {\n        const variations = [\n            `${topic} best practices tips`,\n            `${topic} benefits advantages`,\n            `${topic} challenges problems solutions`,\n            `${topic} examples case studies`,\n            `${topic} tools software resources`,\n            `${topic} future trends 2025`\n        ];\n        const searchResults = await this.searchTool.multiQueryResearch(topic, variations);\n        return searchResults.map((result, index)=>({\n                query: result.query,\n                results: result.results,\n                competitorAnalysis: {\n                    topCompetitors: [],\n                    contentGaps: [],\n                    strengthsWeaknesses: []\n                },\n                searchDepth: 'advanced',\n                timestamp: Date.now() + index\n            }));\n    }\n    /**\n   * Extract key insights from research data\n   */ extractKeyInsights(researchData) {\n        const allResults = researchData.flatMap((r)=>r.results);\n        // Extract top sources (domains with highest scores)\n        const domainScores = new Map();\n        allResults.forEach((result)=>{\n            const domain = result.domain;\n            domainScores.set(domain, (domainScores.get(domain) || 0) + result.score);\n        });\n        const topSources = Array.from(domainScores.entries()).sort((a, b)=>b[1] - a[1]).slice(0, 10).map(([domain])=>domain);\n        // Extract key topics (frequent terms across results)\n        const allText = allResults.map((r)=>r.content).join(' ').toLowerCase();\n        const words = allText.split(/\\W+/).filter((word)=>word.length > 4);\n        const wordCounts = new Map();\n        words.forEach((word)=>{\n            wordCounts.set(word, (wordCounts.get(word) || 0) + 1);\n        });\n        const keyTopics = Array.from(wordCounts.entries()).sort((a, b)=>b[1] - a[1]).slice(0, 20).map(([word])=>word);\n        // Extract expert quotes (sentences with attribution)\n        const expertQuotes = allResults.flatMap((result)=>{\n            const sentences = result.content.split(/[.!?]+/);\n            return sentences.filter((sentence)=>sentence.includes('expert') || sentence.includes('according to') || sentence.includes('research shows'));\n        }).slice(0, 10);\n        // Extract statistics (sentences with numbers/percentages)\n        const statisticsData = allResults.flatMap((result)=>{\n            const sentences = result.content.split(/[.!?]+/);\n            return sentences.filter((sentence)=>/\\d+%|\\d+\\s*(percent|million|billion|thousand)/.test(sentence));\n        }).slice(0, 15);\n        // Generate actionable insights\n        const actionableInsights = [\n            'Focus on comprehensive coverage to outperform competitors',\n            'Include current statistics and data to establish authority',\n            'Address common user questions and pain points',\n            'Leverage expert opinions and research citations',\n            'Optimize for voice search and AI assistants'\n        ];\n        return {\n            topSources,\n            keyTopics,\n            expertQuotes,\n            statisticsData,\n            actionableInsights\n        };\n    }\n    /**\n   * Generate content outline based on research\n   */ generateContentOutline(topic, researchData, competitiveAnalysis, requirements) {\n        const insights = this.extractKeyInsights(researchData);\n        const targetLength = requirements.contentLength || 2000;\n        // Base outline structure\n        const outline = [\n            `Introduction to ${topic}`,\n            `What is ${topic}? - Complete Definition and Overview`\n        ];\n        // Add sections based on content length and research insights\n        if (targetLength >= 1500) {\n            outline.push(`Key Benefits and Advantages of ${topic}`, `How ${topic} Works - Step-by-Step Process`, `Best Practices and Professional Tips`);\n        }\n        if (targetLength >= 2000) {\n            outline.push(`Common Challenges and How to Overcome Them`, `Real-World Examples and Case Studies`, `Tools and Resources for ${topic}`);\n        }\n        if (targetLength >= 3000) {\n            outline.push(`Advanced Techniques and Strategies`, `Future Trends and Predictions`, `Expert Insights and Industry Analysis`);\n        }\n        // Add conclusion\n        outline.push(`Conclusion and Next Steps`);\n        // Add FAQ section for voice search optimization\n        outline.push(`Frequently Asked Questions (FAQ)`);\n        return outline;\n    }\n    /**\n   * Validate research completeness\n   */ validateResearchCompleteness(state) {\n        const missingElements = [];\n        let qualityScore = 0;\n        // Check research data\n        if (state.research.data.length === 0) {\n            missingElements.push('No research data found');\n        } else {\n            qualityScore += 25;\n        }\n        // Check competitive analysis\n        if (state.analysis.competitive.topContent.length === 0) {\n            missingElements.push('Missing competitive analysis');\n        } else {\n            qualityScore += 25;\n        }\n        // Check SEO data\n        if (state.analysis.seo.primaryKeywords.length === 0) {\n            missingElements.push('Missing SEO keyword analysis');\n        } else {\n            qualityScore += 25;\n        }\n        // Check research depth\n        const totalResults = state.research.data.reduce((sum, r)=>sum + r.results.length, 0);\n        if (totalResults < 50) {\n            missingElements.push('Insufficient research depth');\n        } else {\n            qualityScore += 25;\n        }\n        return {\n            isComplete: missingElements.length === 0,\n            missingElements,\n            qualityScore\n        };\n    }\n}\n/**\n * Factory function to create research agent\n */ function createResearchAgent(config) {\n    return new ResearchAgent(config);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/agents/v2/agents/research-agent.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/agents/v2/tools/tavily-search.ts":
/*!**************************************************!*\
  !*** ./src/lib/agents/v2/tools/tavily-search.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TavilySearchTool: () => (/* binding */ TavilySearchTool)\n/* harmony export */ });\n/* harmony import */ var _utils_logger__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/logger */ \"(rsc)/./src/lib/agents/v2/utils/logger.ts\");\n/**\n * Tavily Search Tool - V2 Enhanced Implementation\n * Robust search capabilities with comprehensive error handling\n */ \nclass TavilySearchTool {\n    constructor(config = {}){\n        this.currentKeyIndex = 0;\n        this.lastRequestTime = 0;\n        this.minRequestInterval = 250 // 250ms between requests to avoid rate limits\n        ;\n        // Comprehensive API key collection from all sources\n        const workingKeys = [\n            '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',\n            '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',\n            '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',\n            '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',\n            '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'\n        ];\n        // Environment keys (if available)\n        const envKeys = [\n            config.apiKey,\n            \"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\",\n            process.env.TAVILY_API_KEY_2,\n            process.env.TAVILY_API_KEY_3\n        ].filter(Boolean);\n        // Combine all keys: env keys first, then working fallbacks\n        this.apiKeys = [\n            ...new Set([\n                ...envKeys,\n                ...workingKeys\n            ])\n        ];\n        this.config = {\n            apiKey: this.apiKeys[0],\n            searchDepth: 'basic',\n            maxResults: 5,\n            includeAnswer: true,\n            includeImages: false,\n            includeRawContent: false,\n            maxRetries: 3,\n            ...config\n        };\n        this.searchLogger = (0,_utils_logger__WEBPACK_IMPORTED_MODULE_0__.createLogger)({\n            agentName: 'TavilySearchTool',\n            service: 'search'\n        });\n        this.searchLogger.info('🔍 Tavily Search Tool Initialized', {\n            totalApiKeys: this.apiKeys.length,\n            searchDepth: this.config.searchDepth,\n            maxResults: this.config.maxResults,\n            includeAnswer: this.config.includeAnswer\n        });\n        // Test the first API key on initialization\n        this.validateApiKeys();\n    }\n    /**\n   * Validate API keys by testing them\n   */ async validateApiKeys() {\n        this.searchLogger.info('🧪 Validating Tavily API keys...');\n        for(let i = 0; i < Math.min(3, this.apiKeys.length); i++){\n            const key = this.apiKeys[i];\n            const isValid = await this.testApiKey(key);\n            this.searchLogger.info(`🔑 API Key ${i + 1} validation`, {\n                keyPrefix: key.substring(0, 15) + '...',\n                isValid: isValid ? '✅ Valid' : '❌ Invalid'\n            });\n            if (isValid) {\n                this.currentKeyIndex = i;\n                this.searchLogger.info(`✅ Using API key ${i + 1} as primary`);\n                break;\n            }\n        }\n    }\n    /**\n   * Test a single API key\n   */ async testApiKey(apiKey) {\n        try {\n            const response = await fetch('https://api.tavily.com/search', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'User-Agent': 'Invincible-V2-Agent/1.0'\n                },\n                body: JSON.stringify({\n                    api_key: apiKey,\n                    query: 'test',\n                    search_depth: 'basic',\n                    max_results: 1,\n                    include_answer: false,\n                    include_images: false,\n                    include_raw_content: false\n                }),\n                signal: AbortSignal.timeout(10000)\n            });\n            if (response.ok) {\n                const data = await response.json();\n                return data && Array.isArray(data.results);\n            }\n            return false;\n        } catch (error) {\n            return false;\n        }\n    }\n    /**\n   * Get the current API key and rotate to next if needed\n   */ getCurrentApiKey() {\n        return this.apiKeys[this.currentKeyIndex];\n    }\n    /**\n   * Rotate to the next API key\n   */ rotateApiKey() {\n        this.currentKeyIndex = (this.currentKeyIndex + 1) % this.apiKeys.length;\n        this.searchLogger.info(`🔄 Rotated to API key ${this.currentKeyIndex + 1}/${this.apiKeys.length}`);\n    }\n    /**\n   * Rate limiting to prevent API overload\n   */ async applyRateLimit() {\n        const timeSinceLastRequest = Date.now() - this.lastRequestTime;\n        if (timeSinceLastRequest < this.minRequestInterval) {\n            const waitTime = this.minRequestInterval - timeSinceLastRequest;\n            await new Promise((resolve)=>setTimeout(resolve, waitTime));\n        }\n        this.lastRequestTime = Date.now();\n    }\n    /**\n   * Make a single API request with comprehensive error handling\n   */ async makeApiRequest(query, options = {}) {\n        const requestId = `tavily_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;\n        const requestConfig = {\n            ...this.config,\n            ...options\n        };\n        await this.applyRateLimit();\n        const requestBody = {\n            api_key: this.getCurrentApiKey(),\n            query: query.trim(),\n            search_depth: requestConfig.searchDepth,\n            max_results: requestConfig.maxResults,\n            include_answer: requestConfig.includeAnswer,\n            include_images: requestConfig.includeImages,\n            include_raw_content: requestConfig.includeRawContent\n        };\n        this.searchLogger.info('🚀 Tavily API Request', {\n            requestId,\n            query: query.substring(0, 100) + (query.length > 100 ? '...' : ''),\n            searchDepth: requestConfig.searchDepth,\n            maxResults: requestConfig.maxResults,\n            apiKeyPrefix: this.getCurrentApiKey().substring(0, 15) + '...'\n        });\n        const startTime = Date.now();\n        try {\n            const response = await fetch('https://api.tavily.com/search', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'User-Agent': 'Invincible-V2-Agent/1.0',\n                    'Accept': 'application/json'\n                },\n                body: JSON.stringify(requestBody),\n                signal: AbortSignal.timeout(30000)\n            });\n            const responseTime = Date.now() - startTime;\n            this.searchLogger.info('📡 Tavily API Response', {\n                requestId,\n                status: response.status,\n                statusText: response.statusText,\n                responseTime: `${responseTime}ms`,\n                contentType: response.headers.get('content-type')\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                this.searchLogger.error('❌ Tavily API Error Response', {\n                    requestId,\n                    status: response.status,\n                    statusText: response.statusText,\n                    error: errorText,\n                    responseTime: `${responseTime}ms`\n                });\n                // Check if it's a quota/auth error that requires key rotation\n                if (response.status === 429 || response.status === 401 || response.status === 432) {\n                    this.searchLogger.warn('🔄 Quota/Auth error detected, rotating API key');\n                    this.rotateApiKey();\n                }\n                throw new Error(`Tavily API error ${response.status}: ${errorText}`);\n            }\n            const data = await response.json();\n            this.searchLogger.info('✅ Tavily API Success', {\n                requestId,\n                responseTime: `${responseTime}ms`,\n                resultsCount: data.results?.length || 0,\n                hasAnswer: !!data.answer,\n                query: data.query\n            });\n            return data;\n        } catch (error) {\n            const responseTime = Date.now() - startTime;\n            this.searchLogger.error('❌ Tavily API Request Failed', {\n                requestId,\n                responseTime: `${responseTime}ms`,\n                error: error.message,\n                errorType: error.name,\n                stack: error.stack\n            });\n            throw error;\n        }\n    }\n    /**\n   * Enhanced search with retry logic and fallback handling\n   */ async enhancedSearch(query, options = {}) {\n        const sessionId = `search_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;\n        this.searchLogger.info('🔍 Starting Enhanced Search', {\n            sessionId,\n            query: query.substring(0, 100) + (query.length > 100 ? '...' : ''),\n            options: Object.keys(options)\n        });\n        const startTime = Date.now();\n        let lastError = null;\n        // Try with multiple API keys if needed\n        for(let attempt = 0; attempt < Math.min(this.config.maxRetries, this.apiKeys.length); attempt++){\n            try {\n                const result = await this.makeApiRequest(query, options);\n                const totalTime = Date.now() - startTime;\n                this.searchLogger.info('🎯 Search Completed Successfully', {\n                    sessionId,\n                    totalTime: `${totalTime}ms`,\n                    attempt: attempt + 1,\n                    resultsCount: result.results.length\n                });\n                return result;\n            } catch (error) {\n                lastError = error;\n                this.searchLogger.warn(`⚠️ Search attempt ${attempt + 1} failed`, {\n                    sessionId,\n                    attempt: attempt + 1,\n                    error: lastError.message,\n                    apiKeyIndex: this.currentKeyIndex\n                });\n                // If not the last attempt, rotate key and try again\n                if (attempt < this.config.maxRetries - 1) {\n                    this.rotateApiKey();\n                    await new Promise((resolve)=>setTimeout(resolve, 1000 * (attempt + 1))); // Exponential backoff\n                }\n            }\n        }\n        // All attempts failed, return mock data as fallback\n        this.searchLogger.error('❌ All Tavily attempts failed, using fallback data', {\n            sessionId,\n            totalAttempts: this.config.maxRetries,\n            lastError: lastError?.message\n        });\n        return this.generateFallbackData(query);\n    }\n    /**\n   * Generate fallback mock data when API fails\n   */ generateFallbackData(query) {\n        this.searchLogger.info('🔄 Generating fallback data for development', {\n            query: query.substring(0, 50) + '...'\n        });\n        return {\n            query,\n            answer: `Based on available information about \"${query}\", here are the key insights for content development.`,\n            results: [\n                {\n                    title: `Understanding ${query} - Comprehensive Guide`,\n                    url: `https://example.com/guide-${query.replace(/\\s+/g, '-').toLowerCase()}`,\n                    content: `Comprehensive overview of ${query} including key concepts, benefits, implementation strategies, and best practices. This content provides foundational knowledge for creating authoritative articles.`,\n                    score: 0.95,\n                    publishedDate: new Date().toISOString().split('T')[0]\n                },\n                {\n                    title: `${query} - Latest Trends and Developments`,\n                    url: `https://example.com/trends-${query.replace(/\\s+/g, '-').toLowerCase()}`,\n                    content: `Current trends, recent developments, and emerging patterns in ${query}. Includes market analysis, expert insights, and predictions for future development.`,\n                    score: 0.90,\n                    publishedDate: new Date().toISOString().split('T')[0]\n                },\n                {\n                    title: `Best Practices for ${query} Implementation`,\n                    url: `https://example.com/best-practices-${query.replace(/\\s+/g, '-').toLowerCase()}`,\n                    content: `Expert recommendations and proven strategies for effective ${query} implementation. Covers common challenges, solutions, and optimization techniques.`,\n                    score: 0.85,\n                    publishedDate: new Date().toISOString().split('T')[0]\n                }\n            ]\n        };\n    }\n    /**\n   * Search with automatic retry and intelligent fallback\n   */ async search(query, options = {}) {\n        if (!query || query.trim().length === 0) {\n            throw new Error('Search query cannot be empty');\n        }\n        return this.enhancedSearch(query.trim(), options);\n    }\n    /**\n   * Get current tool status\n   */ getStatus() {\n        return {\n            totalApiKeys: this.apiKeys.length,\n            currentKeyIndex: this.currentKeyIndex,\n            lastRequestTime: this.lastRequestTime,\n            isReady: this.apiKeys.length > 0\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/agents/v2/tools/tavily-search.ts\n");

/***/ })

};
;